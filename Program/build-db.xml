<?xml version="1.0" encoding="UTF-8" ?>
<project basedir="." name="project-db" xmlns:cbx="antlib:com.core.cbx.script">

	<property name="cbx.script.version" value="1.0"/>
<!--	<property name="nexus.repository.url" value="http://************:8088/nexus"/>
 	<get src="${nexus.repository.url}/content/groups/public/com/core/cbx/cbx-script/${cbx.script.version}/cbx-script-${cbx.script.version}.jar" dest="ant-lib/cbx-script-${cbx.script.version}.jar"/> -->
	<taskdef resource="com/core/cbx/script/antlib.xml" classpath="ant-lib/cbx-script-${cbx.script.version}.jar" uri="antlib:com.core.cbx.script"/>

	<property name="ant-lib" value="${basedir}/ant-lib/" />
	<taskdef classpath="${ant-lib}/ant-contrib-1.0b3.jar" resource="net/sf/antcontrib/antlib.xml" />

	<import file="${ant-lib}/macro-project.xml" />

	<!-- read properties -->
	<property file="build.properties" />

	<!-- define interims folder -->
	<property name="dir.build" value="${basedir}/build" />
	<property name="dir.release" value="${basedir}/release" />
	<property name="dir.run_sql" value="${dir.build}/run_sql" />

	<!-- define temporary folder for running db scripts -->
	<property name="macro.db.tmpdir" value="${dir.build}/temp" />
	<property name="dir.build.dump.imp" value="${dir.build}/dump/imp" />
	<property name="dir.build.dump.exp" value="${dir.build}/dump/exp" />
	<property name="dir.build.dump.tmp" value="${dir.build}/dump/template" />

	<!-- =================================
    	Target: clean
    ================================== -->
	<target description="Clean interim folders" name="clean">
		<cbx:cbx-clean dir="${dir.build},${dir.release},${dir.target}" includeemptydirs="true" quiet="true"/>
	</target>

	<!-- =================================
    	Target: pack-sql
        Required parameter:
        	- dir.release.cbx-project : Directory to the cbx-project to get the jar file for building the action mapper SQL
    ================================= -->
	<target depends="clean" name="pack-sql">
		<cbx:packSql dir.build="${dir.build}" dir.target="${dir.target}" project.name="${project.name}" project.version="${project.version}" dir.release.cbx-project="${dir.release.cbx-project}" />
	</target>

	<!-- =================================
		Target: pack-report-sql
	================================== -->
	<target depends="clean" name="pack-report-sql">
		<cbx:packReportSql dir.build="${dir.build}" dir.target="${dir.target}" project.name="${project.name}" project.version="${project.version}" />
	</target>

	<!-- =================================
    	Target: pack-cpm-sql
    ================================== -->
	<target depends="clean" name="pack-cpm-sql">
		<cbx:packCpmSql dir.build="${dir.build}" dir.target="${dir.target}" project.name="${project.name}" project.version="${project.version}" />
	</target>


	<!-- =================================
		Target: pack-product-sql
    ================================== -->
	<target depends="clean" name="pack-product-sql">
		<cbx:packProductSql dir.build="${dir.build}" project.name="${project.name}" project.version="${project.version}" 
		depend.product.version="${depend.product.version}" dir.product.release="${dir.product.release}" release.product.upgrade.run.product.data.migration="${release.product.upgrade.run.product.data_migration}" release.product.upgrade.run.product.hide.new.feature="${release.product.upgrade.run.product.hide_new_feature}" release.product.upgrade.automation.domain.id.replacement="${release.product.upgrade.automation.domain_id.replacement}" domainId="${domainId}" dir.target="${dir.target}"/>
	</target>

	<!-- =================================
    	Target: pack-product-report-sql
    ================================= -->
	<target depends="clean" name="pack-product-report-sql">
		<cbx:packProductReportSql project.name="${project.name}" project.version="${project.version}" dir.target="${dir.target}" dir.build="${dir.build}"/>
	</target>


	<!-- =================================
    	Target: pack-db
    ================================== -->
	<target depends="clean, pack-sql, pack-report-sql, pack-cpm-sql, pack-product-sql, pack-product-report-sql" name="pack-db" />

	<!-- =================================
    	Target: restore-db-dump
    ================================= -->
	<target depends="clean" name="restore-db-dump">
			<cbx:restoreDbDump release.db.dump.path="${release.db.dump.path}" dir.build="${dir.build}" project.name="${project.name}" ant-lib="${ant-lib}" dir.build.dump.imp="${dir.build.dump.imp}" release.db.postscript="${release.db.postscript}" dir.run.sql="${dir.run_sql}" release.db.name="${release.db.name}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" release.db.dump.folder="${release.db.dump.folder}" release.db.dump.file="${release.db.dump.file}"/>
	</target>

    <!-- =================================
    	Target: apply-project-mask-script
    ================================== -->
	<target depends="clean" description="Apply Project Mask DB script" name="apply-project-mask-script">
		<echo>Skip Mask script for sanity test</echo>
		<!--<cbx:applyProjectMaskScript project.name="${project.name}" ant-lib="${ant-lib}" release.db.maskscript="${release.db.maskscript}" dir.run.sql="${dir.run_sql}" release.db.name="${release.db.name}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" />-->
	</target>

	<!-- =================================
    	Target: apply-project-db_scripts
    ================================== -->
	<target depends="clean" description="Apply Project DB script" name="apply-project-db_scripts">
		<cbx:applyProjectDbScripts project.name="${project.name}" project.version="${project.version}" dir.release.full="${dir.release.full}" dir.apply.script.base="${dir.apply_script.base}" dir.build.dump.tmp="${dir.build.dump.tmp}" dir.run.sql="${dir.run_sql}" dir.release="${dir.release}" release.db.name="${release.db.name}" release.db.host="${release.db.host}"  release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" />
	</target>

	<!-- =================================
        Target: apply-single-script
       ================================= -->
	<target depends="clean" description="Apply Single DB script" name="apply-single-script">
		<cbx:applySingleScript dir.apply.script.base="${dir.apply_script.base}" apply.script.path="${apply_script.path}" apply.script.user="${apply_script.user}" dir.run.sql="${dir.run_sql}" dir.release="${dir.release}" release.db.name="${release.db.name}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" />
	</target>

	<!-- =================================
        Target: restore-db-dump-report
       ================================= -->
	<target depends="clean" name="restore-db-dump-report">
		<cbx:restoreDbDumpReport release.db.dump.path="${release.db.dump.path}" dir.build="${dir.build}" dir.run.sql="${dir.run_sql}" dir.build.dump.imp="${dir.build.dump.imp}" project.version="${project.version}" project.name="${project.name}" release.db.name="${release.db.name}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}"/>
	</target>
	<!-- ====================================
        Target: apply-project-db_scripts_report
    ===================================== -->
	<target depends="clean" description="Apply Project DB script" name="apply-project-db_scripts_report">
		<cbx:applyProjectDbScriptsReport dir.release.full="${dir.release.full}" dir.run.sql="${dir.run_sql}" project.name="${project.name}" project.version="${project.version}" release.db.name="${release.db.name}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" />

	</target>

	<!-- ====================================
    	Target: apply-single-script-report
   	===================================== -->
	<target depends="clean" description="Apply Single DB script" name="apply-single-script-report">
		<cbx:applySingleScriptReport dir.apply.script.base="${dir.apply_script.base}" dir.release="${dir.release}" apply.script.path="${apply_script.path}" dir.run.sql="${dir.run_sql}" release.db.name="${release.db.name}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" project.name="${project.name}"/>
	</target>

	<!-- =================================
        Target: backup-db-dump
    =================================== -->
	<target depends="clean" description="Backup DB dump" name="backup-db-dump">
		<cbx:backupDbDump release.db.name="${release.db.name}" dir.build.dump.exp="${dir.build.dump.exp}" dir.release="${dir.release}" release.db.host="${release.db.host}" release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" project.name="${project.name}"/>
	</target>

	<!-- =================================
        Target: release-dbdump
          - dir.release.db-build : the archive folder of the project-db-build
          - dir.release.db-rpt-build : the archive folder of the project-db-rpt-build
          - release.db.dump.folder: the folder to store the releaesd db dump
       ================================= -->
	<target name="release-dbdump">
		<cbx:releaseDbdump  dir.release.db-build="${dir.release.db-build}"  release.db.dump.folder="${release.db.dump.folder}" dir.release.db-rpt-build="${dir.release.db-rpt-build}" project.name="${project.name}" project.version="${project.version}"/>
	</target>


	<target name="export-cpm-dump" depends="clean">
		<mkdir dir="${dir.target}"/>
		<exec executable="pg_dump">
			<env key="PGPASSWORD" value="${release.db.pwd}"/>
			<arg line="-h ${release.db.host}"/>
			<arg line="-p ${release.db.port}"/>
			<arg line="-U ${release.db.user}"/>
			<arg line="-t cpm"/>
			<arg line="-t cpm_milestone"/>
			<arg line="-t cpm_milestone_history"/>
			<arg line="-w"/>
			<arg line="-Fc"/>
			<arg line="-O"/>
			<arg line="-f ${dir.target}/${release.dumpfile}"/>
			<arg line="${release.db.name}"/>
		</exec>
		<var name="macro.db.tmpdir" value="build/tmp"/>
		<mkdir dir="${macro.db.tmpdir}"/>
		<concat destfile="${macro.db.tmpdir}/cbx-dropCpmTable.sql">
DROP TABLE IF EXISTS CPM_MILESTONE_HISTORY;
DROP TABLE IF EXISTS CPM_MILESTONE;
DROP TABLE IF EXISTS CPM;
		</concat>
		<cbx:execsql
				dbhost="${release.db.host}" dbport="${release.db.port}" dbname="${release.db.name}"
				dbuser="${release.db.user}" dbpwd="${release.db.pwd}"
				sqlfile="${macro.db.tmpdir}/cbx-dropCpmTable.sql"
				logfile="${macro.db.tmpdir}/cbx-dropCpmTable.sql.log"/>
		<copy tofile="${dir.release.dbdump}/${dumpfile}" file="${dir.target}/${release.dumpfile}" overwrite="true"/>
	</target>


	<target name="apply-cpm-sql" depends="clean">
		<if>
			<available file="${dir.release.full}/db_scripts_cpm" type="dir" />
            <then>
				<echo>Going to apply the ${project.name} db_scripts_cpm</echo>
				<var name="dir.run_sql.project_db_scripts_cpm" value="${dir.run.sql}/release_db_scripts_cpm" />
				<mkdir dir="${dir.run_sql.project_db_scripts_cpm}" />
				<copy preservelastmodified="true" todir="${dir.run_sql.project_db_scripts_cpm}">
					<fileset dir="${dir.release.full}/db_scripts_cpm">
						<include name="*.sql" />
						<exclude name="${project.name}.${project.version}.sql" />
					</fileset>
				</copy>
				<cbx:execdirsql dbhost="${release.db.host}" dbname="${release.db.name}"
								dbport="${release.db.port}" dbpwd="${release.db.pwd}"
								dbuser="${release.db.user}" dir="${dir.run_sql.project_db_scripts_cpm}"/>
			</then>
		</if>
	</target>

	<target name="release-cpm-dbdump" depends="clean">
		<if>
			<available file="${dir.release.cpm-db-build}/${project.name}.zip" />
			<then>
				<echo>Copy the DB dump zip file to: ${release.db.dump.folder}/${project.name}.${project.version}-cpm.zip</echo>
				<copy file="${dir.release.cpm-db-build}/${project.name}.zip" overwrite="true" preservelastmodified="true" tofile="${release.db.dump.folder}/${project.name}.${project.version}-cpm.zip" />
			</then>
		</if>
	</target>
	

	<!-- =================================
    	Target: apply-project-db_scripts
    ================================== -->
	<target depends="clean" description="only Apply Project script" name="only-apply-project-db_scripts">
		<applyProjectScriptsOnly project.name="${project.name}" project.version="${project.version}" dir.release.full="${dir.release.full}" dir.apply.script.base="${dir.apply_script.base}" dir.build.dump.tmp="${dir.build.dump.tmp}" dir.run.sql="${dir.run_sql}" dir.release="${dir.release}" release.db.name="${release.db.name}" release.db.host="${release.db.host}"  release.db.port="${release.db.port}" release.db.pwd="${release.db.pwd}" release.db.user="${release.db.user}" />
	</target>

</project>
