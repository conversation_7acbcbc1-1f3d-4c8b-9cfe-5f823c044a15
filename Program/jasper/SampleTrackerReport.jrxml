<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SampleTrackerReport" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.464100000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="388"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="1200000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.max.pages" value="2000"/>
	<import value="com.core.cbx.data.entity.*"/>
	<import value="com.core.pepl.printForm.*"/>
	<import value="com.core.cbx.common.*"/>
	<import value="org.apache.commons.collections.Predicate"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<style name="Default Style" isBlankWhenNull="true" fontSize="7">
		<box topPadding="1" leftPadding="1"/>
	</style>
	<style name="page_header_green" mode="Opaque" backcolor="#0066CC" fill="Solid" fontSize="13">
		<box topPadding="2" leftPadding="4">
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25" lineColor="#000000"/>
			<leftPen lineWidth="0.25" lineColor="#000000"/>
			<bottomPen lineWidth="0.25" lineColor="#000000"/>
			<rightPen lineWidth="0.25" lineColor="#000000"/>
		</box>
	</style>
	<style name="Static_Text_Blue" style="Default Style" mode="Transparent" backcolor="#CFDBE6" fill="Solid" hAlign="Left" vAlign="Top" fontSize="7" pdfFontName="Helvetica-Bold">
		<box topPadding="3" leftPadding="2"/>
	</style>
	<style name="Frame_Header_style" style="Default Style" backcolor="#0066CC" fontName="SansSerif" fontSize="12"/>
	<style name="listStyle" fill="Solid" hAlign="Center" vAlign="Middle" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<pen lineWidth="0.25"/>
	</style>
	<style name="style1">
		<conditionalStyle>
			<style/>
		</conditionalStyle>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#8FAFCC">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#CFDBE6" fontSize="7">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF" vAlign="Middle" fontSize="7">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F3F6F8"/>
		</conditionalStyle>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#8FAFCC">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#CFDBE6">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F3F6F8"/>
		</conditionalStyle>
	</style>
	<style name="table">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#8FAFCC">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#CFDBE6">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF" hAlign="Center" vAlign="Middle" fontSize="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box leftPadding="0">
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F3F6F8"/>
		</conditionalStyle>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<subDataset name="specMeasurement">
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="code" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="toleranceNegative" class="java.lang.String"/>
		<field name="tolerancePositive" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specMeaturementSize">
		<parameter name="numberOfSize" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="field1" class="java.math.BigDecimal"/>
		<field name="field2" class="java.math.BigDecimal"/>
		<field name="field3" class="java.math.BigDecimal"/>
		<field name="field4" class="java.math.BigDecimal"/>
		<field name="field5" class="java.math.BigDecimal"/>
		<field name="field6" class="java.math.BigDecimal"/>
		<field name="field7" class="java.math.BigDecimal"/>
		<field name="field8" class="java.math.BigDecimal"/>
		<field name="field9" class="java.math.BigDecimal"/>
		<field name="field10" class="java.math.BigDecimal"/>
		<field name="field11" class="java.math.BigDecimal"/>
		<field name="field12" class="java.math.BigDecimal"/>
		<field name="field13" class="java.math.BigDecimal"/>
		<field name="field14" class="java.math.BigDecimal"/>
		<field name="sampleSizeColumn" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="specMeaturementSize_1">
		<parameter name="numberOfSize" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="field1" class="java.lang.String"/>
		<field name="field2" class="java.lang.String"/>
		<field name="field3" class="java.lang.String"/>
		<field name="field4" class="java.lang.String"/>
		<field name="field5" class="java.lang.String"/>
		<field name="field6" class="java.lang.String"/>
		<field name="field7" class="java.lang.String"/>
		<field name="field8" class="java.lang.String"/>
		<field name="field9" class="java.lang.String"/>
		<field name="field10" class="java.lang.String"/>
		<field name="field11" class="java.lang.String"/>
		<field name="field12" class="java.lang.String"/>
		<field name="field13" class="java.lang.String"/>
		<field name="field14" class="java.lang.String"/>
		<field name="sampleSizeColumn" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="supportingImages">
		<field name="internalSeqNo" class="java.lang.Long"/>
		<field name="imageTypesValue" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="image" class="com.core.cbx.data.entity.DynamicEntity"/>
	</subDataset>
	<subDataset name="sampleDetails">
		<field name="sampleTypeName" class="java.lang.String"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="altColor" class="java.lang.String"/>
		<field name="altSizeCode" class="java.lang.String"/>
		<field name="specVersion" class="java.lang.Long"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="updatedOn" class="com.core.cbx.common.type.DateTime"/>
		<field name="resultName" class="java.lang.String"/>
		<field name="instructions" class="java.lang.String"/>
		<field name="sampleEvaluation" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="sizeCode" class="java.lang.String"/>
		<field name="isLatest" class="java.lang.Boolean"/>
		<field name="altColorAndPattern" class="java.lang.String"/>
		<field name="colorAndPatternValue" class="java.lang.String"/>
		<field name="currentFitDate" class="com.core.cbx.common.type.DateTime"/>
		<field name="currentFitResultName" class="java.lang.String"/>
		<field name="currentComments" class="java.lang.String"/>
		<variable name="currentFitResult" class="java.lang.String">
			<variableExpression><![CDATA[$F{currentFitResultName}==null? "":$F{currentFitResultName}]]></variableExpression>
		</variable>
		<variable name="currentComments" class="java.lang.String">
			<variableExpression><![CDATA[$F{currentComments}==null? "":$F{currentComments}]]></variableExpression>
		</variable>
		<variable name="lastUpdateDate" class="com.core.cbx.common.type.DateTime">
			<variableExpression><![CDATA[$F{updatedOn}]]></variableExpression>
		</variable>
		<variable name="currentFitDate" class="com.core.cbx.common.type.DateTime">
			<variableExpression><![CDATA[$F{currentFitDate}]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="materialDetails">
		<field name="materialSampleTypeName" class="java.lang.String"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="materialName" class="java.lang.String"/>
		<field name="altColor" class="java.lang.String"/>
		<field name="specVersion" class="java.lang.Long"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="updatedOn" class="com.core.cbx.common.type.DateTime"/>
		<field name="resultName" class="java.lang.String"/>
		<field name="instructions" class="java.lang.String"/>
		<field name="custCodelist1Name" class="java.lang.String"/>
		<field name="custMemoText1" class="java.lang.String"/>
		<field name="sampleEvaluation" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="isLatest" class="java.lang.Boolean"/>
		<field name="altColorAndPattern" class="java.lang.String"/>
		<field name="colorAndPatternValue" class="java.lang.String"/>
		<field name="sampleResultName" class="java.lang.String"/>
		<variable name="comments" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleEvaluation}.getString("comments")]]></variableExpression>
		</variable>
		<variable name="evaluateResultName" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleEvaluation}.getString("evaluateResultName")]]></variableExpression>
		</variable>
		<variable name="sampleResult" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleResultName}==null? "":$F{sampleResultName}]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="documentDetails">
		<field name="updatedOn" class="com.core.cbx.common.type.DateTime"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="documentTypeName" class="java.lang.String"/>
		<field name="field" class="java.lang.String"/>
		<field name="specVersion" class="java.lang.Long"/>
		<field name="description" class="java.lang.String"/>
		<field name="resultName" class="java.lang.String"/>
		<field name="instructions" class="java.lang.String"/>
		<field name="statusName" class="java.lang.String"/>
		<field name="custCodelist1Name" class="java.lang.String"/>
		<field name="custMemoText1" class="java.lang.String"/>
	</subDataset>
	<subDataset name="sampleEvaluations">
		<parameter name="JASPER_DIR" class="java.lang.String"/>
		<parameter name="measurementUnit" class="java.lang.String"/>
		<parameter name="season" class="java.lang.String"/>
		<parameter name="currentSpecVer" class="java.lang.String"/>
		<parameter name="description" class="java.lang.String"/>
		<parameter name="buyer" class="java.lang.String"/>
		<parameter name="assistant" class="java.lang.String"/>
		<parameter name="technologist" class="java.lang.String"/>
		<parameter name="trackNo" class="java.lang.String"/>
		<parameter name="theme" class="java.lang.String"/>
		<parameter name="style" class="java.lang.String"/>
		<parameter name="supplier" class="java.lang.String"/>
		<parameter name="custHcl" class="java.lang.String[]"/>
		<parameter name="PLU" class="java.lang.String"/>
		<field name="sampleEvaluation" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="altColor" class="java.lang.String"/>
		<field name="sampleTypeName" class="java.lang.String"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="altSizeCode" class="java.lang.String"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="evalMeasurementFit" class="java.util.Collection"/>
		<field name="id" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="evalAccessoriesMeasurementFit" class="java.util.Collection"/>
		<field name="sampleEvaluationImages" class="java.util.Collection"/>
		<field name="sampleType" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="parentId" class="java.lang.String"/>
		<field name="custText6" class="java.lang.String"/>
		<field name="sampleTrackerRef" class="java.lang.String"/>
		<variable name="sampleTracker" class="com.core.cbx.data.entity.DynamicEntity">
			<variableExpression><![CDATA[VisualReport.loadFullEntityByRefNo($F{sampleTrackerRef},"SampleTracker")]]></variableExpression>
		</variable>
		<variable name="sampleTypeCode" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleType}.getString("code")]]></variableExpression>
		</variable>
		<variable name="measurementSizes" class="java.util.List">
			<variableExpression><![CDATA[VisualReport.getSampleResultsFromSampleRecords($V{sampleTracker},$V{sampleTypeCode},"sampleDetail")]]></variableExpression>
		</variable>
		<variable name="colours" class="java.lang.String">
			<variableExpression><![CDATA[VisualReport.getAllColours($V{measurementSizes})]]></variableExpression>
		</variable>
		<variable name="currentVendorQualityComment" class="java.lang.String">
			<variableExpression><![CDATA[VisualReport.getAllQAComments($V{measurementSizes})]]></variableExpression>
		</variable>
		<variable name="sampleEvaluationImages" class="java.util.Collection">
			<variableExpression><![CDATA[VisualReport.getAllQASupportingImages($V{measurementSizes})]]></variableExpression>
		</variable>
		<variable name="afterWashComments" class="java.lang.String">
			<variableExpression><![CDATA[VisualReport.getAllQAComments($V{measurementSizes},"currentVendorQualityComment")]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="evalMeasurementFits">
		<field name="code" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="tolerancePositive" class="java.math.BigDecimal"/>
		<field name="toleranceNegative" class="java.math.BigDecimal"/>
		<field name="sampleMeasurement" class="java.math.BigDecimal"/>
		<field name="qaMeasurement" class="java.math.BigDecimal"/>
		<field name="qaVariance" class="java.math.BigDecimal"/>
		<field name="curRevisedMeasurement" class="java.math.BigDecimal"/>
		<field name="qaResultName" class="java.lang.String"/>
		<field name="qaComments" class="java.lang.String"/>
		<field name="vendorMeasurement" class="java.math.BigDecimal"/>
		<field name="vendorVariance" class="java.math.BigDecimal"/>
		<field name="revisedMeasurement" class="java.math.BigDecimal"/>
		<variable name="qaVariance" class="java.math.BigDecimal">
			<variableExpression><![CDATA[$F{qaMeasurement}!= null ? $F{qaMeasurement}.subtract($F{sampleMeasurement}):$F{qaVariance}]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="sampleEvaluationImages">
		<field name="internalSeqNo" class="java.lang.Long"/>
		<field name="description" class="java.lang.String"/>
		<field name="image" class="com.core.cbx.data.entity.DynamicEntity"/>
	</subDataset>
	<subDataset name="sampleMeasurementTables">
		<parameter name="sizes" class="java.util.List"/>
		<parameter name="measurementUnit" class="java.lang.String"/>
		<parameter name="custText6" class="java.lang.String"/>
		<parameter name="sampleTypeName" class="java.lang.String"/>
		<parameter name="sampleVersion" class="java.lang.String"/>
		<parameter name="colours" class="java.lang.String"/>
		<field name="index" class="java.lang.Integer"/>
		<field name="number" class="java.lang.Integer"/>
		<field name="pages" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="QAComments">
		<parameter name="currentVendorQualityComment" class="java.lang.String"/>
	</subDataset>
	<parameter name="JASPER_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Desktop\\PEGP\\PEGP-424\\jasper\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="id" class="java.lang.String"/>
	<field name="trackerNo" class="java.lang.String"/>
	<field name="item" class="com.core.cbx.data.entity.DynamicEntity"/>
	<variable name="sampleTracker" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[VisualReport.loadFullEntity($F{id},"SampleTracker")]]></variableExpression>
	</variable>
	<variable name="itemId" class="java.lang.String">
		<variableExpression><![CDATA[$F{item}.getId()]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="fullItem" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[Util.loadFullEntity($V{itemId},"Item")]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="itemParties" class="java.util.List">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("parties")]]></variableExpression>
	</variable>
	<variable name="specMaterial" class="java.util.List">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("specMaterial")]]></variableExpression>
	</variable>
	<variable name="specMeasurement" class="java.util.List">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("specMeasurement")]]></variableExpression>
	</variable>
	<variable name="itemSize" class="java.util.Collection">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("itemSize")]]></variableExpression>
	</variable>
	<variable name="custHcl1FullName" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custHcl1FullName")]]></variableExpression>
	</variable>
	<variable name="measurementUnit" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("measurementUnitName")]]></variableExpression>
	</variable>
	<variable name="buyerId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Buyer")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="buyerName" class="java.lang.String">
		<variableExpression><![CDATA[$V{buyerId} == null ? null: Util.loadFullEntity($V{buyerId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="assistantId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Assistant")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="assistantName" class="java.lang.String">
		<variableExpression><![CDATA[$V{assistantId} == null ? null: Util.loadFullEntity($V{assistantId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="TechnologistId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Technologist")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="TechnologistName" class="java.lang.String">
		<variableExpression><![CDATA[$V{TechnologistId} == null ? null: Util.loadFullEntity($V{TechnologistId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="custHcl" class="java.lang.String[]">
		<variableExpression><![CDATA[$V{custHcl1FullName} == null ? new String[]{"","","","","",""}:$V{custHcl1FullName}.split("/")]]></variableExpression>
	</variable>
	<variable name="custCodelist1Name" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custCodelist1Name")]]></variableExpression>
	</variable>
	<variable name="itemNo" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("itemNo")]]></variableExpression>
	</variable>
	<variable name="seasonName" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("seasonName")]]></variableExpression>
	</variable>
	<variable name="itemDescription" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("itemDesc")]]></variableExpression>
	</variable>
	<variable name="PLU" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custText1")]]></variableExpression>
	</variable>
	<variable name="styleType" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custCodelist2Name")]]></variableExpression>
	</variable>
	<variable name="frontImageRecord" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$V{fullItem}.getEntity("fileId")]]></variableExpression>
	</variable>
	<variable name="frontImageId" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$V{frontImageRecord} == null ? null : $V{frontImageRecord}]]></variableExpression>
	</variable>
	<variable name="comments" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("comments")]]></variableExpression>
	</variable>
	<variable name="fabrics" class="java.lang.String">
		<variableExpression><![CDATA[Pom.getMaterialNamesByType($V{specMaterial}, "CL01")]]></variableExpression>
	</variable>
	<variable name="sample_size_id" class="java.lang.String">
		<variableExpression><![CDATA[($V{fullItem}.getEntity("sampleSize") != null)?$V{fullItem}.getEntity("sampleSize").getString("id"):null]]></variableExpression>
	</variable>
	<variable name="sample_size_name" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemSize},new Find("id",$V{sample_size_id})).getString("sizeName")]]></variableExpression>
	</variable>
	<variable name="sampleDetail" class="java.util.List">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("sampleDetail")]]></variableExpression>
	</variable>
	<variable name="materialDetail" class="java.util.Collection">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("materialDetail")]]></variableExpression>
	</variable>
	<variable name="documentDetail" class="java.util.Collection">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("documentDetail")]]></variableExpression>
	</variable>
	<variable name="vendorName" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntity("vendor")==null?null:$V{sampleTracker}.getEntity("vendor").getString("businessName")]]></variableExpression>
	</variable>
	<variable name="custCodelistName1" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist1Name")]]></variableExpression>
	</variable>
	<variable name="custCodelistName2" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist2Name")]]></variableExpression>
	</variable>
	<variable name="custCodelistName3" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist3Name")]]></variableExpression>
	</variable>
	<variable name="custCodelistName4" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist4Name")]]></variableExpression>
	</variable>
	<variable name="custCodelistName5" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist5Name")]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate1" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "MSTLD10", "materialDetail", false)]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate2" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "MSTLD20", "materialDetail", false)]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate3" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "SMP-02", "sampleDetail", true)]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate4" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "SMP-03", "sampleDetail", true)]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate5" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "MSTLD70", "materialDetail", false)]]></variableExpression>
	</variable>
	<variable name="printDate" class="java.lang.String">
		<variableExpression><![CDATA[new java.text.SimpleDateFormat("dd-MMM-yyyy HH:mm:ss").format(new Date())]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="custCodelistName6" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist6Name")]]></variableExpression>
	</variable>
	<variable name="custCodelistName7" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist7Name")]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate6" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "MSTLD60", "materialDetail", false)]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate7" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "MSTLD20", "materialDetail", false)]]></variableExpression>
	</variable>
	<variable name="specMeasurementImages" class="java.util.List">
		<variableExpression><![CDATA[Pom.getSpecMeasurementImage($V{specMeasurement},"code",null)]]></variableExpression>
	</variable>
	<variable name="QAComments" class="java.util.Collection">
		<variableExpression><![CDATA[VisualReport.buildDetailsGrid(Util.sortList((List<DynamicEntity>)$V{sampleTracker}.getEntityCollection("sampleDetail"), new Sort(new String[]{"sampleType","code"}), new Sort(new String[]{"sizeCode"},true,true),new Sort(new String[]{"sampleVersion"},false)) ,null)]]></variableExpression>
	</variable>
	<variable name="BuyerComments" class="java.util.Collection">
		<variableExpression><![CDATA[VisualReport.buildDetailsGrid(Util.sortList((List<DynamicEntity>)$V{sampleTracker}.getEntityCollection("materialDetail"), new Sort(new String[]{"sampleType","code"}),new Sort(new String[]{"sizeCode","code"},true,true), new Sort(new String[]{"sampleVersion"},false)) ,null)]]></variableExpression>
	</variable>
	<variable name="DocumentComments" class="java.util.Collection">
		<variableExpression><![CDATA[VisualReport.buildDetailsGrid(Util.sortList((List<DynamicEntity>)$V{sampleTracker}.getEntityCollection("documentDetail"), new Sort(new String[]{"sampleType","code"}),new Sort(new String[]{"sizeCode","code"},true,true), new Sort(new String[]{"sampleVersion"},false)) ,null)]]></variableExpression>
	</variable>
	<variable name="sampleTrackerImages" class="java.util.Collection">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("sampleTrackerImages")]]></variableExpression>
	</variable>
	<variable name="firstHTSDate" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[$V{sampleTracker}.getDateTime("custDate1")]]></variableExpression>
	</variable>
	<variable name="measurement_type" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("measurementType")]]></variableExpression>
	</variable>
	<variable name="logo_png" class="java.lang.String">
		<variableExpression><![CDATA["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"]]></variableExpression>
		<initialValueExpression><![CDATA["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"]]></initialValueExpression>
	</variable>
	<variable name="specVersion" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("version")]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="130" splitType="Stretch">
			<textField evaluationTime="Report">
				<reportElement style="page_header_green" x="0" y="0" width="555" height="30" forecolor="#FFFFFF" backcolor="#0066CC"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{itemNo}+" - SAMPLE APPROVAL REPORT"]]></textFieldExpression>
			</textField>
			<image onErrorType="Blank">
				<reportElement x="497" y="0" width="58" height="30"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage(org.apache.commons.codec.binary.Base64.decodeBase64($V{logo_png}))]]></imageExpression>
			</image>
			<frame>
				<reportElement x="0" y="30" width="555" height="100"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank" evaluationTime="Report">
					<reportElement x="440" y="42" width="115" height="58">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getOrigImage($V{frontImageId}))]]></imageExpression>
				</image>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[SEASON]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="0" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{seasonName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="0" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[BUYER]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="0" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{buyerName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="28" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[QA]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="28" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{TechnologistName}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="440" y="28" width="115" height="14"/>
					<box topPadding="1" leftPadding="1">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{vendorName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="28" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[SUPPLIER]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="14" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[ASSISTANT]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="14" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{assistantName}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="440" y="0" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{itemNo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="0" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[STYLE]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="42" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[TRACKER NO]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="42" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{trackerNo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="14" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[DEPARTMENT]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="28" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[CLASS]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="28" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custHcl}.length > 4 ? $V{custHcl}[4]: null]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="42" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custHcl}.length > 5?$V{custHcl}[5]:null]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="14" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custHcl}.length > 3 ? $V{custHcl}[3]: null]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="42" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[SUB CLASS]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="70" width="70" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[DESCRIPTION]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="14" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[PLU]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="70" width="300" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{itemDescription}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="440" y="14" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{PLU}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="85" width="70" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[FABRICS]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="85" width="300" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{fabrics}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="56" width="115" height="14"/>
					<box topPadding="1" leftPadding="1">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{firstHTSDate} == null ? null : $V{firstHTSDate}.format("DD-MMM-YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="56" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[FIRST HTS DATE]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="56" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[STYLE TYPE]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="56" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{styleType}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="42" width="70" height="58">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[PICTURE]]></text>
				</staticText>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="251">
			<break>
				<reportElement positionType="FixRelativeToBottom" x="0" y="250" width="100" height="1">
					<printWhenExpression><![CDATA[!($V{BuyerComments}!= null && $V{BuyerComments}.size()>0) && !($V{documentDetail} != null && $V{documentDetail}.size()>0)]]></printWhenExpression>
				</reportElement>
			</break>
			<frame>
				<reportElement x="0" y="10" width="555" height="160"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textField>
					<reportElement style="Default Style" x="173" y="60" width="175" height="20">
						<printWhenExpression><![CDATA[$V{custCodelistName1} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="60" width="173" height="20">
						<printWhenExpression><![CDATA[$V{custCodelistName1} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[Lab Dip]]></text>
				</staticText>
				<textField pattern="">
					<reportElement style="Default Style" x="348" y="60" width="207" height="20">
						<printWhenExpression><![CDATA[$V{custCodelistName1} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate1} == null ? null : $V{lastUpdateDate1}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" x="173" y="40" width="175" height="20">
						<printWhenExpression><![CDATA[$V{custCodelistName3} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName3}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="40" width="173" height="20">
						<printWhenExpression><![CDATA[$V{custCodelistName3} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[Fit Sample]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="348" y="40" width="207" height="20">
						<printWhenExpression><![CDATA[$V{custCodelistName3} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate3} == null ? null : $V{lastUpdateDate3}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" mode="Opaque" x="173" y="100" width="175" height="20" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$V{custCodelistName4} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName4}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" mode="Opaque" x="0" y="100" width="173" height="20" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$V{custCodelistName4} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[PP Sample (Quality)]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" mode="Opaque" x="348" y="100" width="207" height="20" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$V{custCodelistName4} != "Rejected"]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate4} == null ? null : $V{lastUpdateDate4}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" x="173" y="140" width="175" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName5}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="140" width="173" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[Shipment Sample]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="348" y="140" width="207" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate5} == null ? null : $V{lastUpdateDate5}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" x="348" y="120" width="207" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate6} == null ? null : $V{lastUpdateDate6}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="120" width="173" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[PP Sample (Visual)]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="173" y="120" width="175" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName6}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" x="348" y="80" width="207" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate7} == null ? null : $V{lastUpdateDate7}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="80" width="173" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[Swatch]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="173" y="80" width="175" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName7}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[STATUS SUMMARY]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="173" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[SAMPLE TYPE]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="20" width="175" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[OVERALL STATUS]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="348" y="20" width="207" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[LAST UPDATED DATE]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="60" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName1}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="60" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName1}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Lab Dip]]></text>
				</staticText>
				<textField pattern="">
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="60" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName1}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate1} == null ? null : $V{lastUpdateDate1}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="40" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName3}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName3}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="40" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName3}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Fit Sample]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="40" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName3}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate3} == null ? null : $V{lastUpdateDate3}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="100" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName4}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName4}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="100" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName4}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[PP Sample (Quality)]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="100" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName4}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate4} == null ? null : $V{lastUpdateDate4}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="140" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName5}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate5} == null ? null : $V{lastUpdateDate5}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="140" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName5}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Shipment Sample]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="140" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName5}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName5}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="120" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName6}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate6} == null ? null : $V{lastUpdateDate6}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="120" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName6}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[PP Sample (Visual)]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="120" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName6}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName6}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="80" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName7}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate7} == null ? null : $V{lastUpdateDate7}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="80" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName7}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Swatch]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="80" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName7}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName7}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="181" width="555" height="20">
					<printWhenExpression><![CDATA[$V{QAComments}!= null && $V{QAComments}.size() >0]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25" lineColor="#000000"/>
						<topPen lineWidth="0.25" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineColor="#000000"/>
						<bottomPen lineWidth="0.25" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[QA COMMENTS]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement key="table 3" style="table 3" positionType="Float" x="0" y="201" width="555" height="40">
					<printWhenExpression><![CDATA[$V{QAComments}!= null && $V{QAComments}.size() >0]]></printWhenExpression>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="sampleDetails">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{QAComments})]]></dataSourceExpression>
					</datasetRun>
					<jr:columnGroup width="555">
						<jr:column width="90">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[SAMPLE TYPE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleTypeName}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleTypeName}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="60">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="60" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[COLOUR SIZE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="60" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue})+" - "+($F{sizeCode}==null?"":$F{sizeCode})," - ")," - ")]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="60" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue})+" - "+($F{sizeCode}==null?"":$F{sizeCode})," - ")," - ")]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="30">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="30" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[SUB#]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="30" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="70">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="70" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[LAST UPDATE DATE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitDate} == null ? null : $V{currentFitDate}.format("DD/MM/YYYY")]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitDate} == null ? null : $V{currentFitDate}.format("DD/MM/YYYY")]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="83">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="83" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[QA RESULT]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="83" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitResult}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="83" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitResult}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="222">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="222" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[QA COMMENTS]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="222" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isStrikeThrough="false"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentComments}.replaceAll("[\\t]","")]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="222" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentComments}.replaceAll("[\\t]","")]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="555" height="10"/>
				<textElement/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="241" width="555" height="10"/>
				<textElement/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
		<band height="130">
			<componentElement>
				<reportElement key="table 3" style="table 3" positionType="Float" mode="Opaque" x="0" y="20" width="555" height="40" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$V{BuyerComments}!= null && $V{BuyerComments}.size()>0]]></printWhenExpression>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="materialDetails">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{BuyerComments})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="90">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="90" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[SAMPLE TYPE]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="90" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{materialSampleTypeName}]]></textFieldExpression>
							</textField>
							<textField isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{materialSampleTypeName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="60">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="60" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[COLOUR]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="60" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue}) ," - ")," - ")]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="60" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue}) ," - ")," - ")]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="30">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="30" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[SUB#]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="30" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="70" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[LAST UPDATE DATE]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{updatedOn} == null ? null : $F{updatedOn}.format("DD/MM/YYYY")]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{updatedOn} == null ? null : $F{updatedOn}.format("DD/MM/YYYY")]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="83">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="83" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[BUYER RESULT]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="83" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{sampleResult}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="83" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{sampleResult}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="222">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="222" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[BUYER COMMENTS]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="222" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{instructions}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="222" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{instructions}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="555" height="20" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$V{BuyerComments}!= null && $V{BuyerComments}.size()>0]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25" lineColor="#000000"/>
						<topPen lineWidth="0.25" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineColor="#000000"/>
						<bottomPen lineWidth="0.25" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[BUYER COMMENTS]]></text>
				</staticText>
			</frame>
			<break>
				<reportElement x="0" y="129" width="100" height="1"/>
			</break>
		</band>
		<band height="247">
			<frame>
				<reportElement positionType="Float" x="0" y="1" width="555" height="120" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$V{DocumentComments} != null && $V{DocumentComments}.size()>0]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25" lineColor="#000000"/>
						<topPen lineWidth="0.25" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineColor="#000000"/>
						<bottomPen lineWidth="0.25" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[DOCUMENTS COMMENTS]]></text>
				</staticText>
				<componentElement>
					<reportElement key="table 3" style="table 3" positionType="Float" mode="Opaque" x="0" y="20" width="555" height="40" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$V{DocumentComments}!= null && $V{DocumentComments}.size()>0]]></printWhenExpression>
					</reportElement>
					<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
						<datasetRun subDataset="documentDetails">
							<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{DocumentComments})]]></dataSourceExpression>
						</datasetRun>
						<jr:column width="90">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[DOCUMENT TYPE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{documentTypeName}]]></textFieldExpression>
								</textField>
								<textField isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{documentTypeName}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="102">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="102" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[DESCRIPTION]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="102" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="102" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="35">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="35" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[SUB#]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="35" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="35" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="50">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="50" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[LAST UPDATE DATE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="50" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{updatedOn} == null ? null : $F{updatedOn}.format("DD/MM/YYYY")]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="50" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{updatedOn} == null ? null : $F{updatedOn}.format("DD/MM/YYYY")]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="40">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="40" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[BUYER RESULT]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="40" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{statusName}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="40" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{statusName}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[BUYER COMMENTS]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{instructions}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{instructions}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="54">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="54" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[QA STATUS]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="54" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{custCodelist1Name}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="54" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{custCodelist1Name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="94">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="94" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[QA COMMENTS]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="94" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{custMemoText1}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="94" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$F{resultName}.equals("Rejected")]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{custMemoText1}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:table>
				</componentElement>
			</frame>
			<break>
				<reportElement positionType="Float" x="0" y="0" width="100" height="1"/>
			</break>
		</band>
		<band height="247">
			<printWhenExpression><![CDATA[$V{sampleTrackerImages}!= null && $V{sampleTrackerImages}.size() >0]]></printWhenExpression>
			<break>
				<reportElement positionType="Float" x="0" y="246" width="100" height="1"/>
			</break>
			<frame>
				<reportElement positionType="Float" x="0" y="10" width="555" height="35" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[BUYER COMMENTS - SUPPORTING IMAGES]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="35" y="20" width="175" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[Description]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="210" y="20" width="345" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[Image]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="35" height="15"/>
					<box topPadding="3" leftPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[No]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement positionType="Float" x="0" y="45" width="555" height="202"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="supportingImages">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
   (Collection)$V{sampleTrackerImages}
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="202" width="555">
						<image hAlign="Center">
							<reportElement x="210" y="0" width="345" height="202"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{image}))]]></imageExpression>
						</image>
						<textField>
							<reportElement style="Default Style" x="0" y="0" width="35" height="202"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{internalSeqNo}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" x="35" y="0" width="175" height="202"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<frame>
				<reportElement x="0" y="0" width="555" height="10"/>
			</frame>
		</band>
		<band height="652">
			<componentElement>
				<reportElement x="0" y="0" width="555" height="652" isRemoveLineWhenBlank="true"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="sampleEvaluations">
						<datasetParameter name="JASPER_DIR">
							<datasetParameterExpression><![CDATA[$P{JASPER_DIR}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="measurementUnit">
							<datasetParameterExpression><![CDATA[$V{measurementUnit}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="season">
							<datasetParameterExpression><![CDATA[$V{seasonName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="currentSpecVer">
							<datasetParameterExpression><![CDATA[$V{specVersion}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="description">
							<datasetParameterExpression><![CDATA[$V{itemDescription}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="buyer">
							<datasetParameterExpression><![CDATA[$V{buyerName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="assistant">
							<datasetParameterExpression><![CDATA[$V{assistantName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="technologist">
							<datasetParameterExpression><![CDATA[$V{TechnologistName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="trackNo">
							<datasetParameterExpression><![CDATA[$F{trackerNo}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="theme">
							<datasetParameterExpression><![CDATA[$V{custCodelist1Name}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="style">
							<datasetParameterExpression><![CDATA[$V{itemNo}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="supplier">
							<datasetParameterExpression><![CDATA[$V{vendorName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="custHcl">
							<datasetParameterExpression><![CDATA[$V{custHcl}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PLU">
							<datasetParameterExpression><![CDATA[$V{PLU}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource((Collection)VisualReport.listSampleType($V{sampleDetail},null))]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="652" width="555">
						<frame>
							<reportElement x="0" y="0" width="555" height="416" isRemoveLineWhenBlank="true"/>
							<frame>
								<reportElement positionType="Float" x="0" y="214" width="555" height="202" isRemoveLineWhenBlank="true"/>
								<frame>
									<reportElement x="0" y="0" width="555" height="32" isPrintWhenDetailOverflows="true"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<staticText>
										<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<text><![CDATA[SAMPLE TYPE / ID]]></text>
									</staticText>
									<textField isStretchWithOverflow="true">
										<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<textFieldExpression><![CDATA[$F{sampleTypeName}==null? "" :$F{sampleTypeName}]]></textFieldExpression>
									</textField>
									<staticText>
										<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<text><![CDATA[COLOUR]]></text>
									</staticText>
									<staticText>
										<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<text><![CDATA[SUBMISSION]]></text>
									</staticText>
									<textField>
										<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<textFieldExpression><![CDATA[$V{colours}]]></textFieldExpression>
									</textField>
									<textField>
										<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
									</textField>
								</frame>
								<frame>
									<reportElement positionType="Float" x="0" y="32" width="555" height="170"/>
									<box>
										<pen lineWidth="0.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<staticText>
										<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
										<box topPadding="1" leftPadding="1">
											<pen lineWidth="0.25" lineColor="#000000"/>
											<topPen lineWidth="0.25" lineColor="#000000"/>
											<leftPen lineWidth="0.25" lineColor="#000000"/>
											<bottomPen lineWidth="0.25" lineColor="#000000"/>
											<rightPen lineWidth="0.25" lineColor="#000000"/>
										</box>
										<textElement textAlignment="Center">
											<font isBold="true"/>
										</textElement>
										<text><![CDATA[QUALITY COMMENTS]]></text>
									</staticText>
									<componentElement>
										<reportElement mode="Transparent" x="0" y="20" width="555" height="150" isRemoveLineWhenBlank="true"/>
										<jr:list printOrder="Vertical">
											<datasetRun subDataset="QAComments">
												<datasetParameter name="currentVendorQualityComment">
													<datasetParameterExpression><![CDATA[$V{currentVendorQualityComment}]]></datasetParameterExpression>
												</datasetParameter>
												<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			null
		, 4)
	)]]></dataSourceExpression>
											</datasetRun>
											<jr:listContents height="150" width="555">
												<staticText>
													<reportElement mode="Transparent" x="0" y="0" width="100" height="150"/>
													<box>
														<pen lineWidth="0.0"/>
														<topPen lineWidth="0.0"/>
														<leftPen lineWidth="0.0"/>
														<bottomPen lineWidth="0.0"/>
														<rightPen lineWidth="0.0"/>
													</box>
													<textElement>
														<font size="7"/>
													</textElement>
													<text><![CDATA[]]></text>
												</staticText>
											</jr:listContents>
										</jr:list>
									</componentElement>
									<frame>
										<reportElement positionType="Float" x="0" y="20" width="555" height="76"/>
										<staticText>
											<reportElement style="Static_Text_Blue" stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="100" height="76" isPrintWhenDetailOverflows="true" forecolor="#000000"/>
											<box topPadding="1" leftPadding="1">
												<pen lineWidth="0.25" lineColor="#000000"/>
												<topPen lineWidth="0.25" lineColor="#000000"/>
												<leftPen lineWidth="0.25" lineColor="#000000"/>
												<bottomPen lineWidth="0.25" lineColor="#000000"/>
												<rightPen lineWidth="0.25" lineColor="#000000"/>
											</box>
											<textElement textAlignment="Left">
												<font isBold="true"/>
											</textElement>
											<text><![CDATA[QA COMMENTS]]></text>
										</staticText>
										<textField isStretchWithOverflow="true">
											<reportElement style="Default Style" stretchType="RelativeToTallestObject" mode="Opaque" x="100" y="0" width="455" height="76"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<textElement/>
											<textFieldExpression><![CDATA[$V{currentVendorQualityComment}.replaceAll("[\\t]","")]]></textFieldExpression>
										</textField>
									</frame>
									<frame>
										<reportElement positionType="Float" x="0" y="96" width="555" height="74">
											<printWhenExpression><![CDATA[$V{sampleTypeCode}.equals("SMP-02")]]></printWhenExpression>
										</reportElement>
										<staticText>
											<reportElement style="Static_Text_Blue" stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="100" height="74" isPrintWhenDetailOverflows="true" forecolor="#000000"/>
											<box topPadding="1" leftPadding="1">
												<pen lineWidth="0.25" lineColor="#000000"/>
												<topPen lineWidth="0.25" lineColor="#000000"/>
												<leftPen lineWidth="0.25" lineColor="#000000"/>
												<bottomPen lineWidth="0.25" lineColor="#000000"/>
												<rightPen lineWidth="0.25" lineColor="#000000"/>
											</box>
											<textElement textAlignment="Left">
												<font isBold="true"/>
											</textElement>
											<text><![CDATA[AFTER WASH COMMENTS]]></text>
										</staticText>
										<textField isStretchWithOverflow="true">
											<reportElement style="Default Style" stretchType="RelativeToTallestObject" mode="Opaque" x="100" y="0" width="455" height="74"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<textElement/>
											<textFieldExpression><![CDATA[$V{afterWashComments}]]></textFieldExpression>
										</textField>
									</frame>
								</frame>
							</frame>
							<componentElement>
								<reportElement positionType="Float" x="0" y="0" width="555" height="105" isRemoveLineWhenBlank="true"/>
								<jr:list printOrder="Vertical">
									<datasetRun subDataset="sampleMeasurementTables">
										<datasetParameter name="sizes">
											<datasetParameterExpression><![CDATA[$V{measurementSizes}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="measurementUnit">
											<datasetParameterExpression><![CDATA[$P{measurementUnit}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="custText6">
											<datasetParameterExpression><![CDATA[$F{custText6}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleTypeName">
											<datasetParameterExpression><![CDATA[$F{sampleTypeName}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleVersion">
											<datasetParameterExpression><![CDATA[$F{sampleVersion}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="colours">
											<datasetParameterExpression><![CDATA[$V{colours}]]></datasetParameterExpression>
										</datasetParameter>
										<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource((Collection)VisualReport.listFitMeasurement($V{measurementSizes},5))]]></dataSourceExpression>
									</datasetRun>
									<jr:listContents height="105" width="555">
										<frame>
											<reportElement x="0" y="30" width="555" height="55" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.0"/>
												<topPen lineWidth="0.0"/>
												<leftPen lineWidth="0.0"/>
												<bottomPen lineWidth="0.0"/>
												<rightPen lineWidth="0.0"/>
											</box>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="455" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="435" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="415" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="535" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="495" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="515" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="25" height="35"/>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<textFieldExpression><![CDATA[$P{measurementUnit}==null?"Meas.":"Meas. ("+$P{measurementUnit}+")"]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
												<box topPadding="1" leftPadding="1">
													<pen lineWidth="0.25" lineColor="#000000"/>
													<topPen lineWidth="0.25" lineColor="#000000"/>
													<leftPen lineWidth="0.25" lineColor="#000000"/>
													<bottomPen lineWidth="0.25" lineColor="#000000"/>
													<rightPen lineWidth="0.25" lineColor="#000000"/>
												</box>
												<textElement textAlignment="Center">
													<font isBold="true"/>
												</textElement>
												<text><![CDATA[SAMPLE MEASUREMENTS]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="25" y="20" width="105" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Left" verticalAlignment="Middle"/>
												<text><![CDATA[Point of Measure]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="130" y="20" width="25" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<text><![CDATA[Toler.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="215" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="195" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="175" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="295" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="275" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="255" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[(($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1)]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="355" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="335" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="375" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
										</frame>
										<componentElement>
											<reportElement key="table" style="table" x="0" y="85" width="235" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index})))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
																<paragraph tabStopWidth="10"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="105">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="105" height="18"/>
															<textElement textAlignment="Left" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{tolerancePositive}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 1" style="table" x="235" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+1)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 2" style="table" x="315" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+2)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 5" style="table" x="395" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+3)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 6" style="table" x="475" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+4)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<frame>
											<reportElement x="0" y="0" width="555" height="30" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SAMPLE TYPE / ID]]></text>
											</staticText>
											<textField isStretchWithOverflow="true">
												<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleTypeName}]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[COLOUR]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SUBMISSION]]></text>
											</staticText>
											<textField>
												<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{colours}]]></textFieldExpression>
											</textField>
											<textField>
												<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleVersion}]]></textFieldExpression>
											</textField>
										</frame>
									</jr:listContents>
								</jr:list>
							</componentElement>
							<componentElement>
								<reportElement positionType="Float" x="0" y="105" width="555" height="108" isRemoveLineWhenBlank="true">
									<printWhenExpression><![CDATA[$V{sampleTypeCode}.equals("SMP-02")]]></printWhenExpression>
								</reportElement>
								<jr:list printOrder="Vertical">
									<datasetRun subDataset="sampleMeasurementTables">
										<datasetParameter name="sizes">
											<datasetParameterExpression><![CDATA[$V{measurementSizes}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="measurementUnit">
											<datasetParameterExpression><![CDATA[$P{measurementUnit}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="custText6">
											<datasetParameterExpression><![CDATA[$F{custText6}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleTypeName">
											<datasetParameterExpression><![CDATA[$F{sampleTypeName}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleVersion">
											<datasetParameterExpression><![CDATA[$F{sampleVersion}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="colours">
											<datasetParameterExpression><![CDATA[$V{colours}]]></datasetParameterExpression>
										</datasetParameter>
										<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource((Collection)VisualReport.listFitMeasurement($V{measurementSizes},5))]]></dataSourceExpression>
									</datasetRun>
									<jr:listContents height="108" width="555">
										<frame>
											<reportElement x="0" y="25" width="555" height="83" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.0"/>
												<topPen lineWidth="0.0"/>
												<leftPen lineWidth="0.0"/>
												<bottomPen lineWidth="0.0"/>
												<rightPen lineWidth="0.0"/>
											</box>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="30" width="25" height="35"/>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<textFieldExpression><![CDATA[$P{measurementUnit}==null?"Meas.":"Meas. ("+$P{measurementUnit}+")"]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="10" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
												<box topPadding="1" leftPadding="1">
													<pen lineWidth="0.25" lineColor="#000000"/>
													<topPen lineWidth="0.25" lineColor="#000000"/>
													<leftPen lineWidth="0.25" lineColor="#000000"/>
													<bottomPen lineWidth="0.25" lineColor="#000000"/>
													<rightPen lineWidth="0.25" lineColor="#000000"/>
												</box>
												<textElement textAlignment="Center">
													<font isBold="true"/>
												</textElement>
												<text><![CDATA[SAMPLE MEASUREMENTS (AFTER WASH)]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="25" y="30" width="105" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<text><![CDATA[Point of Measure]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="130" y="30" width="25" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<text><![CDATA[Toler.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="215" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="195" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="175" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="295" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="275" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="255" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[(($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1)]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="355" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="335" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="375" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="455" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="435" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="415" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="535" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="495" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="515" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
										</frame>
										<componentElement>
											<reportElement key="table" style="table" x="0" y="90" width="235" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
																<paragraph tabStopWidth="10"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="105">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box leftPadding="0"/>
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="105" height="18"/>
															<box leftPadding="1"/>
															<textElement textAlignment="Left" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{tolerancePositive}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 1" style="table" x="235" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+1))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 2" style="table" x="315" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+2))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 5" style="table" x="395" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+3))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 6" style="table" x="475" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+4))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<frame>
											<reportElement x="0" y="0" width="555" height="25" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SAMPLE TYPE / ID]]></text>
											</staticText>
											<textField isStretchWithOverflow="true">
												<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleTypeName}]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[COLOUR]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SUBMISSION]]></text>
											</staticText>
											<textField>
												<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{colours}]]></textFieldExpression>
											</textField>
											<textField>
												<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleVersion}]]></textFieldExpression>
											</textField>
										</frame>
									</jr:listContents>
								</jr:list>
							</componentElement>
						</frame>
						<frame>
							<reportElement positionType="Float" x="0" y="416" width="555" height="235" isRemoveLineWhenBlank="true"/>
							<box>
								<pen lineWidth="0.0"/>
								<topPen lineWidth="0.0"/>
								<leftPen lineWidth="0.0"/>
								<bottomPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="29" width="555" height="20" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
								<box topPadding="1" leftPadding="1">
									<pen lineWidth="0.25" lineColor="#000000"/>
									<topPen lineWidth="0.25" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Center">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[QA COMMENTS - SUPPORTING IMAGES]]></text>
							</staticText>
							<frame>
								<reportElement x="0" y="0" width="555" height="29" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
								<box>
									<pen lineWidth="0.25"/>
									<topPen lineWidth="0.25"/>
									<leftPen lineWidth="0.25"/>
									<bottomPen lineWidth="0.25"/>
									<rightPen lineWidth="0.25"/>
								</box>
								<staticText>
									<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<text><![CDATA[SAMPLE TYPE / ID]]></text>
								</staticText>
								<textField isStretchWithOverflow="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<textFieldExpression><![CDATA[$F{sampleTypeName}==null? "" :$F{sampleTypeName}]]></textFieldExpression>
								</textField>
								<staticText>
									<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<text><![CDATA[COLOUR]]></text>
								</staticText>
								<staticText>
									<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<text><![CDATA[SUBMISSION]]></text>
								</staticText>
								<textField>
									<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<textFieldExpression><![CDATA[$V{colours}]]></textFieldExpression>
								</textField>
								<textField>
									<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
							</frame>
							<componentElement>
								<reportElement key="table 1" style="table 1" x="0" y="49" width="555" height="186" isRemoveLineWhenBlank="true"/>
								<jr:table>
									<datasetRun subDataset="sampleEvaluationImages">
										<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
   VisualReport.makeListToBeShownNice($V{sampleEvaluationImages},1)
	)]]></dataSourceExpression>
									</datasetRun>
									<jr:column width="20">
										<jr:columnHeader height="15" rowSpan="1">
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="20" height="15"/>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[No]]></text>
											</staticText>
										</jr:columnHeader>
										<jr:detailCell style="table 1_TD" height="170" rowSpan="1">
											<textField>
												<reportElement style="Default Style" x="0" y="0" width="20" height="170"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$F{internalSeqNo}]]></textFieldExpression>
											</textField>
										</jr:detailCell>
									</jr:column>
									<jr:column width="200">
										<jr:columnHeader height="15" rowSpan="1">
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="200" height="15"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[Description]]></text>
											</staticText>
										</jr:columnHeader>
										<jr:detailCell style="table 1_TD" height="170" rowSpan="1">
											<textField isStretchWithOverflow="true">
												<reportElement style="Default Style" x="0" y="0" width="200" height="170"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
											</textField>
										</jr:detailCell>
									</jr:column>
									<jr:column width="335">
										<jr:columnHeader height="15" rowSpan="1">
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="335" height="15"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[Image]]></text>
											</staticText>
										</jr:columnHeader>
										<jr:detailCell style="table 1_TD" height="170" rowSpan="1">
											<image hAlign="Center">
												<reportElement x="0" y="0" width="335" height="170"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{image}))]]></imageExpression>
											</image>
										</jr:detailCell>
									</jr:column>
								</jr:table>
							</componentElement>
						</frame>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<break>
				<reportElement positionType="Float" x="0" y="651" width="100" height="1"/>
			</break>
		</band>
		<band height="652">
			<frame>
				<reportElement positionType="Float" x="0" y="1" width="555" height="28"/>
				<staticText>
					<reportElement style="page_header_green" positionType="Float" x="0" y="4" width="555" height="24" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[SIZE SPEC]]></text>
				</staticText>
			</frame>
			<image hAlign="Center" onErrorType="Blank">
				<reportElement positionType="Float" mode="Opaque" x="0" y="30" width="555" height="300">
					<printWhenExpression><![CDATA[$V{specMeasurementImages}.size()>0]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage((DynamicEntity)((Map<String, Object>)$V{specMeasurementImages}.get(0)).get("field1")))]]></imageExpression>
			</image>
			<image hAlign="Center" onErrorType="Blank">
				<reportElement positionType="Float" mode="Opaque" x="0" y="342" width="555" height="300">
					<printWhenExpression><![CDATA[$V{specMeasurementImages}.size()>1]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage((DynamicEntity)((Map<String, Object>)$V{specMeasurementImages}.get(1)).get("field1")))]]></imageExpression>
			</image>
		</band>
		<band height="90">
			<frame>
				<reportElement x="0" y="0" width="555" height="30" isPrintWhenDetailOverflows="true"/>
				<staticText>
					<reportElement style="page_header_green" x="0" y="10" width="555" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[POM - MEASUREMENT]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement style="Default Style" mode="Opaque" x="34" y="30" width="126" height="40" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Point of Measure]]></text>
			</staticText>
			<textField>
				<reportElement style="Default Style" mode="Opaque" x="0" y="30" width="34" height="40" isPrintWhenDetailOverflows="true"/>
				<box topPadding="2" leftPadding="4">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{measurementUnit}==null?"Measurement":"Measurement ("+$V{measurementUnit}+")"]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement style="listStyle" x="160" y="30" width="395" height="20" isPrintWhenDetailOverflows="true"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeaturementSize_1">
						<datasetParameter name="numberOfSize">
							<datasetParameterExpression><![CDATA[$V{itemSize} == null ? 0 : Pom.getNumberOfActiveSize($V{itemSize})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="sample_size_name">
							<datasetParameterExpression><![CDATA[$V{sample_size_name}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	Pom.getMeasurementHeader($V{itemSize},
    $V{sample_size_id},
    true)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="395">
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="0" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="28" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="56" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="84" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="112" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="140" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="168" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="196" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="224" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="252" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="280" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="308" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="336" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 11]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field13}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="364" y="0" width="31" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 12]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field14}]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="28" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(1) && "Tolerances".equals($F{field2})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="56" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(2) && "Tolerances".equals($F{field3})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="84" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(3) && "Tolerances".equals($F{field4})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="112" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(4) && "Tolerances".equals($F{field5})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(5) && "Tolerances".equals($F{field6})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="168" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(6) && "Tolerances".equals($F{field7})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="196" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(7) && "Tolerances".equals($F{field8})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="224" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(8) && "Tolerances".equals($F{field9})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="252" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(9) && "Tolerances".equals($F{field10})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(10) && "Tolerances".equals($F{field11})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="308" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(11) && "Tolerances".equals($F{field12})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="336" y="0" width="59" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(12) && "Tolerances".equals($F{field13})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(0) && "Tolerances".equals($F{field1})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(1)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="28" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(2)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="56" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(3)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="84" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(4)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="112" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(5)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(6)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="168" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(7)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="196" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(8)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="224" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(9)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="252" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(10)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(11)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="309" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(12)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}+"*"]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<componentElement>
				<reportElement style="listStyle" positionType="Float" x="160" y="50" width="395" height="20"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeaturementSize">
						<datasetParameter name="numberOfSize">
							<datasetParameterExpression><![CDATA[$V{itemSize} == null ? 0 : Pom.getNumberOfActiveSize($V{itemSize})]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
VisualReport.makeListToBeShownNice(
        Pom.getMeasurementDetail(
            Pom.exludeEntityByString($V{specMeasurement},"code","POM Sketch"),
            $V{fullItem}.getEntityCollection("specMeasurementSize"),
            $V{itemSize},
            $V{sample_size_id},
            true
        )
    , 28)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="395">
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="0" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="28" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="56" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="84" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="112" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="140" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="168" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="196" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="224" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="252" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="280" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="308" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="336" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 11]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field13}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="364" y="0" width="31" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 12]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field14}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(1)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}==null?"0.00":$F{field1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="28" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(2)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}==null?"0.00":$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="56" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1 && $F{sampleSizeColumn}.equals(3)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}==null?"0.00":$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="84" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2 && $F{sampleSizeColumn}.equals(4)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}==null?"0.00":$F{field4}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="112" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3 && $F{sampleSizeColumn}.equals(5)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}==null?"0.00":$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4 && $F{sampleSizeColumn}.equals(6)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}==null?"0.00":$F{field6}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="168" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5 && $F{sampleSizeColumn}.equals(7)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}==null?"0.00":$F{field7}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="196" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6 && $F{sampleSizeColumn}.equals(8)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}==null?"0.00":$F{field8}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="224" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7 && $F{sampleSizeColumn}.equals(9)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}==null?"0.00":$F{field9}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="252" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8 && $F{sampleSizeColumn}.equals(10)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}==null?"0.00":$F{field10}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9 && $F{sampleSizeColumn}.equals(11)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}==null?"0.00":$F{field11}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="308" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10 && $F{sampleSizeColumn}.equals(12)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}==null?"0.00":$F{field12}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<componentElement>
				<reportElement x="0" y="70" width="160" height="20"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeasurement">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
      VisualReport.makeListToBeShownNice(
         Pom.exludeEntityByString($V{specMeasurement},"code","POM Sketch")
        ,28)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="160">
						<textField>
							<reportElement style="Default Style" x="0" y="0" width="34" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="6"/>
								<paragraph tabStopWidth="10"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement style="Default Style" x="34" y="0" width="126" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font size="6"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="50"/>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement style="page_header_green" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Default Style" x="367" y="0" width="68" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Date Printed:]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="40" y="0" width="20" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="80" y="0" width="29" height="17" forecolor="#FFFFFF"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
					<reportElement style="Default Style" x="437" y="0" width="112" height="17" forecolor="#FFFFFF"/>
					<textElement verticalAlignment="Top">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{printDate}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="0" width="40" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Page]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="60" y="0" width="20" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[of]]></text>
				</staticText>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
