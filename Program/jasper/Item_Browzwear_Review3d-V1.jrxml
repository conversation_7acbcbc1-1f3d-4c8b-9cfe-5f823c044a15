<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="INSPECTION REPORT " pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="NoPages" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3e94793d-3a8e-445a-b010-62eb2ff52196">
	<property name="ireport.zoom" value="1.1269722013523664"/>
	<property name="ireport.x" value="6"/>
	<property name="ireport.y" value="1049"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<import value="org.apache.commons.collections.CollectionUtils"/>
	<style name="Sans_Normal" isDefault="true" fontName="思源黑体正常"/>
	<style name="table">
		<box>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#87CEFA">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE7FF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#C3D3D9">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#E6F9FF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="column" mode="Opaque" forecolor="#000000" backcolor="#CCFFCC" fill="Solid" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle" fontName="Arial">
		<box leftPadding="4">
			<pen lineWidth="0.25" lineColor="#CCCCCC"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="field" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#C3CFD9">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#E6F3FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab Data Text" hTextAlign="Center" hImageAlign="Center"/>
	<style name="critical_red" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="major_red" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="minor_red" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="critical_red_1" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="major_red_1" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="minor_red_1" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="critical_red_1_1" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="minor_red_1_1" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="major_red_1_1" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="critical_red_2" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="major_red_2" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="minor_red_2" hTextAlign="Left" hImageAlign="Left" vTextAlign="Middle" vImageAlign="Middle">
		<box leftPadding="4">
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 5_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 5_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 6_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 6_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="review3dImages" uuid="78080185-6ed7-4e4d-961f-f0840abceeda">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="imageSizeName1" class="java.lang.String"/>
		<field name="imageIs1" class="java.io.ByteArrayInputStream"/>
		<field name="imageSizeName2" class="java.lang.String"/>
		<field name="imageIs2" class="java.io.ByteArrayInputStream"/>
		<field name="imageSizeName3" class="java.lang.String"/>
		<field name="imageIs3" class="java.io.ByteArrayInputStream"/>
	</subDataset>
	<subDataset name="review3dSingleImage" uuid="71195adf-e546-4f58-a712-5775e5c0fecb">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="imageIs" class="java.io.ByteArrayInputStream"/>
	</subDataset>
	<field name="detailVo" class="com.cbxsoftware.rest.vo.item.Review3d.DetailVo"/>
	<field name="pageHeaderVo" class="com.cbxsoftware.rest.vo.item.Review3d.PageHeaderVo"/>
	<variable name="itemNo" class="java.lang.String">
		<variableExpression><![CDATA[$F{pageHeaderVo}.getItemNo()]]></variableExpression>
		<initialValueExpression><![CDATA[$F{pageHeaderVo}.getItemNo()]]></initialValueExpression>
	</variable>
	<variable name="review3dImageIsList" class="java.util.List">
		<variableExpression><![CDATA[$F{detailVo}.getReview3dImageIsList()]]></variableExpression>
		<initialValueExpression><![CDATA[$F{detailVo}.getReview3dImageIsList()]]></initialValueExpression>
	</variable>
	<variable name="review3dImageIsSingleList" class="java.util.List">
		<variableExpression><![CDATA[$F{detailVo}.getReview3dImageIsSingleList()]]></variableExpression>
		<initialValueExpression><![CDATA[$F{detailVo}.getReview3dImageIsSingleList()]]></initialValueExpression>
	</variable>
	<title>
		<band height="30">
			<staticText>
				<reportElement style="field" mode="Transparent" x="10" y="10" width="50" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="ce265be7-194b-4004-afaf-f980dc8f1522"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Dashed" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="思源黑体正常" isBold="false"/>
				</textElement>
				<text><![CDATA[Item No :]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement style="field" x="60" y="10" width="300" height="20" uuid="73cc2f60-7e3a-4d0b-a41d-8e54f9cbd1d4"/>
				<box leftPadding="4">
					<pen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="思源黑体正常"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{itemNo}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="520">
			<componentElement>
				<reportElement x="0" y="0" width="810" height="520" uuid="6519debf-96eb-4a4f-91fa-7a7ba7f8b402"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="review3dImages" uuid="ff2bb4de-163c-412d-bebf-01d445e2d98a">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($V{review3dImageIsList})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="520" width="810">
						<textField isBlankWhenNull="true">
							<reportElement x="125" y="450" width="30" height="30" uuid="42374aa0-8e8e-4ec9-9847-2d128b7e38d7"/>
							<textFieldExpression><![CDATA[$F{imageSizeName1}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="400" y="450" width="30" height="30" uuid="0509bad7-6234-46c8-8c2a-dc319d413509"/>
							<textFieldExpression><![CDATA[$F{imageSizeName2}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement x="680" y="450" width="30" height="30" uuid="c694f849-b9fc-4bb2-bc1f-20dbbd773a19"/>
							<textFieldExpression><![CDATA[$F{imageSizeName3}]]></textFieldExpression>
						</textField>
						<image hAlign="Center" vAlign="Middle">
							<reportElement positionType="FixRelativeToBottom" x="0" y="110" width="250" height="300" uuid="6eac5b1f-1912-42d6-9c6b-76d0c2af3102"/>
							<imageExpression><![CDATA[$F{imageIs1}]]></imageExpression>
						</image>
						<image hAlign="Center" vAlign="Middle">
							<reportElement positionType="FixRelativeToBottom" x="271" y="110" width="250" height="300" uuid="c25f5811-7287-414c-b36b-232c1e446b11"/>
							<imageExpression><![CDATA[$F{imageIs2}]]></imageExpression>
						</image>
						<image hAlign="Center" vAlign="Middle">
							<reportElement positionType="FixRelativeToBottom" x="550" y="110" width="250" height="300" uuid="9cf3386e-1f07-42da-b9c7-c039beccce52"/>
							<imageExpression><![CDATA[$F{imageIs3}]]></imageExpression>
						</image>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="520">
			<componentElement>
				<reportElement x="0" y="0" width="800" height="520" uuid="72614ce1-dfb2-448a-adc4-efb853558f63">
					<property name="net.sf.jasperreports.export.headertoolbar.table.name" value=""/>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="review3dSingleImage" uuid="c251cef1-7164-4cda-98cf-a54ab4e75684">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($V{review3dImageIsSingleList})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="520" width="800">
						<image hAlign="Center" vAlign="Middle">
							<reportElement positionType="Float" x="150" y="10" width="500" height="500" uuid="b357b480-25d1-4a80-baa2-70f989ee635c"/>
							<imageExpression><![CDATA[$F{imageIs}]]></imageExpression>
						</image>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
</jasperReport>
