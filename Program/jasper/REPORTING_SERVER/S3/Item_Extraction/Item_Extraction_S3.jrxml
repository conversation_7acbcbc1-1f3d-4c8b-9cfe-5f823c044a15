<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PEPCO Item Extraction" pageWidth="4000" pageHeight="1000" columnWidth="4000" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="20" isIgnorePagination="true">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CBX Pepco UAT"/>
	<property name="net.sf.jasperreports.export.xls.ignore.graphics" value="false"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="Product_Category" class="java.lang.String"/>
	<parameter name="bucket_name" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{bucket_name}]]></defaultValueExpression>
	</parameter>
	<parameter name="credentials_path" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{credentials_path}]]></defaultValueExpression>
	</parameter>
	<parameter name="config_s3" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{config_s3}]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[Select
item.item_no,
item.buyer_item_no,
item.is_latest,
item.season,
item.item_desc,
item.item_sizes,
color1.coloragg,
item.style_type,
cat.display_value Product_Cat,
split_part(item.report_hcl_full_name::text, '/'::text, 2) Division,
split_part(item.report_hcl_full_name::text, '/'::text, 3) Group_,
split_part(item.report_hcl_full_name::text, '/'::text, 4) Department,
split_part(item.report_hcl_full_name::text, '/'::text, 5) Sub_Dept,
split_part(item.report_hcl_full_name::text, '/'::text, 6) Cat,
split_part(item.report_hcl_full_name::text, '/'::text, 7) Sub_Cat,
material.material_name,
material.composition,
item.po_created,
vendor1.vendoragg,
-- config S3 PEPL-1504
--image.file_content,
'THUMBNAIL_B/'||image.file_path file_path
From item
------------Get Materials
Left Join(
Select
item_specification_material.material_name,
item_specification_material.composition,
item.sid,
item_specification_material.item_sid
from item
Inner Join
item_specification_material on item_specification_material.item_sid = item.sid
and
item_specification_material.parts = 'MSF'
) material on material.item_sid = item.sid
--------- Get Image
left join (
select item_att.file_path,item.sid from item
     left join item_sln on item_sln.item_sid = item.sid
         and item_sln.field_id = 'imageTypeId'
         and item_sln.display_value = 'Front Image'
         and item_sln.parent_entity = 'ItemImage'
     left join item_image on item_image.sid = item_sln.parent_id
     left join item_img on item_image.image_sid = item_img.sid
     left join item_att on COALESCE(item_img.with_mark_up_sid, item_img.original_sid) = item_att.sid
                  ) image on image.sid = item.sid
--------- Get Product Category
left join (
select item_sln.display_value, item.sid, item_sln.item_sid from item
    left join item_sln on item_sln.item_sid = item.sid
        AND item_sln.field_id = 'productCategory'
                 ) cat on cat.sid=item.sid
--------- Get All colours
LEFT JOIN (
SELECT DISTINCT
string_agg(item_color.color_pattern_short_name ,CHR(44)) coloragg,
item.sid
FROM item
JOIN item_color ON item.sid= item_color.item_sid
GROUP BY item.sid
ORDER BY item.sid
) color1 on color1.sid = item.sid
------ Get Accepted Vendor
Left Join(
Select distinct
string_agg(vendor.business_name,CHR(44)) vendoragg,
item.sid
from item
Inner Join
item_vendor_fact on item_vendor_fact.item_sid = item.sid
Inner Join
vendor on vendor.sid = item_vendor_fact.vendor_sid
GROUP BY item.sid
ORDER BY item.sid
) vendor1 on vendor1.sid = item.sid
-- Search Product Category
inner join (
select item_sln.display_value, item.sid, item_sln.item_sid from item
	inner join item_sln on item_sln.item_sid = item.sid
  	AND item_sln.field_id = 'productCategory'
    	and item_sln.display_value = (case when $P{Product_Category} != '' then $P{Product_Category} else item_sln.display_value end)
                     ) pcat on pcat.sid=item.sid

Where
item.is_latest = 'True'
and
item.doc_status = 'active'
and
item.season = 'AW18'
;]]>
	</queryString>
	<field name="item_no" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="item_no"/>
	</field>
	<field name="buyer_item_no" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="buyer_item_no"/>
	</field>
	<field name="is_latest" class="java.lang.Boolean">
		<property name="com.jaspersoft.studio.field.label" value="is_latest"/>
	</field>
	<field name="season" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="season"/>
	</field>
	<field name="item_desc" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="item_desc"/>
	</field>
	<field name="item_sizes" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="item_sizes"/>
	</field>
	<field name="coloragg" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="coloragg"/>
	</field>
	<field name="style_type" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="style_type"/>
	</field>
	<field name="product_cat" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="product_cat"/>
	</field>
	<field name="division" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="division"/>
	</field>
	<field name="group_" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="group_"/>
	</field>
	<field name="department" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="department"/>
	</field>
	<field name="sub_dept" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="sub_dept"/>
	</field>
	<field name="cat" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="cat"/>
	</field>
	<field name="sub_cat" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="sub_cat"/>
	</field>
	<field name="material_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="material_name"/>
	</field>
	<field name="composition" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="composition"/>
	</field>
	<field name="po_created" class="java.lang.Boolean">
		<property name="com.jaspersoft.studio.field.label" value="po_created"/>
	</field>
	<field name="vendoragg" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="vendoragg"/>
	</field>
	<field name="file_path" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="30" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Image]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c6f0a64f-20ca-41d4-86cf-5000c8e55b80"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Office]]></text>
			</staticText>
			<staticText>
				<reportElement x="200" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Retailer]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Division]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Product_Category]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Buyer]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Season]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Merchandiser]]></text>
			</staticText>
			<staticText>
				<reportElement x="800" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Style #]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-PGS Style #]]></text>
			</staticText>
			<staticText>
				<reportElement x="1000" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Style Description]]></text>
			</staticText>
			<staticText>
				<reportElement x="1100" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Department]]></text>
			</staticText>
			<staticText>
				<reportElement x="1200" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Category]]></text>
			</staticText>
			<staticText>
				<reportElement x="1300" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Product]]></text>
			</staticText>
			<staticText>
				<reportElement x="1400" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Sub Product]]></text>
			</staticText>
			<staticText>
				<reportElement x="1500" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Class
]]></text>
			</staticText>
			<staticText>
				<reportElement x="1600" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Fabric/Material Type
]]></text>
			</staticText>
			<staticText>
				<reportElement x="1700" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Fabric/Material]]></text>
			</staticText>
			<staticText>
				<reportElement x="1800" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Composition]]></text>
			</staticText>
			<staticText>
				<reportElement x="1900" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Weight]]></text>
			</staticText>
			<staticText>
				<reportElement x="2000" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Fabric/Material Description
]]></text>
			</staticText>
			<staticText>
				<reportElement x="2100" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Character?
]]></text>
			</staticText>
			<staticText>
				<reportElement x="2200" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Basic/Fashion?
]]></text>
			</staticText>
			<staticText>
				<reportElement x="2300" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Size Range]]></text>
			</staticText>
			<staticText>
				<reportElement x="2400" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[IT-Colours]]></text>
			</staticText>
			<staticText>
				<reportElement x="2500" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="555e5eb2-6e5d-4eb7-a747-62a2cdef34f3"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Product Cat]]></text>
			</staticText>
			<staticText>
				<reportElement x="2600" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="869fe79b-30b3-44f5-ba54-7c3cecf53e69"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Division]]></text>
			</staticText>
			<staticText>
				<reportElement x="2700" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0d09a042-9f39-411b-9d63-ff07596e099a"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Group]]></text>
			</staticText>
			<staticText>
				<reportElement x="2800" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7d6446fb-d156-4f61-aa9b-1601c61feba9"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Dept]]></text>
			</staticText>
			<staticText>
				<reportElement x="2900" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c0ede426-29f7-40ba-9d0e-142a47e54670"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Sub-Dept]]></text>
			</staticText>
			<staticText>
				<reportElement x="3000" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="632d3f4d-0c9f-4beb-8f95-6bbde4a873f1"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Cat]]></text>
			</staticText>
			<staticText>
				<reportElement x="3100" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c80ab2e6-1926-4992-b61d-5a014e5bbe58"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Sub-Cat]]></text>
			</staticText>
			<staticText>
				<reportElement x="3200" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ed91a6f0-bae7-4b36-91ab-a10f0d47defc"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[PO Created?]]></text>
			</staticText>
			<staticText>
				<reportElement x="3300" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="74661df0-1c3f-4103-9f7f-036a2868ace0"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Confirmed Vendor]]></text>
			</staticText>
			<staticText>
				<reportElement x="3400" y="0" width="100" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="432946e6-0021-475d-bffe-d82c403c00b0"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Pepco Season]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="100" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank">
				<reportElement x="0" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[javax.imageio.ImageIO.read(new java.io.ByteArrayInputStream(com.core.pepl.printForm.UtilS3.getImageFromS3($F{file_path},$P{bucket_name},$P{credentials_path},$P{config_s3})))]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c6f0a64f-20ca-41d4-86cf-5000c8e55b80"/>
				</reportElement>
				<textElement/>
			</textField>
			<staticText>
				<reportElement x="200" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[PEPCO]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[Apparel]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="800" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{item_no}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="600" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA["AW 2018"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="700" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="900" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{buyer_item_no}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1000" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{item_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1100" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1200" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1300" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1400" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1500" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1600" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1700" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1800" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{material_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1900" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2000" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{composition}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2100" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2200" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{style_type}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2300" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{item_sizes}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2400" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4ddf7c4c-070f-4859-9e4b-e7d6a82ff562"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{coloragg}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2500" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="555e5eb2-6e5d-4eb7-a747-62a2cdef34f3"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{product_cat}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2600" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="869fe79b-30b3-44f5-ba54-7c3cecf53e69"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{division}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2700" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0d09a042-9f39-411b-9d63-ff07596e099a"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{group_}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2800" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7d6446fb-d156-4f61-aa9b-1601c61feba9"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{department}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2900" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c0ede426-29f7-40ba-9d0e-142a47e54670"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{sub_dept}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3000" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="632d3f4d-0c9f-4beb-8f95-6bbde4a873f1"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{cat}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3100" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c80ab2e6-1926-4992-b61d-5a014e5bbe58"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{sub_cat}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3200" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ed91a6f0-bae7-4b36-91ab-a10f0d47defc"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{po_created}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3300" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="74661df0-1c3f-4103-9f7f-036a2868ace0"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{vendoragg}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="3400" y="0" width="100" height="100">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="432946e6-0021-475d-bffe-d82c403c00b0"/>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{season}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
