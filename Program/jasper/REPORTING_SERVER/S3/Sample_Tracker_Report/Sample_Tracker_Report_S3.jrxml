<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Sample_Tracking_Report" pageWidth="9960" pageHeight="5000" columnWidth="600" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isIgnorePagination="true">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CBX Pepco UAT"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="Item_Vendor">
		<property name="com.jaspersoft.studio.data.sql.tables" value="dmVuZG9yIEFTICwxNzksMTUsZmMyOWE2MjctMzI2MC00OWY3LTljYjYtNTE1YmM5Y2QwNTU4O2l0 ZW1fdmVuZG9yICwzODAsMTQsZmQ3ZGJkNTktODZhNy00N2IxLTgxOTMtYzQ5MzhlOTEyODY1Ow=="/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CBX Pepco DEV"/>
		<queryString>
			<![CDATA[SELECT item_vendor.item_id,
	item_vendor.vendor_id,
	vendor.business_name
FROM vendor
	INNER JOIN item_vendor ON
	 item_vendor.vendor_id = vendor.sid]]>
		</queryString>
		<field name="item_id" class="java.lang.String"/>
		<field name="vendor_id" class="java.lang.String"/>
		<field name="business_name" class="java.lang.String"/>
	</subDataset>
	<subDataset name="Dataset1">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CBX Pepco DEV"/>
		<queryString>
			<![CDATA[SELECT code_list_entry.name
 FROM   public.code_list code_list
 INNER JOIN public.code_list_entry code_list_entry ON code_list.sid=code_list_entry.code_list_sys_id
 AND  code_list.is_latest=TRUE
 AND code_list.name='PRODUCT_CATEGORY'
 AND code_list_entry.code<>'CL900'
 ORDER BY code_list_entry.code]]>
		</queryString>
		<field name="name" class="java.lang.String"/>
	</subDataset>
	<parameter name="Product_Category" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="Season" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="Item_No" class="java.lang.String"/>
	<parameter name="bucket_name" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{bucket_name}]]></defaultValueExpression>
	</parameter>
	<parameter name="credentials_path" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA[$P{credentials_path}]]></defaultValueExpression>
	</parameter>
	<parameter name="config_s3" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{config_s3}]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT distinct
				item.item_no,
                item.item_desc,
                item.season_desc,
                cat.display_value Product_Cat,
                item.report_hcl_full_name Hierarchy,
                item.status,
                vendor1.vendoragg Vendors,
                st.vendor_name,
                st.tracker_no,
                ld.ldagg,
                sw.swagg,
                ppv.ppvagg,
                ppq.ppqagg,
                ship.shipagg,
                fit.fitagg,
                st.first_fit_sample_desc fit_sample_desc,
                st.pp_sample_visual_desc,
                st.pp_sample_quality_desc,
                st.shipment_sample_desc,
                st.lab_dips_desc,
                st.swatch_desc,
                st.plu,
                st.requested_handover_date first_handover_date,
                st.updated_on,
                -- config S3 PEPL-1504
                --image.file_content,
	      'THUMBNAIL_B/'||image.file_path file_path
FROM item
--------- Get Image
left join (
select item_att.file_path,item.sid from item
     left join item_sln on item_sln.item_sid = item.sid
     	and item_sln.field_id = 'imageTypeId'
     	and item_sln.display_value = 'Front Image'
     	and item_sln.parent_entity = 'ItemImage'
     left join item_image on item_image.sid = item_sln.parent_id
     left join item_img on item_image.image_sid = item_img.sid
     left join item_att on COALESCE(item_img.with_mark_up_sid, item_img.original_sid) = item_att.sid
      			) image on image.sid = item.sid

--------- Get Product Category
left join (
select item_sln.display_value, item.sid, item_sln.item_sid from item
	left join item_sln on item_sln.item_sid = item.sid
    	AND item_sln.field_id = 'productCategory'
                 ) cat on cat.sid=item.sid

--------- Search Product Category
inner join (
select item_sln.display_value, item.sid, item_sln.item_sid from item
	inner join item_sln on item_sln.item_sid = item.sid
    	AND item_sln.field_id = 'productCategory'
		and item_sln.display_value = (case when $P{Product_Category} != '' then $P{Product_Category} else item_sln.display_value end)
                      ) pcat on pcat.sid=item.sid

--------- Get Vendors
left join (
select item.sid, string_agg( vendor.business_name, CHR(13)) vendoragg from item
                left join item_vendor_fact on item.sid = item_vendor_fact.item_sid
                LEFT JOIN vendor ON item_vendor_fact.vendor_sid =vendor.sid
                GROUP BY  item.sid
     ORDER BY  item.sid DESC) vendor1 on vendor1.sid = item.sid

--------- Get Sample Tracker
left join (
select sample_tracker.vendor_name, item.sid, sample_tracker.item_sid,  sample_tracker.tracker_no, sample_tracker.first_fit_sample_desc, sample_tracker.pp_sample_visual_desc,sample_tracker.pp_sample_quality_desc,
sample_tracker.lab_dips_desc,sample_tracker.swatch_desc,sample_tracker.shipment_sample_desc, sample_tracker.plu,  sample_tracker.requested_handover_date, sample_tracker.updated_on  from item
                left join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                and  sample_tracker.doc_status != 'inactive'
                and  sample_tracker.vendor_code !='PCO-V000042'
                ) st on st.sid=item.sid
--------- Get Sample Tracker - Lab Dips
left join (
select item.sid sid,
item.item_no,
material_detail.sample_tracker_sid ,
string_agg(material_detail.alternate_color|| '-v' || material_detail.ver|| '-' || material_detail.result_desc|| ' on ' || material_detail.received_date ,CHR(13)) ldagg
from item
                join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                join material_detail on material_detail.sample_tracker_sid = sample_tracker.sid
                and  material_detail.sample_type = 'MSTLD10'
                and  material_detail.is_inactive = 'False'
                and  sample_tracker.doc_status != 'inactive'
                GROUP BY item.sid, material_detail.sample_tracker_sid,item.item_no
                ORDER BY item.item_no
                ) ld on ld.sid=item.sid

--------- Get Sample Tracker - Swatch
left join (
select item.sid sid,
item.item_no,
material_detail.sample_tracker_sid ,
string_agg(material_detail.alternate_color|| '-v' || material_detail.ver|| '-' || material_detail.result_desc|| ' on ' || material_detail.received_date ,CHR(13)) swagg
from item
                join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                join material_detail on material_detail.sample_tracker_sid = sample_tracker.sid
                and  material_detail.sample_type = 'MSTLD20'
                and material_detail.is_inactive = 'False'
                and  sample_tracker.doc_status != 'inactive'
                GROUP BY item.sid, material_detail.sample_tracker_sid,item.item_no
                ORDER BY item.item_no
                ) sw on sw.sid=item.sid

 --------- Get Sample Tracker - PP Sample (Visual)
left join (
select item.sid sid,
item.item_no,
material_detail.sample_tracker_sid ,
string_agg(material_detail.alternate_color|| '-v' || material_detail.ver|| '-' || material_detail.result_desc|| ' on ' || material_detail.received_date ,CHR(13)) ppvagg
from item
                join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                join material_detail on material_detail.sample_tracker_sid = sample_tracker.sid
                and  material_detail.sample_type = 'MSTLD60'
                and  material_detail.is_inactive = 'False'
                and  sample_tracker.doc_status != 'inactive'
                GROUP BY item.sid, material_detail.sample_tracker_sid,item.item_no
                ORDER BY item.item_no
                ) ppv on ppv.sid=item.sid

  --------- Get Sample Tracker - Shipment Sample
left join (
select item.sid sid,
item.item_no,
material_detail.sample_tracker_sid ,
string_agg(material_detail.alternate_color|| '-' || material_detail.size_code ||'-v' || material_detail.ver|| '-' || material_detail.result_desc|| ' on ' || material_detail.received_date ,CHR(13)) shipagg
from item
                join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                join material_detail on material_detail.sample_tracker_sid = sample_tracker.sid
                and  material_detail.sample_type = 'MSTLD70'
                and  material_detail.is_inactive = 'False'
                and  sample_tracker.doc_status != 'inactive'
                GROUP BY item.sid, material_detail.sample_tracker_sid,item.item_no
                ORDER BY item.item_no
                ) ship on ship.sid=item.sid

  --------- Get Sample Tracker - Fit Sample
left join(
select item.sid sid,
item.item_no,
sample_tracker.vendor_name,
string_agg(sample_detail.size_code  ||'-v' || sample_detail.ver|| '-' || sample_evaluation.cur_fit_result_desc  || ' on ' ||  sample_evaluation.cur_fit_date  ,CHR(13)) fitagg
from item
                join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                join sample_detail on sample_detail.sample_tracker_sid = sample_tracker.sid
                join sample_evaluation on  sample_detail.sid = sample_evaluation.sample_detail
                and  sample_detail.sample_type = 'SMP-02'
                and  sample_detail.is_inactive = 'False'
                and  sample_tracker.doc_status != 'inactive'
                and  sample_evaluation.cur_fit_result_desc is not null
                GROUP BY item.sid, sample_tracker.vendor_name,item.item_no
                ORDER BY item.item_no
                 ) fit on fit.sid=item.sid

  --------- Get Sample Tracker - PP Sample Quality
left join(
select item.sid sid,
item.item_no,
sample_tracker.vendor_name,
string_agg(   sample_detail.size_code  ||'-v' || sample_detail.ver|| '-' || sample_evaluation.cur_fit_result_desc  || ' on ' ||  sample_evaluation.cur_fit_date  ,CHR(13)) ppqagg
from item
                join sample_tracker on sample_tracker.item_sid= item.sid and sample_tracker.is_latest = true
                join sample_detail on sample_detail.sample_tracker_sid = sample_tracker.sid
                join sample_evaluation on  sample_detail.sid = sample_evaluation.sample_detail
                and  sample_detail.sample_type = 'SMP-03'
                and  sample_detail.is_inactive = 'False'
                and  sample_tracker.doc_status != 'inactive'
                and  sample_evaluation.cur_fit_result_desc is not null
                GROUP BY item.sid, sample_tracker.vendor_name,item.item_no
                ORDER BY item.item_no
                 ) ppq on ppq.sid=item.sid


WHERE item.doc_status = 'active'
AND  item.is_latest ='True'
AND item.season = (case when $P{Season}!='' then $P{Season} else item.season end)
ORDER BY  st.vendor_name,Vendors,  item.item_no DESC]]>
	</queryString>
	<field name="item_no" class="java.lang.String"/>
	<field name="item_desc" class="java.lang.String"/>
	<field name="season_desc" class="java.lang.String"/>
	<field name="product_cat" class="java.lang.String"/>
	<field name="hierarchy" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="vendors" class="java.lang.String"/>
	<field name="vendor_name" class="java.lang.String"/>
	<field name="tracker_no" class="java.lang.String"/>
	<field name="ldagg" class="java.lang.String"/>
	<field name="swagg" class="java.lang.String"/>
	<field name="ppvagg" class="java.lang.String"/>
	<field name="ppqagg" class="java.lang.String"/>
	<field name="shipagg" class="java.lang.String"/>
	<field name="fitagg" class="java.lang.String"/>
	<field name="fit_sample_desc" class="java.lang.String"/>
	<field name="pp_sample_visual_desc" class="java.lang.String"/>
	<field name="pp_sample_quality_desc" class="java.lang.String"/>
	<field name="shipment_sample_desc" class="java.lang.String"/>
	<field name="lab_dips_desc" class="java.lang.String"/>
	<field name="swatch_desc" class="java.lang.String"/>
	<field name="plu" class="java.lang.String"/>
	<field name="first_handover_date" class="java.sql.Date"/>
	<field name="updated_on" class="java.sql.Timestamp"/>
	<field name="file_path" class="java.lang.String"/>
	<variable name="Item_Status" class="java.lang.String">
		<variableExpression><![CDATA[$F{status}.equals ("concept")?"Concept":
$F{status}.equals ("customStatus01")?"Style Confirmed":
$F{status}.equals ("customStatus02")?"Costing":
$F{status}.equals ("customStatus03")?"Adopted":
$F{status}.equals ("customStatus04")?"Final":
$F{status}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Sample Tracking Report]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="100" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Item No]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Image]]></text>
			</staticText>
			<staticText>
				<reportElement x="200" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Item Description]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Season]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Product Category]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Hierarchy]]></text>
			</staticText>
			<staticText>
				<reportElement x="800" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Accepted Vendors]]></text>
			</staticText>
			<staticText>
				<reportElement x="1000" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Vendor in Tracker]]></text>
			</staticText>
			<staticText>
				<reportElement x="1100" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PLU]]></text>
			</staticText>
			<staticText>
				<reportElement x="1200" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[First HTS Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="1300" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Fit Approval]]></text>
			</staticText>
			<staticText>
				<reportElement x="1600" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Lab Dip Approval]]></text>
			</staticText>
			<staticText>
				<reportElement x="1900" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Swatch Approval]]></text>
			</staticText>
			<staticText>
				<reportElement x="2200" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PP Visual Approval]]></text>
			</staticText>
			<staticText>
				<reportElement x="2500" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PP Quality Approval]]></text>
			</staticText>
			<staticText>
				<reportElement x="2800" y="0" width="100" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Shipment_Approval]]></text>
			</staticText>
			<staticText>
				<reportElement x="1700" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Lab Dip Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="2000" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Swatch Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="2300" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PP Visual Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="2900" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Shipment Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="1400" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Fit Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="2600" y="0" width="200" height="20">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PP Quality Comments]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="120">
			<textField>
				<reportElement x="100" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{item_no}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="200" y="0" width="200" height="120">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{item_desc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="500" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{season_desc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="600" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{product_cat}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="700" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{hierarchy}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="800" y="0" width="200" height="120">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vendors}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="1000" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vendor_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Item_Status}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="0" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[javax.imageio.ImageIO.read(new java.io.ByteArrayInputStream(com.core.pepl.printForm.UtilS3.getImageFromS3($F{file_path},$P{bucket_name},$P{credentials_path},$P{config_s3})))]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="1100" y="0" width="100" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{plu}]]></textFieldExpression>
			</textField>
			<textField pattern="MM/dd/yyyy" isBlankWhenNull="true">
				<reportElement x="1200" y="0" width="100" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{first_handover_date}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1300" y="0" width="100" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fit_sample_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1600" y="0" width="100" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lab_dips_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1900" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{swatch_desc}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="2200" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pp_sample_visual_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2500" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pp_sample_quality_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2800" y="0" width="100" height="120">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipment_sample_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1700" y="0" width="200" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ldagg}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2000" y="0" width="200" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{swagg}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="2300" y="0" width="200" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ppvagg}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="2900" y="0" width="200" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipagg}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1400" y="0" width="200" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fitagg}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2600" y="0" width="200" height="120"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ppqagg}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
