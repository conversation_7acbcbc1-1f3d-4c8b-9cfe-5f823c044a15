<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="production_report" language="groovy" pageWidth="842" pageHeight="59500" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\document\\jasper+ireport\\Jasper_tutorial\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="Season" class="java.lang.String"/>
	<parameter name="Product_Category" class="java.lang.String"/>
	<parameter name="Color_Pattern" class="java.lang.String"/>
	<parameter name="department" class="java.lang.String"/>
	<parameter name="bucket_name" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{bucket_name}]]></defaultValueExpression>
	</parameter>
	<parameter name="credentials_path" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{credentials_path}]]></defaultValueExpression>
	</parameter>
	<parameter name="config_s3" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{config_s3}]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select (case
       when $P{Season} like 'SS%' then ('Dec,Jan,Feb,Mar,Apr,May,Jun')
       else ('Jun,Jul,Aug,Sep,Oct,Nov,Dec')
       end) as months]]>
	</queryString>
	<field name="months" class="java.lang.String"/>
	<pageHeader>
		<band height="20">
			<textField>
				<reportElement x="0" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(0,3).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="115" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(4,7).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="230" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(8,11).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="344" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(12,15).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="459" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(16,19).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="574" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(20,23).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="689" y="0" width="113" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(24,27).toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="220" splitType="Stretch">
			<subreport>
				<reportElement x="115" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(4,7).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="230" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(8,11).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="344" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(12,15).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="459" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(16,19).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="574" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(20,23).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="689" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(24,27).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="0" y="0" width="113" height="220"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="credentials_path">
					<subreportParameterExpression><![CDATA[$P{credentials_path}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="bucket_name">
					<subreportParameterExpression><![CDATA[$P{bucket_name}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="config_s3">
					<subreportParameterExpression><![CDATA[$P{config_s3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(0,3).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
