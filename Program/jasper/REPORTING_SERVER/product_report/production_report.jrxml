<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="production_report" language="groovy" pageWidth="842" pageHeight="59500" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="e00d0507-0fa4-41e9-ac14-03cabc717f2b">
	<property name="ireport.zoom" value="1.3310000000000033"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\document\\jasper+ireport\\Jasper_tutorial\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="Season" class="java.lang.String"/>
	<parameter name="Product_Category" class="java.lang.String"/>
	<parameter name="Color_Pattern" class="java.lang.String"/>
	<parameter name="department" class="java.lang.String"/>
	<queryString>
		<![CDATA[select (case
       when $P{Season} like 'SS%' then ('Dec,Jan,Feb,Mar,Apr,May,Jun')
       else ('Jun,Jul,Aug,Sep,Oct,Nov,Dec')
       end) as months]]>
	</queryString>
	<field name="months" class="java.lang.String"/>
	<pageHeader>
		<band height="20">
			<textField>
				<reportElement x="0" y="0" width="113" height="20" uuid="8ca60baf-7e84-4a80-95f3-cfb457108121"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(0,3).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="115" y="0" width="113" height="20" uuid="58cc15e8-56ea-4c06-ad81-3604a16685bd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(4,7).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="230" y="0" width="113" height="20" uuid="2c7839f3-6f3f-4574-bb30-ac4a02568536"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(8,11).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="344" y="0" width="113" height="20" uuid="6bf6802d-b374-49b4-969d-045c03bab2ca"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(12,15).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="459" y="0" width="113" height="20" uuid="41cb1f3e-131e-44a5-bfd0-4439435f0ee9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(16,19).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="574" y="0" width="113" height="20" uuid="6af97f9e-814e-4a19-bb1e-6a770139d3d9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(20,23).toUpperCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="689" y="0" width="113" height="20" uuid="c7877cef-cbc1-4a13-9fa2-bc759348ba8f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{months}.substring(24,27).toUpperCase()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="220" splitType="Stretch">
			<subreport>
				<reportElement x="115" y="0" width="113" height="220" uuid="9b7bac0e-8c5b-40a7-a8f8-633e9cf4c652"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(4,7).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="230" y="0" width="113" height="220" uuid="316e81f1-9b22-4db4-b27a-aa07007efe6a"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(8,11).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="344" y="0" width="113" height="220" uuid="f17aa772-95e7-4dbe-9799-ee2f98356caa"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(12,15).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="459" y="0" width="113" height="220" uuid="3f210f69-ad1f-4fba-9070-6687aad62b76"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(16,19).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="574" y="0" width="113" height="220" uuid="3a49eec8-7a7a-41a3-b183-bd1b08fc5b67"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(20,23).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="689" y="0" width="113" height="220" uuid="68cd6fa7-9700-43e6-962d-fb253a1f1f53"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(24,27).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="0" y="0" width="113" height="220" uuid="41e49e98-ebe0-4318-9557-1250d2e18cb7"/>
				<subreportParameter name="Product_Category">
					<subreportParameterExpression><![CDATA[$P{Product_Category}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="department">
					<subreportParameterExpression><![CDATA[$P{department}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Season">
					<subreportParameterExpression><![CDATA[$P{Season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Month">
					<subreportParameterExpression><![CDATA[$F{months}.substring(0,3).toUpperCase()]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="color_palette">
					<subreportParameterExpression><![CDATA[$P{Color_Pattern}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:production_sub_report.jrxml"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
