<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="production_sub_report" language="groovy" pageWidth="114" pageHeight="500" whenNoDataType="AllSectionsNoDetail" columnWidth="114" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Key" uuid="898ed3ec-e632-4603-a234-7ae5b02cccb2">
	<property name="ireport.zoom" value="1.6105100000000032"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="Season" class="java.lang.String"/>
	<parameter name="Month" class="java.lang.String"/>
	<parameter name="Product_Category" class="java.lang.String"/>
	<parameter name="color_palette" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT item.item_desc item_name,
  item.buyer_item_no Style_no, --Style no
  item.plu Plu,                --plu
  (SELECT COALESCE (SUM(shipped_qty), item.Est_Order_Qty)
  FROM vendor_po_item
  JOIN vendor_po
  ON vendor_po_item.vendor_po_sid = vendor_po.sid
  WHERE vendor_po_item.item_sid   = item.sid
  AND vendor_po.doc_status        = 'active'
  AND vendor_po.status           IN ('customStatus01','customStatus02','customStatus03','customStatus05','customStatus06')
  ) Quantity,
  item.in_store_month store_month,
  item.selling_price_pln_desc Price,  --retail price
  cost_sheet.estimated_margin Margin, --margin
  quotation.vendor_name Vendor,
  encode(item_att.file_content,'base64') Picture
FROM item
LEFT JOIN quotation
ON quotation.item_sid    = item.sid
AND quotation.doc_status = 'active'
AND quotation.status     = 'confirmedToBuy'
LEFT JOIN cost_sheet
ON cost_sheet.document_sid = quotation.sid
AND cost_sheet.doc_status  = 'active'
LEFT JOIN item_image
ON item_image.item_sid = item.sid
AND item_image.is_default
LEFT JOIN item_img
ON item_image.image_sid = item_img.sid
LEFT JOIN item_att
ON COALESCE(item_img.with_mark_up_sid, item_img.original_sid) = item_att.sid
WHERE item.doc_status                                         = 'active'
AND item.is_latest
and item.season = $P{Season}
and item.in_store_month = $P{Month}
and ($P{Product_Category} is null or $P{Product_Category} = ''  or item.REPORT_HCL_FULL_CODE like $P{Product_Category} || '%')
and ($P{color_palette} is null or $P{color_palette} = '' or '$P!{color_palette}' = '[]' or exists (
      select 1 from item_color where item_color.item_sid = item.sid
        and (color_pattern =  $P{color_palette}
        or ','||replace(substr('$P!{color_palette}', 2, length('$P!{color_palette}') - 2),' ','')||',' like '%,'||replace(color_palette,' ','')||',%')
    ))]]>
	</queryString>
	<field name="item_name" class="java.lang.String"/>
	<field name="style_no" class="java.lang.String"/>
	<field name="plu" class="java.lang.String"/>
	<field name="quantity" class="java.math.BigDecimal"/>
	<field name="store_month" class="java.lang.String"/>
	<field name="price" class="java.lang.String"/>
	<field name="margin" class="java.math.BigDecimal"/>
	<field name="vendor" class="java.lang.String"/>
	<field name="picture" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="220" splitType="Stretch">
			<image>
				<reportElement x="0" y="0" width="114" height="60" uuid="68f5397a-74f7-4dd6-94b5-1aeaada40210"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(new SimpleJasperReportsContext()).loadAwtImageFromBytes(javax.xml.bind.DatatypeConverter.parseBase64Binary($F{picture}))]]></imageExpression>
			</image>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="114" height="60" uuid="ae9e0062-e6a7-4498-8683-b1c3a04675e3"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement markup="none">
					<font size="12"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="60" width="114" height="20" uuid="0466e2d5-74ec-44f1-92af-40ddb307d048"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{item_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="80" width="114" height="20" uuid="084c51b0-dc45-4e16-b645-e44c7c6cce15"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{style_no}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="100" width="114" height="20" uuid="ce1d1ca3-3040-4567-be21-8ecf060a8a84"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{plu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="120" width="114" height="20" uuid="0082902e-5427-4b03-9dd6-a83b61dc734f"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantity}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="140" width="114" height="20" uuid="7326da4d-3e21-47f8-858d-b90906ffabb9"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{store_month}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="160" width="114" height="20" uuid="a876d951-7821-4dd1-9550-3697c5278df8"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{price}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="180" width="114" height="20" uuid="22a20361-a8d3-40dc-86d7-124c3a915aee"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{margin}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="200" width="114" height="20" uuid="d77dd961-b871-4cad-85a4-dbfec4560b78"/>
				<box topPadding="1" leftPadding="2" bottomPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vendor}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
