<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="3dreport1_subreport1" language="groovy" pageWidth="4608" pageHeight="5000" orientation="Landscape" columnWidth="4608" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="7bb22286-5be9-4783-ad07-6cdbf553b487">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="3351"/>
	<property name="ireport.y" value="0"/>
	<parameter name="IN_SEASON_LIST" class="java.lang.String"/>
	<parameter name="pc" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT
	N'ITEM' AS "entityName",
	IT.SID AS "id",
	ATT.FILE_PATH AS "thumbnail",
	--	ATT.FILE_CONTENT        AS "fileContent",
 IMG.WITH_MARK_UP_SID AS "thumbnailMockUpId",
	ATTCONSTRUCTION.FILE_PATH AS "image",
	--    ATTCONSTRUCTION.FILE_CONTENT        AS "3dFileContent",
 IT.REF_NO AS "refNo",
	IT.VERSION AS "entityVersion",
	IT.BUSINESS_REF_NO AS "businessRefNo",
	IT.ITEM_NO AS "itemNo",
	IT.SEASON AS "season",
	IT.SEASON_DESC AS "seasonName",
	isln.display_value AS "productCategory",
	CSAB.type AS "type",
	CSAB.type_desc AS "typeName",
	CSAB.status AS "status",
	CSAB.status_desc AS "statusName",
	IT.style_type AS "style_type",
	--style_type
 IT.style_type_desc AS "style_typeName",
	---style_type_desc
 IT.style_template AS "styleTemplate",
	-- style_template
 IT.style_template_desc AS "styleTemplateName",
	--- style_template_desc
 IT.pattern_copy AS "patternCopy",
	--pattern_copy
 IT.pattern_copy_desc AS "patternCopyName",
	----pattern_copy_desc
 IT.priority AS "priority",
	IT.priority_desc AS "priorityName",
	IT.collection_drop AS "collection_drop",
	IT.collection_drop_desc AS "collection_dropName",
	IT.team AS "team",
	IT.team_desc AS "teamName",
	IT.pattern_maker AS "patternMaker",
	IT.pattern_maker_desc AS "patternMakerName",
	IT.collection AS "collection",
	IT.vendor_info AS "vendorInfo",
	PARENT.NAME AS "category",
	CSAB.upload_date AS "upload_date",
	CSAB.comments_date AS "comments_date",
	CSAB.pattern_send_date AS "patternSendDate",
	CSAB.version AS "versionCust",
	CSAB.comments AS "comments",
	CSAB.sh_hyperlink AS "shLink",
	CASE WHEN CSAB.physical_pattern_sent='t' THEN 'Yes'
            WHEN CSAB.physical_pattern_sent='f' THEN 'No'
            ELSE 'No'
       END
	 AS "physicalPatternSent",
	IT.PARTY_NAME1 AS "buyer",
	IT.PARTY_NAME2 AS "assistant",
	IT.party_name3 as "technologist",
	BRIEFING.upload_date AS "briefingUploadDate",
	ppp.comments_date AS "previousPP3DCommentsDate",
	pfit.comments_date AS "previousFit3DCommentsDate",
	BRIEFING.comments_date AS "previousBriefDate",
	CASE WHEN IT.c3d_pp_required='t' THEN 'Yes'
            WHEN IT.c3d_pp_required='f' THEN 'No'
            ELSE 'No'
       END
	 AS "3dpprequired"
FROM
	ITEM_SPECIFICATION_CONSTRUCTION CSAB
LEFT JOIN ITEM IT ON
	CSAB.ITEM_SID = IT.SID
	AND IT.IS_LATEST = TRUE
JOIN item_sln isln ON
	isln.parent_id = it.sid
	AND isln.field_id = 'productCategory'
LEFT JOIN item_img IMG ON
	IT.IMAGE_SID = IMG.SID
LEFT JOIN item_img IMGCONSTRUCTION ON
	CSAB.IMAGE_SID = IMGCONSTRUCTION.SID
LEFT JOIN item_att ATTCONSTRUCTION ON
	IMGCONSTRUCTION.ORIGINAL_SID = ATTCONSTRUCTION.SID
LEFT JOIN item_att ATT ON
	IMG.ORIGINAL_SID = ATT.SID
LEFT JOIN (
	SELECT
		BRIEFING.ITEM_SID,
		BRIEFING.upload_date,
		BRIEFING.comments_date,
		BRIEFING.SID
	FROM
		ITEM_SPECIFICATION_CONSTRUCTION BRIEFING
	WHERE
		BRIEFING.type = 'Briefing' ) BRIEFING ON
	BRIEFING.ITEM_SID = IT.SID
	AND CSAB.type != 'Briefing'
	----join version-1 PP 3D record
LEFT JOIN (
	SELECT
		ppp.item_sid,
		ppp.comments_date,
		ppp.version,
		ppp.sid
	FROM
		ITEM_SPECIFICATION_CONSTRUCTION ppp
	WHERE
		ppp.type = 'PP_3D' ) ppp ON
	CSAB.type = 'PP_3D'
	AND CSAB.version > 1
	AND CSAB.version = ppp.version + 1
	AND ppp.item_sid = IT.sid
	----join version-1 Fit3D record . if PP3D version = 1, show Max version Fit3D record
LEFT JOIN (
	SELECT
		pfit.item_sid,
		pfit.comments_date,
		pfit.version,
		pfit.sid
	FROM
		ITEM_SPECIFICATION_CONSTRUCTION pfit
	WHERE
		pfit.type = 'Fit_3D' ) pfit ON
	((CSAB.type = 'Fit_3D'
	AND CSAB.version > 1
	AND CSAB.version = pfit.version + 1)
	OR (CSAB.type = 'PP_3D'
	AND CSAB.version = 1
	AND pfit.version = (
	SELECT
		MAX(CSCON.version)
	FROM
		ITEM_SPECIFICATION_CONSTRUCTION CSCON
	WHERE
		CSCON.ITEM_SID = IT.SID
		AND CSCON.type = 'Fit_3D' )))
	AND pfit.item_sid = IT.sid
	--left join item_party BUYER on
	--	IT.SID = BUYER.DOC_SID
	--	and BUYER.PARTY_NAME = 'RPN10'
	--	and BUYER.contact_user is not null
	--left join item_party ASSIS on
	--	IT.SID = ASSIS.DOC_SID
	--	and ASSIS.PARTY_NAME = 'RPN20'
	--	and ASSIS.contact_user is not null
LEFT JOIN HCL_ENTRY HN ON
	HN.SID = IT.report_hcl
LEFT JOIN HCL_ENTRY PARENT ON
	PARENT.SID = HN.parent_entry_sys_id
WHERE
	--CSAB.status NOT IN ('Cancelled','AIUploaded','PatternSent')
 IT.IS_LATEST
	AND IT.DOC_STATUS = 'active'
	AND IT.HUB_DOMAIN_ID = 'PEPL'
	------?
	AND IT.IS_LATEST = TRUE
	AND IT.IS_FOR_REFERENCE = FALSE
    AND $X{IN,IT.Season_desc,IN_SEASON_LIST}]]>
	</queryString>
	<field name="image" class="java.lang.String"/>
	<field name="type" class="java.lang.String"/>
	<field name="versionCust" class="java.lang.String"/>
	<field name="briefingUploadDate" class="java.lang.String"/>
	<field name="upload_date" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="comments" class="java.lang.String"/>
	<field name="comments_date" class="java.lang.String"/>
	<field name="patternSendDate" class="java.lang.String"/>
	<field name="physicalPatternSent" class="java.lang.String"/>
	<field name="previousCommentsDate" class="java.lang.String"/>
	<field name="previousPP3DCommentsDate" class="java.lang.String"/>
	<field name="previousBriefDate" class="java.lang.String"/>
	<field name="fileContent" class="java.io.InputStream"/>
	<field name="previousFit3DCommentsDate" class="java.lang.String"/>
	<field name="3dFileContent" class="java.io.InputStream"/>
	<field name="seasonName" class="java.lang.String"/>
	<field name="productCategory" class="java.lang.String"/>
	<field name="category" class="java.lang.String"/>
	<field name="itemNo" class="java.lang.String"/>
	<field name="collection" class="java.lang.String"/>
	<field name="collection_dropName" class="java.lang.String"/>
	<field name="priorityName" class="java.lang.String"/>
	<field name="buyer" class="java.lang.String"/>
	<field name="assistant" class="java.lang.String"/>
	<field name="vendorInfo" class="java.lang.String"/>
	<field name="teamName" class="java.lang.String"/>
	<field name="patternMakerName" class="java.lang.String"/>
	<field name="patternCopyName" class="java.lang.String"/>
	<field name="styleTemplateName" class="java.lang.String"/>
	<field name="style_typeName" class="java.lang.String"/>
	<field name="typeName" class="java.lang.String"/>
	<field name="shLink" class="java.lang.String"/>
	<field name="statusName" class="java.lang.String"/>
	<field name="3dpprequired" class="java.lang.String"/>
	<field name="technologist" class="java.lang.String"/>
	<variable name="previousCommentsDate" class="java.lang.String">
		<variableExpression><![CDATA[$F{previousPP3DCommentsDate}==null? ($F{previousFit3DCommentsDate}==null?$F{previousBriefDate}:$F{previousFit3DCommentsDate}):$F{previousPP3DCommentsDate}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="50">
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="290" height="50" uuid="db3bd3c8-842b-4b7c-9a15-63c9b3e1ab0d"/>
				<box leftPadding="10">
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font size="18" isBold="true" isStrikeThrough="false"/>
				</textElement>
				<text><![CDATA[3D Report]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="50">
			<staticText>
				<reportElement x="0" y="0" width="145" height="50" uuid="cfb3ea8e-3489-4499-ab0b-4bead332d881"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Image]]></text>
			</staticText>
			<staticText>
				<reportElement x="145" y="0" width="145" height="50" uuid="6ef93263-ba7f-462d-abe9-9a0683a52763"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[3D Image]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="0" width="145" height="50" uuid="4e3cdd32-7901-420b-960a-e865eebdb99d"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Season]]></text>
			</staticText>
			<staticText>
				<reportElement x="435" y="0" width="145" height="50" uuid="fd9b6c73-164a-4974-a533-78a91eaee26a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Product Category]]></text>
			</staticText>
			<staticText>
				<reportElement x="580" y="0" width="145" height="50" uuid="a66b7fe5-93fd-403f-8a7f-4fedda01cb08"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Category]]></text>
			</staticText>
			<staticText>
				<reportElement x="725" y="0" width="145" height="50" uuid="b32082f5-b0de-4f8e-888f-09acc492a9c5"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Item No.]]></text>
			</staticText>
			<staticText>
				<reportElement x="870" y="0" width="145" height="50" uuid="98aaa1d1-19d4-4946-b266-2fe55e9b5f14"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Collection]]></text>
			</staticText>
			<staticText>
				<reportElement x="1015" y="0" width="145" height="50" uuid="e6ba1ac5-0a6d-4b02-b641-92120c9b631a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Collection drop]]></text>
			</staticText>
			<staticText>
				<reportElement x="1160" y="0" width="145" height="50" uuid="4b4e5b58-5bf2-44b3-b0e8-897f0bbcc14a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Priority]]></text>
			</staticText>
			<staticText>
				<reportElement x="1305" y="0" width="145" height="50" uuid="750c6f0d-c545-4daa-bd45-4ba360d2f4d5"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Buyer]]></text>
			</staticText>
			<staticText>
				<reportElement x="1450" y="0" width="145" height="50" uuid="5247ff1a-fa8d-4690-b879-a8ecdb6033a3"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Assistant]]></text>
			</staticText>
			<staticText>
				<reportElement x="1881" y="0" width="145" height="50" uuid="bb85f79f-3398-4534-b371-87b97f614d04"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Vendor Info]]></text>
			</staticText>
			<staticText>
				<reportElement x="2026" y="0" width="145" height="50" uuid="caeb1857-d2be-4f6b-8f44-7e612ee32ecd"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Team]]></text>
			</staticText>
			<staticText>
				<reportElement x="2171" y="0" width="145" height="50" uuid="10e8921d-d69d-46fe-a254-2f02606a934b"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Pattern Maker]]></text>
			</staticText>
			<staticText>
				<reportElement x="2316" y="0" width="145" height="50" uuid="454c293e-1727-4c66-ade1-d285de92b177"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Pattern Copy]]></text>
			</staticText>
			<staticText>
				<reportElement x="2461" y="0" width="145" height="50" uuid="9167ee0b-87bd-48c8-be0f-832f6a4ced52"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Style Template]]></text>
			</staticText>
			<staticText>
				<reportElement x="2606" y="0" width="145" height="50" uuid="519a4555-2a28-4dee-8e2b-8ad63d26c0e2"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Style Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="2751" y="0" width="145" height="50" uuid="df7a2041-873c-4a9c-ba55-e936e569aeba"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="2896" y="0" width="145" height="50" uuid="70fef90c-2078-46bb-8867-695ed465d28a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Version]]></text>
			</staticText>
			<staticText>
				<reportElement x="3041" y="0" width="145" height="50" uuid="f25a05c3-a673-4c97-a02a-a3bc4e5776f0"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[1st Briefing Upload Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="3186" y="0" width="145" height="50" uuid="90428d93-7d83-4bff-adf3-8da0733f0f2c"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Previous Comments from Buyer Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="3331" y="0" width="145" height="50" uuid="ae52c2f6-b643-41ee-bdd8-7fe2af5d84c2"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Upload date]]></text>
			</staticText>
			<staticText>
				<reportElement x="3761" y="0" width="145" height="50" uuid="39fbd484-f456-41d3-b8a2-f76ed234f135"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement x="3906" y="0" width="145" height="50" uuid="a7896db0-1f2b-410f-935d-a367a07a7212"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="4051" y="0" width="145" height="50" uuid="9e53182d-0110-40db-9122-5db51445e57f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Comments date]]></text>
			</staticText>
			<staticText>
				<reportElement x="4196" y="0" width="145" height="50" uuid="c8270f11-8103-4d12-a3bc-e35f2174656d"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Pattern Sent Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="4341" y="0" width="150" height="50" uuid="625c5b4d-b434-4693-950d-db0d15458843"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Physical Pattern Sent]]></text>
			</staticText>
			<staticText>
				<reportElement x="3476" y="0" width="285" height="50" uuid="85737a5e-e0df-4507-a2d5-443934c6ef91"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[SH Link]]></text>
			</staticText>
			<staticText>
				<reportElement x="4491" y="0" width="117" height="50" uuid="79982774-4e27-40c1-beaf-4948f0839a71"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PP 3D Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="1595" y="0" width="286" height="50" uuid="d77f0fe5-5b75-411d-a193-edbb76b9ea9d"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Technologist]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="60" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="290" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="6f32a939-4987-4c8d-88cf-e7ec3fbbebb5"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{seasonName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="435" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="c7d73784-4194-4076-81b8-39d56c5dd525"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{productCategory}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="580" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="b60656c2-940f-46a2-85df-bbf1c7b72dc0"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{category}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="870" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="abcfc03b-1074-4d46-bb31-0da62291fa01"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{collection}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1015" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="6e94f4fe-20b0-4357-9f7a-d748cf9cd5ab"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{collection_dropName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1160" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a20feeab-af50-4919-a42c-662d2b4fe773"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{priorityName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1305" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="69144f84-68fb-4cc1-bfdc-3a27dc7217e0"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{buyer}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1450" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dc996bf8-ce70-4942-81ae-c104df9e8875"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{assistant}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1881" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="0972d712-54f5-4601-899b-1b4e289ab564"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vendorInfo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="725" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="f531a57b-d72b-4efa-823b-a7554276e7d8"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{itemNo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2026" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="be321e3b-41c6-4a45-bfb1-6bab754e37af"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{teamName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2171" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="4e9afa93-afcd-4d64-983c-28764edbe9a3"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patternMakerName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2316" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="aa55e672-006d-4916-a404-38b37e39f6e1"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patternCopyName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2606" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="2febacf9-71da-4269-a808-c7aca5db98e6"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{style_typeName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2751" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="5983f08a-1409-4e62-bb30-bc60e9c3d955"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{typeName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2896" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="73fede27-aa11-40eb-beeb-bfb6b838c167"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{versionCust}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="3041" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="e0019d7d-61af-4bca-a162-0c07cb1c4194"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{briefingUploadDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="3186" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="dc9db112-fbe7-423b-ae9f-f116e9e46206"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{previousCommentsDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="3331" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="935f3d4f-7de4-4020-bf92-ed3b57c6d8ea"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{upload_date}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="2461" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="27175ada-76e1-41d6-957a-cc4e7c319d5d"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{styleTemplateName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="3906" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="6f2148c5-347c-4e4c-83aa-2dd40ca876cd"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{comments}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="4196" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="9914430f-e3b3-47f9-bef5-2e3791316e91"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patternSendDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="3761" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="9b6bbd06-ea43-451e-8ae3-c48da39cecbc"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{statusName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="4051" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="da99af31-0775-4223-ab14-fc062149de0a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{comments_date}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="4341" y="0" width="150" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="a5404f90-ae74-40be-8e63-d2a85b4403c3"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{physicalPatternSent}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="3476" y="0" width="285" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="673f32c1-dfc8-419a-b2ad-be64d55ac533"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shLink}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="ee0e1159-cf5c-4546-8876-201aae02a881"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="145" y="0" width="145" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="2787b67b-b4db-4325-8903-42c0aba2abc0"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="4491" y="0" width="117" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="4bd9c9c9-5728-4178-8aaa-ca0a4783cc3d"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{3dpprequired}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1595" y="0" width="286" height="60" isPrintInFirstWholeBand="true" isPrintWhenDetailOverflows="true" uuid="2881efae-86fa-4024-a4c4-4f0a88c7cbb8"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{technologist}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
