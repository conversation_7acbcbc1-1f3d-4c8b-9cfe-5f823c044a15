<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Artworks_3D_Reports5_subreport1" language="groovy" pageWidth="4000" pageHeight="5000" orientation="Landscape" columnWidth="4000" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="c5adbfc2-e0f3-45f2-b865-292c7fc5bf2d">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="900000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<parameter name="IN_SEASON_LIST" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT *
FROM
  (SELECT N'Item'           AS "entityName",
    IT.SID                  AS "id",
    ATT.FILE_PATH           AS "thumbnail",
 --   ATT.FILE_CONTENT        AS "fileContent",
    IMG.WITH_MARK_UP_SID    AS "thumbnailMockUpId",
    ATTARTWORK.FILE_PATH    AS "image",
 --   ATTARTWORK.FILE_CONTENT    AS "artworkFileContent",
    IT.REF_NO               AS "refNo",
    IT.VERSION              AS "entityVersion",
    IT.BUSINESS_REF_NO      AS "businessRefNo",
    IT.ITEM_NO              AS "itemNo",
    IT.SEASON               AS "season",
    IT.SEASON_DESC          AS "seasonName",
    isln.display_value      AS "productCategory",
    CSAB.type               AS "type",
    CSAB.type_desc          AS "typeName",
    CSAB.status             AS "status",
    CSAB.status_desc        AS "statusName",
    IT.priority             AS "priority",
    IT.priority_desc        AS "priorityName",
    IT.collection_drop      AS "collection_drop",
    IT.collection_drop_desc AS "collection_dropName",
    IT.team                 AS "team",
    IT.team_desc            AS "teamName",
    IT.c3d_pp_required      AS "pp_required_3d",
    IT.collection           AS "collection",
    IT.artwork_Job_Number   AS "artworkJobNumber",
    IT.designer             AS "designer",
    IT.designer_desc        AS "designerName",
    IT.complexity           AS "complexity",
    IT.complexity_desc      AS "complexityName",
	psub.comments_date  	AS "previousSubCommentsDate",
	psub.sid  	AS "previousSubId",
	BRIEFING.comments_date  AS "previousBriefDate",
    CSAB.upload_date        AS "upload_date",
    CSAB.comments_date      AS "comments_date",
    CSAB.version            AS "versionCust",
    CSAB.comments           AS "comments",
    CSAB.sh_hyperlink       AS "shLink",
    CSAB.ai_hyperlink       AS "aiLink",
    CSAB.body_colors        AS "bodyColours",
    CSAB.additional_picture AS "additionalPic",
    BUYER.contact_user_ref  AS "buyer",
    ASSIS.contact_user_ref  AS "assistant",
    briefing.Upload_Date    AS "briefingUploadDate"
  FROM ITEM_SPECIFICATION_ARTWORK_BOM CSAB
  LEFT JOIN ITEM IT
  ON CSAB.item_SID = IT.SID
  AND IT.IS_LATEST =TRUE
  LEFT JOIN item_sln isln
  ON isln.parent_id = it.sid
  AND isln.field_id = 'productCategory'
  left join item_sln imgsln on imgsln.item_sid = IT.sid
						  and imgsln.field_id = 'imageTypeId'
						  and imgsln.display_value = 'Front Image'
						  and imgsln.parent_entity = 'ItemImage'
	left join item_image IMAGE on IMAGE.sid = imgsln.parent_id
	left join item_img IMG on IMAGE.image_sid = IMG.sid
	left join item_att ATT on COALESCE(IMG.with_mark_up_sid, IMG.original_sid) = ATT.sid
  LEFT JOIN item_img IMGARTWORK
  ON CSAB.IMAGE_SID = IMGARTWORK.SID
  LEFT JOIN item_att ATTARTWORK
  ON IMGARTWORK.ORIGINAL_SID = ATTARTWORK.SID
  LEFT JOIN
    (SELECT briefing.ITEM_SID,
      BRIEFING.upload_date,
      BRIEFING.comments_date,
      BRIEFING.sid
    FROM ITEM_SPECIFICATION_ARTWORK_BOM BRIEFING
      where BRIEFING.type = 'Briefing'
    ) briefing
  ON briefing.ITEM_SID = IT.SID
  AND CSAB.type       != 'Briefing'
	left join
	(select psub.item_sid,
		psub.comments_date,
		psub.version,
		psub.sid
	from ITEM_SPECIFICATION_ARTWORK_BOM psub
	where psub.type = 'Submission'
	) psub on CSAB.type= 'Submission' and CSAB.version > 1 and CSAB.version = psub.version+1
	and psub.item_sid = IT.sid
  LEFT JOIN item_party BUYER
  ON IT.SID               = BUYER.DOC_SID
  AND BUYER.PARTY_NAME    = 'RPN10'
  AND BUYER.contact_user IS NOT NULL
  LEFT JOIN item_party ASSIS
  ON IT.SID               = ASSIS.DOC_SID
  AND ASSIS.PARTY_NAME    = 'RPN20'
  AND ASSIS.contact_user IS NOT NULL
  WHERE IT.IS_LATEST   = TRUE
  AND CSAB.status NOT IN ('Cancelled','AIUploaded','PatternSent')
  AND ((IT.DOC_STATUS     = 'active'))
  AND (IT.HUB_DOMAIN_ID   = 'PEPL')
  AND IT.IS_LATEST        = true
  AND IT.IS_FOR_REFERENCE = false
  AND $X{IN,IT.Season_desc,IN_SEASON_LIST}
  )A
UNION ALL
SELECT *
FROM
  (SELECT N'Item'           AS "entityName",
    IT.SID                  AS "id",
    ATT.FILE_PATH           AS "thumbnail",
 --   ATT.FILE_CONTENT        AS "fileContent",
    IMG.WITH_MARK_UP_SID    AS "thumbnailMockUpId",
    ATTARTWORK.FILE_PATH    AS "image",
 --   ATTARTWORK.FILE_CONTENT    AS "artworkFileContent",
    IT.REF_NO               AS "refNo",
    IT.VERSION              AS "entityVersion",
    IT.BUSINESS_REF_NO      AS "businessRefNo",
    IT.ITEM_NO              AS "itemNo",
    IT.SEASON               AS "season",
    IT.SEASON_DESC          AS "seasonName",
    isln.display_value      AS "productCategory",
    CSAB.type               AS "type",
    CSAB.type_desc          AS "typeName",
    CSAB.status             AS "status",
    CSAB.status_desc        AS "statusName",
    IT.priority             AS "priority",
    IT.priority_desc        AS "priorityName",
    IT.collection_drop      AS "collection_drop",
    IT.collection_drop_desc AS "collection_dropName",
    IT.team                 AS "team",
    IT.team_DESC            AS "teamName",
    IT.c3d_pp_required      AS "pp_required_3d",
    IT.collection           AS "collection",
    IT.artwork_Job_Number   AS "artworkJobNumber",
    IT.designer             AS "designer",
    IT.designer_desc        AS "designerName",
    IT.complexity           AS "complexity",
    IT.complexity_desc      AS "complexityName",
	psub.comments_date  	AS "previousSubCommentsDate",
	psub.sid  	AS "previousSubId",
	BRIEFING.comments_date  AS "previousBriefDate",
    CSAB.upload_date        AS "upload_date",
    CSAB.comments_date      AS "comments_date",
    CSAB.version            AS "versionCust",
    CSAB.comments           AS "comments",
    CSAB.sh_hyperlink       AS "shLink",
    CSAB.ai_hyperlink       AS "aiLink",
    CSAB.body_colors        AS "bodyColours",
    CSAB.additional_picture AS "additionalPic",
    BUYER.contact_user_ref  AS "buyer",
    ASSIS.contact_user_ref  AS "assistant",
    BRIEFING.Upload_Date    AS "briefingUploadDate"
  FROM ITEM IT
  LEFT JOIN ITEM_SPECIFICATION_ARTWORK_BOM CSAB
  ON CSAB.item_SID = IT.SID
  LEFT JOIN item_sln isln
  ON isln.parent_id = it.sid
  AND isln.field_id = 'productCategory'
    left join item_sln imgsln on imgsln.item_sid = IT.sid
						  and imgsln.field_id = 'imageTypeId'
						  and imgsln.display_value = 'Front Image'
						  and imgsln.parent_entity = 'ItemImage'
	left join item_image IMAGE on IMAGE.sid = imgsln.parent_id
	left join item_img IMG on IMAGE.image_sid = IMG.sid
	left join item_att ATT on COALESCE(IMG.with_mark_up_sid, IMG.original_sid) = ATT.sid
  LEFT JOIN item_img IMGARTWORK
  ON CSAB.IMAGE_SID = IMGARTWORK.SID
  LEFT JOIN item_att ATTARTWORK
  ON IMGARTWORK.ORIGINAL_SID = ATTARTWORK.SID
  LEFT JOIN
    (SELECT briefing.ITEM_SID,
      BRIEFING.upload_date,
      BRIEFING.comments_date,
      BRIEFING.sid
    FROM ITEM_SPECIFICATION_ARTWORK_BOM BRIEFING
      where BRIEFING.type = 'Briefing'--2
    ) briefing
  ON briefing.ITEM_SID = IT.SID
   AND CSAB.type       != 'Briefing'---1

	left join
	(select psub.item_sid,
		psub.comments_date,
		psub.version,
		psub.sid
	from ITEM_SPECIFICATION_ARTWORK_BOM psub
	where psub.type = 'Submission'--3
	) psub on
	CSAB.type= 'Submission'
	and CSAB.version > 1
	and CSAB.version = psub.version+1
	and psub.item_sid = IT.sid
  LEFT JOIN item_party BUYER
  ON IT.SID               = BUYER.DOC_SID
  AND BUYER.PARTY_NAME    = 'RPN10'
  AND BUYER.contact_user IS NOT NULL
  LEFT JOIN item_party ASSIS
  ON IT.SID               = ASSIS.DOC_SID
  AND ASSIS.PARTY_NAME    = 'RPN20'
  AND ASSIS.contact_user IS NOT NULL
	WHERE NOT EXISTS
    (SELECT 1
    FROM ITEM_SPECIFICATION_ARTWORK_BOM csab
	WHERE CSAB.item_SID = IT.SID
    )
  AND IT.IS_LATEST        =TRUE
  AND ((IT.DOC_STATUS     = 'active'))
  AND (IT.HUB_DOMAIN_ID   = 'PEPL')
  AND IT.IS_LATEST        = true
  AND IT.IS_FOR_REFERENCE = false
  AND $X{IN,IT.Season_desc,IN_SEASON_LIST}
  )B;]]>
	</queryString>
	<field name="image" class="java.lang.String"/>
	<field name="Artwork IMG" class="java.lang.String"/>
	<field name="season" class="java.lang.String"/>
	<field name="itemNo" class="java.lang.String"/>
	<field name="productCategory" class="java.lang.String"/>
	<field name="collection" class="java.lang.String"/>
	<field name="collection_drop" class="java.lang.String"/>
	<field name="priority" class="java.lang.String"/>
	<field name="buyer" class="java.lang.String"/>
	<field name="assistant" class="java.lang.String"/>
	<field name="team" class="java.lang.String"/>
	<field name="artworkJobNumber" class="java.lang.String"/>
	<field name="designer" class="java.lang.String"/>
	<field name="complexity" class="java.lang.String"/>
	<field name="type" class="java.lang.String"/>
	<field name="Version" class="java.lang.Long"/>
	<field name="Previous Comments from Buyer Date" class="java.lang.String"/>
	<field name="upload_date" class="java.lang.String"/>
	<field name="shLink" class="java.lang.String"/>
	<field name="aiLink" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="comments" class="java.lang.String"/>
	<field name="comments_date" class="java.lang.String"/>
	<field name="bodyColours" class="java.lang.String"/>
	<field name="Additional Picture Check Box" class="java.lang.String"/>
	<field name="versionCust" class="java.lang.String"/>
	<field name="briefingUploadDate" class="java.lang.String"/>
	<field name="additionalPic" class="java.lang.String"/>
	<field name="fileContent" class="java.io.InputStream"/>
	<field name="artworkFileContent" class="java.io.InputStream"/>
	<field name="previousSubCommentsDate" class="java.lang.String"/>
	<field name="previousBriefDate" class="java.lang.String"/>
	<variable name="previousCommentsDate" class="java.lang.String">
		<variableExpression><![CDATA[Integer.parseInt(($F{versionCust}==null? "0":$F{versionCust}))>1? $F{previousSubCommentsDate}: $F{previousBriefDate}]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="50">
			<staticText>
				<reportElement x="0" y="0" width="300" height="50" uuid="4081923c-05c5-480b-9850-eb8829607ebc"/>
				<textElement>
					<font size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[Artwork Report]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="50">
			<staticText>
				<reportElement x="0" y="0" width="150" height="50" uuid="695a01f4-d0df-4952-80a8-f07443c1bb14"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Item Image]]></text>
			</staticText>
			<staticText>
				<reportElement x="150" y="0" width="150" height="50" uuid="ff28543b-d38b-4252-a883-fbca64818287"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Artwork Image]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="150" height="50" uuid="40cfb268-f28a-4b24-90cb-e019bd5344b1"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Season]]></text>
			</staticText>
			<staticText>
				<reportElement x="450" y="0" width="150" height="50" uuid="69d88a92-da21-4b6c-80fd-0fa0cda2df15"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Item No.]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="150" height="50" uuid="a3079174-7dc4-4ff0-9246-c1d1edb571a0"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Product Category]]></text>
			</staticText>
			<staticText>
				<reportElement x="750" y="0" width="150" height="50" uuid="99738a37-b375-4849-bce5-117a1c9b5790"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Collection]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="150" height="50" uuid="4bcf94d1-40b9-4ab8-bc33-9a3b9f2491d8"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Collection drop]]></text>
			</staticText>
			<staticText>
				<reportElement x="1050" y="0" width="150" height="50" uuid="7836afd0-0c8d-46f3-97d6-3e5af7f16fba"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Priority]]></text>
			</staticText>
			<staticText>
				<reportElement x="1200" y="0" width="150" height="50" uuid="17544bc9-13db-436c-9170-64c8a6bb67b4"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Buyer]]></text>
			</staticText>
			<staticText>
				<reportElement x="1350" y="0" width="150" height="50" uuid="7a94a0f1-82bb-4a60-8c0a-285d81106703"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Assistant]]></text>
			</staticText>
			<staticText>
				<reportElement x="1500" y="0" width="150" height="50" uuid="aa236d9d-e946-45ef-9f1d-57e217cc047f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Team]]></text>
			</staticText>
			<staticText>
				<reportElement x="1650" y="0" width="150" height="50" uuid="441f5c52-80b7-4a3b-9905-3eca858f1f49"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Artwork Job Number]]></text>
			</staticText>
			<staticText>
				<reportElement x="1800" y="0" width="150" height="50" uuid="a96be1f8-515c-46ff-8b48-22aa0b9fe70f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Designer]]></text>
			</staticText>
			<staticText>
				<reportElement x="1950" y="0" width="150" height="50" uuid="500fa1f8-5c79-4f10-ab1d-24b56aea1507"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Complexity]]></text>
			</staticText>
			<staticText>
				<reportElement x="2100" y="0" width="150" height="50" uuid="9bea5f66-2847-428a-a8d0-7f5ed3229aaa"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="2250" y="0" width="150" height="50" uuid="b87968ae-4faf-4a41-9673-a424967e090c"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Version]]></text>
			</staticText>
			<staticText>
				<reportElement x="2400" y="0" width="150" height="50" uuid="6fee79fd-1aff-4b86-9ca4-80d27511d408"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[1st Briefing Upload Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="2550" y="0" width="200" height="50" uuid="16735fea-c54a-4e76-928c-dbb9588a50ca"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Previous Comments from Buyer Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="2750" y="0" width="150" height="50" uuid="daaf70c1-6440-439a-8e05-f43c92a51623"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Upload date]]></text>
			</staticText>
			<staticText>
				<reportElement x="2900" y="0" width="150" height="50" uuid="53a773f8-3edb-4bdf-82c8-54fd19372eb8"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[SH Link]]></text>
			</staticText>
			<staticText>
				<reportElement x="3050" y="0" width="150" height="50" uuid="a2a7b1cc-654f-4e4a-ab2e-7ebb09917b54"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[AI Link]]></text>
			</staticText>
			<staticText>
				<reportElement x="3200" y="0" width="150" height="50" uuid="cec9499b-0ebd-4ebf-b79d-39559fc15c3c"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement x="3350" y="0" width="150" height="50" uuid="04052a43-e08c-4193-9bc6-0d73bf2406de"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Comments]]></text>
			</staticText>
			<staticText>
				<reportElement x="3500" y="0" width="150" height="50" uuid="c0b2ecc4-460c-4eb5-9cec-a58ffce4c23f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Comments date]]></text>
			</staticText>
			<staticText>
				<reportElement x="3650" y="0" width="150" height="50" uuid="46a0bf84-84e0-4fe4-9819-989c32180097"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Body Colours]]></text>
			</staticText>
			<staticText>
				<reportElement x="3800" y="0" width="150" height="50" uuid="37391ef6-ec46-432e-923d-4c4c7823e660"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Additional Picture Check Box]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="60" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="0" width="150" height="60" uuid="3716c8c4-4ffe-495b-893b-a5d4d202febb"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{season}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="450" y="0" width="150" height="60" uuid="7fb8b218-a34a-4e71-bb5c-6d5c8bb71b4c"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{itemNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="600" y="0" width="150" height="60" uuid="a9b9eb6f-ec35-472b-8c7c-0c0a19799003"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{productCategory}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="750" y="0" width="150" height="60" uuid="2e9175a0-91d8-4b88-b355-785bd3d7d7b1"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{collection}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="900" y="0" width="150" height="60" uuid="c7531d4e-913c-4772-9e52-3805cbb63e7a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{collection_drop}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1050" y="0" width="150" height="60" uuid="f32cf097-1280-40bf-91a0-3f5ab72a54a1"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{priority}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1200" y="0" width="150" height="60" uuid="043b58df-cb12-4f72-b583-55aa4dc739a2"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{buyer}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1350" y="0" width="150" height="60" uuid="6da4e9a7-37dd-43c9-a42f-398bfd46996d"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{assistant}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1500" y="0" width="150" height="60" uuid="dc36d488-bd18-4637-ac82-7777c68a257b"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{team}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1650" y="0" width="150" height="60" uuid="bece8b0d-e80d-4489-9425-bf318767cd68"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{artworkJobNumber}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1800" y="0" width="150" height="60" uuid="d7d3e8dc-5ab0-490a-b0d9-1d6125088912"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{designer}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1950" y="0" width="150" height="60" uuid="c9a7ee0b-bb75-4aab-b3a6-286eaec1b221"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{complexity}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2100" y="0" width="150" height="60" uuid="87d83f1a-8bf7-4059-bd0c-40290e9b8a72"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{type}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2250" y="0" width="150" height="60" uuid="ed65cc50-6ea5-42f6-ad51-2f1397901119"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{versionCust}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2400" y="0" width="150" height="60" uuid="ee9a2277-35c5-4ad5-9f1d-e0727740a736"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{briefingUploadDate}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2750" y="0" width="150" height="60" uuid="7a56e546-676d-4632-92a5-f1a321f9b5f9"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{upload_date}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2900" y="0" width="150" height="60" uuid="aafc22bd-febd-4a50-90f8-ec41cd499777"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shLink}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3050" y="0" width="150" height="60" uuid="ea61b2cb-1e46-4e8f-a0d1-f414babb845a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{aiLink}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3200" y="0" width="150" height="60" uuid="91fda701-126c-43aa-ac9c-74fd715fa57b"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3350" y="0" width="150" height="60" uuid="9491de77-d5fc-423b-8069-1f74f09a97c8"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{comments}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3500" y="0" width="150" height="60" uuid="0f25dbab-c4ff-42fa-a9f7-e043c4f9f15f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{comments_date}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3650" y="0" width="150" height="60" uuid="7c6f42ea-53fc-48b0-876f-9e03e6b9f20a"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bodyColours}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3800" y="0" width="150" height="60" uuid="5c2fbdd6-86b9-4752-be81-39eb70f6f01f"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{additionalPic}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2550" y="0" width="200" height="60" uuid="e07285f0-788d-4291-bc77-6ac047f1013b"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{previousCommentsDate}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="150" height="60" uuid="7c8e94ef-c72c-465e-b9a5-9d89d8b6d9ea"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="150" y="0" width="150" height="60" uuid="f4319b2e-ef5f-4a80-bd66-4355dcd8320e"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
