<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Common" pageWidth="554" pageHeight="699" orientation="Landscape" columnWidth="554" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a74639fb-f9af-4afd-a026-acfea80d8959">
	<property name="ireport.zoom" value="1.6105100000000026"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="900000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.max.pages" value="200"/>
	<import value="com.core.pcpl.printForm.*"/>
	<import value="java.util.*"/>
	<import value="java.text.*"/>
	<parameter name="JASPER_DIR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="THEME" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Desktop\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="INSTORE_DATE" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_1" class="java.lang.String">
		<defaultValueExpression><![CDATA["/001"]]></defaultValueExpression>
	</parameter>
	<parameter name="Report_Hierarchy_level_2" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_3" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_4" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_5" class="java.lang.String"/>
	<parameter name="season" class="java.lang.String"/>
	<parameter name="buyerId" class="java.lang.String"/>
	<queryString>
		<![CDATA[select item.cust_hcl1_full_name,item.theme,trim(to_char(item.instore_date,'MONTH')) as instore_date from item
        left join item_party on doc_sid = item.sid and item_party.party_name_desc = 'Buyer'
        join usr on item_party.contact_user = usr.sid
where 1=1
and ( $P{THEME} is null or  item.theme = $P{THEME} )
and (( $P{INSTORE_DATE} is null and item.instore_date is null ) or ( $P{INSTORE_DATE} is not null and trim(to_char(item.instore_date,'MONTH')) = $P{INSTORE_DATE} ))
and ( $P{Report_Hierarchy_level_1} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_1} || '/%')
and ( $P{Report_Hierarchy_level_2} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_2} || '/%')
and ( $P{Report_Hierarchy_level_3} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_3} || '/%')
and ( $P{Report_Hierarchy_level_4} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_4} || '/%')
and ( $P{Report_Hierarchy_level_5} is null or item.cust_hcl1_full_code = $P{Report_Hierarchy_level_5} )
and item.season = $P{season}
and item.doc_status = 'active'
and ( $P{buyerId} is null or  usr.sid = $P{buyerId} )
and item.sid in (select sid from
	(select item_att.file_content,item.sid from item
		left join item_sln on item_sln.item_sid = item.sid and item_sln.field_id = 'imageTypeId'
			  and item_sln.ref_display_value = 'Front Image'
			  and item_sln.doc_entity_name = 'ItemImage'
		left join item_image on item_image.sid = item_sln.doc_sid
		left join item_img on item_image.image_sid = item_img.sid
		left join item_att on COALESCE(item_img.with_mark_up_sid, item_img.original_sid) = item_att.sid
		 where  item.sid not in (select specification.item_sid from specification
		left join specification_set on specification_set.specification_sid = specification.sid
		left join specification_artwork on specification_artwork.specification_sid = specification.sid and upper(specification_artwork.artwork_type_desc) = 'OVERALL'
		left join specification_img on specification_img.sid = specification_artwork.image_sid
		left join specification_att on COALESCE(specification_img.with_mark_up_sid, specification_img.original_sid) = specification_att.sid
		where specification_att.file_content is not null)
		) a
	union all
select item_sid from
	(select specification.item_sid from specification
		left join specification_set on specification_set.specification_sid = specification.sid
		left join specification_artwork on specification_artwork.specification_sid = specification.sid and upper(specification_artwork.artwork_type_desc) = 'OVERALL'
		left join specification_img on specification_img.sid = specification_artwork.image_sid
		left join specification_att on COALESCE(specification_img.with_mark_up_sid, specification_img.original_sid) = specification_att.sid
		where specification_att.file_content is not null) spec)
group by item.cust_hcl1_full_name,item.theme,trim(to_char(item.instore_date,'MONTH'))
order by item.cust_hcl1_full_name]]>
	</queryString>
	<field name="cust_hcl1_full_name" class="java.lang.String"/>
	<field name="theme" class="java.lang.String"/>
	<field name="instore_date" class="java.lang.String"/>
	<variable name="cust_hcl1" class="java.lang.String[]">
		<variableExpression><![CDATA[$F{cust_hcl1_full_name} == null ? new String[]{"","","","","",""}:$F{cust_hcl1_full_name}.split("/")]]></variableExpression>
	</variable>
	<detail>
		<band height="233" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="554" height="233" uuid="b0470259-4be5-4f50-861d-f2dab3f07043"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<subreport>
					<reportElement x="14" y="0" width="540" height="233" uuid="eb399f94-f91f-46b8-b797-b1c4894fbde7"/>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="season">
						<subreportParameterExpression><![CDATA[$P{season}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="INSTORE_DATE">
						<subreportParameterExpression><![CDATA[$F{instore_date}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SUB_CLASS">
						<subreportParameterExpression><![CDATA[$F{cust_hcl1_full_name}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="JASPER_DIR">
						<subreportParameterExpression><![CDATA[$P{JASPER_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="buyerId">
						<subreportParameterExpression><![CDATA[$P{buyerId}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="THEME">
						<subreportParameterExpression><![CDATA[$F{theme}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["repo:Range_Review_Report_sub_sub.jrxml"]]></subreportExpression>
				</subreport>
				<textField>
					<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="14" height="233" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#0066CC" uuid="0a3bcd54-321a-441d-bcd4-a880c435f284"/>
					<box>
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left"/>
					<textFieldExpression><![CDATA[$V{cust_hcl1}[5]]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
