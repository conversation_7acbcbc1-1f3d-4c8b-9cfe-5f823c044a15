<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Common" columnCount="4" printOrder="Horizontal" pageWidth="540" pageHeight="699" orientation="Landscape" columnWidth="135" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a74639fb-f9af-4afd-a026-acfea80d8959">
	<property name="ireport.zoom" value="1.6105100000000026"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="900000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.max.pages" value="200"/>
	<import value="com.core.pcpl.printForm.*"/>
	<import value="java.text.*"/>
	<import value="java.util.*"/>
	<parameter name="JASPER_DIR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="THEME" class="java.lang.String"/>
	<parameter name="SUB_CLASS" class="java.lang.String"/>
	<parameter name="INSTORE_DATE" class="java.lang.String"/>
	<parameter name="season" class="java.lang.String"/>
	<parameter name="buyerId" class="java.lang.String"/>
	<queryString>
		<![CDATA[select image.file_content,
	item.item_no,
	item.THEME,
	item.plu,
	item.selling_price_pln_desc,
	specification.est_order_qty
	from item
	left join specification on specification.item_sid = item.sid
	left join (
		select item_att.file_content,item.sid from item
					left join item_sln on item_sln.item_sid = item.sid and item_sln.field_id = 'imageTypeId'
						  and item_sln.ref_display_value = 'Front Image'
						  and item_sln.doc_entity_name = 'ItemImage'
					left join item_image on item_image.sid = item_sln.doc_sid
					left join item_img on item_image.image_sid = item_img.sid
					left join item_att on COALESCE(item_img.with_mark_up_sid, item_img.original_sid) = item_att.sid
					 where  not exists (select 1 from specification
						left join specification_set on specification_set.specification_sid = specification.sid
						left join specification_artwork on specification_artwork.specification_sid = specification.sid and upper(specification_artwork.artwork_type_desc) = 'OVERALL'
						left join specification_img on specification_img.sid = specification_artwork.image_sid
						left join specification_att on COALESCE(specification_img.with_mark_up_sid, specification_img.original_sid) = specification_att.sid
						where specification_att.file_content is not null and item.sid  = specification.item_sid)
		union all
			select specification_att.file_content,specification.item_sid from specification
					left join specification_set on specification_set.specification_sid = specification.sid
					left join specification_artwork on specification_artwork.specification_sid = specification.sid and upper(specification_artwork.artwork_type_desc) = 'OVERALL'
					left join specification_img on specification_img.sid = specification_artwork.image_sid
					left join specification_att on COALESCE(specification_img.with_mark_up_sid, specification_img.original_sid) = specification_att.sid
					where specification_att.file_content is not null
		) image
		on image.sid = item.sid

	left join item_party on doc_sid = item.sid and item_party.party_name_desc = 'Buyer'
            join usr on item_party.contact_user = usr.sid

	where 1=1
	and ( $P{buyerId} is null or  usr.sid = $P{buyerId} )
	and ( $P{THEME} is null or  item.theme = $P{THEME} )
	and (( $P{INSTORE_DATE} is null and item.instore_date is null ) or ( $P{INSTORE_DATE} is not null and trim(to_char(item.instore_date,'MONTH')) = $P{INSTORE_DATE} ))
	and item.season = $P{season}
	and item.cust_hcl1_full_name = $P{SUB_CLASS}
	and item.doc_status = 'active'
		order by item.item_no]]>
	</queryString>
	<field name="file_content" class="java.lang.Object"/>
	<field name="item_no" class="java.lang.String"/>
	<field name="theme" class="java.lang.String"/>
	<field name="plu" class="java.lang.String"/>
	<field name="selling_price_pln_desc" class="java.lang.String"/>
	<field name="est_order_qty" class="java.math.BigDecimal"/>
	<detail>
		<band height="233" splitType="Prevent">
			<frame>
				<reportElement x="0" y="0" width="135" height="233" uuid="b8472ff7-47a3-4cf6-a221-93e66f39cc1a"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
					<reportElement x="0" y="0" width="135" height="123" uuid="174144fb-fc8b-4ec8-aec4-a49701e303e8"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage(
	(byte[])$F{file_content}
)]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="123" width="135" height="12" uuid="6f1e8fdc-af1f-4f76-9aaf-96b12a7a1f14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{item_no}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="159" width="135" height="12" uuid="9ea11c67-af42-422d-97fe-6136aa487390"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{est_order_qty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="147" width="135" height="12" uuid="f7eaee4e-2003-4c8d-8a0a-be0c76dd3f10"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{selling_price_pln_desc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="135" width="135" height="12" uuid="e738da3a-65ba-48b5-b000-4b5c562e9d7f"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{plu}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="171" width="135" height="62" uuid="d2d041c2-7124-4eee-a9ac-f7de2d8cded5"/>
					<text><![CDATA[]]></text>
				</staticText>
			</frame>
		</band>
	</detail>
</jasperReport>
