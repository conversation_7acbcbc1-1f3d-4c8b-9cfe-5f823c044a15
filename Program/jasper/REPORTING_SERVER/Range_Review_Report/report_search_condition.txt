Report Hierarchy level 1:
	Prompt Text (required):Report Hierarchy level 1
	Parameter Name (read-only):Report_Hierarchy_level_1

	SQL:	SELECT DISTINCT hcl_entry1.full_lineage, hcl_entry1.full_lineage_name 
				 FROM ITEM 
				 JOIN hcl_entry hcl_entry5 ON ITEM.REPORTING_HIERARCHY = hcl_entry5.SID 
				 join hcl_entry hcl_entry4 on hcl_entry4.sid = hcl_entry5.parent_entry_sys_id
				 join hcl_entry hcl_entry3 on hcl_entry3.sid = hcl_entry4.parent_entry_sys_id
				 join hcl_entry hcl_entry2 on hcl_entry2.sid = hcl_entry3.parent_entry_sys_id
				 join hcl_entry hcl_entry1 on hcl_entry1.sid = hcl_entry2.parent_entry_sys_id
				 order by hcl_entry1.full_lineage;


	Value Column :full_lineage
	Visible Columns :full_lineage_name		 

 
Report Hierarchy level 2:
	Prompt Text (required):Report Hierarchy level 2
	Parameter Name (read-only):Report_Hierarchy_level_2
				
	SQL:	SELECT DISTINCT hcl_entry2.full_lineage, hcl_entry2.full_lineage_name 
				 FROM ITEM 
				 JOIN hcl_entry hcl_entry5 ON ITEM.REPORTING_HIERARCHY = hcl_entry5.SID 
				 join hcl_entry hcl_entry4 on hcl_entry4.sid = hcl_entry5.parent_entry_sys_id
				 join hcl_entry hcl_entry3 on hcl_entry3.sid = hcl_entry4.parent_entry_sys_id
				 join hcl_entry hcl_entry2 on hcl_entry2.sid = hcl_entry3.parent_entry_sys_id
				 order by hcl_entry2.full_lineage;
	 
	Value Column :full_lineage
	Visible Columns :full_lineage_name


Report Hierarchy level 3:
	Prompt Text (required):Report Hierarchy level 3
	Parameter Name (read-only):Report_Hierarchy_level_3

	SQL:	SELECT DISTINCT hcl_entry3.full_lineage, hcl_entry3.full_lineage_name 
				 FROM ITEM 
				 JOIN hcl_entry hcl_entry5 ON ITEM.REPORTING_HIERARCHY = hcl_entry5.SID 
				 join hcl_entry hcl_entry4 on hcl_entry4.sid = hcl_entry5.parent_entry_sys_id
				 join hcl_entry hcl_entry3 on hcl_entry3.sid = hcl_entry4.parent_entry_sys_id
				 order by hcl_entry3.full_lineage;

	Value Column :full_lineage
	Visible Columns :full_lineage_name


Report Hierarchy level 4:
	Prompt Text (required):Report Hierarchy level 4
	Parameter Name (read-only):Report_Hierarchy_level_4

	SQL:	SELECT DISTINCT hcl_entry4.full_lineage, hcl_entry4.full_lineage_name 
				 FROM ITEM 
				 JOIN hcl_entry hcl_entry5 ON ITEM.REPORTING_HIERARCHY = hcl_entry5.SID 
				 join hcl_entry hcl_entry4 on hcl_entry4.sid = hcl_entry5.parent_entry_sys_id
				 order by hcl_entry4.full_lineage;

	Value Column :full_lineage
	Visible Columns :full_lineage_name

		 
Report Hierarchy level 5:
	Prompt Text (required):Report Hierarchy level 5
	Parameter Name (read-only):Report_Hierarchy_level_5

	SQL:   SELECT DISTINCT hcl_entry.full_lineage, hcl_entry.full_lineage_name 
				FROM ITEM JOIN hcl_entry ON ITEM.REPORTING_HIERARCHY = hcl_entry.SID  
				order by hcl_entry.full_lineage;

	Value Column :full_lineage
	Visible Columns :full_lineage_name


season(mandatory):
	Prompt Text (required):Season
	Parameter Name (read-only):season

	SQL:	select DISTINCT season,season_desc from item 
				order by season;
			
	Value Column :season
	Visible Columns :season_desc
		

theme:
	Prompt Text (required):Theme
	Parameter Name (read-only):theme

	SQL:	select DISTINCT theme,theme_desc from item 
				where item.theme is not null 
				order by theme;

	Value Column :theme
	Visible Columns :theme_desc
		
buyer:
	Prompt Text (required):Buyer
	Parameter Name (read-only):Buyer

	SQL:	select DISTINCT item_party.contact_user a,concat_ws(' ',USER_PROFILE.user_first_name,USER_PROFILE.user_last_name) b from item
			join item_party on doc_sid = item.sid and party_name_desc = 'Buyer'
			join USER_PROFILE on item_party.contact_user = USER_PROFILE.sid
			order by concat_ws(' ',USER_PROFILE.user_first_name,USER_PROFILE.user_last_name);
	 
	Value Column :a
	Visible Columns :b


Instore Month:
	Prompt Text (required):Instore Month
	Parameter Name (read-only):Instore_Month

	SQL:	select DISTINCT trim(to_char(item.instore_date,'MONTH')) a,trim(to_char(item.instore_date,'MONTH')) b 
			from item where item.instore_date is not null
			order by trim(to_char(item.instore_date,'MM')); (Exception)
			
	updated-SQL:select DISTINCT trim(to_char(item.instore_date,'MONTH')) a,trim(to_char(item.instore_date,'MONTH')) b 
			   from item where item.instore_date is not null
			   order by trim(to_char(item.instore_date,'MONTH')); 
			
	Value Column :a
	Visible Columns :b	
