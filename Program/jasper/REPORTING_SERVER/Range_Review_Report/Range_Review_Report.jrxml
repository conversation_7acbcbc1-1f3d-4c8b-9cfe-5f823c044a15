<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Common" pageWidth="1190" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1118" leftMargin="36" rightMargin="36" topMargin="36" bottomMargin="36" uuid="a74639fb-f9af-4afd-a026-acfea80d8959">
	<property name="ireport.zoom" value="0.9090909090909091"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="36"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="900000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.max.pages" value="200"/>
	<property name="net.sf.jasperreports.export.xls.one.page.per.sheet" value="true"/>
	<import value="com.core.pcpl.printForm.*"/>
	<import value="java.util.*"/>
	<import value="java.text.*"/>
	<style name="style1">
		<conditionalStyle>
			<style fill="Solid"/>
		</conditionalStyle>
	</style>
	<parameter name="JASPER_DIR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Desktop\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="Report_Hierarchy_level_1" class="java.lang.String">
		<defaultValueExpression><![CDATA["/001"]]></defaultValueExpression>
	</parameter>
	<parameter name="Report_Hierarchy_level_2" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_3" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_4" class="java.lang.String"/>
	<parameter name="Report_Hierarchy_level_5" class="java.lang.String"/>
	<parameter name="season" class="java.lang.String"/>
	<parameter name="theme" class="java.lang.String"/>
	<parameter name="Instore_Month" class="java.lang.String"/>
	<parameter name="Buyer" class="java.lang.String"/>
	<queryString>
		<![CDATA[select
	max(theme_left) theme_left,
	max(theme_right) theme_right,
	all_tb2.Instore_Date,
	all_tb2.buyer,
	all_tb2.buyerId,
	( select max(code_list_entry.name) from code_list_entry join code_list on code_list_entry.code_list_sys_id = code_list.sid
		where code_list.name = 'SEASON' and code_list_entry.code = 'AW16'
	) season_name,
	page
from (
	select
	case when internal_counter%2 = 1 then all_tb.theme else  null end  as theme_left,
	case when internal_counter%2 = 0 then all_tb.theme else  null end  as theme_right,
	sign(internal_counter%2) + trunc(internal_counter/2)  as page,
	all_tb.Instore_Date,
	CAST(all_tb.buyer  AS text) buyer ,
	all_tb.buyerId,
	all_tb.internal_counter
	 from (
		select  item.theme as theme,
			trim(to_char(item.instore_date,'MONTH')) as Instore_Date,
			row_number()over(partition by trim(to_char(item.instore_date,'MONTH')),concat_ws(' ',USER_PROFILE.user_first_name,USER_PROFILE.user_last_name) order by theme )internal_counter,
			concat_ws(' ',USER_PROFILE.user_first_name,USER_PROFILE.user_last_name) as buyer,USER_PROFILE.sid as buyerId
		from item
		  left join item_party on doc_sid = item.sid and item_party.party_name_desc = 'Buyer'
		  join USER_PROFILE on item_party.contact_user = USER_PROFILE.sid
		where item.theme is not null
			and ( $P{Report_Hierarchy_level_1} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_1} || '/%')
			and ( $P{Report_Hierarchy_level_2} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_2} || '/%')
			and ( $P{Report_Hierarchy_level_3} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_3} || '/%')
			and ( $P{Report_Hierarchy_level_4} is null or (item.cust_hcl1_full_code|| '/' ) like $P{Report_Hierarchy_level_4} || '/%')
			and ( $P{Report_Hierarchy_level_5} is null or item.cust_hcl1_full_code =  $P{Report_Hierarchy_level_5} )
			and item.season = $P{season}
			and ( $P{theme} is null or  item.theme = $P{theme} )
			and ( trim($P{Instore_Month}) is null or trim(to_char(item.instore_date,'MONTH')) = trim($P{Instore_Month}) )
			and ( $P{Buyer} is null or  USER_PROFILE.sid = $P{Buyer} )
			and item.doc_status = 'active'
			and item.sid in (select sid from
	(select item.sid from item
		left join item_sln on item_sln.item_sid = item.sid and item_sln.field_id = 'imageTypeId'
			  and item_sln.ref_display_value = 'Front Image'
			  and item_sln.doc_entity_name = 'ItemImage'
		left join item_image on item_image.sid = item_sln.doc_sid
		left join item_img on item_image.image_sid = item_img.sid
		left join item_att on COALESCE(item_img.with_mark_up_sid, item_img.original_sid) = item_att.sid
		 where  item.sid not in (select specification.item_sid from specification
		left join specification_set on specification_set.specification_sid = specification.sid
		left join specification_artwork on specification_artwork.specification_sid = specification.sid and upper(specification_artwork.artwork_type_desc) = 'OVERALL'
		left join specification_img on specification_img.sid = specification_artwork.image_sid
		left join specification_att on COALESCE(specification_img.with_mark_up_sid, specification_img.original_sid) = specification_att.sid
		where specification_att.file_content is not null)
		 ) a
	union all
select item_sid from
	(select specification.item_sid from specification
		left join specification_set on specification_set.specification_sid = specification.sid
		left join specification_artwork on specification_artwork.specification_sid = specification.sid and upper(specification_artwork.artwork_type_desc) = 'OVERALL'
		left join specification_img on specification_img.sid = specification_artwork.image_sid
		left join specification_att on COALESCE(specification_img.with_mark_up_sid, specification_img.original_sid) = specification_att.sid
		where specification_att.file_content is not null) spec)
		group by trim(to_char(item.instore_date,'MONTH')),item.theme,
			concat_ws(' ',USER_PROFILE.user_first_name,USER_PROFILE.user_last_name),USER_PROFILE.sid
	) all_tb
) all_tb2
group by
all_tb2.Instore_Date,
all_tb2.buyer,
all_tb2.buyerId,
page
order by
all_tb2.buyer,
to_DATE(Instore_Date,'MONTH'),
page]]>
	</queryString>
	<field name="theme_left" class="java.lang.String"/>
	<field name="theme_right" class="java.lang.String"/>
	<field name="instore_date" class="java.lang.String"/>
	<field name="buyer" class="java.lang.String"/>
	<field name="buyerid" class="java.lang.String"/>
	<field name="season_name" class="java.lang.String"/>
	<field name="page" class="java.lang.Double"/>
	<group name="instore_date" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{instore_date}]]></groupExpression>
		<groupHeader>
			<band height="36">
				<frame>
					<reportElement mode="Opaque" x="0" y="0" width="1118" height="18" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#0066CC" uuid="70cd1564-3532-412b-82db-9d459847cb20"/>
					<staticText>
						<reportElement x="4" y="2" width="103" height="16" forecolor="#FFFFFF" uuid="db28f2e2-36ef-4ec9-87eb-8c9f545b3ddc"/>
						<text><![CDATA[RANGE REVIEW]]></text>
					</staticText>
					<staticText>
						<reportElement x="852" y="2" width="37" height="16" forecolor="#FFFFFF" uuid="a9b207f8-a893-498d-9f58-6593485c2930"/>
						<textElement verticalAlignment="Middle"/>
						<text><![CDATA[buyer:]]></text>
					</staticText>
					<image hAlign="Right">
						<reportElement x="1050" y="2" width="68" height="16" uuid="3f334b58-6424-4a24-b839-5ca343a17784"/>
						<imageExpression><![CDATA["repo:icorn_pepkor.png"]]></imageExpression>
					</image>
					<textField isBlankWhenNull="true">
						<reportElement x="344" y="2" width="100" height="16" forecolor="#FFFFFF" uuid="36184ccb-3605-43e0-8327-e1ff8785eb2d"/>
						<textElement verticalAlignment="Middle"/>
						<textFieldExpression><![CDATA[$F{season_name}]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement x="297" y="2" width="47" height="16" forecolor="#FFFFFF" uuid="e4f56fc6-9417-41e4-a34a-5198aa0e965a"/>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<text><![CDATA[season:]]></text>
					</staticText>
					<textField>
						<reportElement x="889" y="2" width="161" height="16" forecolor="#FFFFFF" uuid="2c930c2f-0d8b-4f60-bbd0-962a2b04fabb"/>
						<textElement verticalAlignment="Middle"/>
						<textFieldExpression><![CDATA[$F{buyer}==null?"":$F{buyer}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement mode="Opaque" x="0" y="19" width="1118" height="16" backcolor="#0066CC" uuid="d9a2ff09-c5ac-4f60-a50d-2ecdfa61fde0"/>
					<textField isBlankWhenNull="true">
						<reportElement mode="Opaque" x="418" y="0" width="280" height="16" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#0066CC" uuid="e0d31023-96d6-45be-896b-60f252dad81d"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="10"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{instore_date}]]></textFieldExpression>
					</textField>
				</frame>
			</band>
		</groupHeader>
	</group>
	<detail>
		<band height="716">
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="0" y="0" width="554" height="16" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#0066CC" uuid="*************-42b8-bf41-748a65bec2ce"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{theme_left}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="0" y="17" width="554" height="699" uuid="31f13a2a-8116-445a-b12b-2f52fbac414f">
					<printWhenExpression><![CDATA[$F{theme_left}!=null]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_4">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_4}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_5">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_5}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_2">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="season">
					<subreportParameterExpression><![CDATA[$P{season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_3">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="INSTORE_DATE">
					<subreportParameterExpression><![CDATA[$F{instore_date}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_1">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="JASPER_DIR">
					<subreportParameterExpression><![CDATA[$P{JASPER_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="buyerId">
					<subreportParameterExpression><![CDATA[$F{buyerid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="THEME">
					<subreportParameterExpression><![CDATA[$F{theme_left}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:Range_Review_Report_sub.jrxml"]]></subreportExpression>
			</subreport>
			<textField isBlankWhenNull="true">
				<reportElement mode="Opaque" x="563" y="0" width="554" height="16" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF" backcolor="#0066CC" uuid="a19c2dbb-1a1b-4da3-8919-d127dd4884c7"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$F{theme_right}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="564" y="17" width="554" height="699" uuid="ce24a2f5-1b71-429b-a0b8-e86c4fc8b19c">
					<printWhenExpression><![CDATA[$F{theme_right}!=null]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_4">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_4}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_5">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_5}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_2">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_2}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="season">
					<subreportParameterExpression><![CDATA[$P{season}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_3">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_3}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="INSTORE_DATE">
					<subreportParameterExpression><![CDATA[$F{instore_date}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="Report_Hierarchy_level_1">
					<subreportParameterExpression><![CDATA[$P{Report_Hierarchy_level_1}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="JASPER_DIR">
					<subreportParameterExpression><![CDATA[$P{JASPER_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="buyerId">
					<subreportParameterExpression><![CDATA[$F{buyerid}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="THEME">
					<subreportParameterExpression><![CDATA[$F{theme_right}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["repo:Range_Review_Report_sub.jrxml"]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="715" width="100" height="1" uuid="c7d02807-635a-4ed9-8229-443cf3289977"/>
			</break>
		</band>
	</detail>
	<pageFooter>
		<band height="18">
			<frame>
				<reportElement mode="Opaque" x="0" y="0" width="1118" height="18" forecolor="#FFFFFF" backcolor="#0066CC" uuid="d9484f9d-7519-4048-b1ce-ec329127c7d9"/>
				<box topPadding="2" leftPadding="4">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement x="889" y="0" width="71" height="16" forecolor="#FFFFFF" uuid="8e761386-e5db-48de-9730-81497882bf16"/>
					<text><![CDATA[Date Printed:]]></text>
				</staticText>
				<textField>
					<reportElement x="40" y="0" width="20" height="16" forecolor="#FFFFFF" uuid="a83c4a20-ec72-41fb-aae2-1cc6a83dea37"/>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="77" y="0" width="29" height="16" forecolor="#FFFFFF" uuid="66945ca2-d9ca-44b3-8cc6-2a33283e6e25"/>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField pattern="dd-MMMMM-yy, HH:mm" isBlankWhenNull="false">
					<reportElement x="960" y="0" width="148" height="16" forecolor="#FFFFFF" uuid="dd5d28c2-a677-4a99-94d7-055788e13687"/>
					<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="11" y="0" width="29" height="16" forecolor="#FFFFFF" uuid="bc967778-60ce-4f05-93db-333bf8a31cfc"/>
					<text><![CDATA[page]]></text>
				</staticText>
				<staticText>
					<reportElement x="60" y="0" width="17" height="16" forecolor="#FFFFFF" uuid="c61bc24c-ca72-485b-ada7-9c028f07b022"/>
					<text><![CDATA[of]]></text>
				</staticText>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
