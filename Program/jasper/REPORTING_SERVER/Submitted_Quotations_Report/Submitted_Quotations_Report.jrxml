<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Submitted_Quotations_Report" language="groovy" pageWidth="1600" pageHeight="842" columnWidth="1600" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="36e5d42d-bfc8-4e61-b989-1752a60cefb8">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="655"/>
	<property name="ireport.y" value="0"/>
	<queryString>
		<![CDATA[select
QUOTATION.quotation_no
,HCL_ITEM.Department
,HCL_ITEM.subDetp
,HCL_ITEM.cat
,HCL_ITEM.subCat
,ITEM.item_no
,ITEM.PLU
,coalesce(VENDOR_PO_SUM.PO_TOTAL_QTY_SUM,ITEM.est_order_qty) qty
,ITEM.selling_price_pln_desc
,COST_SHEET.net_sales_price
,COST_SHEET.unit_cost
,COST_SHEET.estimated_margin
from QUOTATION
left join ITEM ON  ITEM.sid = QUOTATION.item_sid
left join (select item.sid
                , hcl_entry3.code|| ' - '||hcl_entry3.name Department
                , hcl_entry4.code|| ' - '||hcl_entry4.name subDetp
                , hcl_entry5.code|| ' - '||hcl_entry5.name cat
                , hcl_entry6.code|| ' - '||hcl_entry6.name subCat
           from hcl_entry hcl_entry6,hcl,item
           ,hcl_entry hcl_entry5
           ,hcl_entry hcl_entry4
           ,hcl_entry hcl_entry3
           where hcl.name ='REPORTING_HIERARCHY' and hcl.is_latest = 't'
           and hcl_entry6.hcl_sys_id = hcl.sid and hcl_entry6.node_level = 6
           and hcl_entry5.hcl_sys_id = hcl.sid and hcl_entry5.node_level = 5 and hcl_entry6.parent_entry_sys_id = hcl_entry5.sid
           and hcl_entry4.hcl_sys_id = hcl.sid and hcl_entry4.node_level = 4 and hcl_entry5.parent_entry_sys_id = hcl_entry4.sid
           and hcl_entry3.hcl_sys_id = hcl.sid and hcl_entry3.node_level = 3 and hcl_entry4.parent_entry_sys_id = hcl_entry3.sid
           and item.report_hcl = hcl_entry6.sid) HCL_ITEM on HCL_ITEM.sid = ITEM.sid
left join (select ITEM_LEVEL_QTY.item_sid,sum(ITEM_LEVEL_QTY.PO_TOTAL_QTY_SUM) as PO_TOTAL_QTY_SUM
           from
              (select
                  SUM(coalesce(sum(VENDOR_PO.PO_TOTAL_QTY), 0)) OVER (PARTITION BY ITEM.sid || VENDOR_PO.sid) as PO_TOTAL_QTY_SUM
                  ,ITEM.sid as item_sid
                  ,VENDOR_PO.sid
               from ITEM ,VENDOR_PO_ITEM,VENDOR_PO
               where ITEM.is_latest = 't'
               and VENDOR_PO_ITEM.item_sid = ITEM.sid and VENDOR_PO_ITEM.season = ITEM.season
               and VENDOR_PO.sid  = VENDOR_PO_ITEM.vendor_po_sid and VENDOR_PO.is_latest = 't'
               group by ITEM.sid,VENDOR_PO.sid
               order by ITEM.sid,VENDOR_PO.sid) ITEM_LEVEL_QTY
          group by ITEM_LEVEL_QTY.item_sid) AS VENDOR_PO_SUM on VENDOR_PO_SUM.item_sid = ITEM.sid
left join COST_SHEET on COST_SHEET.document_sid = QUOTATION.sid and COST_SHEET.is_latest = 't'
where QUOTATION.doc_status = 'active'
and QUOTATION.is_latest = 't'
and QUOTATION.status ='quoted'
order by QUOTATION.quotation_no;]]>
	</queryString>
	<field name="quotation_no" class="java.lang.String"/>
	<field name="department" class="java.lang.String"/>
	<field name="subdetp" class="java.lang.String"/>
	<field name="cat" class="java.lang.String"/>
	<field name="subcat" class="java.lang.String"/>
	<field name="item_no" class="java.lang.String"/>
	<field name="plu" class="java.lang.String"/>
	<field name="qty" class="java.math.BigDecimal"/>
	<field name="selling_price_pln_desc" class="java.lang.String"/>
	<field name="net_sales_price" class="java.math.BigDecimal"/>
	<field name="unit_cost" class="java.math.BigDecimal"/>
	<field name="estimated_margin" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement mode="Transparent" x="0" y="0" width="250" height="20" uuid="d3930a87-708e-462d-900f-1c72770f2d47"/>
				<textElement>
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Submitted Quotations Report]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="150" height="20" uuid="81eff242-6c4b-4249-98df-eceab08964cf"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Quotation no.]]></text>
			</staticText>
			<staticText>
				<reportElement x="150" y="0" width="150" height="20" uuid="079bfbba-df25-4cfa-92f9-babb46cdf23c"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Department]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="150" height="20" uuid="e2f3eea4-7b9d-445d-8568-06cc4dbf1e56"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Sub-detp]]></text>
			</staticText>
			<staticText>
				<reportElement x="450" y="0" width="150" height="20" uuid="bc4b7bff-e53e-4daa-8845-61194bdc1d08"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Cat]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="200" height="20" uuid="e88f8e66-8c9e-409c-8ca7-cfee7d6307f2"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Sub-cat]]></text>
			</staticText>
			<staticText>
				<reportElement x="800" y="0" width="100" height="20" uuid="b3554992-e8fa-4b87-82f9-0f5f71a00d9c"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="150" height="20" uuid="dfa93283-fe2b-44b6-bf0f-54122c107fdc"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[PLU]]></text>
			</staticText>
			<staticText>
				<reportElement x="1050" y="0" width="100" height="20" uuid="6d1b34d0-04cb-463d-af50-c02e739cc2f6"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[qty]]></text>
			</staticText>
			<staticText>
				<reportElement x="1150" y="0" width="150" height="20" uuid="d7775e5c-e8e8-4155-911f-2ea9aa3e7711"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[sales reatil]]></text>
			</staticText>
			<staticText>
				<reportElement x="1300" y="0" width="100" height="20" uuid="ddf651df-bada-49e8-af19-b61f98b5753d"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[sale net]]></text>
			</staticText>
			<staticText>
				<reportElement x="1400" y="0" width="100" height="20" uuid="7b03a55d-72ad-4c7d-ae07-88a6324a2453"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[LCP]]></text>
			</staticText>
			<staticText>
				<reportElement x="1500" y="0" width="100" height="20" uuid="fcf2f598-dc2e-4468-b6e6-acf5ab0d3f06"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Margin]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="61" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="150" height="60" uuid="e49111ec-01bf-4187-a139-57ba48528533"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{quotation_no}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="800" y="0" width="100" height="60" uuid="5c9df2b0-86f5-41c3-9f27-15c1ae5853a8"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{item_no}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="900" y="0" width="150" height="60" uuid="0f962176-77a7-408c-b30b-6f7eda66ad8c"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{plu}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1150" y="1" width="150" height="60" uuid="1910a00d-baa8-4d23-96e7-f5fbc79c8181"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{selling_price_pln_desc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="150" y="0" width="150" height="60" uuid="a24b60d6-ec7d-4206-b6ac-70f499372b74"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{department}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="1" width="150" height="60" uuid="3e8db367-2ef6-40de-82d0-22ce96c4da13"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{subdetp}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="450" y="0" width="150" height="60" uuid="9eb762ba-e7d8-4340-82ed-99bb4686d7a9"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{cat}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="600" y="1" width="200" height="60" uuid="2d671ece-5326-452b-b34b-0fac6a4403b1"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{subcat}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1050" y="1" width="100" height="60" uuid="ca2a3f26-0427-4015-916f-46bc2ed8473a"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{qty}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="1300" y="1" width="100" height="60" uuid="19a04e84-2b42-488f-b7cf-b4699b49269e"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{net_sales_price}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="1400" y="0" width="100" height="60" uuid="11239b4a-1751-486e-ac46-54389ea62871"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{unit_cost}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="1500" y="1" width="100" height="60" uuid="c38f5888-6a09-4ccb-ab64-3c34d904725c"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textFieldExpression><![CDATA[$F{estimated_margin}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
