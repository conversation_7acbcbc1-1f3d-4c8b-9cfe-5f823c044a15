<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Common" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.0000000000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="336"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="900000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.max.pages" value="200"/>
	<import value="com.core.cbx.data.entity.*"/>
	<import value="org.apache.commons.collections.Predicate"/>
	<import value="com.core.pepl.printForm.*"/>
	<style name="Default Style" isBlankWhenNull="true">
		<box topPadding="1" leftPadding="1"/>
	</style>
	<style name="page_header_green" mode="Opaque" backcolor="#0066CC" fill="Solid" fontSize="13">
		<box topPadding="2" leftPadding="4">
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25" lineColor="#000000"/>
			<leftPen lineWidth="0.25" lineColor="#000000"/>
			<bottomPen lineWidth="0.25" lineColor="#000000"/>
			<rightPen lineWidth="0.25" lineColor="#000000"/>
		</box>
	</style>
	<style name="Frame_Header_style" style="Default Style" backcolor="#0066CC" fontName="SansSerif" fontSize="12">
		<box topPadding="1" leftPadding="1">
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25" lineColor="#000000"/>
			<leftPen lineWidth="0.25" lineColor="#000000"/>
			<bottomPen lineWidth="0.25" lineColor="#000000"/>
			<rightPen lineWidth="0.25" lineColor="#000000"/>
		</box>
	</style>
	<style name="Static_Text_Blue" style="Default Style" backcolor="#D5E5FF" fontSize="9" pdfFontName="Helvetica-Bold">
		<box topPadding="2" leftPadding="4"/>
	</style>
	<style name="value_style" style="Default Style" fontSize="7">
		<box topPadding="4" leftPadding="4"/>
	</style>
	<style name="listStyle" fill="Solid" hAlign="Center" vAlign="Middle" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<pen lineWidth="0.25"/>
	</style>
	<style name="Fabrication_value" style="Default Style" fontSize="8"/>
	<style name="sample_size_style" forecolor="#000000" backcolor="#99CCFF" hAlign="Center" vAlign="Middle">
		<conditionalStyle>
			<style/>
		</conditionalStyle>
	</style>
	<subDataset name="itemColor">
		<field name="shortName" class="java.lang.String"/>
		<field name="colorCode" class="java.lang.String"/>
		<field name="color" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="colorId" class="java.lang.String"/>
		<field name="id" class="java.lang.String"/>
		<field name="colorPattern" class="com.core.cbx.data.entity.DynamicEntity"/>
		<variable name="itemColorEnt" class="com.core.cbx.data.entity.DynamicEntity" resetType="Group" resetGroup="group1">
			<variableExpression><![CDATA[Util.loadFullEntity($F{id},"ItemColor")]]></variableExpression>
		</variable>
		<group name="group1">
			<groupExpression><![CDATA[$F{id}]]></groupExpression>
		</group>
	</subDataset>
	<subDataset name="itemSize">
		<field name="sizeCode" class="java.lang.String"/>
		<field name="sizeDisplayName" class="java.lang.String"/>
		<field name="altLabel" class="java.lang.String"/>
		<field name="custNumber1" class="java.lang.Number"/>
		<field name="custText1" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specInstruction">
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
		<field name="typeName" class="java.lang.String"/>
		<field name="name" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="notesOrInstructions" class="java.lang.String"/>
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
	</subDataset>
	<subDataset name="specArtworks">
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="artworkName" class="java.lang.String"/>
		<field name="positionValue" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specDesign">
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="imageTypeName" class="java.lang.String"/>
	</subDataset>
	<subDataset name="otherDesign">
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="imageTypeName" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specMaterial">
		<field name="field1" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="field2" class="java.lang.String"/>
		<field name="field3" class="java.lang.String"/>
		<field name="field4" class="java.lang.String"/>
		<field name="field5" class="java.lang.String"/>
		<field name="field6" class="java.lang.String"/>
		<field name="field7" class="java.lang.String"/>
		<field name="field8" class="java.lang.String"/>
		<field name="field9" class="java.lang.String"/>
		<field name="field10" class="java.lang.String"/>
		<field name="field11" class="java.lang.String"/>
		<field name="field12" class="java.lang.String"/>
		<field name="isDuplicated" class="java.lang.Boolean"/>
		<field name="composition" class="java.lang.String"/>
	</subDataset>
	<subDataset name="ColorBOM">
		<field name="field1" class="java.lang.String"/>
		<field name="field2" class="java.lang.String"/>
		<field name="field3" class="java.lang.String"/>
		<field name="field4" class="java.lang.String"/>
		<field name="field5" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specSize">
		<field name="sizeCode" class="java.lang.String"/>
		<field name="sizeName" class="java.lang.String"/>
		<field name="altLabel" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specMeasurement">
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="code" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="toleranceNegative" class="java.lang.String"/>
		<field name="tolerancePositive" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specMeaturementSize">
		<parameter name="numberOfSize" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="field1" class="java.math.BigDecimal"/>
		<field name="field2" class="java.math.BigDecimal"/>
		<field name="field3" class="java.math.BigDecimal"/>
		<field name="field4" class="java.math.BigDecimal"/>
		<field name="field5" class="java.math.BigDecimal"/>
		<field name="field6" class="java.math.BigDecimal"/>
		<field name="field7" class="java.math.BigDecimal"/>
		<field name="field8" class="java.math.BigDecimal"/>
		<field name="field9" class="java.math.BigDecimal"/>
		<field name="field10" class="java.math.BigDecimal"/>
		<field name="field11" class="java.math.BigDecimal"/>
		<field name="field12" class="java.math.BigDecimal"/>
		<field name="field13" class="java.math.BigDecimal"/>
		<field name="field14" class="java.math.BigDecimal"/>
		<field name="sampleSizeColumn" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="specMeaturementSize_header">
		<parameter name="numberOfSize" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="field1" class="java.lang.String"/>
		<field name="field2" class="java.lang.String"/>
		<field name="field3" class="java.lang.String"/>
		<field name="field4" class="java.lang.String"/>
		<field name="field5" class="java.lang.String"/>
		<field name="field6" class="java.lang.String"/>
		<field name="field7" class="java.lang.String"/>
		<field name="field8" class="java.lang.String"/>
		<field name="field9" class="java.lang.String"/>
		<field name="field10" class="java.lang.String"/>
		<field name="field11" class="java.lang.String"/>
		<field name="field12" class="java.lang.String"/>
		<field name="field13" class="java.lang.String"/>
		<field name="field14" class="java.lang.String"/>
		<field name="sampleSizeColumn" class="java.lang.Integer"/>
		<field name="composition" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specRequirement">
		<field name="category" class="java.lang.String"/>
		<field name="type" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="custCheckbox1" class="java.lang.Boolean"/>
	</subDataset>
	<subDataset name="specPack">
		<field name="typeName" class="java.lang.String"/>
		<field name="notesOrInstructions" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specPackArtwork">
		<field name="type" class="java.lang.String"/>
		<field name="code" class="java.lang.String"/>
	</subDataset>
	<parameter name="JASPER_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="currentUser" class="com.core.cbx.data.entity.DynamicEntity" isForPrompting="false"/>
	<parameter name="createdUserFullName" class="java.lang.String"/>
	<parameter name="modifiedUserFullName" class="java.lang.String"/>
	<parameter name="documentTitle" class="java.lang.String"/>
	<parameter name="refNo" class="java.lang.String"/>
	<parameter name="editingStatus" class="java.lang.String"/>
	<parameter name="status" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="itemId" class="java.lang.String"/>
	<field name="id" class="java.lang.String"/>
	<field name="entityName" class="java.lang.String"/>
	<field name="refNo" class="java.lang.String"/>
	<field name="version" class="java.lang.Long"/>
	<field name="specMaterial" class="java.util.Collection"/>
	<field name="specInstruction" class="java.util.Collection"/>
	<field name="specDesign" class="java.util.Collection"/>
	<field name="specArtworkBom" class="java.util.Collection"/>
	<field name="specMeasurement" class="java.util.List"/>
	<field name="specNotesOrInstructions" class="java.lang.String"/>
	<field name="itemColor" class="java.util.Collection"/>
	<field name="specColorBom" class="java.util.Collection"/>
	<field name="specColorBomItemColor" class="java.util.Collection"/>
	<field name="specRequirement" class="java.util.Collection"/>
	<field name="specPack" class="java.util.Collection"/>
	<field name="specPackArtwork" class="java.util.Collection"/>
	<field name="custCheckbox5" class="java.lang.Boolean"/>
	<variable name="item" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$F{itemId} == null ? Util.loadFullEntity($F{id},"Item"): Util.loadFullEntity($F{itemId},"Item")]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="itemParties" class="java.util.List">
		<variableExpression><![CDATA[$V{item}.getEntityCollection("parties")]]></variableExpression>
	</variable>
	<variable name="frontImageRecord" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[Util.findEntity($V{item}.getEntityCollection("itemImage"), new Find("imageTypeIdValue","~","Front Image"))]]></variableExpression>
	</variable>
	<variable name="frontImageId" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$V{frontImageRecord} == null ? null : $V{frontImageRecord} .getEntity("fileId")]]></variableExpression>
	</variable>
	<variable name="backImageRecord" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[Util.findEntity($V{item}.getEntityCollection("itemImage"), new Find("imageTypeIdValue","~","Back Image"))]]></variableExpression>
	</variable>
	<variable name="backImageId" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$V{backImageRecord} == null ? null : $V{backImageRecord} .getEntity("fileId")]]></variableExpression>
	</variable>
	<variable name="DetailSketchRecord" class="java.util.List">
		<variableExpression><![CDATA[Util.findEntities($F{specDesign}, new Find("imageTypeName","=","Detailed Sketch"))]]></variableExpression>
	</variable>
	<variable name="OtherDesignRecord" class="java.util.List">
		<variableExpression><![CDATA[Util.findEntities($F{specDesign}, new Find("imageTypeName","<>","Detailed Sketch"))]]></variableExpression>
	</variable>
	<variable name="custHcl1FullName" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("custHcl1FullName")]]></variableExpression>
	</variable>
	<variable name="measurementUnit" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("measurementUnitName")]]></variableExpression>
	</variable>
	<variable name="status" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("status")]]></variableExpression>
	</variable>
	<variable name="itemNo" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("itemNo")]]></variableExpression>
	</variable>
	<variable name="createdOn" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[$V{item}.getDateTime("createdOn")]]></variableExpression>
	</variable>
	<variable name="description" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("description")]]></variableExpression>
	</variable>
	<variable name="summary" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("summary")]]></variableExpression>
	</variable>
	<variable name="remark" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("remark")]]></variableExpression>
	</variable>
	<variable name="custCodelist1Name" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("custCodelist1Name")]]></variableExpression>
	</variable>
	<variable name="seasonName" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("seasonName")]]></variableExpression>
	</variable>
	<variable name="itemDesc" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("itemDesc")]]></variableExpression>
	</variable>
	<variable name="custCodelist3Name" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("custCodelist3Name")]]></variableExpression>
	</variable>
	<variable name="custDate2" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[$V{item}.getDateTime("custDate2")]]></variableExpression>
	</variable>
	<variable name="itemColor" class="java.util.Collection">
		<variableExpression><![CDATA[$V{item}.getEntityCollection("itemColor")]]></variableExpression>
	</variable>
	<variable name="itemSize" class="java.util.Collection">
		<variableExpression><![CDATA[$V{item}.getEntityCollection("itemSize")]]></variableExpression>
	</variable>
	<variable name="itemImage" class="java.util.Collection">
		<variableExpression><![CDATA[$V{item}.getEntityCollection("itemImage")]]></variableExpression>
	</variable>
	<variable name="custMemoText1" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("custMemoText1")]]></variableExpression>
	</variable>
	<variable name="custHcl" class="java.lang.String[]">
		<variableExpression><![CDATA[$V{custHcl1FullName} == null ? new String[]{"","","","","",""}:$V{custHcl1FullName}.split("/")]]></variableExpression>
	</variable>
	<variable name="statusLabel" class="java.lang.String">
		<variableExpression><![CDATA["customStatus01".equals($V{status})?"Style Confirmed" : ("customStatus02".equals($V{status})?"Costing" : ("adopted".equals($V{status})?"Adopted" : ("concept".equals($V{status})?"Concept" : ("customStatus04".equals($V{status})?"Style Confirmed" : ("customStatus03".equals($V{status})?"Adopted" : ("final".equals($V{status})?"Final" : ("costing".equals($V{status})?"Costing" : "")))))))]]></variableExpression>
	</variable>
	<variable name="buyerId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Buyer")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="buyerName" class="java.lang.String">
		<variableExpression><![CDATA[$V{buyerId} == null ? null: Util.loadFullEntity($V{buyerId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="assistantId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Assistant")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="assistantName" class="java.lang.String">
		<variableExpression><![CDATA[$V{assistantId} == null ? null: Util.loadFullEntity($V{assistantId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="TechnologistId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Technologist")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="TechnologistName" class="java.lang.String">
		<variableExpression><![CDATA[$V{TechnologistId} == null ? null: Util.loadFullEntity($V{TechnologistId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="sample_size_id" class="java.lang.String">
		<variableExpression><![CDATA[($V{item}.getEntity("sampleSize") != null)?$V{item}.getEntity("sampleSize").getString("id"):null]]></variableExpression>
	</variable>
	<variable name="sample_size_name" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemSize},new Find("id",$V{sample_size_id})).getString("sizeName")]]></variableExpression>
	</variable>
	<variable name="pomImages" class="java.util.List">
		<variableExpression><![CDATA[(java.util.List)Pom.getSpecMeasurementImage($F{specMeasurement},"code",null)]]></variableExpression>
	</variable>
	<variable name="printTime" class="java.lang.String">
		<variableExpression><![CDATA[new java.text.SimpleDateFormat("dd-MMM-yyyy HH:mm:ss").format(new Date())]]></variableExpression>
	</variable>
	<variable name="headerText" class="java.lang.String">
		<variableExpression><![CDATA[$V{itemNo} +" - TECH PACK Version "+($F{version}==null?"NA":$F{version})+" - "+ $V{statusLabel}]]></variableExpression>
	</variable>
	<variable name="custCodelist2Name" class="java.lang.String">
		<variableExpression><![CDATA[$V{item}.getString("custCodelist2Name")]]></variableExpression>
	</variable>
	<variable name="logo_png" class="java.lang.String">
		<variableExpression><![CDATA["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"]]></variableExpression>
		<initialValueExpression><![CDATA["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"]]></initialValueExpression>
	</variable>
	<pageHeader>
		<band height="40">
			<textField evaluationTime="Report">
				<reportElement style="page_header_green" x="0" y="0" width="802" height="40" forecolor="#FFFFFF" backcolor="#0066CC"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{headerText}]]></textFieldExpression>
			</textField>
			<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle" onErrorType="Blank">
				<reportElement x="724" y="1" width="75" height="37"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage(org.apache.commons.codec.binary.Base64.decodeBase64($V{logo_png}))]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<detail>
		<band height="495">
			<frame>
				<reportElement x="267" y="0" width="535" height="164"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="535" height="20" forecolor="#FFFFFF" backcolor="#0066CC"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" markup="none">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[General Infomation]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="20" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Buyer]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="354" y="44" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Style]]></text>
				</staticText>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="85" y="116" width="88" height="24">
						<printWhenExpression><![CDATA[$V{custHcl}.length >4]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{custHcl}[4]]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="140" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Req. HTS Date]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="68" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Group]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="116" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Item Confirm Date]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="value_style" x="85" y="20" width="88" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{seasonName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="140" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Sub Class]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="354" y="20" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="354" y="92" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Pattern to be provided]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="92" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Department]]></text>
				</staticText>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="85" y="92" width="88" height="24">
						<printWhenExpression><![CDATA[$V{custHcl}.length >3]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{custHcl}[3]]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="92" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8"/>
					</textElement>
					<text><![CDATA[Style Type]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="354" y="140" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Est. Order Qty]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="44" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Division]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="value_style" x="85" y="68" width="88" height="24">
						<printWhenExpression><![CDATA[$V{custHcl}.length >2]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{custHcl}[2]]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="116" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Class]]></text>
				</staticText>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="85" y="140" width="88" height="24">
						<printWhenExpression><![CDATA[$V{custHcl}.length >5]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none"/>
					<textFieldExpression><![CDATA[$V{custHcl}[5]]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="44" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Assistant]]></text>
				</staticText>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="85" y="44" width="88" height="24">
						<printWhenExpression><![CDATA[$V{custHcl}.length >1]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{custHcl}[1]]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="354" y="68" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Description]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="354" y="116" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Target Price]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="85" height="24" forecolor="#000000" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Season]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="68" width="85" height="24" backcolor="#D5E5FF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Top" markup="none">
						<font size="8" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Technologist]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="dd-MMM-yy">
					<reportElement style="value_style" x="258" y="116" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{createdOn} == null ? null : $V{createdOn}.format("DD-MMM-YYYY")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="258" y="20" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{buyerName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="258" y="68" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{TechnologistName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="dd-MMM-yy">
					<reportElement style="value_style" x="258" y="140" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{custDate2} == null ? null : $V{custDate2}.format("DD-MMM-YYYY")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="258" y="44" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{assistantName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0">
					<reportElement style="value_style" x="439" y="140" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{item}.get("custNumber1")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="439" y="20" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
				</textField>
				<textField>
					<reportElement style="value_style" x="439" y="68" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{itemDesc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement style="value_style" x="439" y="44" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{itemNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="#,##0.00">
					<reportElement style="value_style" x="439" y="116" width="96" height="24"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement markup="none"/>
					<textFieldExpression><![CDATA[$V{item}.get("custDecimal1")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="258" y="92" width="96" height="24"/>
					<box topPadding="4" leftPadding="4">
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement>
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelist2Name}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="439" y="92" width="96" height="24"/>
					<box topPadding="4" leftPadding="4"/>
					<textElement>
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{item}.get("custCodelist7Name") == null ? "" : $V{item}.get("custCodelist7Name")]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="267" y="173" width="534" height="100"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="534" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Comments]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="0" y="20" width="534" height="80"/>
					<textElement>
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{specNotesOrInstructions}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Transparent" x="267" y="282" width="269" height="33"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="269" height="18" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Main Body Colours]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="18" width="148" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement>
						<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Colour Name]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="148" y="18" width="121" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Colour Pantone Number]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="555" y="282" width="247" height="33"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="247" height="18" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Sizes]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="18" width="121" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement>
						<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Size]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="121" y="18" width="126" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Size Ratio]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement mode="Transparent" x="0" y="0" width="267" height="470"/>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="267" height="20" forecolor="#FFFFFF"/>
					<box topPadding="2" leftPadding="4">
						<pen lineWidth="0.25" lineColor="#000000"/>
						<topPen lineWidth="0.25" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineColor="#000000"/>
						<bottomPen lineWidth="0.25" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Original Garment Photo]]></text>
				</staticText>
				<image hAlign="Center" onErrorType="Blank">
					<reportElement x="0" y="241" width="255" height="225"/>
					<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($V{backImageId}))]]></imageExpression>
				</image>
				<image hAlign="Center" onErrorType="Blank">
					<reportElement x="0" y="22" width="255" height="201"/>
					<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($V{frontImageId}))]]></imageExpression>
				</image>
			</frame>
			<componentElement>
				<reportElement x="267" y="315" width="269" height="15"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="itemColor">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{itemColor})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="15" width="269">
						<textField>
							<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="148" height="15"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="7" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[($V{itemColorEnt}.get("shortName")==null?"":$V{itemColorEnt}.get("shortName"))]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" x="148" y="0" width="121" height="15"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{colorCode}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<componentElement>
				<reportElement x="555" y="315" width="247" height="15"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="itemSize">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{itemSize})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="15" width="247">
						<textField>
							<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="121" height="15"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8" pdfFontName="Helvetica-Bold"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{sizeDisplayName}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" x="121" y="0" width="126" height="15"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{custText1}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="327">
			<printWhenExpression><![CDATA[$F{specColorBom}.size()!=0 || $F{specInstruction}.size()!=0 || $F{specMaterial}.size()!=0 || ($F{specRequirement}.size() !=0 && $F{custCheckbox5} == true)]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="802" height="64">
					<printWhenExpression><![CDATA[$F{specMaterial}.size()!=0]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" forecolor="#FFFFFF" backcolor="#0066CC"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Fabrication & Trims]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="0" y="24" width="150" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Image]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="150" y="24" width="150" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Material Name]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="300" y="24" width="72" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Material No]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="522" y="24" width="150" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Consumption &
 Notes/Instruction & Finishing]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="672" y="24" width="60" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Parts]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="732" y="24" width="70" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Positions]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="372" y="24" width="150" height="40"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Composition]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement positionType="Float" x="0" y="64" width="802" height="35">
					<printWhenExpression><![CDATA[$F{specMaterial}.size()!=0]]></printWhenExpression>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical" ignoreWidth="false">
					<datasetRun subDataset="specMaterial">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(Bom.getData($F{itemColor},$F{specColorBomItemColor},$F{specColorBom},$F{specMaterial}))]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="35" width="802">
						<textField isStretchWithOverflow="true">
							<reportElement style="Fabrication_value" stretchType="RelativeToTallestObject" x="150" y="0" width="150" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Fabrication_value" stretchType="RelativeToTallestObject" x="300" y="0" width="72" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Fabrication_value" stretchType="RelativeToTallestObject" x="522" y="0" width="150" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA[$F{field4} == null ? null : ($F{field4}.startsWith(",")?$F{field4}.substring(1):$F{field4})]]></textFieldExpression>
						</textField>
						<image hAlign="Center">
							<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="150" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{field1}))]]></imageExpression>
						</image>
						<textField isStretchWithOverflow="true">
							<reportElement style="Fabrication_value" stretchType="RelativeToTallestObject" x="672" y="0" width="60" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Fabrication_value" stretchType="RelativeToTallestObject" x="732" y="0" width="70" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<frame>
							<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="672" height="35">
								<printWhenExpression><![CDATA[$F{isDuplicated}]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</frame>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Fabrication_value" stretchType="RelativeToTallestObject" x="372" y="0" width="150" height="35"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement verticalAlignment="Middle"/>
							<textFieldExpression><![CDATA[$F{composition}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<frame>
				<reportElement positionType="Float" x="0" y="202" width="802" height="59">
					<printWhenExpression><![CDATA[$F{specInstruction}.size()!=0]]></printWhenExpression>
				</reportElement>
				<box topPadding="0"/>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" forecolor="#FFFFFF" backcolor="#0066CC"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Labelling]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="0" y="24" width="119" height="35"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Image]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="119" y="24" width="124" height="35"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Type]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="243" y="24" width="179" height="35"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Name]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="422" y="24" width="180" height="35"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Description]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="602" y="24" width="200" height="35"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Notes/Instruction]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement positionType="Float" mode="Transparent" x="0" y="261" width="802" height="64">
					<printWhenExpression><![CDATA[$F{specInstruction}.size()!=0]]></printWhenExpression>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specInstruction">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($F{specInstruction})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="64" width="802">
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="119" y="0" width="124" height="64"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{typeName}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="243" y="0" width="179" height="64"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="422" y="0" width="180" height="64"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="602" y="0" width="200" height="64"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{notesOrInstructions}]]></textFieldExpression>
						</textField>
						<image hAlign="Center" vAlign="Middle" onErrorType="Blank">
							<reportElement x="0" y="0" width="119" height="64"/>
							<box>
								<pen lineWidth="0.0"/>
								<topPen lineWidth="0.0"/>
								<leftPen lineWidth="0.0"/>
								<bottomPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{imageId}))]]></imageExpression>
						</image>
						<staticText>
							<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="119" height="64"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement/>
							<text><![CDATA[]]></text>
						</staticText>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<frame>
				<reportElement positionType="Float" x="0" y="110" width="802" height="50">
					<printWhenExpression><![CDATA[$F{specRequirement}.size() !=0 && $F{custCheckbox5} == true]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="20" forecolor="#FFFFFF" backcolor="#0066CC"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Requirements]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="0" y="20" width="300" height="30"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Category]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="300" y="20" width="200" height="30"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Type]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="500" y="20" width="302" height="30"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Description]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement positionType="Float" mode="Transparent" x="0" y="160" width="802" height="30">
					<printWhenExpression><![CDATA[$F{specRequirement}.size() !=0 && $F{custCheckbox5} == true]]></printWhenExpression>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specRequirement">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($F{specRequirement})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="30" width="802">
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="300" y="0" width="200" height="30"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{type}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="500" y="0" width="302" height="30"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="300" height="30"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{category}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<break>
				<reportElement positionType="FixRelativeToBottom" x="0" y="326" width="100" height="1"/>
			</break>
		</band>
		<band height="495">
			<printWhenExpression><![CDATA[$V{DetailSketchRecord}.size()!=0]]></printWhenExpression>
			<break>
				<reportElement positionType="FixRelativeToBottom" x="0" y="494" width="100" height="1"/>
			</break>
			<componentElement>
				<reportElement x="0" y="0" width="802" height="495"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specDesign">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{DetailSketchRecord})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="495" width="802">
						<frame>
							<reportElement x="0" y="24" width="802" height="471"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<image hAlign="Center">
								<reportElement x="12" y="9" width="777" height="453"/>
								<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{imageId}))]]></imageExpression>
							</image>
						</frame>
						<staticText>
							<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" forecolor="#FFFFFF"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center">
								<font isBold="true"/>
							</textElement>
							<text><![CDATA[Detail Sketch]]></text>
						</staticText>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="270">
			<printWhenExpression><![CDATA[$F{specArtworkBom}.size()!=0]]></printWhenExpression>
			<break>
				<reportElement positionType="FixRelativeToBottom" mode="Transparent" x="0" y="269" width="100" height="1"/>
			</break>
			<frame>
				<reportElement x="0" y="0" width="802" height="45" isPrintWhenDetailOverflows="true"/>
				<staticText>
					<reportElement style="Default Style" x="0" y="24" width="536" height="21"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Image]]></text>
				</staticText>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Artwork]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="536" y="24" width="140" height="21"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Artwork Name]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="676" y="24" width="126" height="21"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Position]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement x="0" y="45" width="802" height="224"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specArtworks">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($F{specArtworkBom})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="224" width="802">
						<textField>
							<reportElement style="Default Style" x="536" y="0" width="140" height="224"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center">
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{artworkName}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement style="Default Style" x="676" y="0" width="126" height="224"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center">
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{positionValue}]]></textFieldExpression>
						</textField>
						<frame>
							<reportElement x="0" y="0" width="536" height="224"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<image hAlign="Center">
								<reportElement x="3" y="5" width="530" height="210"/>
								<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{imageId}))]]></imageExpression>
							</image>
						</frame>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="495">
			<printWhenExpression><![CDATA[$V{pomImages}.size()!=0]]></printWhenExpression>
			<break>
				<reportElement positionType="FixRelativeToBottom" x="0" y="494" width="100" height="1"/>
			</break>
			<staticText>
				<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Size Spec]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="24" width="401" height="471"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textField>
					<reportElement x="111" y="0" width="57" height="24" isPrintWhenDetailOverflows="true"/>
					<textElement/>
					<textFieldExpression><![CDATA[$V{item}.get("custCodelist7Name") == null ? "" : $V{item}.get("custCodelist7Name")]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="5" y="25" width="384" height="403">
						<printWhenExpression><![CDATA[$V{pomImages}.size()>0]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$V{pomImages}.size()>0 ? net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage((DynamicEntity)((Map<String, Object>)$V{pomImages}.get(0)).get("field1"))) : null]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="2" y="0" width="110" height="24" isPrintWhenDetailOverflows="true"/>
					<textElement/>
					<text><![CDATA[Pattern to be provided:]]></text>
				</staticText>
			</frame>
			<frame>
				<reportElement x="401" y="24" width="401" height="471"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="5" y="25" width="384" height="403">
						<printWhenExpression><![CDATA[$V{pomImages}.size()>1]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[$V{pomImages}.size()>1 ? net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage((DynamicEntity)((Map<String, Object>)$V{pomImages}.get(1)).get("field1"))) : null]]></imageExpression>
				</image>
			</frame>
		</band>
		<band height="76">
			<printWhenExpression><![CDATA[$F{specMeasurement}.size()!=0]]></printWhenExpression>
			<break>
				<reportElement positionType="FixRelativeToBottom" x="0" y="75" width="100" height="1"/>
			</break>
			<componentElement>
				<reportElement style="listStyle" x="312" y="58" width="490" height="17"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeaturementSize">
						<datasetParameter name="numberOfSize">
							<datasetParameterExpression><![CDATA[$V{itemSize} == null ? 0 : Pom.getNumberOfActiveSize($V{itemSize})]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	Pom.getMeasurementDetail(
		Pom.exludeEntityByString($F{specMeasurement},"code","POM Sketch"),
		$V{item}.getEntityCollection("specMeasurementSize"),
		$V{itemSize},
        $V{sample_size_id},
        true
    )
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="17" width="490">
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="0" y="0" width="35" height="17"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="35" y="0" width="35" height="17"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="70" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="105" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="140" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="175" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="210" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="245" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="280" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="315" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="350" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="385" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="420" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 11]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field13}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" x="455" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 12]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field14}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(1)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="35" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(2)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="70" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(3)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="105" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(4)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(5)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="175" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(6)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="210" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(7)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="245" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(8)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(9)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="315" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(10)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="350" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(11)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="385" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(12)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<staticText>
				<reportElement style="Default Style" x="40" y="24" width="272" height="34" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Point of Measure]]></text>
			</staticText>
			<staticText>
				<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[POM - Measurement Section]]></text>
			</staticText>
			<textField>
				<reportElement style="Default Style" x="0" y="24" width="40" height="34" isPrintWhenDetailOverflows="true"/>
				<box topPadding="2" leftPadding="4">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{measurementUnit}==null?"Measurement":"Measurement ("+$V{measurementUnit}+")"]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="0" y="58" width="312" height="17"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeasurement">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(Pom.exludeEntityByString($V{item}.getEntityCollection("specMeasurement"),"code","POM Sketch"))]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="17" width="312">
						<textField>
							<reportElement style="Default Style" x="0" y="0" width="40" height="17">
								<printWhenExpression><![CDATA[$F{code} != null && !"POM SKETCH".equals($F{code}.toUpperCase().trim())]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement style="Default Style" x="40" y="0" width="272" height="17">
								<printWhenExpression><![CDATA[$F{description}!=null && !"POM SKETCH".equals($F{description}.toUpperCase().trim())]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<componentElement>
				<reportElement style="listStyle" x="312" y="24" width="490" height="17" isPrintWhenDetailOverflows="true"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeaturementSize_header">
						<datasetParameter name="numberOfSize">
							<datasetParameterExpression><![CDATA[$V{itemSize} == null ? 0 : Pom.getNumberOfActiveSize($V{itemSize})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="sample_size_name">
							<datasetParameterExpression><![CDATA[$V{sample_size_name}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	Pom.getMeasurementHeader($V{itemSize},
    $V{sample_size_id},
    true)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="17" width="490">
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="0" y="0" width="35" height="17"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="35" y="0" width="35" height="17"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="70" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="105" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="140" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="175" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="210" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="245" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="280" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="315" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="350" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="385" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="420" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 11]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field13}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" x="455" y="0" width="35" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 12]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field14}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(1)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="35" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(2)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="70" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(3)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="105" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(4)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(5)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="175" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(6)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="210" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(7)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="245" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(8)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(9)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="315" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(10)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="350" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(11)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}+" *"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="385" y="0" width="35" height="17" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(12)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}+" *"]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="35" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(1) && "Tolerances".equals($F{field2})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="70" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(2) && "Tolerances".equals($F{field3})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="105" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(3) && "Tolerances".equals($F{field4})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(4) && "Tolerances".equals($F{field5})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="175" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(5) && "Tolerances".equals($F{field6})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="210" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(6) && "Tolerances".equals($F{field7})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="245" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(7) && "Tolerances".equals($F{field8})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(8) && "Tolerances".equals($F{field9})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="315" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(9) && "Tolerances".equals($F{field10})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="350" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(10) && "Tolerances".equals($F{field11})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="385" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(11) && "Tolerances".equals($F{field12})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="420" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(12) && "Tolerances".equals($F{field13})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="70" height="17">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(0) && "Tolerances".equals($F{field1})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
		<band height="270">
			<printWhenExpression><![CDATA[$V{OtherDesignRecord}.size()!=0]]></printWhenExpression>
			<frame>
				<reportElement x="0" y="0" width="802" height="46" isPrintWhenDetailOverflows="true"/>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="802" height="24" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Pattern & 3D Samples]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="0" y="24" width="655" height="22"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Image]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="655" y="24" width="147" height="22"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Image Type]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement x="0" y="46" width="802" height="224"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="otherDesign">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{OtherDesignRecord})]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="224" width="802">
						<frame>
							<reportElement x="0" y="0" width="802" height="224"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<image hAlign="Center">
								<reportElement x="5" y="5" width="645" height="214"/>
								<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{imageId}))]]></imageExpression>
							</image>
							<textField>
								<reportElement style="Default Style" x="655" y="0" width="147" height="224"/>
								<box>
									<pen lineWidth="0.25"/>
									<topPen lineWidth="0.25"/>
									<leftPen lineWidth="0.25"/>
									<bottomPen lineWidth="0.25"/>
									<rightPen lineWidth="0.25"/>
								</box>
								<textElement textAlignment="Center"/>
								<textFieldExpression><![CDATA[$F{imageTypeName}]]></textFieldExpression>
							</textField>
						</frame>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
	<pageFooter>
		<band height="20">
			<frame>
				<reportElement style="page_header_green" x="0" y="0" width="802" height="20" forecolor="#FFFFFF"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Default Style" x="573" y="0" width="80" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Date Printed:]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="40" y="0" width="20" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="80" y="0" width="29" height="17" forecolor="#FFFFFF"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement style="Default Style" x="655" y="0" width="140" height="17" forecolor="#FFFFFF"/>
					<textElement verticalAlignment="Top">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{printTime}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="2" y="0" width="38" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Page]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="60" y="0" width="20" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[of]]></text>
				</staticText>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
