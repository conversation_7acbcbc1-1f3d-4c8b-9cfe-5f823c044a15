<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ShipmentSampleReport" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.771561000000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="962"/>
	<property name="net.sf.jasperreports.governor.timeout.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.timeout" value="1200000"/>
	<property name="net.sf.jasperreports.governor.max.pages.enabled" value="true"/>
	<property name="net.sf.jasperreports.governor.max.pages" value="2000"/>
	<import value="com.core.cbx.data.entity.*"/>
	<import value="com.core.pepl.printForm.*"/>
	<import value="com.core.cbx.common.*"/>
	<import value="org.apache.commons.collections.Predicate"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<style name="Default Style" isBlankWhenNull="true" fontSize="7">
		<box topPadding="1" leftPadding="1"/>
	</style>
	<style name="page_header_green" mode="Opaque" backcolor="#0066CC" fill="Solid" fontSize="13">
		<box topPadding="2" leftPadding="4">
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25" lineColor="#000000"/>
			<leftPen lineWidth="0.25" lineColor="#000000"/>
			<bottomPen lineWidth="0.25" lineColor="#000000"/>
			<rightPen lineWidth="0.25" lineColor="#000000"/>
		</box>
	</style>
	<style name="Static_Text_Blue" style="Default Style" mode="Transparent" backcolor="#CFDBE6" fill="Solid" hAlign="Left" vAlign="Top" fontSize="7" pdfFontName="Helvetica-Bold">
		<box topPadding="3" leftPadding="2"/>
	</style>
	<style name="Frame_Header_style" style="Default Style" backcolor="#0066CC" fontName="SansSerif" fontSize="12"/>
	<style name="listStyle" fill="Solid" hAlign="Center" vAlign="Middle" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<pen lineWidth="0.25"/>
	</style>
	<style name="style1">
		<conditionalStyle>
			<style/>
		</conditionalStyle>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#8FAFCC">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#CFDBE6" fontSize="7">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF" vAlign="Middle" fontSize="7">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F3F6F8"/>
		</conditionalStyle>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#8FAFCC">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#CFDBE6">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F3F6F8"/>
		</conditionalStyle>
	</style>
	<style name="table">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#8FAFCC">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#CFDBE6">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF" hAlign="Center" vAlign="Middle" fontSize="6" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box leftPadding="0">
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#F3F6F8"/>
		</conditionalStyle>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="0.25" lineColor="#000000"/>
			<topPen lineWidth="0.25"/>
			<leftPen lineWidth="0.25"/>
			<bottomPen lineWidth="0.25"/>
			<rightPen lineWidth="0.25"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<subDataset name="specMeasurement">
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="imageId" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="code" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="toleranceNegative" class="java.lang.String"/>
		<field name="tolerancePositive" class="java.lang.String"/>
	</subDataset>
	<subDataset name="specMeaturementSize">
		<parameter name="numberOfSize" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="field1" class="java.math.BigDecimal"/>
		<field name="field2" class="java.math.BigDecimal"/>
		<field name="field3" class="java.math.BigDecimal"/>
		<field name="field4" class="java.math.BigDecimal"/>
		<field name="field5" class="java.math.BigDecimal"/>
		<field name="field6" class="java.math.BigDecimal"/>
		<field name="field7" class="java.math.BigDecimal"/>
		<field name="field8" class="java.math.BigDecimal"/>
		<field name="field9" class="java.math.BigDecimal"/>
		<field name="field10" class="java.math.BigDecimal"/>
		<field name="field11" class="java.math.BigDecimal"/>
		<field name="field12" class="java.math.BigDecimal"/>
		<field name="field13" class="java.math.BigDecimal"/>
		<field name="field14" class="java.math.BigDecimal"/>
		<field name="sampleSizeColumn" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="specMeaturementSize_1">
		<parameter name="numberOfSize" class="java.lang.Integer">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="sample_size_name" class="java.lang.String"/>
		<field name="field1" class="java.lang.String"/>
		<field name="field2" class="java.lang.String"/>
		<field name="field3" class="java.lang.String"/>
		<field name="field4" class="java.lang.String"/>
		<field name="field5" class="java.lang.String"/>
		<field name="field6" class="java.lang.String"/>
		<field name="field7" class="java.lang.String"/>
		<field name="field8" class="java.lang.String"/>
		<field name="field9" class="java.lang.String"/>
		<field name="field10" class="java.lang.String"/>
		<field name="field11" class="java.lang.String"/>
		<field name="field12" class="java.lang.String"/>
		<field name="field13" class="java.lang.String"/>
		<field name="field14" class="java.lang.String"/>
		<field name="sampleSizeColumn" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="supportingImages">
		<field name="internalSeqNo" class="java.lang.Long"/>
		<field name="imageTypesValue" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="image" class="com.core.cbx.data.entity.DynamicEntity"/>
	</subDataset>
	<subDataset name="sampleDetails">
		<field name="sampleTypeName" class="java.lang.String"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="altColor" class="java.lang.String"/>
		<field name="altSizeCode" class="java.lang.String"/>
		<field name="specVersion" class="java.lang.Long"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="updatedOn" class="com.core.cbx.common.type.DateTime"/>
		<field name="resultName" class="java.lang.String"/>
		<field name="instructions" class="java.lang.String"/>
		<field name="sampleEvaluation" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="sizeCode" class="java.lang.String"/>
		<field name="isLatest" class="java.lang.Boolean"/>
		<field name="field1" class="java.lang.String"/>
		<field name="altColorAndPattern" class="java.lang.String"/>
		<field name="colorAndPatternValue" class="java.lang.String"/>
		<field name="currentFitDate" class="com.core.cbx.common.type.DateTime"/>
		<field name="currentFitResultName" class="java.lang.String"/>
		<field name="currentComments" class="java.lang.String"/>
		<variable name="currentFitResult" class="java.lang.String">
			<variableExpression><![CDATA[$F{currentFitResultName}==null? "":$F{currentFitResultName}]]></variableExpression>
		</variable>
		<variable name="currentComments" class="java.lang.String">
			<variableExpression><![CDATA[$F{currentComments}==null? "":$F{currentComments}]]></variableExpression>
		</variable>
		<variable name="lastUpdateDate" class="com.core.cbx.common.type.DateTime">
			<variableExpression><![CDATA[$F{updatedOn}]]></variableExpression>
		</variable>
		<variable name="currentFitDate" class="com.core.cbx.common.type.DateTime">
			<variableExpression><![CDATA[$F{currentFitDate}]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="materialDetails">
		<field name="materialSampleTypeName" class="java.lang.String"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="materialName" class="java.lang.String"/>
		<field name="altColor" class="java.lang.String"/>
		<field name="specVersion" class="java.lang.Long"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="updatedOn" class="com.core.cbx.common.type.DateTime"/>
		<field name="resultName" class="java.lang.String"/>
		<field name="instructions" class="java.lang.String"/>
		<field name="custCodelist1Name" class="java.lang.String"/>
		<field name="custMemoText1" class="java.lang.String"/>
		<field name="sampleEvaluation" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="isLatest" class="java.lang.Boolean"/>
		<field name="altColorAndPattern" class="java.lang.String"/>
		<field name="colorAndPatternValue" class="java.lang.String"/>
		<field name="sampleResultName" class="java.lang.String"/>
		<variable name="comments" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleEvaluation}.getString("comments")]]></variableExpression>
		</variable>
		<variable name="sampleResult" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleResultName}==null? "":$F{sampleResultName}]]></variableExpression>
		</variable>
		<variable name="evaluateResultName" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleEvaluation}.getString("evaluateResultName")]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="documentDetails">
		<field name="updatedOn" class="com.core.cbx.common.type.DateTime"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="documentTypeName" class="java.lang.String"/>
		<field name="field" class="java.lang.String"/>
		<field name="specVersion" class="java.lang.Long"/>
		<field name="description" class="java.lang.String"/>
		<field name="resultName" class="java.lang.String"/>
		<field name="instructions" class="java.lang.String"/>
		<field name="custCodelist1Name" class="java.lang.String"/>
		<field name="custMemoText1" class="java.lang.String"/>
	</subDataset>
	<subDataset name="sampleEvaluations">
		<parameter name="JASPER_DIR" class="java.lang.String"/>
		<parameter name="measurementUnit" class="java.lang.String"/>
		<parameter name="season" class="java.lang.String"/>
		<parameter name="currentSpecVer" class="java.lang.String"/>
		<parameter name="description" class="java.lang.String"/>
		<parameter name="buyer" class="java.lang.String"/>
		<parameter name="assistant" class="java.lang.String"/>
		<parameter name="technologist" class="java.lang.String"/>
		<parameter name="trackNo" class="java.lang.String"/>
		<parameter name="theme" class="java.lang.String"/>
		<parameter name="style" class="java.lang.String"/>
		<parameter name="supplier" class="java.lang.String"/>
		<parameter name="custHcl" class="java.lang.String[]"/>
		<parameter name="PLU" class="java.lang.String"/>
		<field name="sampleEvaluation" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="altColor" class="java.lang.String"/>
		<field name="sampleTypeName" class="java.lang.String"/>
		<field name="sampleId" class="java.lang.String"/>
		<field name="altSizeCode" class="java.lang.String"/>
		<field name="sampleVersion" class="java.lang.String"/>
		<field name="evalMeasurementFit" class="java.util.Collection"/>
		<field name="id" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="evalAccessoriesMeasurementFit" class="java.util.Collection"/>
		<field name="sampleEvaluationImages" class="java.util.Collection"/>
		<field name="sampleType" class="com.core.cbx.data.entity.DynamicEntity"/>
		<field name="parentId" class="java.lang.String"/>
		<field name="custText6" class="java.lang.String"/>
		<field name="sampleTrackerRef" class="java.lang.String"/>
		<variable name="sampleTracker" class="com.core.cbx.data.entity.DynamicEntity">
			<variableExpression><![CDATA[VisualReport.loadFullEntityByRefNo($F{sampleTrackerRef},"SampleTracker")]]></variableExpression>
		</variable>
		<variable name="sampleTypeCode" class="java.lang.String">
			<variableExpression><![CDATA[$F{sampleType}.getString("code")]]></variableExpression>
		</variable>
		<variable name="measurementSizes" class="java.util.List">
			<variableExpression><![CDATA[VisualReport.getSampleResultsFromSampleRecords($V{sampleTracker},$V{sampleTypeCode},"sampleDetail")]]></variableExpression>
		</variable>
		<variable name="colours" class="java.lang.String">
			<variableExpression><![CDATA[VisualReport.getAllColours($V{measurementSizes})]]></variableExpression>
		</variable>
		<variable name="currentVendorQualityComment" class="java.lang.String">
			<variableExpression><![CDATA[VisualReport.getAllQAComments($V{measurementSizes})]]></variableExpression>
		</variable>
		<variable name="sampleEvaluationImages" class="java.util.Collection">
			<variableExpression><![CDATA[VisualReport.getAllQASupportingImages($V{measurementSizes})]]></variableExpression>
		</variable>
		<variable name="afterWashComments" class="java.lang.String">
			<variableExpression><![CDATA[VisualReport.getAllQAComments($V{measurementSizes},"currentVendorQualityComment")]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="evalMeasurementFits">
		<field name="code" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="tolerancePositive" class="java.math.BigDecimal"/>
		<field name="toleranceNegative" class="java.math.BigDecimal"/>
		<field name="sampleMeasurement" class="java.math.BigDecimal"/>
		<field name="qaMeasurement" class="java.math.BigDecimal"/>
		<field name="qaVariance" class="java.math.BigDecimal"/>
		<field name="curRevisedMeasurement" class="java.math.BigDecimal"/>
		<field name="qaResultName" class="java.lang.String"/>
		<field name="qaComments" class="java.lang.String"/>
		<field name="vendorMeasurement" class="java.math.BigDecimal"/>
		<field name="vendorVariance" class="java.math.BigDecimal"/>
		<field name="revisedMeasurement" class="java.math.BigDecimal"/>
		<variable name="qaVariance" class="java.math.BigDecimal">
			<variableExpression><![CDATA[$F{qaMeasurement}!= null ? $F{qaMeasurement}.subtract($F{sampleMeasurement}):$F{qaVariance}]]></variableExpression>
		</variable>
	</subDataset>
	<subDataset name="sampleEvaluationImages">
		<field name="internalSeqNo" class="java.lang.Long"/>
		<field name="description" class="java.lang.String"/>
		<field name="image" class="com.core.cbx.data.entity.DynamicEntity"/>
	</subDataset>
	<subDataset name="sampleMeasurementTables">
		<parameter name="sizes" class="java.util.List"/>
		<parameter name="measurementUnit" class="java.lang.String"/>
		<parameter name="custText6" class="java.lang.String"/>
		<parameter name="sampleTypeName" class="java.lang.String"/>
		<parameter name="sampleVersion" class="java.lang.String"/>
		<parameter name="colours" class="java.lang.String"/>
		<field name="index" class="java.lang.Integer"/>
		<field name="number" class="java.lang.Integer"/>
		<field name="pages" class="java.lang.Integer"/>
	</subDataset>
	<subDataset name="QAComments">
		<parameter name="currentVendorQualityComment" class="java.lang.String"/>
	</subDataset>
	<parameter name="JASPER_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Desktop\\PEGP\\PEGP-424\\jasper\\"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="id" class="java.lang.String"/>
	<field name="trackerNo" class="java.lang.String"/>
	<field name="item" class="com.core.cbx.data.entity.DynamicEntity"/>
	<variable name="sampleTracker" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[VisualReport.loadFullEntity($F{id},"SampleTracker")]]></variableExpression>
	</variable>
	<variable name="itemId" class="java.lang.String">
		<variableExpression><![CDATA[$F{item}.getId()]]></variableExpression>
	</variable>
	<variable name="fullItem" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[Util.loadFullEntity($V{itemId},"Item")]]></variableExpression>
	</variable>
	<variable name="itemParties" class="java.util.List">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("parties")]]></variableExpression>
	</variable>
	<variable name="specMaterial" class="java.util.List">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("specMaterial")]]></variableExpression>
	</variable>
	<variable name="specMeasurement" class="java.util.List">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("specMeasurement")]]></variableExpression>
	</variable>
	<variable name="itemSize" class="java.util.Collection">
		<variableExpression><![CDATA[$V{fullItem}.getEntityCollection("itemSize")]]></variableExpression>
	</variable>
	<variable name="custHcl1FullName" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custHcl1FullName")]]></variableExpression>
	</variable>
	<variable name="measurementUnit" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("measurementUnitName")]]></variableExpression>
	</variable>
	<variable name="buyerId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Buyer")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="buyerName" class="java.lang.String">
		<variableExpression><![CDATA[$V{buyerId} == null ? null: Util.loadFullEntity($V{buyerId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="assistantId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Assistant")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="assistantName" class="java.lang.String">
		<variableExpression><![CDATA[$V{assistantId} == null ? null: Util.loadFullEntity($V{assistantId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="TechnologistId" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemParties},new Find("partyNameName","Technologist")).getEntity("contactUser").getString("id")]]></variableExpression>
	</variable>
	<variable name="TechnologistName" class="java.lang.String">
		<variableExpression><![CDATA[$V{TechnologistId} == null ? null: Util.loadFullEntity($V{TechnologistId},"User").getString("userName")]]></variableExpression>
	</variable>
	<variable name="custHcl" class="java.lang.String[]">
		<variableExpression><![CDATA[$V{custHcl1FullName} == null ? new String[]{"","","","","",""}:$V{custHcl1FullName}.split("/")]]></variableExpression>
	</variable>
	<variable name="custCodelist1Name" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custCodelist1Name")]]></variableExpression>
	</variable>
	<variable name="itemNo" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("itemNo")]]></variableExpression>
	</variable>
	<variable name="specVersion" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("version")]]></variableExpression>
	</variable>
	<variable name="seasonName" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("seasonName")]]></variableExpression>
	</variable>
	<variable name="itemDescription" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("itemDesc")]]></variableExpression>
	</variable>
	<variable name="PLU" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custText1")]]></variableExpression>
	</variable>
	<variable name="styleType" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("custCodelist2Name")]]></variableExpression>
	</variable>
	<variable name="frontImageRecord" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$V{fullItem}.getEntity("fileId")]]></variableExpression>
	</variable>
	<variable name="frontImageId" class="com.core.cbx.data.entity.DynamicEntity">
		<variableExpression><![CDATA[$V{frontImageRecord} == null ? null : $V{frontImageRecord}]]></variableExpression>
	</variable>
	<variable name="comments" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("comments")]]></variableExpression>
	</variable>
	<variable name="fabrics" class="java.lang.String">
		<variableExpression><![CDATA[Pom.getMaterialNamesByType($V{specMaterial}, "CL01")]]></variableExpression>
	</variable>
	<variable name="sample_size_id" class="java.lang.String">
		<variableExpression><![CDATA[($V{fullItem}.getEntity("sampleSize") != null)?$V{fullItem}.getEntity("sampleSize").getString("id"):null]]></variableExpression>
	</variable>
	<variable name="sample_size_name" class="java.lang.String">
		<variableExpression><![CDATA[Util.findEntity($V{itemSize},new Find("id",$V{sample_size_id})).getString("sizeName")]]></variableExpression>
	</variable>
	<variable name="sampleDetail" class="java.util.List">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("sampleDetail")]]></variableExpression>
	</variable>
	<variable name="materialDetail" class="java.util.Collection">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("materialDetail")]]></variableExpression>
	</variable>
	<variable name="documentDetail" class="java.util.Collection">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("documentDetail")]]></variableExpression>
	</variable>
	<variable name="vendorName" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntity("vendor")==null?null:$V{sampleTracker}.getEntity("vendor").getString("businessName")]]></variableExpression>
	</variable>
	<variable name="custCodelistName5" class="java.lang.String">
		<variableExpression><![CDATA[$V{sampleTracker}.getString("custCodelist5Name")]]></variableExpression>
	</variable>
	<variable name="lastUpdateDate5" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[VisualReport.findLatestQAEvaluateDateInST($V{sampleTracker}, "MSTLD70", "materialDetail", false)]]></variableExpression>
	</variable>
	<variable name="printDate" class="java.lang.String">
		<variableExpression><![CDATA[new java.text.SimpleDateFormat("dd-MMM-yyyy HH:mm:ss").format(new Date())]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="specMeasurementImages" class="java.util.List">
		<variableExpression><![CDATA[Pom.getSpecMeasurementImage($V{specMeasurement},"code",null)]]></variableExpression>
	</variable>
	<variable name="QAComments" class="java.util.Collection">
		<variableExpression><![CDATA[VisualReport.buildDetailsGrid(Util.sortList((List<DynamicEntity>)$V{sampleTracker}.getEntityCollection("sampleDetail"), new Sort(new String[]{"sampleType","code"}),new Sort(new String[]{"sizeCode"},true,true),new Sort(new String[]{"sampleVersion"},false)) ,"SMP-04")]]></variableExpression>
	</variable>
	<variable name="BuyerComments" class="java.util.Collection">
		<variableExpression><![CDATA[VisualReport.buildDetailsGrid(Util.sortList((List<DynamicEntity>)$V{sampleTracker}.getEntityCollection("materialDetail"), new Sort(new String[]{"sampleType","code"}),new Sort(new String[]{"sizeCode","code"},true,true), new Sort(new String[]{"sampleVersion"},false)) ,"MSTLD70")]]></variableExpression>
	</variable>
	<variable name="sampleTrackerImages" class="java.util.Collection">
		<variableExpression><![CDATA[$V{sampleTracker}.getEntityCollection("sampleTrackerImages")]]></variableExpression>
	</variable>
	<variable name="firstHTSDate" class="com.core.cbx.common.type.DateTime">
		<variableExpression><![CDATA[$V{sampleTracker}.getDateTime("custDate1")]]></variableExpression>
	</variable>
	<variable name="measurement_type" class="java.lang.String">
		<variableExpression><![CDATA[$V{fullItem}.getString("measurementType")]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="130" splitType="Stretch">
			<textField evaluationTime="Report">
				<reportElement style="page_header_green" x="0" y="0" width="555" height="30" forecolor="#FFFFFF" backcolor="#0066CC"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{itemNo}+" - SAMPLE APPROVAL REPORT"]]></textFieldExpression>
			</textField>
			<image onErrorType="Blank">
				<reportElement x="497" y="0" width="58" height="30"/>
				<imageExpression><![CDATA[$P{JASPER_DIR}  + "images" + java.io.File.separator + "icorn_pepkor.png"]]></imageExpression>
			</image>
			<frame>
				<reportElement x="0" y="30" width="555" height="100"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[SEASON]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="0" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{seasonName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="0" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[BUYER]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="0" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{buyerName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="28" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[QA]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="28" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{TechnologistName}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="440" y="28" width="115" height="14"/>
					<box topPadding="1" leftPadding="1">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{vendorName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="28" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[SUPPLIER]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="14" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[ASSISTANT]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="14" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{assistantName}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="440" y="0" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{itemNo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="0" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[STYLE]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="42" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[TRACKER NO]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="42" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$F{trackerNo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="14" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[DEPARTMENT]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="28" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[CLASS]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="28" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custHcl}.length > 4 ? $V{custHcl}[4]: null]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="42" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custHcl}.length > 5?$V{custHcl}[5]:null]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="14" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custHcl}.length > 3 ? $V{custHcl}[3]: null]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="42" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[SUB CLASS]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="70" width="70" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[DESCRIPTION]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="14" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[PLU]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="70" width="300" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{itemDescription}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="440" y="14" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{PLU}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="85" width="70" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[FABRICS]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="85" width="300" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{fabrics}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="70" y="56" width="115" height="14"/>
					<box topPadding="1" leftPadding="1">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{firstHTSDate} == null ? null : $V{firstHTSDate}.format("DD-MMM-YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="56" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[FIRST HTS DATE]]></text>
				</staticText>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="255" y="56" width="115" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{styleType}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="185" y="56" width="70" height="14"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[STYLE TYPE]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="370" y="42" width="70" height="58">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[PICTURE]]></text>
				</staticText>
				<image hAlign="Center" vAlign="Middle" onErrorType="Blank" evaluationTime="Report">
					<reportElement x="440" y="42" width="115" height="58">
						<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getOrigImage($V{frontImageId}))]]></imageExpression>
				</image>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="213">
			<break>
				<reportElement positionType="FixRelativeToBottom" x="0" y="212" width="100" height="1"/>
			</break>
			<frame>
				<reportElement x="0" y="10" width="555" height="60"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textField>
					<reportElement style="Default Style" x="173" y="40" width="175" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{custCodelistName5}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="40" width="173" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[Shipment Sample]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="348" y="40" width="207" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$V{lastUpdateDate5} == null ? null : $V{lastUpdateDate5}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[STATUS SUMMARY]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="173" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[SAMPLE TYPE]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="173" y="20" width="175" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[OVERALL STATUS]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="348" y="20" width="207" height="20"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle"/>
					<text><![CDATA[LAST UPDATED DATE]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="348" y="40" width="207" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName5}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{lastUpdateDate5} == null ? null : $V{lastUpdateDate5}.format("DD/MM/YYYY")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="0" y="40" width="173" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName5}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Shipment Sample]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" isPrintRepeatedValues="false" mode="Opaque" x="173" y="40" width="175" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
						<printWhenExpression><![CDATA[$V{custCodelistName5}.equals("Rejected")]]></printWhenExpression>
					</reportElement>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{custCodelistName5}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="81" width="555" height="20">
					<printWhenExpression><![CDATA[$V{QAComments}!= null && $V{QAComments}.size() >0]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25" lineColor="#000000"/>
						<topPen lineWidth="0.25" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineColor="#000000"/>
						<bottomPen lineWidth="0.25" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[QA COMMENTS]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement key="table 3" style="table 3" positionType="Float" x="0" y="101" width="555" height="40">
					<printWhenExpression><![CDATA[$V{QAComments}!= null && $V{QAComments}.size() >0]]></printWhenExpression>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="sampleDetails">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{QAComments})]]></dataSourceExpression>
					</datasetRun>
					<jr:columnGroup width="555">
						<jr:column width="90">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[SAMPLE TYPE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="90" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleTypeName}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleTypeName}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="60">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="60" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[COLOUR SIZE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="60" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue})+" - "+($F{sizeCode}==null?"":$F{sizeCode})," - ")," - ")]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="60" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue})+" - "+($F{sizeCode}==null?"":$F{sizeCode})," - ")," - ")]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="30">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="30" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[SUB#]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="30" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="70">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="70" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[LAST UPDATE DATE]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitDate} == null ? null : $V{currentFitDate}.format("DD/MM/YYYY")]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitDate} == null ? null : $V{currentFitDate}.format("DD/MM/YYYY")]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="83">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="83" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[QA RESULT]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="83" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitResult}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="83" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentFitResult}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="222">
							<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
								<staticText>
									<reportElement style="Default Style" x="0" y="0" width="222" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isBold="true"/>
									</textElement>
									<text><![CDATA[QA COMMENTS]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="222" height="20"/>
									<textElement verticalAlignment="Middle">
										<font size="7" isStrikeThrough="false"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentComments}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="222" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
										<printWhenExpression><![CDATA[$V{currentFitResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
									</reportElement>
									<textElement verticalAlignment="Middle">
										<font size="7"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{currentComments}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
			<frame>
				<reportElement positionType="Float" x="0" y="153" width="555" height="20" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$V{BuyerComments}!= null && $V{BuyerComments}.size()>0]]></printWhenExpression>
				</reportElement>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25" lineColor="#000000"/>
						<topPen lineWidth="0.25" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineColor="#000000"/>
						<bottomPen lineWidth="0.25" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[BUYER COMMENTS]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement key="table 3" style="table 3" positionType="Float" mode="Opaque" x="0" y="173" width="555" height="40" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$V{BuyerComments}!= null && $V{BuyerComments}.size()>0]]></printWhenExpression>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="materialDetails">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource($V{BuyerComments})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="90">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="90" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[SAMPLE TYPE]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="90" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{materialSampleTypeName}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{materialSampleTypeName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="60">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="60" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[COLOUR]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="60" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue}) ," - ")," - ")]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="60" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[StringUtils.removeEnd(StringUtils.removeStart(($F{altColorAndPattern}==null?"":$F{altColorAndPattern})+" - "+($F{colorAndPatternValue}==null?"":$F{colorAndPatternValue}) ," - ")," - ")]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="30">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="30" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[SUB#]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="30" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="70">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="70" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[LAST UPDATE DATE]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="70" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{updatedOn} == null ? null : $F{updatedOn}.format("DD/MM/YYYY")]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{updatedOn} == null ? null : $F{updatedOn}.format("DD/MM/YYYY")]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="83">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="83" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[BUYER RESULT]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="83" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{sampleResult}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="83" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{sampleResult}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="222">
						<jr:columnHeader style="table 3_CH" height="20" rowSpan="1">
							<staticText>
								<reportElement style="Default Style" x="0" y="0" width="222" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7" isBold="true"/>
								</textElement>
								<text><![CDATA[BUYER COMMENTS]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToTallestObject" x="0" y="0" width="222" height="20"/>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{instructions}]]></textFieldExpression>
							</textField>
							<textField isStretchWithOverflow="true" isBlankWhenNull="true">
								<reportElement style="Default Style" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="222" height="20" forecolor="#FFFFFF" backcolor="#FF0000">
									<printWhenExpression><![CDATA[$V{sampleResult}.equals("Rejected") && $F{isLatest}]]></printWhenExpression>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font size="7"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{instructions}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="555" height="10"/>
				<textElement/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
		<band height="247">
			<printWhenExpression><![CDATA[$V{sampleTrackerImages}!= null && $V{sampleTrackerImages}.size() >0]]></printWhenExpression>
			<break>
				<reportElement positionType="Float" x="0" y="246" width="100" height="1"/>
			</break>
			<frame>
				<reportElement positionType="Float" x="0" y="10" width="555" height="35" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[BUYER COMMENTS - SUPPORTING IMAGES]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="35" y="20" width="175" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[Description]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="210" y="20" width="345" height="15"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[Image]]></text>
				</staticText>
				<staticText>
					<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="35" height="15"/>
					<box topPadding="3" leftPadding="2">
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement/>
					<text><![CDATA[No]]></text>
				</staticText>
			</frame>
			<componentElement>
				<reportElement positionType="Float" x="0" y="45" width="555" height="202"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="supportingImages">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
   (Collection)$V{sampleTrackerImages}
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="202" width="555">
						<image hAlign="Center">
							<reportElement x="210" y="0" width="345" height="202"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{image}))]]></imageExpression>
						</image>
						<textField>
							<reportElement style="Default Style" x="0" y="0" width="35" height="202"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{internalSeqNo}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true">
							<reportElement style="Default Style" x="35" y="0" width="175" height="202"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement>
								<font size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<frame>
				<reportElement x="0" y="0" width="555" height="10"/>
			</frame>
		</band>
		<band height="652">
			<componentElement>
				<reportElement x="0" y="0" width="555" height="652" isRemoveLineWhenBlank="true"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="sampleEvaluations">
						<datasetParameter name="JASPER_DIR">
							<datasetParameterExpression><![CDATA[$P{JASPER_DIR}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="measurementUnit">
							<datasetParameterExpression><![CDATA[$V{measurementUnit}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="season">
							<datasetParameterExpression><![CDATA[$V{seasonName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="currentSpecVer">
							<datasetParameterExpression><![CDATA[$V{specVersion}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="description">
							<datasetParameterExpression><![CDATA[$V{itemDescription}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="buyer">
							<datasetParameterExpression><![CDATA[$V{buyerName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="assistant">
							<datasetParameterExpression><![CDATA[$V{assistantName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="technologist">
							<datasetParameterExpression><![CDATA[$V{TechnologistName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="trackNo">
							<datasetParameterExpression><![CDATA[$F{trackerNo}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="theme">
							<datasetParameterExpression><![CDATA[$V{custCodelist1Name}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="style">
							<datasetParameterExpression><![CDATA[$V{itemNo}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="supplier">
							<datasetParameterExpression><![CDATA[$V{vendorName}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="custHcl">
							<datasetParameterExpression><![CDATA[$V{custHcl}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PLU">
							<datasetParameterExpression><![CDATA[$V{PLU}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource((Collection)VisualReport.listSampleType($V{sampleDetail},"SMP-03"))]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="652" width="555">
						<frame>
							<reportElement x="0" y="0" width="555" height="416" isRemoveLineWhenBlank="true"/>
							<frame>
								<reportElement positionType="Float" x="0" y="214" width="555" height="202" isRemoveLineWhenBlank="true"/>
								<frame>
									<reportElement x="0" y="0" width="555" height="32" isPrintWhenDetailOverflows="true"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<staticText>
										<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<text><![CDATA[SAMPLE TYPE / ID]]></text>
									</staticText>
									<textField isStretchWithOverflow="true">
										<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<textFieldExpression><![CDATA[$F{sampleTypeName}==null? "" :$F{sampleTypeName}]]></textFieldExpression>
									</textField>
									<staticText>
										<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<text><![CDATA[COLOUR]]></text>
									</staticText>
									<staticText>
										<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<text><![CDATA[SUBMISSION]]></text>
									</staticText>
									<textField>
										<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<textFieldExpression><![CDATA[$V{colours}]]></textFieldExpression>
									</textField>
									<textField>
										<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
										<box>
											<pen lineWidth="0.25"/>
											<topPen lineWidth="0.25"/>
											<leftPen lineWidth="0.25"/>
											<bottomPen lineWidth="0.25"/>
											<rightPen lineWidth="0.25"/>
										</box>
										<textElement/>
										<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
									</textField>
								</frame>
								<frame>
									<reportElement positionType="Float" x="0" y="32" width="555" height="170"/>
									<box>
										<pen lineWidth="0.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<staticText>
										<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
										<box topPadding="1" leftPadding="1">
											<pen lineWidth="0.25" lineColor="#000000"/>
											<topPen lineWidth="0.25" lineColor="#000000"/>
											<leftPen lineWidth="0.25" lineColor="#000000"/>
											<bottomPen lineWidth="0.25" lineColor="#000000"/>
											<rightPen lineWidth="0.25" lineColor="#000000"/>
										</box>
										<textElement textAlignment="Center">
											<font isBold="true"/>
										</textElement>
										<text><![CDATA[QUALITY COMMENTS]]></text>
									</staticText>
									<componentElement>
										<reportElement mode="Transparent" x="0" y="20" width="555" height="150" isRemoveLineWhenBlank="true"/>
										<jr:list printOrder="Vertical">
											<datasetRun subDataset="QAComments">
												<datasetParameter name="currentVendorQualityComment">
													<datasetParameterExpression><![CDATA[$V{currentVendorQualityComment}]]></datasetParameterExpression>
												</datasetParameter>
												<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			null
		, 4)
	)]]></dataSourceExpression>
											</datasetRun>
											<jr:listContents height="150" width="555">
												<staticText>
													<reportElement mode="Transparent" x="0" y="0" width="100" height="150"/>
													<box>
														<pen lineWidth="0.0"/>
														<topPen lineWidth="0.0"/>
														<leftPen lineWidth="0.0"/>
														<bottomPen lineWidth="0.0"/>
														<rightPen lineWidth="0.0"/>
													</box>
													<textElement>
														<font size="7"/>
													</textElement>
													<text><![CDATA[]]></text>
												</staticText>
											</jr:listContents>
										</jr:list>
									</componentElement>
									<frame>
										<reportElement positionType="Float" x="0" y="20" width="555" height="76"/>
										<staticText>
											<reportElement style="Static_Text_Blue" stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="100" height="76" isPrintWhenDetailOverflows="true" forecolor="#000000"/>
											<box topPadding="1" leftPadding="1">
												<pen lineWidth="0.25" lineColor="#000000"/>
												<topPen lineWidth="0.25" lineColor="#000000"/>
												<leftPen lineWidth="0.25" lineColor="#000000"/>
												<bottomPen lineWidth="0.25" lineColor="#000000"/>
												<rightPen lineWidth="0.25" lineColor="#000000"/>
											</box>
											<textElement textAlignment="Left">
												<font isBold="true"/>
											</textElement>
											<text><![CDATA[QA COMMENTS]]></text>
										</staticText>
										<textField isStretchWithOverflow="true">
											<reportElement style="Default Style" stretchType="RelativeToTallestObject" mode="Opaque" x="100" y="0" width="455" height="76"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<textElement/>
											<textFieldExpression><![CDATA[$V{currentVendorQualityComment}]]></textFieldExpression>
										</textField>
									</frame>
									<frame>
										<reportElement positionType="Float" x="0" y="96" width="555" height="74">
											<printWhenExpression><![CDATA[$V{sampleTypeCode}.equals("SMP-02")]]></printWhenExpression>
										</reportElement>
										<staticText>
											<reportElement style="Static_Text_Blue" stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="100" height="74" isPrintWhenDetailOverflows="true" forecolor="#000000"/>
											<box topPadding="1" leftPadding="1">
												<pen lineWidth="0.25" lineColor="#000000"/>
												<topPen lineWidth="0.25" lineColor="#000000"/>
												<leftPen lineWidth="0.25" lineColor="#000000"/>
												<bottomPen lineWidth="0.25" lineColor="#000000"/>
												<rightPen lineWidth="0.25" lineColor="#000000"/>
											</box>
											<textElement textAlignment="Left">
												<font isBold="true"/>
											</textElement>
											<text><![CDATA[AFTER WASH COMMENTS]]></text>
										</staticText>
										<textField isStretchWithOverflow="true">
											<reportElement style="Default Style" stretchType="RelativeToTallestObject" mode="Opaque" x="100" y="0" width="455" height="74"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<textElement/>
											<textFieldExpression><![CDATA[$V{afterWashComments}]]></textFieldExpression>
										</textField>
									</frame>
								</frame>
							</frame>
							<componentElement>
								<reportElement positionType="Float" x="0" y="0" width="555" height="105" isRemoveLineWhenBlank="true"/>
								<jr:list printOrder="Vertical">
									<datasetRun subDataset="sampleMeasurementTables">
										<datasetParameter name="sizes">
											<datasetParameterExpression><![CDATA[$V{measurementSizes}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="measurementUnit">
											<datasetParameterExpression><![CDATA[$P{measurementUnit}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="custText6">
											<datasetParameterExpression><![CDATA[$F{custText6}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleTypeName">
											<datasetParameterExpression><![CDATA[$F{sampleTypeName}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleVersion">
											<datasetParameterExpression><![CDATA[$F{sampleVersion}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="colours">
											<datasetParameterExpression><![CDATA[$V{colours}]]></datasetParameterExpression>
										</datasetParameter>
										<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource((Collection)VisualReport.listFitMeasurement($V{measurementSizes},5))]]></dataSourceExpression>
									</datasetRun>
									<jr:listContents height="105" width="555">
										<frame>
											<reportElement x="0" y="30" width="555" height="55" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.0"/>
												<topPen lineWidth="0.0"/>
												<leftPen lineWidth="0.0"/>
												<bottomPen lineWidth="0.0"/>
												<rightPen lineWidth="0.0"/>
											</box>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="455" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="435" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="415" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="535" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="495" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="515" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="20" width="25" height="35"/>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<textFieldExpression><![CDATA[$P{measurementUnit}==null?"Meas.":"Meas. ("+$P{measurementUnit}+")"]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
												<box topPadding="1" leftPadding="1">
													<pen lineWidth="0.25" lineColor="#000000"/>
													<topPen lineWidth="0.25" lineColor="#000000"/>
													<leftPen lineWidth="0.25" lineColor="#000000"/>
													<bottomPen lineWidth="0.25" lineColor="#000000"/>
													<rightPen lineWidth="0.25" lineColor="#000000"/>
												</box>
												<textElement textAlignment="Center">
													<font isBold="true"/>
												</textElement>
												<text><![CDATA[SAMPLE MEASUREMENTS]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="25" y="20" width="105" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Left" verticalAlignment="Middle"/>
												<text><![CDATA[Point of Measure]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="130" y="20" width="25" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<text><![CDATA[Toler.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="215" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="195" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="175" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="295" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="275" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="255" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[(($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1)]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="355" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="20" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="335" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="375" y="35" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
										</frame>
										<componentElement>
											<reportElement key="table" style="table" x="0" y="85" width="235" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index})))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
																<paragraph tabStopWidth="10"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="105">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="105" height="18"/>
															<textElement textAlignment="Left" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{tolerancePositive}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 1" style="table" x="235" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+1)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 2" style="table" x="315" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+2)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 5" style="table" x="395" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+3)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 6" style="table" x="475" y="85" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	VisualReport.makeListToBeShownNice(
			(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+4)))
				.getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
		, 31)
	)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($V{qaVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{qaMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$V{qaVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<frame>
											<reportElement x="0" y="0" width="555" height="30" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SAMPLE TYPE / ID]]></text>
											</staticText>
											<textField isStretchWithOverflow="true">
												<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleTypeName}]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[COLOUR]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SUBMISSION]]></text>
											</staticText>
											<textField>
												<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{colours}]]></textFieldExpression>
											</textField>
											<textField>
												<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleVersion}]]></textFieldExpression>
											</textField>
										</frame>
									</jr:listContents>
								</jr:list>
							</componentElement>
							<componentElement>
								<reportElement positionType="Float" x="0" y="105" width="555" height="108" isRemoveLineWhenBlank="true">
									<printWhenExpression><![CDATA[$V{sampleTypeCode}.equals("SMP-02")]]></printWhenExpression>
								</reportElement>
								<jr:list printOrder="Vertical">
									<datasetRun subDataset="sampleMeasurementTables">
										<datasetParameter name="sizes">
											<datasetParameterExpression><![CDATA[$V{measurementSizes}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="measurementUnit">
											<datasetParameterExpression><![CDATA[$P{measurementUnit}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="custText6">
											<datasetParameterExpression><![CDATA[$F{custText6}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleTypeName">
											<datasetParameterExpression><![CDATA[$F{sampleTypeName}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="sampleVersion">
											<datasetParameterExpression><![CDATA[$F{sampleVersion}]]></datasetParameterExpression>
										</datasetParameter>
										<datasetParameter name="colours">
											<datasetParameterExpression><![CDATA[$V{colours}]]></datasetParameterExpression>
										</datasetParameter>
										<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource((Collection)VisualReport.listFitMeasurement($V{measurementSizes},5))]]></dataSourceExpression>
									</datasetRun>
									<jr:listContents height="108" width="555">
										<frame>
											<reportElement x="0" y="25" width="555" height="83" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.0"/>
												<topPen lineWidth="0.0"/>
												<leftPen lineWidth="0.0"/>
												<bottomPen lineWidth="0.0"/>
												<rightPen lineWidth="0.0"/>
											</box>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="30" width="25" height="35"/>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<textFieldExpression><![CDATA[$P{measurementUnit}==null?"Meas.":"Meas. ("+$P{measurementUnit}+")"]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="10" width="555" height="20" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
												<box topPadding="1" leftPadding="1">
													<pen lineWidth="0.25" lineColor="#000000"/>
													<topPen lineWidth="0.25" lineColor="#000000"/>
													<leftPen lineWidth="0.25" lineColor="#000000"/>
													<bottomPen lineWidth="0.25" lineColor="#000000"/>
													<rightPen lineWidth="0.25" lineColor="#000000"/>
												</box>
												<textElement textAlignment="Center">
													<font isBold="true"/>
												</textElement>
												<text><![CDATA[SAMPLE MEASUREMENTS (AFTER WASH)]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="25" y="30" width="105" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<text><![CDATA[Point of Measure]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="130" y="30" width="25" height="35"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<text><![CDATA[Toler.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="215" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="195" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="175" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="155" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index})).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="295" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="275" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="255" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="235" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[(($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1)]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+1)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="355" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+2)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="315" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="335" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="375" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="455" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="435" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="395" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode") + (((DynamicEntity)$P{sizes}.get($F{index}+3)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="415" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="535" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[Revis. Meas.]]></text>
											</staticText>
											<textField>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="30" width="80" height="15">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle"/>
												<textFieldExpression><![CDATA[((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode")+ (((DynamicEntity)$P{sizes}.get($F{index}+4)).getString("sizeCode").equals($P{custText6})?"*":"")]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="475" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[POM Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="495" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Meas.]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="515" y="45" width="20" height="20">
													<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
												</reportElement>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement textAlignment="Center" verticalAlignment="Middle">
													<font size="6"/>
												</textElement>
												<text><![CDATA[QA Variance]]></text>
											</staticText>
										</frame>
										<componentElement>
											<reportElement key="table" style="table" x="0" y="90" width="235" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>0 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
																<paragraph tabStopWidth="10"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="105">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box leftPadding="0"/>
														<textField isBlankWhenNull="true">
															<reportElement x="0" y="0" width="105" height="18"/>
															<box leftPadding="1"/>
															<textElement textAlignment="Left" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="25">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="25" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{tolerancePositive}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 1" style="table" x="235" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>1 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+1))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<box>
															<pen lineWidth="0.25"/>
															<topPen lineWidth="0.25"/>
															<leftPen lineWidth="0.25"/>
															<bottomPen lineWidth="0.25"/>
															<rightPen lineWidth="0.25"/>
														</box>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 2" style="table" x="315" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>2 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+2))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 5" style="table" x="395" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>3 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+3))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<componentElement>
											<reportElement key="table 6" style="table" x="475" y="90" width="80" height="18">
												<printWhenExpression><![CDATA[($F{index}/5 +1) == $F{pages}? $F{number}>4 :1==1]]></printWhenExpression>
											</reportElement>
											<jr:table>
												<datasetRun subDataset="evalMeasurementFits">
													<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
  VisualReport.makeListToBeShownNice(
	(Collection)Util.findEntities(Util.sortListByLongFields((List<DynamicEntity>)Util.loadFullEntity(((DynamicEntity)$P{sizes}.get($F{index}+4))).getEntityCollection("evalMeasurementFit"),"seq"),new Find("code","<>","POM Sketch"))
	, 31)
)]]></dataSourceExpression>
												</datasetRun>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{sampleMeasurement}==null? $F{revisedMeasurement}:$F{sampleMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement mode="Opaque" x="0" y="0" width="20" height="18" forecolor="#FFFFFF" backcolor="#FF0000">
																<printWhenExpression><![CDATA[Math.abs($F{vendorVariance}.doubleValue()) > $F{tolerancePositive}.doubleValue()]]></printWhenExpression>
															</reportElement>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{vendorVariance}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
												<jr:column width="20">
													<jr:detailCell style="table_TD" height="18" rowSpan="1">
														<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
															<reportElement x="0" y="0" width="20" height="18"/>
															<textElement textAlignment="Center" verticalAlignment="Middle">
																<font size="6"/>
															</textElement>
															<textFieldExpression><![CDATA[$F{curRevisedMeasurement}]]></textFieldExpression>
														</textField>
													</jr:detailCell>
												</jr:column>
											</jr:table>
										</componentElement>
										<frame>
											<reportElement x="0" y="0" width="555" height="25" isPrintWhenDetailOverflows="true"/>
											<box>
												<pen lineWidth="0.25"/>
												<topPen lineWidth="0.25"/>
												<leftPen lineWidth="0.25"/>
												<bottomPen lineWidth="0.25"/>
												<rightPen lineWidth="0.25"/>
											</box>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SAMPLE TYPE / ID]]></text>
											</staticText>
											<textField isStretchWithOverflow="true">
												<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleTypeName}]]></textFieldExpression>
											</textField>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[COLOUR]]></text>
											</staticText>
											<staticText>
												<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[SUBMISSION]]></text>
											</staticText>
											<textField>
												<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{colours}]]></textFieldExpression>
											</textField>
											<textField>
												<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$P{sampleVersion}]]></textFieldExpression>
											</textField>
										</frame>
									</jr:listContents>
								</jr:list>
							</componentElement>
						</frame>
						<frame>
							<reportElement positionType="Float" x="0" y="416" width="555" height="235" isRemoveLineWhenBlank="true"/>
							<box>
								<pen lineWidth="0.0"/>
								<topPen lineWidth="0.0"/>
								<leftPen lineWidth="0.0"/>
								<bottomPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
							<staticText>
								<reportElement style="Frame_Header_style" mode="Opaque" x="0" y="29" width="555" height="20" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
								<box topPadding="1" leftPadding="1">
									<pen lineWidth="0.25" lineColor="#000000"/>
									<topPen lineWidth="0.25" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Center">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[QA COMMENTS - SUPPORTING IMAGES]]></text>
							</staticText>
							<frame>
								<reportElement x="0" y="0" width="555" height="29" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
								<box>
									<pen lineWidth="0.25"/>
									<topPen lineWidth="0.25"/>
									<leftPen lineWidth="0.25"/>
									<bottomPen lineWidth="0.25"/>
									<rightPen lineWidth="0.25"/>
								</box>
								<staticText>
									<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="70" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<text><![CDATA[SAMPLE TYPE / ID]]></text>
								</staticText>
								<textField isStretchWithOverflow="true">
									<reportElement style="Default Style" stretchType="RelativeToBandHeight" x="70" y="0" width="115" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<textFieldExpression><![CDATA[$F{sampleTypeName}==null? "" :$F{sampleTypeName}]]></textFieldExpression>
								</textField>
								<staticText>
									<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="185" y="0" width="70" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<text><![CDATA[COLOUR]]></text>
								</staticText>
								<staticText>
									<reportElement style="Static_Text_Blue" stretchType="RelativeToBandHeight" mode="Opaque" x="370" y="0" width="70" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<text><![CDATA[SUBMISSION]]></text>
								</staticText>
								<textField>
									<reportElement style="Default Style" x="255" y="0" width="115" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<textFieldExpression><![CDATA[$V{colours}]]></textFieldExpression>
								</textField>
								<textField>
									<reportElement style="Default Style" x="440" y="0" width="115" height="25"/>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.25"/>
										<leftPen lineWidth="0.25"/>
										<bottomPen lineWidth="0.25"/>
										<rightPen lineWidth="0.25"/>
									</box>
									<textElement/>
									<textFieldExpression><![CDATA[$F{sampleVersion}]]></textFieldExpression>
								</textField>
							</frame>
							<componentElement>
								<reportElement key="table 1" style="table 1" x="0" y="49" width="555" height="186" isRemoveLineWhenBlank="true"/>
								<jr:table>
									<datasetRun subDataset="sampleEvaluationImages">
										<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
   VisualReport.makeListToBeShownNice($V{sampleEvaluationImages},1)
	)]]></dataSourceExpression>
									</datasetRun>
									<jr:column width="20">
										<jr:columnHeader height="15" rowSpan="1">
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="20" height="15"/>
												<box topPadding="3" leftPadding="2">
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[No]]></text>
											</staticText>
										</jr:columnHeader>
										<jr:detailCell style="table 1_TD" height="170" rowSpan="1">
											<textField>
												<reportElement style="Default Style" x="0" y="0" width="20" height="170"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$F{internalSeqNo}]]></textFieldExpression>
											</textField>
										</jr:detailCell>
									</jr:column>
									<jr:column width="200">
										<jr:columnHeader height="15" rowSpan="1">
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="200" height="15"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[Description]]></text>
											</staticText>
										</jr:columnHeader>
										<jr:detailCell style="table 1_TD" height="170" rowSpan="1">
											<textField isStretchWithOverflow="true">
												<reportElement style="Default Style" x="0" y="0" width="200" height="170"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
											</textField>
										</jr:detailCell>
									</jr:column>
									<jr:column width="335">
										<jr:columnHeader height="15" rowSpan="1">
											<staticText>
												<reportElement style="Static_Text_Blue" mode="Opaque" x="0" y="0" width="335" height="15"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<textElement/>
												<text><![CDATA[Image]]></text>
											</staticText>
										</jr:columnHeader>
										<jr:detailCell style="table 1_TD" height="170" rowSpan="1">
											<image hAlign="Center">
												<reportElement x="0" y="0" width="335" height="170"/>
												<box>
													<pen lineWidth="0.25"/>
													<topPen lineWidth="0.25"/>
													<leftPen lineWidth="0.25"/>
													<bottomPen lineWidth="0.25"/>
													<rightPen lineWidth="0.25"/>
												</box>
												<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage($F{image}))]]></imageExpression>
											</image>
										</jr:detailCell>
									</jr:column>
								</jr:table>
							</componentElement>
						</frame>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<break>
				<reportElement positionType="Float" x="0" y="651" width="100" height="1"/>
			</break>
		</band>
		<band height="652">
			<frame>
				<reportElement positionType="Float" x="0" y="1" width="555" height="28"/>
				<staticText>
					<reportElement style="page_header_green" positionType="Float" x="0" y="4" width="555" height="24" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[SIZE SPEC]]></text>
				</staticText>
			</frame>
			<image hAlign="Center" onErrorType="Blank">
				<reportElement positionType="Float" mode="Opaque" x="0" y="30" width="555" height="300">
					<printWhenExpression><![CDATA[$V{specMeasurementImages}.size()>0]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage((DynamicEntity)((Map<String, Object>)$V{specMeasurementImages}.get(0)).get("field1")))]]></imageExpression>
			</image>
			<image hAlign="Center" onErrorType="Blank">
				<reportElement positionType="Float" mode="Opaque" x="0" y="342" width="555" height="300">
					<printWhenExpression><![CDATA[$V{specMeasurementImages}.size()>1]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.loadImage((byte[]) com.core.cbx.util.PrintUtil.getMarkupImage((DynamicEntity)((Map<String, Object>)$V{specMeasurementImages}.get(1)).get("field1")))]]></imageExpression>
			</image>
		</band>
		<band height="90">
			<frame>
				<reportElement x="0" y="0" width="555" height="30" isPrintWhenDetailOverflows="true"/>
				<staticText>
					<reportElement style="page_header_green" x="0" y="10" width="555" height="20" forecolor="#FFFFFF"/>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="0.25"/>
						<leftPen lineWidth="0.25"/>
						<bottomPen lineWidth="0.25"/>
						<rightPen lineWidth="0.25"/>
					</box>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[POM - MEASUREMENT]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement style="Default Style" mode="Opaque" x="34" y="30" width="126" height="40" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Point of Measure]]></text>
			</staticText>
			<textField>
				<reportElement style="Default Style" mode="Opaque" x="0" y="30" width="34" height="40" isPrintWhenDetailOverflows="true"/>
				<box topPadding="2" leftPadding="4">
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{measurementUnit}==null?"Measurement":"Measurement ("+$V{measurementUnit}+")"]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement style="listStyle" x="160" y="30" width="395" height="20" isPrintWhenDetailOverflows="true"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeaturementSize_1">
						<datasetParameter name="numberOfSize">
							<datasetParameterExpression><![CDATA[$V{itemSize} == null ? 0 : Pom.getNumberOfActiveSize($V{itemSize})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="sample_size_name">
							<datasetParameterExpression><![CDATA[$V{sample_size_name}]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
	Pom.getMeasurementHeader($V{itemSize},
    $V{sample_size_id},
    true)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="395">
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="0" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="28" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="56" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="84" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="112" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="140" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="168" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="196" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="224" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="252" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="280" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="308" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="336" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 11]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field13}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement style="Default Style" x="364" y="0" width="31" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 12]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field14}]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="28" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(1) && "Tolerances".equals($F{field2})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="56" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(2) && "Tolerances".equals($F{field3})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="84" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(3) && "Tolerances".equals($F{field4})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="112" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(4) && "Tolerances".equals($F{field5})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(5) && "Tolerances".equals($F{field6})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="168" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(6) && "Tolerances".equals($F{field7})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="196" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(7) && "Tolerances".equals($F{field8})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="224" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(8) && "Tolerances".equals($F{field9})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="252" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(9) && "Tolerances".equals($F{field10})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(10) && "Tolerances".equals($F{field11})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="308" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(11) && "Tolerances".equals($F{field12})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="336" y="0" width="59" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(12) && "Tolerances".equals($F{field13})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<staticText>
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="56" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize}.equals(0) && "Tolerances".equals($F{field1})]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="8"/>
							</textElement>
							<text><![CDATA[Tolerances]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(1)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="28" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(2)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="56" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(3)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="84" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(4)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="112" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(5)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(6)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="168" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(7)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="196" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(8)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="224" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(9)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="252" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(10)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(11)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}+"*"]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="309" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(12)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isBold="true" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}+"*"]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<componentElement>
				<reportElement style="listStyle" positionType="Float" x="160" y="50" width="395" height="20"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeaturementSize">
						<datasetParameter name="numberOfSize">
							<datasetParameterExpression><![CDATA[$V{itemSize} == null ? 0 : Pom.getNumberOfActiveSize($V{itemSize})]]></datasetParameterExpression>
						</datasetParameter>
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
VisualReport.makeListToBeShownNice(
        Pom.getMeasurementDetail(
            Pom.exludeEntityByString($V{specMeasurement},"code","POM Sketch"),
            $V{fullItem}.getEntityCollection("specMeasurementSize"),
            $V{itemSize},
            $V{sample_size_id},
            true
        )
    , 28)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="395">
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="0" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="28" y="0" width="28" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="56" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="84" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="112" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="140" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="168" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="196" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="224" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="252" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="280" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="308" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="336" y="0" width="28" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 11]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field13}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Transparent" x="364" y="0" width="31" height="20">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 12]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field14}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="0" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(1)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field1}==null?"0.00":$F{field1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="28" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$F{sampleSizeColumn}.equals(2)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field2}==null?"0.00":$F{field2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="56" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=1 && $F{sampleSizeColumn}.equals(3)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field3}==null?"0.00":$F{field3}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="84" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >=2 && $F{sampleSizeColumn}.equals(4)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field4}==null?"0.00":$F{field4}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="112" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 3 && $F{sampleSizeColumn}.equals(5)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field5}==null?"0.00":$F{field5}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="140" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 4 && $F{sampleSizeColumn}.equals(6)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field6}==null?"0.00":$F{field6}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="168" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 5 && $F{sampleSizeColumn}.equals(7)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field7}==null?"0.00":$F{field7}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="196" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 6 && $F{sampleSizeColumn}.equals(8)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field8}==null?"0.00":$F{field8}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="224" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 7 && $F{sampleSizeColumn}.equals(9)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field9}==null?"0.00":$F{field9}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="252" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 8 && $F{sampleSizeColumn}.equals(10)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field10}==null?"0.00":$F{field10}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="280" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 9 && $F{sampleSizeColumn}.equals(11)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field11}==null?"0.00":$F{field11}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" pattern="###0.00;-###0.00" isBlankWhenNull="true">
							<reportElement style="Default Style" mode="Opaque" x="308" y="0" width="28" height="20" backcolor="#99CCFF">
								<printWhenExpression><![CDATA[$P{numberOfSize} >= 10 && $F{sampleSizeColumn}.equals(12)]]></printWhenExpression>
							</reportElement>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="7" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{field12}==null?"0.00":$F{field12}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<componentElement>
				<reportElement x="0" y="70" width="160" height="20"/>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="specMeasurement">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRMapCollectionDataSource(
      VisualReport.makeListToBeShownNice(
         Pom.exludeEntityByString($V{specMeasurement},"code","POM Sketch")
        ,28)
)]]></dataSourceExpression>
					</datasetRun>
					<jr:listContents height="20" width="160">
						<textField>
							<reportElement style="Default Style" x="0" y="0" width="34" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font size="6"/>
								<paragraph tabStopWidth="10"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement style="Default Style" x="34" y="0" width="126" height="20"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
							<textElement textAlignment="Left" verticalAlignment="Middle">
								<font size="6"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
		</band>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement style="page_header_green" mode="Opaque" x="0" y="0" width="555" height="20" forecolor="#FFFFFF"/>
				<box>
					<pen lineWidth="0.25"/>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<staticText>
					<reportElement style="Default Style" x="367" y="0" width="68" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Date Printed:]]></text>
				</staticText>
				<textField>
					<reportElement style="Default Style" x="40" y="0" width="20" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement style="Default Style" x="80" y="0" width="29" height="17" forecolor="#FFFFFF"/>
					<textElement>
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
					<reportElement style="Default Style" x="437" y="0" width="112" height="17" forecolor="#FFFFFF"/>
					<textElement verticalAlignment="Top">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{printDate}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="Default Style" x="0" y="0" width="40" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Page]]></text>
				</staticText>
				<staticText>
					<reportElement style="Default Style" x="60" y="0" width="20" height="17" forecolor="#FFFFFF"/>
					<textElement textAlignment="Center">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[of]]></text>
				</staticText>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
