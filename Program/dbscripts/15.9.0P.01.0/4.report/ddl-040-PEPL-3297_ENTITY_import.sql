--// FILE: DDL_ENTITY_import.sql

ALTER TABLE CNT_IMPORT DROP COLUMN IMPORT_TABLE;
ALTER TABLE CNT_IMPORT DROP COLUMN IMPORT_TABLE_DISPLAY;
ALTER TABLE CNT_IMPORT_MAPPING ALTER COLUMN EXCEL_LINE TYPE VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF ADD COLUMN IF NOT EXISTS FIELD_MAPPING_ENTITY_NAME VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF ADD COLUMN IF NOT EXISTS FIELD_MAPPING_SECTION_ID VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF ADD COLUMN IF NOT EXISTS TABLE_ENTITY_NAME VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF ADD COLUMN IF NOT EXISTS TABLE_ENTITY_NAME_DISPLAY VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF ADD COLUMN IF NOT EXISTS TABLE_ACTION VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL ADD COLUMN IF NOT EXISTS FIELD_MAPPING_ENTITY_NAME VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL ADD COLUMN IF NOT EXISTS FIELD_MAPPING_SECTION_ID VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL ADD COLUMN IF NOT EXISTS TABLE_ENTITY_NAME VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL ADD COLUMN IF NOT EXISTS TABLE_ENTITY_NAME_DISPLAY VARCHAR(400);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL ADD COLUMN IF NOT EXISTS TABLE_ACTION VARCHAR(400);
