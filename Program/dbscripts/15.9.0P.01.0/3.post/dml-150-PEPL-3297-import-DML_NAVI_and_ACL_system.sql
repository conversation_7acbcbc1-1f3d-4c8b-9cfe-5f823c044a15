
DO $BODY$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';

BEGIN

--// FILE: DML_NAVI.sql

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'Cnt_navi_entry') AND TARGET_ID IN (SELECT ID FROM cnt_navi_entry WHERE DOMAIN_ID = DOMAINID);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'Cnt_navi_module') AND TARGET_ID IN (SELECT ID FROM cnt_navi_module WHERE DOMAIN_ID = DOMAINID);

INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', DOMAINID, DOMAINID, NULL, '1', 'lbl.dashboard.module.import', '1', '0', 'import', TO_TIMESTAMP('2025-07-06 14:58:14', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.import', 'system', 'import', '1', 10  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'import');

INSERT INTO cnt_navi_entry(REVISION, navi_Module_Id, IS_FOR_REFERENCE, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, view_Name, internal_Seq_No, show_In_Dashboard, NAVI_ENTRY_ID, type, entry_Group, dashboard_Label, dashboard_Seq_No, ENTITY_VERSION, is_Default, action, LABEL, id, IS_LATEST, SEQ) SELECT '0', 'product', '0', DOMAINID, DOMAINID, NULL, 'itemImportedView', '12', '0', 'itemImported', 'Entry', 'itemGroup', 'lbl.dashboard.entry.itemImported', '0', '1', '0', NULL, 'lbl.navi.entry.itemImported', LOWER(SYS_GUID()), '1', 80  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_entry WHERE NAVI_ENTRY_ID = 'itemImported' AND DOMAIN_ID = DOMAINID);

INSERT INTO cnt_navi_entry(REVISION, navi_Module_Id, IS_FOR_REFERENCE, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, view_Name, internal_Seq_No, show_In_Dashboard, NAVI_ENTRY_ID, type, entry_Group, dashboard_Label, dashboard_Seq_No, ENTITY_VERSION, is_Default, action, LABEL, id, IS_LATEST, SEQ) SELECT '0', 'import', '0', DOMAINID, DOMAINID, NULL, NULL, '1', '0', 'importGroup', 'EntryGroup', NULL, 'lbl.dashboard.entry.importGroup', '0', '1', '0', NULL, 'lbl.navi.entry.importGroup', LOWER(SYS_GUID()), '1', 795  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_entry WHERE NAVI_ENTRY_ID = 'importGroup' AND DOMAIN_ID = DOMAINID);

INSERT INTO cnt_navi_entry(REVISION, navi_Module_Id, IS_FOR_REFERENCE, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, view_Name, internal_Seq_No, show_In_Dashboard, NAVI_ENTRY_ID, type, entry_Group, dashboard_Label, dashboard_Seq_No, ENTITY_VERSION, is_Default, action, LABEL, id, IS_LATEST, SEQ) SELECT '0', 'import', '0', DOMAINID, DOMAINID, NULL, 'importView', '1', '0', 'import', 'Entry', 'importGroup', 'lbl.dashboard.entry.import', '0', '1', '1', NULL, 'lbl.navi.entry.import', LOWER(SYS_GUID()), '1', 796  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_entry WHERE NAVI_ENTRY_ID = 'import' AND DOMAIN_ID = DOMAINID);


--// FILE: DML_ACL_system.sql

--// ACL: system

INSERT INTO CNT_ROLE(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, REF_NO, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, DOMAIN_ID, DESCN, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import.Author', 'import.Author', 'system', 'system', TO_TIMESTAMP('2025-07-06 14:59:09', 'YYYY-MM-DD HH24:MI:SS'), DOMAINID, 'Import - Import: Author', '1', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE WHERE  NAME = 'import.Author' AND  DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ROLE')  WHERE NOT EXISTS (SELECT 1 FROM CTM_ROLE WHERE  ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));


INSERT INTO CNT_ROLE(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, REF_NO, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, DOMAIN_ID, DESCN, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import.Editor', 'import.Editor', 'system', 'system', TO_TIMESTAMP('2025-07-06 14:59:09', 'YYYY-MM-DD HH24:MI:SS'), DOMAINID, 'Import - Import: Editor', '1', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE WHERE  NAME = 'import.Editor' AND  DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ROLE')  WHERE NOT EXISTS (SELECT 1 FROM CTM_ROLE WHERE  ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));


INSERT INTO CNT_ROLE(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, REF_NO, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, DOMAIN_ID, DESCN, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import.ReadOnly', 'import.ReadOnly', 'system', 'system', TO_TIMESTAMP('2025-07-06 14:59:09', 'YYYY-MM-DD HH24:MI:SS'), DOMAINID, 'Import - Import: Read-only', '1', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE WHERE  NAME = 'import.ReadOnly' AND  DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ROLE')  WHERE NOT EXISTS (SELECT 1 FROM CTM_ROLE WHERE  ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));



DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ROLE') AND TARGET_ID IN (SELECT ID FROM CNT_ROLE WHERE ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));

UPDATE CNT_ROLE SET REVISION = REVISION+1,DESCN = 'Import - Import: Author',UPDATE_USER = 'system',UPDATE_USER_NAME = 'system',UPDATED_ON = TO_TIMESTAMP('2025-07-06 14:59:09', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ROLE') AND TARGET_ID IN (SELECT ID FROM CNT_ROLE WHERE ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));

UPDATE CNT_ROLE SET REVISION = REVISION+1,DESCN = 'Import - Import: Editor',UPDATE_USER = 'system',UPDATE_USER_NAME = 'system',UPDATED_ON = TO_TIMESTAMP('2025-07-06 14:59:09', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ROLE') AND TARGET_ID IN (SELECT ID FROM CNT_ROLE WHERE ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));

UPDATE CNT_ROLE SET REVISION = REVISION+1,DESCN = 'Import - Import: Read-only',UPDATE_USER = 'system',UPDATE_USER_NAME = 'system',UPDATED_ON = TO_TIMESTAMP('2025-07-06 14:59:09', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

END;
$BODY$ LANGUAGE PLPGSQL;

delete from cnt_serialized_entity cse where target_entity like 'Role%';

