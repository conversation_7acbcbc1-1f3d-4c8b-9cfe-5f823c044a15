--// FILE: DML_SEQ.sql

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_FACT_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FACT_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'NULL', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FACT_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CUST_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CUST_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'NULL', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CUST_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VENDOR_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VENDOR_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'NULL', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VENDOR_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CPO_CPO_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CPO_CPO_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CPO_CPO_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VPO_VPO_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VPO_VPO_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VPO_VPO_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CSO_CSO_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CSO_CSO_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CSO_CSO_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MPO_MPO_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MPO_MPO_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MPO_MPO_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_OFFER_SHEET_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_OFFER_SHEET_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_OFFER_SHEET_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_COMMITMENT_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COMMITMENT_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COMMITMENT_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_RFQ_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_RFQ_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_RFQ_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_RFI_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_RFI_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_RFI_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_RFS_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_RFS_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_RFS_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VQ_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VQ_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VQ_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SOURCING_RECORD_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SOURCING_RECORD_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SOURCING_RECORD_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_FACT_AUDIT_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FACT_AUDIT_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FACT_AUDIT_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_IC_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IC_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IC_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_QC_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_QC_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_QC_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CI_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CI_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CI_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SHIP_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIP_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIP_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SHIPMENT_ADVICE_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIPMENT_ADVICE_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIPMENT_ADVICE_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SHIPMENT_BOOKING_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIPMENT_BOOKING_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIPMENT_BOOKING_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CBX_SAMPLE_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CBX_SAMPLE_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CBX_SAMPLE_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_ITEM_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ITEM_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ITEM_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CS_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CS_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CS_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PROGRAM_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PROGRAM_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PROGRAM_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CATALOG_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CATALOG_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CATALOG_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_COMPONENT_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COMPONENT_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COMPONENT_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_FORW_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FORW_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'NULL', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FORW_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_EAR_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EAR_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EAR_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PROJECT_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PROJECT_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PROJECT_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_COLOR_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COLOR_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COLOR_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_LABEL_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LABEL_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LABEL_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SPEC_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SPEC_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SPEC_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_EXTERNAL_DOC_TITLE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EXTERNAL_DOC_TITLE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EXTERNAL_DOC_TITLE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_APRV_TEMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_APRV_TEMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_APRV_TEMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_OPC_TMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_OPC_TMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_OPC_TMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CPM_TEMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CPM_TEMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CPM_TEMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_FACT_AUDIT_TMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FACT_AUDIT_TMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FACT_AUDIT_TMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_QC_TEMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_QC_TEMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_QC_TEMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MEASURE_TMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MEASURE_TMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MEASURE_TMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SIZE_TEMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SIZE_TEMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SIZE_TEMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VENDOR_CHA_TMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VENDOR_CHA_TMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VENDOR_CHA_TMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CONDITION_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CONDITION_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CONDITION_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CPM_TASK_TMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CPM_TASK_TMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CPM_TASK_TMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CUST_FIELD_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CUST_FIELD_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CUST_FIELD_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_DATA_LIST_TYPE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_DOMAIN_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOMAIN_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOMAIN_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_EVENT_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EVENT_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EVENT_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_GROUP_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_GROUP_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_GROUP_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_HCL_TYPE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_HCL_TYPE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_HCL_TYPE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_HCL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_HCL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_HCL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_LOOKUP_BOOK_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_NOTIFICATION_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_NOTIFICATION_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_NOTIFICATION_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_ROLE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ROLE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ROLE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_USER_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_USER_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_USER_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CLAIM_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CLAIM_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CLAIM_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_LC_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LC_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LC_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VI_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VI_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VI_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_COST_TEMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COST_TEMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COST_TEMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CODE_LIST_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VPO_ACK_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VPO_ACK_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VPO_ACK_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_ACCESS_OBJECT_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ACCESS_OBJECT_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ACCESS_OBJECT_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_DEFAULT_PROFILE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DEFAULT_PROFILE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DEFAULT_PROFILE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SYSTEM_FILE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SYSTEM_FILE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SYSTEM_FILE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PACKAING_ARTWORK_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PACKAING_ARTWORK_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PACKAING_ARTWORK_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PACKAGING_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PACKAGING_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PACKAGING_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MATERIAL_ATTR_TEMPL', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_ATTR_TEMPL');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_ATTR_TEMPL';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_ARTWORK_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ARTWORK_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ARTWORK_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_COLOR_PALETTE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COLOR_PALETTE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COLOR_PALETTE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MATERIAL_PALEETE_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_PALEETE_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_PALEETE_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_ARTWORK_PALEETE_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ARTWORK_PALEETE_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ARTWORK_PALEETE_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CNT_VIEW_ADMIN_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CNT_VIEW_ADMIN_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CNT_VIEW_ADMIN_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PATTERN_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PATTERN_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PATTERN_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PRT_FORM_EXPT_TMPL_REF', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PRT_FORM_EXPT_TMPL_REF');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PRT_FORM_EXPT_TMPL_REF';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SAMPLE_REQUEST_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_REQUEST_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_REQUEST_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_TRACKER_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TRACKER_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TRACKER_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SAMPLE_REQ_TMPL_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_REQ_TMPL_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_REQ_TMPL_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MATERIAL_REQ_TMPL_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_REQ_TMPL_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_REQ_TMPL_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_DOCUMENT_REQ_TMPL_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOCUMENT_REQ_TMPL_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOCUMENT_REQ_TMPL_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SAMPLE_DETAIL_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_DETAIL_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_DETAIL_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MATERIAL_DETAIL_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_DETAIL_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MATERIAL_DETAIL_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_DOCUMENT_DETAIL_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOCUMENT_DETAIL_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOCUMENT_DETAIL_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_EVALUATION_TEMPLATE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EVALUATION_TEMPLATE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_EVALUATION_TEMPLATE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PARTY_TEMPLATE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PARTY_TEMPLATE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PARTY_TEMPLATE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SAMPLE_EVALUATION', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_EVALUATION');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SAMPLE_EVALUATION';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_ACC_REF_KEY', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ACC_REF_KEY');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_ACC_REF_KEY';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_MES_REF_KEY', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MES_REF_KEY');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_MES_REF_KEY';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_GDR_REF_KEY', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_GDR_REF_KEY');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_GDR_REF_KEY';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_INVITATION_REQUEST_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INVITATION_REQUEST_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INVITATION_REQUEST_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_INVITED_VENDORS_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INVITED_VENDORS_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INVITED_VENDORS_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_AGRM_TEMPL_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_AGRM_TEMPL_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_AGRM_TEMPL_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_CONTRACT_FILE_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CONTRACT_FILE_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_CONTRACT_FILE_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_DOMAIN_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOMAIN_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_DOMAIN_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_INSPECT_RPT_TMPL_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INSPECT_RPT_TMPL_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INSPECT_RPT_TMPL_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_IB_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IB_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IB_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_IR_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IR_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IR_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PROJECT_NEW_ITEM', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PROJECT_NEW_ITEM');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PROJECT_NEW_ITEM';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VQ2_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VQ2_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VQ2_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_QQ_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_QQ_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_QQ_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VC_REF_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VC_REF_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'D', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VC_REF_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_FC_REF_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FC_REF_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'D', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FC_REF_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PC_REF_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PC_REF_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'D', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PC_REF_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_VC_ACTIVITY_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VC_ACTIVITY_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'D', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_VC_ACTIVITY_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_FC_ACTIVITY_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FC_ACTIVITY_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'D', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_FC_ACTIVITY_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PC_ACTIVITY_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PC_ACTIVITY_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'D', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PC_ACTIVITY_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_COPTMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COPTMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_COPTMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SHIPMENT_COMPLIANCE_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIPMENT_COMPLIANCE_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHIPMENT_COMPLIANCE_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SHARE_FILE_FILE_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHARE_FILE_FILE_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SHARE_FILE_FILE_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_SPECIFICATION_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SPECIFICATION_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_SPECIFICATION_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_INGREDIENT_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INGREDIENT_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INGREDIENT_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_PACKING_LIST_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PACKING_LIST_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_PACKING_LIST_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_TEST_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TEST_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'NULL', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TEST_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_TEST_TEMPL_REF_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TEST_TEMPL_REF_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'Y', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TEST_TEMPL_REF_NO';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_TEST_ACCREDITATION_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TEST_ACCREDITATION_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_TEST_ACCREDITATION_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_REQUEST_ID', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_REQUEST_ID');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_REQUEST_ID';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_LINE_SHEET_CODE', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LINE_SHEET_CODE');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'M', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_LINE_SHEET_CODE';

INSERT INTO CNT_SEQ_DEF (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, SEQ_ID, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CYCLE_STARTED_ON, UPDATED_ON) SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_IMPORT_NO', 1, 'PEPL', '0',TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'),TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS') WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IMPORT_NO');

UPDATE CNT_SEQ_DEF SET START_WITH = 1, MAX_VALUE = 999999, INCREMENT_BY = 1, CYCLE = 'NULL', CACHE_SIZE = 0 WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_IMPORT_NO';

