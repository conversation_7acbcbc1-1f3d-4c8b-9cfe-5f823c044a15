
--Add new UI Component Rules , e.g : excel config : Role : Admin, Object = 'color' , Type='form', Condition='$ANY'  , ComponentId='ui.tabImage', accessRight ='readonly' (mean accessRight = '2'),
--<ROLE> : role name, e.g. 'ADMIN'
--<OBJ_ID>: object id, e.g. 'color'
--<OBJ_TYPE>: object type, e.g. 'form'
--<CONDITION>: condition, e.g. '$ANY'
--<ACCESS_RIGHT>: access right value, e.g. '2'
--<DOMAIN_ID>: domain id, e.g. 'QAPEGP'
--<COMPONENT_ID> : e.g : 'ui.tabImage'
--<ACTION_ID> : e.g : testAction

BEGIN;
DO $body$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
role varchar[];
type varchar[];
accessRight numeric = 1;
componentId varchar[];
formComponentId varchar[] :=array[['ui.import'],['ui.import.import'],['ui.import.importGroup']];
uiId varchar[];
objectId VARCHAR(200) := 'navi';
objectTypeArr varchar[] :=array[['system']];
roleArr varchar[] :=array[['CommonFunctionsRole']];
conditionName VARCHAR(200) :='$ANY';
adminCount numeric;
adminId varchar;
BEGIN
    --add new action begin--
    FOREACH type SLICE 1 IN ARRAY objectTypeArr
    LOOP
    
     FOREACH role SLICE 1 IN ARRAY roleArr
      LOOP
      
      IF type[1] = 'system' THEN componentId := formComponentId;
      END IF;
      
      FOREACH uiId SLICE 1 IN ARRAY componentId
       LOOP

        DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME=role[1] AND DOMAIN_ID = DOMAINID);
        ---create a new CNT_RULE_UI ,uiId=uiId[1], role_id in ADMIN role, condition in $ANY condition, accessRight = 2
        INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE)
        SELECT SYS_GUID(), 0, 1, ao.ID, r.ID, uiId[1],c.ID, d.ID, accessRight, d.ID, '0'
        FROM CNT_DOMAIN d
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = d.ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON r.NAME = role[1] AND r.DOMAIN_ID = d.ID AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION c ON c.NAME = conditionName AND c.DOMAIN_ID = d.ID AND c.IS_LATEST = '1'
        where d.ID = DOMAINID AND NOT EXISTS (SELECT 1 FROM CNT_RULE_UI WHERE DOMAIN_ID = d.ID AND UI_ID = uiId[1]
        AND ACCESS_OBJECT_ID = ao.ID AND ROLE_ID = r.id AND CONDITION_ID = c.id AND ACCESS_RIGHT= accessRight);
        
        SELECT count(*) into adminCount FROM CNT_RULE_UI_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND r.IS_LATEST = '1' AND admin.domain_id =r.domain_id
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_UI rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = admin.ACCESS_RIGHT::numeric
        inner join cnt_role_sln sln on sln.parent_id = admin.id and condition.id = sln.ref_id and sln.ref_entity ='Condition' and sln.display_value = condition.NAME
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ui_id = uiId[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.ACCESS_RIGHT = accessRight :: varchar
        AND admin.DOMAIN_ID = DOMAINID;
        
        IF adminCount != 0 THEN
        
        --CNT_ROLE_SLN (componentId)
        INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE)
        SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, admin.ID, 'RuleUiAdmin', 'uiFieldId', null, null, uiId[1]
        FROM CNT_RULE_UI_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND r.IS_LATEST = '1' AND admin.domain_id =r.domain_id
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_UI rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = admin.ACCESS_RIGHT::numeric
        inner join cnt_role_sln sln on sln.parent_id = admin.id and condition.id = sln.ref_id and sln.ref_entity ='Condition' and sln.display_value = condition.NAME
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ui_id = uiId[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.ACCESS_RIGHT = accessRight :: varchar
        AND admin.DOMAIN_ID = DOMAINID
        WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE_SLN WHERE DOMAIN_ID = DOMAINID AND PARENT_ID = admin.ID AND PARENT_ENTITY= 'RuleUiAdmin' AND FIELD_ID='uiFieldId' AND DISPLAY_VALUE= uiId[1]);

        ELSE
        
        SELECT SYS_GUID() INTO adminId;
        
        INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT)
        SELECT adminId, 0, 1, d.ID, d.ID, '0', r.id, objectId,type[1] ,conditionName, ao.NAME, accessRight
        FROM CNT_DOMAIN d
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = d.DOMAIN_ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON r.NAME = role[1] AND r.DOMAIN_ID = d.ID AND r.IS_LATEST = '1'
        where d.ID = DOMAINID
        AND NOT EXISTS (SELECT 1 FROM CNT_RULE_UI_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND r.IS_LATEST = '1' AND admin.domain_id =r.domain_id
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_UI rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = admin.ACCESS_RIGHT::numeric
        inner join cnt_role_sln sln on sln.parent_id = admin.id and condition.id = sln.ref_id and sln.ref_entity ='Condition' and sln.display_value = condition.NAME
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ui_id = uiId[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.ACCESS_RIGHT = accessRight :: varchar
        AND admin.DOMAIN_ID = DOMAINID);

        ---CNT_ROLE_SLN (condition)
        INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE)
        SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, admin.ID, 'RuleUiAdmin', 'conditionId', 'Condition', condition.ID, conditionName
        FROM CNT_RULE_UI_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND r.IS_LATEST = '1' AND admin.domain_id =r.domain_id
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_UI rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = admin.ACCESS_RIGHT::numeric
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ui_id = uiId[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.ACCESS_RIGHT = accessRight :: varchar
        AND admin.DOMAIN_ID = DOMAINID
        and admin.id = adminId
        WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE_SLN WHERE DOMAIN_ID = DOMAINID AND PARENT_ID = admin.ID AND PARENT_ENTITY ='RuleUiAdmin' AND FIELD_ID = 'conditionId' AND REF_ENTITY='Condition'
        AND REF_ID = condition.ID AND DISPLAY_VALUE = conditionName);

        ---CTM_ROLE
        INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME)
        SELECT admin.ID, DOMAINID, 'RuleUiAdmin'
        FROM CNT_RULE_UI_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND r.IS_LATEST = '1' AND admin.domain_id =r.domain_id
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_UI rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = admin.ACCESS_RIGHT::numeric
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ui_id = uiId[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.ACCESS_RIGHT = accessRight :: varchar
        AND admin.DOMAIN_ID = DOMAINID
        and admin.id = adminId
        WHERE NOT EXISTS (SELECT 1 FROM CTM_ROLE WHERE ID = admin.ID);

        --CNT_ROLE_SLN (componentId)
        INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE)
        SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, admin.ID, 'RuleUiAdmin', 'uiFieldId', null, null, uiId[1]
        FROM CNT_RULE_UI_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND r.IS_LATEST = '1' AND admin.domain_id =r.domain_id
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_UI rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = admin.ACCESS_RIGHT::numeric
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ui_id = uiId[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.ACCESS_RIGHT = accessRight :: varchar
        AND admin.DOMAIN_ID = DOMAINID
        and admin.id = adminId
        WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE_SLN WHERE DOMAIN_ID = DOMAINID AND PARENT_ID = admin.ID AND PARENT_ENTITY= 'RuleUiAdmin' AND FIELD_ID='uiFieldId' AND DISPLAY_VALUE= uiId[1]);
        
        
        END IF;
        
        END LOOP;
     END LOOP;
    END LOOP;
    --add new action end--
     
    DELETE FROM CNT_DOMAIN_GROUP_CACHED_RULES;
    DELETE FROM CNT_CHECKSUM_CACHE_RULE;
    DELETE FROM CNT_CHECKSUM_CACHE_HCL_NODE;
    DELETE FROM CNT_CKSUM_CACHE_CLASSIFICATION;
    DELETE FROM CNT_CHECKSUM_CACHE_DATA;
    DELETE FROM CNT_USER_CACHE_RULE;
    DELETE FROM CNT_USER_CACHE_HCL_NODE;
    DELETE FROM CNT_USER_CACHE_CLASSIFICATION;
    DELETE FROM CNT_USER_CACHE_DATA;
END;
$body$ LANGUAGE PLPGSQL;

COMMIT;

