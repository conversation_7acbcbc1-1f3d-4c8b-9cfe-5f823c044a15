--// FILE: DML_ACL_import_view.sql

--// ACL: import_view

INSERT INTO CNT_ACCESS_OBJECT(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, OBJECT_ID, REF_NO, OBJECT_TYPE, OBJECT_VERSION, DOMAIN_ID, DESCN, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import', 'system', 'system', TO_TIMESTAMP('2025-07-30 15:14:56', 'YYYY-MM-DD HH24:MI:SS'), 'import', 'view' || ':' || 'import', 'view', 1, 'RD2', NULL, '1', 'RD2', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ACCESS_OBJECT') AND TARGET_ID IN (SELECT ID FROM CNT_ACCESS_OBJECT WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'));

UPDATE CNT_ACCESS_OBJECT SET REVISION = REVISION+1,ENTITY_VERSION = 1,DOC_STATUS = 'active',NAME = 'import',OBJECT_ID = 'import',REF_NO = 'view' || ':' || 'import',OBJECT_TYPE = 'view',OBJECT_VERSION = 1,DOMAIN_ID = 'RD2',DESCN = NULL,IS_LATEST = '1',CREATE_USER = 'system',CREATE_USER_NAME = 'system',CREATED_ON = TO_TIMESTAMP('2025-07-30 15:14:56', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');





DELETE FROM CNT_ACCESS_OBJECT_ACTION WHERE DOMAIN_ID = 'RD2' AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');





DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '0d52bec627384a419a5dd02ec6e6d767', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '0d52bec627384a419a5dd02ec6e6d767', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0d52bec627384a419a5dd02ec6e6d767', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0d52bec627384a419a5dd02ec6e6d767', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0d52bec627384a419a5dd02ec6e6d767', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0d52bec627384a419a5dd02ec6e6d767', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0d52bec627384a419a5dd02ec6e6d767', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0d52bec627384a419a5dd02ec6e6d767', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'ee5ab8d9ff5b4626bff4913d00f0c283', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'ee5ab8d9ff5b4626bff4913d00f0c283', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '5e27b1e61e1045ffa5430a99a9f61298', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '5e27b1e61e1045ffa5430a99a9f61298', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5e27b1e61e1045ffa5430a99a9f61298', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5e27b1e61e1045ffa5430a99a9f61298', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5e27b1e61e1045ffa5430a99a9f61298', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5e27b1e61e1045ffa5430a99a9f61298', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5e27b1e61e1045ffa5430a99a9f61298', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5e27b1e61e1045ffa5430a99a9f61298', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'b36666918f654fd6bde267d901edf008', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'b36666918f654fd6bde267d901edf008', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b36666918f654fd6bde267d901edf008', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b36666918f654fd6bde267d901edf008', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b36666918f654fd6bde267d901edf008', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b36666918f654fd6bde267d901edf008', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b36666918f654fd6bde267d901edf008', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b36666918f654fd6bde267d901edf008', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '6b51769449aa43f9863c94dfa2627cd9', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '6b51769449aa43f9863c94dfa2627cd9', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6b51769449aa43f9863c94dfa2627cd9', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6b51769449aa43f9863c94dfa2627cd9', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6b51769449aa43f9863c94dfa2627cd9', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6b51769449aa43f9863c94dfa2627cd9', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6b51769449aa43f9863c94dfa2627cd9', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6b51769449aa43f9863c94dfa2627cd9', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';











