--// FILE: DML_ENTITY_import.sql

DELETE FROM CNT_FIELD_DEFINITION WHERE PARENT_ID IN (SELECT ID FROM CNT_ENTITY_DEFINITION WHERE MODULE = 'import' AND ENTITY_VERSION = 1);
DELETE FROM CNT_ENTITY_DEFINITION WHERE MODULE = 'import' AND ENTITY_VERSION = 1;
DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = ('Import');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('52c9f2a83ea740f7920171459f468a87', 0, 'Import', 1, 'CNT_IMPORT', 'CNT_IMPORT_M', 'CTM_IMPORT', 'IMPORT', 'import', 'main', 'ALL', 'ALL', 'ImportHistory', '${importNo}', '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f8e3b45e62164ef19dfd8c9dec801a1d', 0, '52c9f2a83ea740f7920171459f468a87', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('83404c2e82804de0a84167b8cb6a08c8', 0, '52c9f2a83ea740f7920171459f468a87', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('eca5f35c02264eb6b4b42b8c476f86fe', 0, '52c9f2a83ea740f7920171459f468a87', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('46d24fdd237346a3bf672692bb72cf61', 0, '52c9f2a83ea740f7920171459f468a87', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f47daca369414317a82725f254d1457c', 0, '52c9f2a83ea740f7920171459f468a87', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('56ce97e58be24a3cba8a99d75118e197', 0, '52c9f2a83ea740f7920171459f468a87', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a367af93d8ed42ed8b9a4e31048f1950', 0, '52c9f2a83ea740f7920171459f468a87', 'version', 'VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.version', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c43926cf58d34a1ebe16d841fbdb5f52', 0, '52c9f2a83ea740f7920171459f468a87', 'status', 'STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.status', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ec2f93d8d1fd4c73b703858134e0b0fe', 0, '52c9f2a83ea740f7920171459f468a87', 'docStatus', 'DOC_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.docStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('58f71605b0494eb891cfc867d8d8145b', 0, '52c9f2a83ea740f7920171459f468a87', 'editingStatus', 'EDITING_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.editingStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('742ed7df54414952901560b8697069da', 0, '52c9f2a83ea740f7920171459f468a87', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dda6b8e26c59499fa2ba8caf0fe61827', 0, '52c9f2a83ea740f7920171459f468a87', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cf9c8086bcca4a17a472a23214303145', 0, '52c9f2a83ea740f7920171459f468a87', 'updateUser', 'UPDATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.updateUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0633e32445a04c919e3ee616878dff3e', 0, '52c9f2a83ea740f7920171459f468a87', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.updateUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('214745857cbd411c888eb8989cd16e6a', 0, '52c9f2a83ea740f7920171459f468a87', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('afb3cd3296c842758cc4527bc02d5318', 0, '52c9f2a83ea740f7920171459f468a87', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.updatedOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d510775323484d6daa4ad1f8bdca7acb', 0, '52c9f2a83ea740f7920171459f468a87', 'integrationSource', 'INTEGRATION_SOURCE', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.integrationSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('80bf2f8963314bed8b389106534bed93', 0, '52c9f2a83ea740f7920171459f468a87', 'integrationStatus', 'INTEGRATION_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.integrationStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e5e78a39c89b4431942c8320f0e03e19', 0, '52c9f2a83ea740f7920171459f468a87', 'integrationNote', 'INTEGRATION_NOTE', 'VARCHAR(1000)', NULL, '2', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.integrationNote', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1d715836a1254cae9269681f875835cd', 0, '52c9f2a83ea740f7920171459f468a87', 'isCpmInitialized', 'IS_CPM_INITIALIZED', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.isCpmInitialized', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1886260bb8fd404380c917e8184f1158', 0, '52c9f2a83ea740f7920171459f468a87', 'isLatest', 'IS_LATEST', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.isLatest', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7777155931bd44908d08af6e8d40628e', 0, '52c9f2a83ea740f7920171459f468a87', 'importNo', 'IMPORT_NO', 'VARCHAR(400)', 'IMPORT_NO', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.importNo', NULL, NULL, NULL, NULL, 'refNo', NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5cca661b398a47fc9c8220822f19dd4e', 0, '52c9f2a83ea740f7920171459f468a87', 'headerRow', 'HEADER_ROW', 'NUMERIC(20, 0)', 'HEADER_ROW', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.headerRow', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cecb03fa643f4a01a0c35ee86983d2f3', 0, '52c9f2a83ea740f7920171459f468a87', 'startRow', 'START_ROW', 'NUMERIC(20, 0)', 'START_ROW', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.startRow', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6d0c3aabe1294f86bb9527c58f3be3ab', 0, '52c9f2a83ea740f7920171459f468a87', 'fileId', 'FILE_ID', 'VARCHAR(32)', 'ATTACHMENT_SID', '0', 'entity', 'Attachment', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.fileId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8ad3d14be74c45d9860e6aea3c6d27e2', 0, '52c9f2a83ea740f7920171459f468a87', 'importModule', 'IMPORT_MODULE', 'VARCHAR(400)', 'IMPORT_MODULE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.importModule', 'codelist', 'IMPORT_MODULE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ef7d8e69e45f4f1fa5cad59b16a501d2', 0, '52c9f2a83ea740f7920171459f468a87', 'importKey', 'IMPORT_KEY', 'VARCHAR(400)', 'IMPORT_KEY', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '26', '0', 'entity.importKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('88931e3bb32a43aea1424d06b1d2c90f', 0, '52c9f2a83ea740f7920171459f468a87', 'importKeyDisplay', 'IMPORT_KEY_DISPLAY', 'VARCHAR(400)', 'IMPORT_KEY_DISPLAY', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '27', '0', 'entity.importKeyDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('83749fc4742a47a3854e782e49eae4c7', 0, '52c9f2a83ea740f7920171459f468a87', 'checksum', 'CHECKSUM', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '28', '0', 'entity.checksum', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('06c6d41ccf934b8480bf885934579cc4', 0, '52c9f2a83ea740f7920171459f468a87', 'importMappings', 'IMPORT_MAPPINGS', NULL, NULL, '0', 'collection', 'ImportMapping', 'importId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '29', '0', 'entity.importMappings', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('03fdf81006084dbab50d6c9c2b8d5e6c', 0, '52c9f2a83ea740f7920171459f468a87', 'refNo', 'REF_NO', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, 'com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_IMPORT_NO","system.pattern.ImportNo","IMPT#{Date:YYMM}-#{Seq:6}")', NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '30', '0', 'entity.refNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c15876ff81d24e48b7ee0967eb3a778a', 0, '52c9f2a83ea740f7920171459f468a87', 'businessRefNo', 'BUSINESS_REF_NO', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '31', '0', 'entity.businessRefNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('626470d50b9a450782e938767ae101c6', 0, '52c9f2a83ea740f7920171459f468a87', 'partyTemplateRef', 'PARTY_TEMPLATE_REF', 'VARCHAR(400)', NULL, '3', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '32', '0', 'entity.partyTemplateRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3ec4c64621f041f3b96e226e8b69c4c9', 0, '52c9f2a83ea740f7920171459f468a87', 'partyTemplateVer', 'PARTY_TEMPLATE_VER', 'NUMERIC(20, 0)', NULL, '3', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '33', '0', 'entity.partyTemplateVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a0ab8ee866b94b59bc9df39f3b6557a4', 0, '52c9f2a83ea740f7920171459f468a87', 'partyTemplateName', 'PARTY_TEMPLATE_NAME', 'VARCHAR(400)', NULL, '3', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '34', '0', 'entity.partyTemplateName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('74bb90f5ae1d414393c4b2f8b9e673d7', 0, '52c9f2a83ea740f7920171459f468a87', 'partyName1', 'PARTY_NAME1', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '35', '0', 'entity.partyName1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6fc1f8c7ffb745a1a8b9035d89a8053d', 0, '52c9f2a83ea740f7920171459f468a87', 'partyName2', 'PARTY_NAME2', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '36', '0', 'entity.partyName2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1e771604c6c04789ae1fba062c373e68', 0, '52c9f2a83ea740f7920171459f468a87', 'partyName3', 'PARTY_NAME3', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '37', '0', 'entity.partyName3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3ef5b8f6d0ad4d2bab91ae1e69406dd5', 0, '52c9f2a83ea740f7920171459f468a87', 'partyName4', 'PARTY_NAME4', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '38', '0', 'entity.partyName4', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('72d0801949eb41a7b5944f281885a04f', 0, '52c9f2a83ea740f7920171459f468a87', 'partyName5', 'PARTY_NAME5', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '39', '0', 'entity.partyName5', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bacc0ee772734a47b74b9b853862f211', 0, '52c9f2a83ea740f7920171459f468a87', 'parties', 'PARTIES', NULL, NULL, '2', 'collection', 'ImportParty', 'docId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '40', '0', 'entity.parties', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9a3bbd7c22e14606b35ac610e5c8c9d0', 0, '52c9f2a83ea740f7920171459f468a87', 'fileRef', 'FILE_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '41', '0', 'entity.fileRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f9c3189c86244c27b70270f40e7e3268', 0, '52c9f2a83ea740f7920171459f468a87', 'fileVer', 'FILE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '42', '0', 'entity.fileVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f44e401f5c1749cd96b02abd8ffd6a73', 0, '52c9f2a83ea740f7920171459f468a87', 'fileRef2', 'FILE_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '43', '0', 'entity.fileRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bed16cdd3a6e4dfeb737bcf7125ea528', 0, '52c9f2a83ea740f7920171459f468a87', 'fileDuid', 'FILE_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '44', '0', 'entity.fileDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('87ccafcc65e948e1b6e475af6ae30d5e', 0, '52c9f2a83ea740f7920171459f468a87', 'importModuleName', 'IMPORT_MODULE_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '45', '0', 'entity.importModuleName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('47d5230d2923422cb6242a4a657c2dfc', 0, '52c9f2a83ea740f7920171459f468a87', 'importModuleVer', 'IMPORT_MODULE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '46', '0', 'entity.importModuleVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('0bf48e3c57f14a6aa33b32fc7f0ad406', 0, 'ImportSelection', 1, 'CNT_IMPORT_SLN', NULL, 'CTM_IMPORT', 'N/A', 'import', NULL, NULL, NULL, NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bf65d07deda94bde8541547783247411', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.custSelection1.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f42b59ff641b4e71839ca2949e221bb5', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.custSelection1.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1d3b7e6f81694566b265b8373914668f', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.custSelection1.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('54f4061148ee4e299d8d8d1e16d3d307', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.custSelection1.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('531c6d2c12604c48892914eea673005f', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.custSelection1.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9fcc18a343a24a1cbf96c8d0da9a006a', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.custSelection1.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0119b40e131f451ca0904899e69d3b13', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'version', 'VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.custSelection1.version', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('aef7fe88a0d548b99ba9545adee37bd7', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'status', 'STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.custSelection1.status', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5f0963dd6ce8492a9c9c98cf0d70da25', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'docStatus', 'DOC_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.custSelection1.docStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8c3ecf79a6c34631b0749cc5f165e721', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'editingStatus', 'EDITING_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.custSelection1.editingStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0b02a9bc097d4661833f3499ac521a91', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.custSelection1.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c045aadb2d424bfeae379c2ab217f8cf', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.custSelection1.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0ae9056592524b64bd7bb72a58491b02', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'updateUser', 'UPDATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.custSelection1.updateUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a4025530aaef44aebecde1052b0bd125', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.custSelection1.updateUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b9ffc5c021fa4d52ae6b11c6dd207723', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.custSelection1.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('389f183df4604f499292e35ce2527dfb', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.custSelection1.updatedOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('87282f3e1a814408a2f7aa76cc1ddef1', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'integrationSource', 'INTEGRATION_SOURCE', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.custSelection1.integrationSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6a315e561fd84f6e8d0c5d66199c8f8a', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'integrationStatus', 'INTEGRATION_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.custSelection1.integrationStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f3ae0dcd7aad477283ceb44e579df4c5', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'integrationNote', 'INTEGRATION_NOTE', 'VARCHAR(1000)', NULL, '2', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.custSelection1.integrationNote', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9c6eb0cbbd44405ab4da6f499fa26eb7', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'isCpmInitialized', 'IS_CPM_INITIALIZED', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.custSelection1.isCpmInitialized', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('93a9c5a9e8d8483c80f8e0aad5aba70b', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'isLatest', 'IS_LATEST', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.custSelection1.isLatest', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('53fb643a74ca464d855cb57a314b5d24', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'parentId', 'PARENT_ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.custSelection1.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bbdc9bb328f44c1b955d2ec46dd5ea47', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'parentEntity', 'PARENT_ENTITY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.custSelection1.parentEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('08afd336d46b491eb0d93fea80f37c4e', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'fieldId', 'FIELD_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.custSelection1.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('94cc238ba9ce44768b693a22c7ad1dd8', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'ref', 'REF_ID', 'VARCHAR(32)', NULL, '0', 'entity-dynamic', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.custSelection1.ref', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cf5286f0d1204d75af0cf24238244b96', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'refEntity', 'REF_ENTITY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.custSelection1.refEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9d769a7262924daa98a4d452346d38f8', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'displayValue', 'DISPLAY_VALUE', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '26', '0', 'entity.custSelection1.displayValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('54c94991a8e14e62a4569f1b47318321', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '27', '0', 'entity.custSelection1.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('33ed71395f0447c49b689ff69a7483c7', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'refRef', 'REF_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '28', '0', 'entity.custSelection1.refRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d3691206ab6b4a168a42016ed1a006ce', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'refVer', 'REF_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '29', '0', 'entity.custSelection1.refVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('140a7d428b504d7cba760452057990f0', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'refRef2', 'REF_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '30', '0', 'entity.custSelection1.refRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('30d21c77bd2e46bfbb58b7fdba7a1b2f', 0, '0bf48e3c57f14a6aa33b32fc7f0ad406', 'refDuid', 'REF_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '31', '0', 'entity.custSelection1.refDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('02d48660c23b4cbf9824725c22be7a3d', 0, 'ImportMapping', 1, 'CNT_IMPORT_MAPPING', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('152a190489d04a969304c6a4394a4f1d', 0, '02d48660c23b4cbf9824725c22be7a3d', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.importMappings.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('03c9a4d72d1e4b23b33dac0e7cc509dc', 0, '02d48660c23b4cbf9824725c22be7a3d', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.importMappings.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7464773470144a7bba225adfd4168427', 0, '02d48660c23b4cbf9824725c22be7a3d', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.importMappings.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('12b4c5fe8b064c408a8b1d90adbf6280', 0, '02d48660c23b4cbf9824725c22be7a3d', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.importMappings.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('aed4b2f8d4954fa4a29eb5c75ce388b6', 0, '02d48660c23b4cbf9824725c22be7a3d', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.importMappings.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0e6a3a106f084ee98c16528623fcbcbc', 0, '02d48660c23b4cbf9824725c22be7a3d', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.importMappings.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8468713d55b74c83850132f29162df15', 0, '02d48660c23b4cbf9824725c22be7a3d', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.importMappings.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('653c863faf614166b50840f5a62c17ae', 0, '02d48660c23b4cbf9824725c22be7a3d', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.importMappings.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('70671f83559f4dbe936e83d4259a76fb', 0, '02d48660c23b4cbf9824725c22be7a3d', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.importMappings.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1a94e88508c44250a280aca6c6e4a444', 0, '02d48660c23b4cbf9824725c22be7a3d', 'importId', 'IMPORT_ID', 'VARCHAR(32)', 'IMPORT_SYS_ID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '1', 'entity.importMappings.importId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b0aabd0def4e46018e2ab82f13150263', 0, '02d48660c23b4cbf9824725c22be7a3d', 'excelLine', 'EXCEL_LINE', 'VARCHAR(400)', 'ATTACHMENT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.importMappings.excelLine', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('44ffe32a07fe4a5ebd2543cb2a5de8db', 0, '02d48660c23b4cbf9824725c22be7a3d', 'importStatus', 'IMPORT_STATUS', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.importMappings.importStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d02b96e944914c99a155a1337291fa77', 0, '02d48660c23b4cbf9824725c22be7a3d', 'error', 'ERROR', 'VARCHAR(5000)', NULL, '0', 'string-xl', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 5000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.importMappings.error', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9d264f19125e4e4cbd03ef91a4014849', 0, '02d48660c23b4cbf9824725c22be7a3d', 'fieldDatas', 'FIELD_DATAS', NULL, NULL, '0', 'collection', 'ImportMappingFieldData', 'mappingId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.importMappings.fieldDatas', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('ac5319f1e76947949373f454dd916cb2', 0, 'ImportMappingFieldDef', 1, 'CNT_IMPORT_MAPPING_FIELD_DEF', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING_FIELD_DEF', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('94cf972de4974a1eb02f9d768832b92c', 0, 'ac5319f1e76947949373f454dd916cb2', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bbecd1d70f9d4fe5b2e8868bafcce164', 0, 'ac5319f1e76947949373f454dd916cb2', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e99f8c36cd2b4c599a95369928ebf146', 0, 'ac5319f1e76947949373f454dd916cb2', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0022f93bc5eb48c89380ab64455ae067', 0, 'ac5319f1e76947949373f454dd916cb2', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b94f84378d04493cb99431c4896b7f8a', 0, 'ac5319f1e76947949373f454dd916cb2', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('04acc9f6bf824b128fa62745914e5626', 0, 'ac5319f1e76947949373f454dd916cb2', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f2fedb374f8e4fe98ce4ff5b354f0d29', 0, 'ac5319f1e76947949373f454dd916cb2', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0d22f64e2d474ee2a051c6b83c51e88e', 0, 'ac5319f1e76947949373f454dd916cb2', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('da82fb1fffec4e108bdc787a1d0f4b11', 0, 'ac5319f1e76947949373f454dd916cb2', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c2fa58f3552b4ca7949224ae49f2e62a', 0, 'ac5319f1e76947949373f454dd916cb2', 'excelHeader', 'EXCEL_HEADER', 'VARCHAR(400)', 'ATTACHMENT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.excelHeader', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f45c344afaf241678ab50e845eb69bb7', 0, 'ac5319f1e76947949373f454dd916cb2', 'fieldMapping', 'FIELD_MAPPING', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.fieldMapping', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7c94cb2e7fd846b1a1f9d5c2759f3744', 0, 'ac5319f1e76947949373f454dd916cb2', 'fieldMappingDisplay', 'FIELD_MAPPING_DISPLAY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.fieldMappingDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a1307a7ace0240d1bfe4c1a8291a29fa', 0, 'ac5319f1e76947949373f454dd916cb2', 'fieldMappingEntityName', 'FIELD_MAPPING_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.fieldMappingEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b3aeb52f92ab4f1699f12e0fa0b57919', 0, 'ac5319f1e76947949373f454dd916cb2', 'fieldMappingSectionId', 'FIELD_MAPPING_SECTION_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.fieldMappingSectionId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b84eea402d2b4187a1bf63b3d3099c0d', 0, 'ac5319f1e76947949373f454dd916cb2', 'dataType', 'DATA_TYPE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.dataType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('942cf4fd024445eeb9d963c70befe4b4', 0, 'ac5319f1e76947949373f454dd916cb2', 'tableEntityName', 'TABLE_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.tableEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9af1594df503457383ec8e943a259379', 0, 'ac5319f1e76947949373f454dd916cb2', 'tableEntityNameDisplay', 'TABLE_ENTITY_NAME_DISPLAY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.tableEntityNameDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b744916e26414922be7c181712580aa9', 0, 'ac5319f1e76947949373f454dd916cb2', 'tableAction', 'TABLE_ACTION', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.tableAction', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('331bc2c5694f412685f66d60862f74cb', 0, 'ac5319f1e76947949373f454dd916cb2', 'module', 'MODULE', 'VARCHAR(400)', 'IMPORT_MODULE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.module', 'codelist', 'IMPORT_MODULE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('77ff38219bfd499b86d349f94703fe5c', 0, 'ac5319f1e76947949373f454dd916cb2', 'checksum', 'CHECKSUM', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.checksum', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7540b54cde764d649c44655c66ca52e2', 0, 'ac5319f1e76947949373f454dd916cb2', 'templId', 'TEMPL_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.templId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('76c58791c13846b38fb178eef3085203', 0, 'ac5319f1e76947949373f454dd916cb2', 'importId', 'IMPORT_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.importId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('62292290059c4b1bab0466620981a0eb', 0, 'ac5319f1e76947949373f454dd916cb2', 'moduleName', 'MODULE_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.moduleName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8aeac1d13cf844188e478c98d85148e2', 0, 'ac5319f1e76947949373f454dd916cb2', 'moduleVer', 'MODULE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.moduleVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('0150ed9fcac14bffbca7d8a3c895f3df', 0, 'ImportMappingFieldDefTempl', 1, 'CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING_FIELD_DEF_TEMPL', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f5d0009094e946a3b43e34b091a18a82', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6563eae964544311b528e7596fdb271f', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e2a27166ca2b46dfb376fb9515b11500', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('07472af266ba4de6ae918e34a58f13d0', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1db5f818f83047bea669cd86a5297dc3', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ce8c24b01cc246c196fce63058367936', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c9907fb875e94ced9afb56f5908795aa', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('63fb09d2afc844a987970a71c0512dc9', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3171fa4f8d42438a9b2307e4b8117f54', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('350fcebabe044c8fbe17062ca5f417cf', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'excelHeader', 'EXCEL_HEADER', 'VARCHAR(400)', 'ATTACHMENT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.excelHeader', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('78d2b92a0d8448b791d5b0e123dc2c71', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'fieldMapping', 'FIELD_MAPPING', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.fieldMapping', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fa3beb39b96a44cdbf87ca74dbb36b6a', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'fieldMappingDisplay', 'FIELD_MAPPING_DISPLAY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.fieldMappingDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('47c3d709b1c54cf3afd2089baf12e505', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'fieldMappingEntityName', 'FIELD_MAPPING_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.fieldMappingEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e8d8157014fb40e28ac3c13b7df9cdfb', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'fieldMappingSectionId', 'FIELD_MAPPING_SECTION_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.fieldMappingSectionId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f3e941b223a342d284600dffb844cb7e', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'dataType', 'DATA_TYPE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.dataType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('444c60f0cb814523bd39a2a8d0fbbc46', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'tableEntityName', 'TABLE_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.tableEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('62e70958add2467697aff571605c50cf', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'tableEntityNameDisplay', 'TABLE_ENTITY_NAME_DISPLAY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.tableEntityNameDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6ab1597776104258b7f7e050f74ef239', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'tableAction', 'TABLE_ACTION', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.tableAction', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ddd1c52a7581492e952aa8b62cfc3bc7', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'module', 'MODULE', 'VARCHAR(400)', 'IMPORT_MODULE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.module', 'codelist', 'IMPORT_MODULE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b4fe68fc70f448adaa0ed798af5d9a75', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'checksum', 'CHECKSUM', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.checksum', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fbefd74fef144c09a03049ddf74854fa', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.updateUserName', NULL, NULL, 'UPDATE_USER_NAME', NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('84646002847a43168f490236939f198a', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '0', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.updatedOn', NULL, NULL, 'UPDATED_ON', NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('af912bffa783444299348fd5d1b70381', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'moduleName', 'MODULE_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.moduleName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1bfba291a1374088a89a26b36acf506a', 0, '0150ed9fcac14bffbca7d8a3c895f3df', 'moduleVer', 'MODULE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.moduleVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('cfbdd4fd7efe4d319d015c2c1a71bc67', 0, 'ImportMappingFieldData', 1, 'CNT_IMPORT_MAPPING_FIELD_DATA', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING_FIELD_DATA', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4dfcbec61df643468ff3c2261829e9c8', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.importMappings.fieldDatas.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('77af24a22afa4a39857d0b02a41a2cf0', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.importMappings.fieldDatas.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bfc70a426922401eb3c0f7be04fe66f8', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.importMappings.fieldDatas.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4eee1c8d3efa4e08a5c16b27b77b9f09', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.importMappings.fieldDatas.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e21d02f434384ed3b25284f4251a4ea3', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.importMappings.fieldDatas.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('babfafb8ccb04a5ebd9b9e59e1a84ae3', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.importMappings.fieldDatas.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1508902c37814911a5ff512b4280bcc1', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.importMappings.fieldDatas.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ea8fd9e7c2b94849bcfe6b77c4702a4a', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.importMappings.fieldDatas.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ddf520d2f3d643fb9b39b008ebe7ed2a', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.importMappings.fieldDatas.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('90ed485c0125464bbb905f6350ebf9a3', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'mappingId', 'MAPPING_ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.importMappings.fieldDatas.mappingId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('95583218bec64437a416a5a0095bb32b', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'defId', 'DEF_ID', 'VARCHAR(400)', 'IMPORT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '1', 'entity.importMappings.fieldDatas.defId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5a387d9a65ad4268b2cb2e1ab9833cd9', 0, 'cfbdd4fd7efe4d319d015c2c1a71bc67', 'dataValue', 'DATA_VALUE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.importMappings.fieldDatas.dataValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('da43b7f39186448ba89babb852d6e05a', 0, 'ImportParty', 1, 'CNT_IMPORT_PARTY', NULL, 'CTM_IMPORT', 'IMPORT_PARTY', 'import', NULL, NULL, NULL, NULL, '${partyName.code}-${contactUserRef}', '0', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8d0fd830f55b4b45bc9bb49627766ef5', 0, 'da43b7f39186448ba89babb852d6e05a', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.parties.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4e637a67ae014d8c946016d5f1850189', 0, 'da43b7f39186448ba89babb852d6e05a', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.parties.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7198c720e40d4318bf9b82bbfb81b095', 0, 'da43b7f39186448ba89babb852d6e05a', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.parties.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2154b93c3b124569bc36f4e1d540b2e3', 0, 'da43b7f39186448ba89babb852d6e05a', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.parties.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1ecb41e5938747418ecf874e0c8511cc', 0, 'da43b7f39186448ba89babb852d6e05a', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.parties.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8b08e123935145ec97f61c9d43cb1f3a', 0, 'da43b7f39186448ba89babb852d6e05a', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.parties.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('550cad7c9fa748608b1aeb30ceaf23f2', 0, 'da43b7f39186448ba89babb852d6e05a', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.parties.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2e190224c40347e7a1fda2d05e9fe198', 0, 'da43b7f39186448ba89babb852d6e05a', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.parties.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6eaf081864df4dab852aa18ab3f953c8', 0, 'da43b7f39186448ba89babb852d6e05a', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.parties.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e53afe78c9364791bb42c2aaf58d2b0a', 0, 'da43b7f39186448ba89babb852d6e05a', 'docId', 'DOC_ID', 'VARCHAR(32)', 'DOC_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.parties.docId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('11976b225ef0451d850e82f79576845a', 0, 'da43b7f39186448ba89babb852d6e05a', 'partyName', 'PARTY_NAME', 'VARCHAR(400)', 'PARTY_NAME', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.parties.partyName', 'codelist', 'RESPONSIBLE_PARTIES_NAME', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e3ee43d1a76c4d78992eb2f54d2b9fbb', 0, 'da43b7f39186448ba89babb852d6e05a', 'partyNameSeqNo', 'PARTY_NAME_SEQ_NO', 'NUMERIC(20, 0)', 'PARTY_NAME_SEQ_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.parties.partyNameSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5901f590cef74d98afa7ee89ee1b0980', 0, 'da43b7f39186448ba89babb852d6e05a', 'contactUser', 'CONTACT_USER_ID', 'VARCHAR(32)', 'CONTACT_USER', '0', 'entity', 'User', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.parties.contactUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7ef548390bc44e589805defe6bfbf6e7', 0, 'da43b7f39186448ba89babb852d6e05a', 'contactUserSeqNo', 'CONTACT_USER_SEQ_NO', 'NUMERIC(20, 0)', 'CONTACT_USER_SEQ_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.parties.contactUserSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('01ac4fd43f4346e49ee6a4f3e839383d', 0, 'da43b7f39186448ba89babb852d6e05a', 'isOwner', 'IS_OWNER', 'BOOLEAN', 'IS_OWNER', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.parties.isOwner', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7bab2e971aad49b89b12051f02327f8d', 0, 'da43b7f39186448ba89babb852d6e05a', 'partyNameName', 'PARTY_NAME_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.parties.partyNameName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('aec932fb69684e39a35dcab205af18c7', 0, 'da43b7f39186448ba89babb852d6e05a', 'partyNameVer', 'PARTY_NAME_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.parties.partyNameVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f480f6f6c45b4d2dbb51504a97ffc19a', 0, 'da43b7f39186448ba89babb852d6e05a', 'contactUserRef', 'CONTACT_USER_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.parties.contactUserRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8873c770f7c642e9a765d823cb5713ae', 0, 'da43b7f39186448ba89babb852d6e05a', 'contactUserVer', 'CONTACT_USER_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.parties.contactUserVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('fcffdd2c4dec42029467c927500c3108', 0, 'ImportHistory', 1, 'CNT_IMPORT_H', NULL, NULL, NULL, 'import', 'inner', NULL, NULL, NULL, NULL, '1', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('48254259ceab4f1da0009fb3844aae92', 0, 'fcffdd2c4dec42029467c927500c3108', 'parentId', 'PARENT_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c59333a2eb4b43b983930fd03b82d41b', 0, 'fcffdd2c4dec42029467c927500c3108', 'docVersion', 'DOC_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.docVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('966b732fbf884b8b8806ab7db250492c', 0, 'fcffdd2c4dec42029467c927500c3108', 'docRevision', 'DOC_REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.docRevision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dcd7e4878f314786a007efccedfcbf57', 0, 'fcffdd2c4dec42029467c927500c3108', 'itemId', 'ITEM_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.itemId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('87c129b3bd3c4111ad4edc6c21dad034', 0, 'fcffdd2c4dec42029467c927500c3108', 'itemRevision', 'ITEM_REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.itemRevision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b87b909855534e6f811bcd8060826e6a', 0, 'fcffdd2c4dec42029467c927500c3108', 'itemEntityName', 'ITEM_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.itemEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('26445114b42b4dbd9445d2fa0bdd2494', 0, 'fcffdd2c4dec42029467c927500c3108', 'type', 'TYPE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.type', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('352164d0a51d43a59be7def0a8daed0f', 0, 'fcffdd2c4dec42029467c927500c3108', 'fieldId', 'FIELD_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b06ddf70c0e34deb9cd2ee60f60e4036', 0, 'fcffdd2c4dec42029467c927500c3108', 'path', 'PATH', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.path', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6abbd38a58344400a64e11c97547637a', 0, 'fcffdd2c4dec42029467c927500c3108', 'refValue', 'REF_VALUE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.refValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('59145b83be9146eba568b800618b409f', 0, 'fcffdd2c4dec42029467c927500c3108', 'operation', 'OPERATION', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.operation', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ff131f965bf547fab4de8eab55744c6b', 0, 'fcffdd2c4dec42029467c927500c3108', 'valueBefore', 'VALUE_BEFORE', 'TEXT', NULL, '0', 'clob', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.valueBefore', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('577248f89641443fb26e67154d1a0b04', 0, 'fcffdd2c4dec42029467c927500c3108', 'valueAfter', 'VALUE_AFTER', 'TEXT', NULL, '0', 'clob', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.valueAfter', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bc787e86a8474284be0b56e0cebfee49', 0, 'fcffdd2c4dec42029467c927500c3108', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bffc8b23bffa4f5dba1d934462dc83a4', 0, 'fcffdd2c4dec42029467c927500c3108', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8f1476e256c24bbc859a968766a37f00', 0, 'fcffdd2c4dec42029467c927500c3108', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '0', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('088137a7b20542f985d5f4d5ca713a28', 0, 'fcffdd2c4dec42029467c927500c3108', 'trackingLevel', 'TRACKING_LEVEL', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.trackingLevel', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('42fa60bba12b495e8414ba66cf282da2', 0, 'fcffdd2c4dec42029467c927500c3108', 'id', 'ID', 'VARCHAR(32)', NULL, '2', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('401346b7ca4f4760adc2029e6195f693', 0, 'fcffdd2c4dec42029467c927500c3108', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a32515cb9cd4492f8e70e963d4a234a2', 0, 'fcffdd2c4dec42029467c927500c3108', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b2a3c0d266cb4589bf290140ae6886a3', 0, 'fcffdd2c4dec42029467c927500c3108', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('11045ae7cd1a464c8c3aee34657043d0', 0, 'fcffdd2c4dec42029467c927500c3108', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4dcf1eee893c4f2ea199211a545f0d77', 0, 'fcffdd2c4dec42029467c927500c3108', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('69da5b86dca04c1283c54fac663b1d0e', 0, 'fcffdd2c4dec42029467c927500c3108', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e950f7e0feb94ca588af771db628d990', 0, 'fcffdd2c4dec42029467c927500c3108', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('212758a7e1a14049b35cc7396823eca7', 0, 'fcffdd2c4dec42029467c927500c3108', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '2', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');




DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'Import' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'Import' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'Import' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportSelection' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportSelection' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportSelection' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMapping' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMapping' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMapping' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDef' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDef' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDef' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDefTempl' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDefTempl' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDefTempl' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldData' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldData' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldData' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportParty' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportParty' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportParty' AND ENTITY_VERSION = 1;

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('8732a1683afe47d294c01e2204b80bab', 0, 'Import', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5098148084f049f7b74b39612a46164d', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('21f1a293eb7f4137a9dd2d9c514f9353', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('56efaf0858534598998398774b4250e9', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ff4ce519bb18482cb38d0050d352b6c0', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('28c22a84d90b4c29b5de0b2ba734e92c', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1c954e4e1a074724ad2f1f3a47c3a325', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0e75c068e77c41b5a0fcde599fbf9dfc', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '6', 'version', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7630170df0a14b71ac033a792636ad5d', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '7', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('32eaf7e9bc2b45e7b3dc8b0af057cafd', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '8', 'docStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3af976bce27b48b2a86a1d844afef2d2', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '9', 'editingStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1b2357cfe801487cb55a737ba0fab57a', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '10', 'createUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('471b9965ac754e558a797ba2fcf7e94a', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '11', 'createUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7c50ba55f1eb4ced8b3594c3c1505821', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '12', 'updateUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('15e81996ae35431eb4fab2c8cbfe5496', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '13', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a1681eb1a8ce4fbeb63957e63d8655af', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '14', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('555d5b1678d545c085a72c876a2872f1', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '15', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('356e0ab180a9472a83effbd5dc6777f3', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '16', 'integrationSource', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0eaf3438c8414c93a9fad5513991b004', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '17', 'integrationStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b088f57d9adc425683965cfb3e29a350', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '18', 'integrationNote', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('787cedf86f524963b55c1d6521ed1efc', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '19', 'isCpmInitialized', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5d03e7a4665b4f849a72f76352f9bee5', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '20', 'isLatest', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('60f6acf977324d879d0226720c8c5b71', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '21', 'importNo', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5588618909d547e4b1cd2958a3605b80', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '22', 'headerRow', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b1148d2b71c94a7b855b3e0eec19bd6d', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '23', 'startRow', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7cee2fae956345a99f308bcc483c6d55', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '24', 'fileId', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('25bb993575e642babeb9efde31d8f813', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '25', 'importModule', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5fa7b755218143819fee284390c9b3a0', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '26', 'importKey', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5bf3b5f4d2f64ac2a649b91d2b19a81c', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '27', 'importKeyDisplay', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e02d9256d27c4522b3b6e2ed85919784', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '28', 'checksum', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d06d887fe3484f56b35c492bf3aba986', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '29', 'importMappings', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('064ec8ac65534164972aa040bb314af4', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '30', 'refNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f7249293f2b049e4b6f4584b7dc870b6', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '31', 'businessRefNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('50c39212ffdd4ef29c7bf77967fb5e79', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '32', 'partyTemplateRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3d93df3bf18f422bba3c1efa669199e1', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '33', 'partyTemplateVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ebca73683ca544d0b86d8e15c3c0a334', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '34', 'partyTemplateName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7f497474f08d4148b829e846e0ea6153', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '35', 'partyName1', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5ae9fdd4dbb14bf8bb167109b7be11cf', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '36', 'partyName2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a31883a8cd314f2dad327cddcc96f803', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '37', 'partyName3', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7bc1d1cf81a94da8b3527ced44d089a0', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '38', 'partyName4', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e6454686237e4cb5a5c40327b744198d', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '39', 'partyName5', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('45cc9d41c24747d69a11f91d832da46e', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '40', 'parties', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bad7be921c134cfd832bfd6f4311daad', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '41', 'fileRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cb733b44f1a14516977dcc0448065179', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '42', 'fileVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('35ebda2e78e14e0d86a7e6e77488c13b', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '43', 'fileRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0334431624054f8399cecf80d78a80a4', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '44', 'fileDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('83561106e6a2487da74e0eb965e9b8f9', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '45', 'importModuleName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('083ff245e04d4d1ea98192d66a0393a4', 0, 1, '/', '/', '0', '8732a1683afe47d294c01e2204b80bab', '46', 'importModuleVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('eab1f520cda940ed908ab558d270f1b7', 0, 'ImportSelection', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('718e8bac6ab040e3bb47f141109fdf13', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fe0c3b4faf054bc58bd06eb8af4b0a36', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6fb48a0d02e44ff2a2a0cc5d8af2c629', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('88cedaa6d0d24d929fd3871129a663bd', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c9543d6158a941788d58524f4eeda6ba', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('523e4e79cad74aaf87acc30a7b0be43e', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('13c66cf5ccf34e2b8a1512c2d066ca72', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '6', 'version', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bb2fe7a216534820aa19eced21c67712', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '7', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6e459007088e470d9f882334742a5b22', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '8', 'docStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a01b489b807840d7a1704f40fbc28725', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '9', 'editingStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fc8d2b295bab48069706108cb17f50f3', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '10', 'createUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e8e6c7b5ffb6410d82030585fd018880', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '11', 'createUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1b5aa41dd60b410a97383dfc694f93c7', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '12', 'updateUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e3bfb9df4d414260a648e54bbd0ec5bb', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '13', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1ebb999d189640e9a53f9abd849851cb', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '14', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('199dc53e1e6f46c4bd8b0bc4e27abb9e', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '15', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3616cdc3e2714e25b8584536a037a528', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '16', 'integrationSource', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2ec7ecb8ff49422188040fc368db8902', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '17', 'integrationStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3957af3c65494f44a0b87ecc4ccde923', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '18', 'integrationNote', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('71a04c8f8d2d4d32937e60061c243f03', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '19', 'isCpmInitialized', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f857f4a9f5d9434e9111f0299809d517', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '20', 'isLatest', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e1cd112195da48c5a19e8ab4191f1d19', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '21', 'parentId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f63b24dd250146caa3b461be2f506ff1', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '22', 'parentEntity', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('37da64e00e4c45f08b21f89b84f458db', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '23', 'fieldId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c65e6295dfbd4f86b950ae3afa8c21c0', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '24', 'ref', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c5469a1dfa5c473eae95fc4f18b2f9d2', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '25', 'refEntity', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('358dad4350c74dd38fa4a15bfc62fba0', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '26', 'displayValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('612379146a6044a5a852055eed7b5a13', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '27', 'internalSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5f0e58c60b514a2b906887a328b1e4ea', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '28', 'refRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b91089f1592f4abba00bb4434b117ee2', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '29', 'refVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4c21987f663e4f5e9bf97ff661ad95a5', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '30', 'refRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('48dfdfac6068488fb85c746b6ddd399e', 0, 1, '/', '/', '0', 'eab1f520cda940ed908ab558d270f1b7', '31', 'refDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('de731697d7b3465ca8d7c50fdb4bf0d7', 0, 'ImportMapping', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1b7414e4017041b0afeffc929182b742', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b10973ded1104b9e827f97acd71f27e4', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f52767827e1041c19adeddf41edf9322', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cd4f5852b6c545479b63a9ee52a71ae0', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e47e1d24e6674eafa9aa82a4cd9485ca', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6a90254b158e436fb0bd22c5351114e8', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4b92938040394cd5b7571abda7f62da8', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('af1e9122e21e428e9ba64307661b0894', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('df5fc64f02ab430b914b10d77d581575', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('11a5e1693fd0408797bee8702962f0dd', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '9', 'importId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1f8c54c5a1324167914b980278293e45', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '10', 'excelLine', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1c46505ea275426d8559aa4f2cac7272', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '11', 'importStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9e74a71ab7504402be3b192bcc8c8021', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '12', 'error', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('05a5401114474051a116f8e67bfb46d4', 0, 1, '/', '/', '0', 'de731697d7b3465ca8d7c50fdb4bf0d7', '13', 'fieldDatas', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('8a04186606064ab9a43e14fd560d0436', 0, 'ImportMappingFieldDef', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5b1b02c097bc496bad42b7dde3d5c0aa', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f3b35dde4fdc44e399761d3728860b8c', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7bf25e7456ba4ade9a0837b61009fc70', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b143656408174423a14dbd0768ce051c', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a289fc72a99648c6a8a3e0d2aba2f009', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4f280c062cec49d1be1ed8adf9d7434e', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bba8a8d1664a424c81504f2eb4abcf0d', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ce5b0999ab774c33806574c0c354e5fe', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6bb85183600d4b63a63d4196980efc02', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('97693c1995c04922866d02d8fec647a9', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '9', 'excelHeader', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b1d3664da0c1413d9b338dcf3d7e7c8c', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '10', 'fieldMapping', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('aaa3062025824949964ad915ff2f9d37', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '11', 'fieldMappingDisplay', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e686d5c3431f41c7ac5d154587bd2ce2', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '12', 'fieldMappingEntityName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1fd878e7f31f4e2a8875f10ffc3e77ca', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '13', 'fieldMappingSectionId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9c31c43e21e04686a3ca4b9a90bcf21d', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '14', 'dataType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('059f14d308e2463aa8bc15e5bcd5f95b', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '15', 'tableEntityName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f8336cb1821c44a18fe0c1ec9bc11c1a', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '16', 'tableEntityNameDisplay', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3fd1d8469dae44d38494caceb719a590', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '17', 'tableAction', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('785c6b07dadb453f866424e1634cfc28', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '18', 'module', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6143d93c19bb4069b0dd6c7a23d2008e', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '19', 'checksum', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('393aaacb343442faa475814f1f8fe743', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '20', 'templId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('98fbbefb58f84090862c4b3d8e30fd29', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '21', 'importId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5fc2ef62f96440eba25d48bdd0f9d607', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '22', 'moduleName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ba7b467a9018465385e4d88f4da6ef44', 0, 1, '/', '/', '0', '8a04186606064ab9a43e14fd560d0436', '23', 'moduleVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('5ba19bbd789940b0a1cf02b5210fe1da', 0, 'ImportMappingFieldDefTempl', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2520de38e13d4a2eac811662951ce09f', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('43b6065b4f404cc38685a0946e563ebd', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fe8dfdacd0f246db9bc3c9f38fed9aba', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c44299c8040945d4aad9e739cc94d11f', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d21d3481347e4b78987ea313a6273089', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c8c09790e21b4fafb6658e8be6aad08e', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0b8e33844ae44b48926e53909bf03480', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('41bb7b7841574688b6d0c6209a69e074', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('510085219e4b4f3c8b2ebab14cea3198', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5297b5386a644cb3a614d2de3d4fd3dd', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '9', 'excelHeader', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4b86f7e229d84855840d24d29ae7931d', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '10', 'fieldMapping', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('058ae76a092a4eaea60bf0c859fc23b9', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '11', 'fieldMappingDisplay', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ba6cfc74c8a14d5ebbfb7a923ca50111', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '12', 'fieldMappingEntityName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0213a7e17dd94fa2abb8721952e8f08c', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '13', 'fieldMappingSectionId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e6cb7e6d3a8c4842a8684f797d0973c8', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '14', 'dataType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0bda09a169ad4a52ae47226be7b8debd', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '15', 'tableEntityName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('40eed4f069d84688950af362e1525614', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '16', 'tableEntityNameDisplay', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('33aed7b08ba74b9abc2e5ee1ae35dbec', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '17', 'tableAction', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ca3e4b9a6daf46078b2ef6d28df0fbe9', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '18', 'module', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('931914c333434572a4027366e769dafa', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '19', 'checksum', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9bc2f97f452c420b9dd5d321a8c75b39', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '20', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ddbacf3047d34ba2af65acc12165a2f7', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '21', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('97de981c0af74ad697093d17865d7db2', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '22', 'moduleName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dd3c6df56cfa4ee5926c9a7efdcf3d67', 0, 1, '/', '/', '0', '5ba19bbd789940b0a1cf02b5210fe1da', '23', 'moduleVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('6f1917b5e2084538878d0f153b0105b0', 0, 'ImportMappingFieldData', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('642831e7cfd547479d600f98788e38ed', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8bac8fa7b8ac49ccaad9a90cc458139a', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('231680da70f74d5492d6d42500b5686d', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('80850e7c3bc047c6aa3300aef4ce28a6', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('89061088e7e44703bcfb2083b285e222', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0f164c1c906e4c62b144a62cc8901ace', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3a711e73b5b6427c9e2f873324054083', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6ae7e863e51a4e4688ef68f3fc3d4dec', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('622a63f216ad43c4a5d0ede22bee2e8d', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('10b8472d4fd44658b2399d18a5f22eb3', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '9', 'mappingId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b44f1daa840d4183a6510e3f01ac4118', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '10', 'defId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3b0a89d0ceb5444087d83b4cbcacbbed', 0, 1, '/', '/', '0', '6f1917b5e2084538878d0f153b0105b0', '11', 'dataValue', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('691711455f0043bb9c6355ceb84957ef', 0, 'ImportParty', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-08-01 13:53:39', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1e52f4e995114be69bec61a88668dd32', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('40a5ed429c874632b6ffd2c97060e37a', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0605029ab5bc4166ba9d0525116ce25e', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fbe8db0a49b44ce6ac314c5eafa76fd3', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8ab9b6e481c74f80a9d2ab4e26b9bb80', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b1984c28eea443b3b572574219643d30', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ee76d51de93c4c9983e6b69ad8b61d1c', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a8c1699f344c4d638bf212471a888c93', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c380454599bc41d2956b36360e89ff7e', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('390e2a4482fc490d80c41ba4bcbf9eea', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '9', 'docId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('25ff972019e04df28fda4f837b642c2f', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '10', 'partyName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ad32d01ceff543d886ce618876f63807', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '11', 'partyNameSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4a2fc8b35dcd4c3bb3bd5be8d3c849ed', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '12', 'contactUser', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('86337cc74a02403d8201dff716fa2593', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '13', 'contactUserSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cf56eb32c9ac4ae186ca37a37bf6a56b', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '14', 'isOwner', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4cb3f1b8df0e490a82c641985198c03a', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '15', 'partyNameName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8a1c3ee05e424872ae680bcec33effa9', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '16', 'partyNameVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1050b3df9a864e93ae3a2c14019b1047', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '17', 'contactUserRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('986fabe805f34f6781c323c4d1244e1e', 0, 1, '/', '/', '0', '691711455f0043bb9c6355ceb84957ef', '18', 'contactUserVer', 0, 'PathRenderer', 'DetailRenderer');

