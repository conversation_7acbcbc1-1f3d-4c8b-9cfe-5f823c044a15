
--<ROLE> : role name, e.g. ['SuperAdministratorRole'],['GeneralAdministratorRole'],['ClientAdministratorRole'],['CommonFunctionsRole']
--<OBJ_ID>: object id, e.g. 'color'
--<OBJ_TYPE>: object type, e.g. 'form', 'view'
--<CONDITION>: condition, e.g. '$ANY'
--<ACCESS_RIGHT>: access right, e.g. '1'
--<DOMAIN_ID>: domain id, e.g. 'QAPEGP'
--<IS_DISALLOW>: is_disallow, e.g. when access right = '1', IS_DISALLOW = false , then access right = '2', IS_DISALLOW = '1'
--<ACTION_ID> : e.g : testFormAction, testViewAction

BEGIN;
DO $body$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
role varchar[];
type varchar[];
action varchar[];
objectId VARCHAR(200) := 'import';
actionId varchar[];
formActionId varchar[] :=array[['openImportPopupWin'],['doneDoc'],['saveDoc'],['discardDoc']];
viewActionId varchar[] :=array[['openImportPopupWin'],['importRawData']];
objectTypeArr varchar[] :=array[['form'],['view']];
roleArr varchar[] :=array[['CommonFunctionsRole']];
conditionName VARCHAR(200) :='$ANY';
accessRight numeric := 1;
isDisallow boolean := false;
adminCount numeric;
adminId VARCHAR(200);
BEGIN
    --add new action begin--
    FOREACH type SLICE 1 IN ARRAY objectTypeArr
    LOOP
    
     FOREACH role SLICE 1 IN ARRAY roleArr
      LOOP
      
      IF type[1] = 'form' THEN    actionId := formActionId;
      ELSEIF type[1] = 'view' THEN  actionId := viewActionId;
      END IF;
      
      FOREACH action SLICE 1 IN ARRAY actionId
       LOOP

        DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME=role[1] AND DOMAIN_ID = DOMAINID);
        --- insert cnt_rule_action
        INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE)
        SELECT SYS_GUID(), 0, 1, ao.ID, r.id, action[1],c.id, d.ID, accessRight , d.ID, '0'
        FROM CNT_DOMAIN d
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = d.ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON r.NAME = role[1] AND r.DOMAIN_ID = d.ID AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION c ON c.NAME = conditionName AND c.DOMAIN_ID = d.ID AND c.IS_LATEST = '1'
        where d.ID = DOMAINID
        AND NOT EXISTS (SELECT 1 FROM CNT_RULE_ACTION WHERE DOMAIN_ID = d.ID AND ACTION_ID = action[1]
        AND ACCESS_OBJECT_ID = ao.ID AND ROLE_ID = r.id AND CONDITION_ID = c.id AND ACCESS_RIGHT= accessRight);

        
        SELECT count(*) into adminCount FROM CNT_RULE_ACTION_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND admin.domain_id =r.domain_id AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_ACTION rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = (case when admin.IS_DISALLOW then 2 else 1 end)
        inner join cnt_role_sln sln on sln.parent_id = admin.id and condition.id = sln.ref_id and sln.ref_entity ='Condition' and sln.display_value = condition.NAME
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ACTION_ID = action[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.IS_DISALLOW =isDisallow
        AND admin.DOMAIN_ID = DOMAINID;

        IF adminCount != 0 THEN
        --adminCount !=0 means not need insert CNT_RULE_ACTION_ADMIN record

        --CNT_ROLE_SLN (actionId)
        INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE)
        SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, admin.ID, 'RuleActionAdmin', 'actionId', null, null, action[1]
        FROM CNT_RULE_ACTION_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND admin.domain_id =r.domain_id AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_ACTION rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = (case when admin.IS_DISALLOW then 2 else 1 end)
        inner join cnt_role_sln sln on sln.parent_id = admin.id and condition.id = sln.ref_id and sln.ref_entity ='Condition' and sln.display_value = condition.NAME
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ACTION_ID = action[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.IS_DISALLOW =isDisallow
        AND admin.DOMAIN_ID = DOMAINID
        WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE_SLN WHERE DOMAIN_ID = DOMAINID AND PARENT_ID = admin.ID AND PARENT_ENTITY= 'RuleActionAdmin' AND FIELD_ID='actionId' AND DISPLAY_VALUE= action[1]);
        
        ELSE
        --adminCount = 0 means need insert new CNT_RULE_ACTION_ADMIN record , and the related selection table need to link to this new CNT_RULE_ACTION_ADMIN record
        
        SELECT SYS_GUID() INTO adminId;
        
        INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW)
        SELECT adminId, 0, 1, d.ID, d.ID, '0', r.id, objectId, type[1] , ao.NAME, conditionName, isDisallow
        FROM CNT_DOMAIN d
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID=objectId AND ao.OBJECT_TYPE = type[1] AND ao.DOMAIN_ID = d.ID AND ao.IS_LATEST = '1'
        INNER JOIN CNT_ROLE r ON r.NAME = role[1] AND r.DOMAIN_ID = d.ID AND r.IS_LATEST = '1'
        where d.ID = DOMAINID
        AND NOT EXISTS (
        SELECT 1 FROM CNT_RULE_ACTION_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND admin.domain_id =r.domain_id AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_ACTION rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = (case when admin.IS_DISALLOW then 2 else 1 end)
        inner join cnt_role_sln sln on sln.parent_id = admin.id and condition.id = sln.ref_id and sln.ref_entity ='Condition' and sln.display_value = condition.NAME
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ACTION_ID = action[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.IS_DISALLOW =isDisallow
        AND admin.DOMAIN_ID = DOMAINID);

        --CNT_ROLE_SLN (condition)
        INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE)
        SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, admin.ID, 'RuleActionAdmin', 'conditionId', 'Condition', condition.ID, conditionName
        FROM CNT_RULE_ACTION_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND admin.domain_id =r.domain_id AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_ACTION rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = (case when admin.IS_DISALLOW then 2 else 1 end)
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ACTION_ID = action[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.IS_DISALLOW =isDisallow
        AND admin.DOMAIN_ID = DOMAINID
        and admin.id = adminId
        WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE_SLN WHERE DOMAIN_ID = DOMAINID AND PARENT_ID = admin.ID AND PARENT_ENTITY ='RuleActionAdmin' AND FIELD_ID = 'conditionId' AND REF_ENTITY='Condition' AND REF_ID = condition.ID AND DISPLAY_VALUE = conditionName);

        ---CTM_ROLE

        INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT admin.ID, DOMAINID, 'RuleActionAdmin'
        FROM CNT_RULE_ACTION_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND admin.domain_id =r.domain_id AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_ACTION rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = (case when admin.IS_DISALLOW then 2 else 1 end)
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ACTION_ID = action[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.IS_DISALLOW =isDisallow
        AND admin.DOMAIN_ID = DOMAINID
        and admin.id = adminId
        WHERE NOT EXISTS (SELECT 1 FROM CTM_ROLE WHERE ID = admin.ID);

        --CNT_ROLE_SLN (actionId)
        INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE)
        SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, admin.ID, 'RuleActionAdmin', 'actionId', null, null, action[1]
        FROM CNT_RULE_ACTION_ADMIN admin
        INNER JOIN CNT_ACCESS_OBJECT ao ON ao.OBJECT_ID  = admin.OBJ_ID  AND ao.OBJECT_TYPE = admin.OBJ_TYPE AND ao.DOMAIN_ID = admin.DOMAIN_ID AND ao.IS_LATEST  = '1'
        INNER JOIN CNT_ROLE r ON admin.ROLE_ID = r.ID AND r.NAME = role[1] AND admin.domain_id =r.domain_id AND r.IS_LATEST = '1'
        INNER JOIN CNT_CONDITION condition on admin.domain_id =condition.domain_id AND condition.NAME= conditionName AND condition.IS_LATEST = '1'
        INNER JOIN CNT_RULE_ACTION rule ON rule.ROLE_ID = admin.ROLE_ID AND rule.ACCESS_RIGHT = (case when admin.IS_DISALLOW then 2 else 1 end)
        AND rule.CONDITION_ID = condition.ID and rule.domain_id =admin.domain_id AND rule.ACTION_ID = action[1] AND rule.ACCESS_OBJECT_ID = ao.ID
        AND admin.OBJ_ID = objectId
        AND admin.OBJ_TYPE = type[1]
        AND admin.IS_DISALLOW =isDisallow
        AND admin.DOMAIN_ID = DOMAINID
        and admin.id = adminId
        WHERE NOT EXISTS (SELECT 1 FROM CNT_ROLE_SLN WHERE DOMAIN_ID = DOMAINID AND PARENT_ID = admin.ID AND PARENT_ENTITY= 'RuleActionAdmin' AND FIELD_ID='actionId' AND DISPLAY_VALUE= action[1]);
        
        
        END IF;

        END LOOP;
     END LOOP;
    END LOOP;
    --add new action end--
     
    DELETE FROM CNT_DOMAIN_GROUP_CACHED_RULES;
    DELETE FROM CNT_CHECKSUM_CACHE_RULE;
    DELETE FROM CNT_CHECKSUM_CACHE_HCL_NODE;
    DELETE FROM CNT_CKSUM_CACHE_CLASSIFICATION;
    DELETE FROM CNT_CHECKSUM_CACHE_DATA;
    DELETE FROM CNT_USER_CACHE_RULE;
    DELETE FROM CNT_USER_CACHE_HCL_NODE;
    DELETE FROM CNT_USER_CACHE_CLASSIFICATION;
    DELETE FROM CNT_USER_CACHE_DATA;
END;
$body$ LANGUAGE PLPGSQL;
COMMIT;

