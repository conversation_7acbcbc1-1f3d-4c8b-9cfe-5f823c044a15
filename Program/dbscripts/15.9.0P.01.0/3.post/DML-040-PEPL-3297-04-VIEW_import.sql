--// FILE: DML_VIEW_import.sql

INSERT INTO CNT_VIEW(ID, DESCRIPTION, NAME, MODULE_ID, QUERY_ID, TITLE, REVISION, ENTITY_VERSION, ADVANCED_SEARCH_ID, CREATE_USER, CREATE_USER_NAME, UPDATE_USER, UPDATE_USER_NAME, CREATED_ON, UPDATED_ON, DOMAIN_ID, REF_NO, IS_LATEST, VIEW_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), 'Imports View', 'importView', 'import', 'listImports', 'lbl.view.importView', 1, 1, NULL, 'DUMMY_SYSTEM_ID', 'DUMMY_SYSTEM_ID', 'DUMMY_SYSTEM_ID', 'DUMMY_SYSTEM_ID', TO_TIMESTAMP('2025-07-30 14:47:41', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2025-07-30 14:47:41', 'YYYY-MM-DD HH24:MI:SS'), '/', 'importView', '1', 'listing', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_VIEW WHERE  DOMAIN_ID = '/' AND  NAME = 'importView');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_VIEW') AND TARGET_ID IN (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/');

UPDATE CNT_VIEW SET DESCRIPTION = 'Imports View' ,MODULE_ID = 'import' ,QUERY_ID = 'listImports' ,TITLE = 'lbl.view.importView' ,REVISION = 1 ,ENTITY_VERSION = 1 ,ADVANCED_SEARCH_ID = NULL ,CREATE_USER = 'DUMMY_SYSTEM_ID' ,CREATE_USER_NAME = 'DUMMY_SYSTEM_ID' ,UPDATE_USER = 'DUMMY_SYSTEM_ID' ,UPDATE_USER_NAME = 'DUMMY_SYSTEM_ID' ,CREATED_ON = TO_TIMESTAMP('2025-07-30 14:47:41', 'YYYY-MM-DD HH24:MI:SS') ,UPDATED_ON = TO_TIMESTAMP('2025-07-30 14:47:41', 'YYYY-MM-DD HH24:MI:SS') ,REF_NO = 'importView' ,IS_LATEST = '1' ,VIEW_CATEGORY = 'listing' ,HUB_DOMAIN_ID = '/' ,IS_FOR_REFERENCE = '0' WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/';

DELETE FROM CNT_VIEW_OPTION WHERE VIEW_ID IN (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/');

INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'SELECTION_MODE', 'multiple', 1, 1, '/', '/', '0' ;


INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'SUPPORTED_MODE', 'LIST', 1, 1, '/', '/', '0' ;


INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'DEFAULT_MODE', 'LIST', 1, 1, '/', '/', '0' ;


INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'FREEZE_COLUMN_INDEX', '1', 1, 1, '/', '/', '0' ;


INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'LATEST_VERSION', 'TRUE', 1, 1, '/', '/', '0' ;


INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'ES_ENTITY_NAME', 'Import', 1, 1, '/', '/', '0' ;


INSERT INTO CNT_VIEW_OPTION(ID, VIEW_ID, OPTION_NAME, OPTION_TEXT, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'ES_RETURN_FIELD', 'id:id:string,businessRefNo:businessReference:string,refNo:refNo:string,isLatest:isLatest:boolean', 1, 1, '/', '/', '0' ;


DELETE FROM CNT_VIEW_ACTION WHERE VIEW_ID IN (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/');

INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'openImportPopupWin', 'button', 'moduleId=import&entityName=Import&winId=popImportNewAction', 0, 'lbl.view.importView.action.openImportPopupWin', 1, 1, '/', NULL, '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'markAs', 'buttonGroup', NULL, 1, 'lbl.view.importView.action.markAs', 1, 1, '/', NULL, '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus01', 'button', 'moduleId=import&entityName=Import', 2, 'lbl.view.importView.action.searchMarkAsCustomStatus01', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus02', 'button', 'moduleId=import&entityName=Import', 3, 'lbl.view.importView.action.searchMarkAsCustomStatus02', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus03', 'button', 'moduleId=import&entityName=Import', 4, 'lbl.view.importView.action.searchMarkAsCustomStatus03', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus04', 'button', 'moduleId=import&entityName=Import', 5, 'lbl.view.importView.action.searchMarkAsCustomStatus04', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus05', 'button', 'moduleId=import&entityName=Import', 6, 'lbl.view.importView.action.searchMarkAsCustomStatus05', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus06', 'button', 'moduleId=import&entityName=Import', 7, 'lbl.view.importView.action.searchMarkAsCustomStatus06', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus07', 'button', 'moduleId=import&entityName=Import', 8, 'lbl.view.importView.action.searchMarkAsCustomStatus07', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus08', 'button', 'moduleId=import&entityName=Import', 9, 'lbl.view.importView.action.searchMarkAsCustomStatus08', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus09', 'button', 'moduleId=import&entityName=Import', 10, 'lbl.view.importView.action.searchMarkAsCustomStatus09', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchMarkAsCustomStatus10', 'button', 'moduleId=import&entityName=Import', 11, 'lbl.view.importView.action.searchMarkAsCustomStatus10', 1, 1, '/', 'markAs', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'actions', 'buttonGroup', NULL, 12, 'lbl.view.importView.action.actions', 1, 1, '/', NULL, '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchActivateDoc', 'button', 'moduleId=import&entityName=Import', 13, 'lbl.view.importView.action.searchActivateDoc', 1, 1, '/', 'actions', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchDeactivateDoc', 'button', 'moduleId=import&entityName=Import', 14, 'lbl.view.importView.action.searchDeactivateDoc', 1, 1, '/', 'actions', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'searchCancelDoc', 'button', 'moduleId=import&entityName=Import', 15, 'lbl.view.importView.action.searchCancelDoc', 1, 1, '/', 'actions', '/', '0' ;


INSERT INTO CNT_VIEW_ACTION(ID, VIEW_ID, ACTION_NAME, ACTION_TYPE, ACTION_PARAMS, ACTION_SEQ, LABEL, REVISION, ENTITY_VERSION, DOMAIN_ID, BUTTON_GROUP_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'importRawData', 'button', 'moduleId=import&upload=true&listener=com.core.cbx.ui.listener.ImportRawDataListener', 16, 'lbl.view.importView.action.importRawData', 1, 1, '/', NULL, '/', '0' ;


DELETE FROM CNT_VIEW_COLUMN WHERE VIEW_ID IN (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/');

INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'importNo', 'RefNo', NULL, NULL, NULL, 'left', '1', 'M', '1', 'importNo:importNo:string', '1', '0', 'lbl.view.importView.column.importNo', 'IMP.IMPORT_NO', '/', 'lbl.view.importView.column.importNo.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'headerRow', 'Number', NULL, NULL, NULL, 'left', '1', 'M', '1', 'headerRow:headerRow:integer', '1', '1', 'lbl.view.importView.column.headerRow', 'IMP.HEADER_ROW', '/', 'lbl.view.importView.column.headerRow.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'startRow', 'Number', NULL, NULL, NULL, 'left', '1', 'L', '1', 'startRow:startRow:integer', '1', '2', 'lbl.view.importView.column.startRow', 'IMP.START_ROW', '/', 'lbl.view.importView.column.startRow.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'importModule', 'Text', '$CODELIST_CODE&bookName=IMPORT_MODULE', NULL, NULL, 'left', '1', '100px', '0', 'importModule:importModule.code:string', '1', '3', 'lbl.view.importView.column.importModule', 'IMP.IMPORT_MODULE', '/', 'lbl.view.importView.column.importModule.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'importModuleName', 'Text', '$CODELIST_NAME&bookName=IMPORT_MODULE', NULL, NULL, 'left', '1', 'L', '1', 'importModuleName:importModule.name:string', '1', '4', 'lbl.view.importView.column.importModuleName', 'IMP.IMPORT_MODULE_NAME', '/', 'lbl.view.importView.column.importModuleName.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'importTable', 'Text', NULL, NULL, NULL, 'left', '1', 'L', '0', 'importTable:importTable:string', '1', '5', 'lbl.view.importView.column.importTable', 'IMP.IMPORT_TABLE', '/', 'lbl.view.importView.column.importTable.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'importKeyDisplay', 'Text', NULL, NULL, NULL, 'left', '1', 'L', '1', 'importKeyDisplay:importKeyDisplay:string', '1', '6', 'lbl.view.importView.column.importKeyDisplay', 'IMP.IMPORT_KEY', '/', 'lbl.view.importView.column.importKeyDisplay.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'Import', 'CustomFields', NULL, NULL, NULL, 'left', '1', 'L', '0', '1', '7', 'lbl.view.importView.column.Import', 'IMP', '/', 'lbl.view.importView.column.Import.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'Import', 'ResponsibleParty', NULL, NULL, NULL, 'left', '1', 'L', '0', '1', '8', 'lbl.view.importView.column.Import', 'IMP', '/', 'lbl.view.importView.column.Import.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'systemTag', 'Text', NULL, NULL, NULL, 'left', '1', 'L', '0', 'systemTag:docTag.systemTag:string', '1', '9', 'lbl.view.importView.column.systemTag', 'TAG.SYSTEM_TAG', '/', 'lbl.view.importView.column.systemTag.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'status', 'Label', 'labelIdPrefix=lbl.status.project.', NULL, NULL, 'left', '1', 'S', '1', 'status:status:string', '1', '10', 'lbl.view.importView.column.status', 'IMP.STATUS', '/', 'lbl.view.importView.column.status.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'docStatus', 'Label', 'labelIdPrefix=lbl.docStatus.', NULL, NULL, 'left', '1', 'S', '0', 'docStatus:docStatus:string', '1', '11', 'lbl.view.importView.column.docStatus', 'IMP.DOC_STATUS', '/', 'lbl.view.importView.column.docStatus.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'editingStatus', 'Label', 'labelIdPrefix=lbl.editingStatus.', NULL, NULL, 'left', '1', 'S', '0', 'editingStatus:editingStatus:string', '1', '12', 'lbl.view.importView.column.editingStatus', 'IMP.EDITING_STATUS', '/', 'lbl.view.importView.column.editingStatus.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'version', 'Number', NULL, NULL, NULL, 'right', '1', 'XS', '0', 'version:version:integer', '1', '13', 'lbl.view.importView.column.version', 'IMP.VERSION', '/', 'lbl.view.importView.column.version.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'updatedOn', 'Datetime', NULL, NULL, NULL, 'left', '1', 'S', '1', 'updatedOn:updatedOn:timestamp', '1', '14', 'lbl.view.importView.column.updatedOn', 'IMP.UPDATED_ON', '/', 'lbl.view.importView.column.updatedOn.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'docRef', 'Text', NULL, NULL, NULL, 'left', '1', 'M', '0', 'docRef:docRef:string', '1', '15', 'lbl.view.importView.column.docRef', 'IMP.REF_NO', '/', 'lbl.view.importView.column.docRef.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'createdOn', 'Datetime', NULL, NULL, NULL, 'left', '1', 'S', '0', 'createdOn:createdOn:timestamp', '1', '16', 'lbl.view.importView.column.createdOn', 'IMP.CREATED_ON', '/', 'lbl.view.importView.column.createdOn.block', NULL, '0', '/', '0' ;


INSERT INTO CNT_VIEW_COLUMN(ID, VIEW_ID, REVISION, ENTITY_VERSION, FIELD_ID, COLUMN_TYPE, DATA_FORMAT, ACTION, ACTION_PARAMS, ALIGNMENT, SORTABLE, COLUMN_WIDTH, VISIBLE, ES_MAPPING, ALLOW_SIMPLE_SEARCH, COLUMN_SEQ, LABEL, MAPPED_FIELD, DOMAIN_ID, BLOCK_DISPLAY_LABEL, BLOCK_DISPLAY_TYPE, IS_DETAIL_VIEW_COLUMN, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 1, 1, 'updateUserName', 'Text', NULL, NULL, NULL, 'left', '1', 'M', '1', 'updateUserName:updatedBy:string', '1', '17', 'lbl.view.importView.column.updateUserName', 'IMP.UPDATE_USER_NAME', '/', 'lbl.view.importView.column.updateUserName.block', NULL, '0', '/', '0' ;


DELETE FROM CNT_VIEW_SORTING WHERE VIEW_ID IN (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/');

INSERT INTO CNT_VIEW_SORTING(ID, REVISION, ENTITY_VERSION, VIEW_ID, FIELD_ID, SORTING_TYPE, SORTING_OPTION, SORTING_SEQ, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT LOWER(SYS_GUID()), 1, 1, (SELECT ID FROM CNT_VIEW WHERE BASE_VIEW_ID IS NULL AND NAME = 'importView' AND DOMAIN_ID = '/'), 'updatedOn', 'desc', 'blankLast', '1', '/', '/', '0' ;


