-----------------------------BEGIN CTC-8 00010.root_DATA_LIST_TYPE.sql--------------------------------
-- JiRa No.: CTC-8
-- Description: Create new datalist type IMPORT_KEY
-- Input parameter:'/'
-- Scripts re-runnable: Yes

do $BODY$
DECLARE
DOMAINID VARCHAR(200) := '/';
begin

    DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_SEQ_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_DATA_LIST_TYPE where name = 'IMPORT_KEY' and REF_NO LIKE 'DLT%'));
    UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_DATA_LIST_TYPE where name = 'IMPORT_KEY' and REF_NO LIKE 'DLT%');

    INSERT INTO CNT_DATA_LIST_TYPE(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, NAME, APPLY_TO_ENTITY, REMARKS, REF_NO, LABEL_KEY_PREFIX, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, HUB_DOMAIN_ID, IS_FOR_REFERENCE, IS_LATEST, CREATE_USER_NAME, UPDATE_USER_NAME, IS_CPM_INITIALIZED) SELECT LOWER(SYS_GUID()), 1, 1, DOMAINID, 1, null, 'active', 'confirmed', 'system', 'system', TO_TIMESTAMP('2025-04-08 14:17:29', 'YYYY-MM-DD HH24:MI:SS'), null, 'IMPORT_KEY', 'lookup', 'IMPORT_KEY', (SELECT 'DLT' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID), 'lbl.lookup.tabHeader', null, null, null, DOMAINID, '0', '1', 'system', 'system', null  
    WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE WHERE  DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 0,  'custText1',      'Text',     'Module', 'lbl.lookup.tabHeader.IMPORT_KEY.custText1',    '1', '0', 0,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 1,  'custText2',      'Text',     'Key', 'lbl.lookup.tabHeader.IMPORT_KEY.custText2',    '1', '0', 1,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 2,  'custText3',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText3',    '0', '0', 2,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 3,  'custText4',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText4',    '0', '0', 3,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 4,  'custText5',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText5',    '0', '0', 4,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 5,  'custText6',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText6',    '0', '0', 5,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 6,  'custText7',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText7',    '0', '0', 6,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 7,  'custNumber1',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber1',  '0', '0', 7,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 8,  'custNumber2',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber2',  '0', '0', 8,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 9,  'custNumber3',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber3',  '0', '0', 9,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 10, 'custNumber4',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber4',  '0', '0', 10, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 11, 'custNumber5',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber5',  '0', '0', 11, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 12, 'custNumber6',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber6',  '0', '0', 12, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 13, 'custNumber7',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber7',  '0', '0', 13, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 14, 'custDate1',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate1',    '0', '0', 14, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 15, 'custDate2',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate2',    '0', '0', 15, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 16, 'custDate3',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate3',    '0', '0', 16, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 17, 'custDate4',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate4',    '0', '0', 17, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 18, 'custDate5',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate5',    '0', '0', 18, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 19, 'custDate6',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate6',    '0', '0', 19, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 20, 'custDate7',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate7',    '0', '0', 20, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 21, 'custDecimal1',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal1', '0', '0', 21, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 22, 'custDecimal2',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal2', '0', '0', 22, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 23, 'custDecimal3',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal3', '0', '0', 23, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 24, 'custDecimal4',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal4', '0', '0', 24, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 25, 'custDecimal5',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal5', '0', '0', 25, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 26, 'custDecimal6',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal6', '0', '0', 26, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 27, 'custDecimal7',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal7', '0', '0', 27, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 28, 'custCodelist1',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist1', '0', '0', 28, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 29, 'custCodelist2',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist2', '0', '0', 29, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 30, 'custCodelist3',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist3', '0', '0', 30, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 31, 'custCodelist4',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist4', '0', '0', 31, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 32, 'custCodelist5',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist5', '0', '0', 32, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 33, 'custCodelist6',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist6', '0', '0', 33, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 34, 'custCodelist7',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist7', '0', '0', 34, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 35, 'custMemoText1',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText1', '0', '0', 35, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 36, 'custMemoText2',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText2', '0', '0', 36, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 37, 'custMemoText3',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText3', '0', '0', 37, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 38, 'custMemoText4',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText4', '0', '0', 38, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 39, 'custMemoText5',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText5', '0', '0', 39, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 40, 'custMemoText6',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText6', '0', '0', 40, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 41, 'custMemoText7',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText7', '0', '0', 41, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 42, 'custHcl1', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl1', '0', '0', 42, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 43, 'custHcl2', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl2', '0', '0', 43, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 44, 'custHcl3', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl3', '0', '0', 44, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 45, 'custHcl4', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl4', '0', '0', 45, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 46, 'custHcl5', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl5', '0', '0', 46, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl5');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 47, 'custCheckbox1',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox1', '0', '0', 47, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 48, 'custCheckbox2',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox2', '0', '0', 48, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 49, 'custCheckbox3',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox3', '0', '0', 49, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 50, 'custCheckbox4',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox4', '0', '0', 50, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 51, 'custCheckbox5',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox5', '0', '0', 51, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 52, 'custCheckbox6',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox6', '0', '0', 52, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 53, 'custCheckbox7',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox7', '0', '0', 53, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox7');

END;
$BODY$ LANGUAGE PLPGSQL;

-----------------------------END CTC-8 00010.root_DATA_LIST_TYPE.sql--------------------------------


----------------------------- BEGIN CTC-7 00030.root_CODELIST_V15_10_0.sql-----------------------------
-- JiRa No.: CTC-7
-- Description: add codelist book 'IMPORT_MODULE'
-- Input parameter:'/'
-- Scripts re-runnable: Yes
BEGIN;
DO $body$
DECLARE
DOMAINID VARCHAR(200) := '/';
BEGIN

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_CODELIST_BOOK where name = 'IMPORT_MODULE' and REF_NO LIKE 'CL%');

INSERT INTO CNT_CODELIST_BOOK(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, IS_LATEST, NAME, TYPE_ID, EFFECTIVE_FROM, EFFECTIVE_TO, DESCRIPTION, REF_NO, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER_NAME, UPDATE_USER_NAME, IS_CPM_INITIALIZED) 
SELECT SYS_GUID(), 1, 1, DOMAINID, 1, null, 'active', 'confirmed', 'system', null, CURRENT_TIMESTAMP, null, '1', 'IMPORT_MODULE', (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'Default' AND IS_LATEST = '1'), CURRENT_TIMESTAMP, null, 'IMPORT_MODULE', (SELECT 'CL' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = DOMAINID), null, null, null, DOMAINID, '0', 'system', null, null 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1');

INSERT INTO CNT_CODELIST(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, SEQ_NO, CODE, NAME, DISABLED, INTERNAL_SEQ_NO, REF_NO, business_ref_no, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
SELECT SYS_GUID(), 1, 1, DOMAINID, (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), 10, 'item', 'Item', '0', 1, 'item', 'item', DOMAINID, '0' 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CNT_CODELIST(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, SEQ_NO, CODE, NAME, DISABLED, INTERNAL_SEQ_NO, REF_NO, business_ref_no, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
SELECT SYS_GUID(), 1, 1, DOMAINID, (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), 20, 'project', 'Project', '0', 1, 'project', 'project', DOMAINID, '0' 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_TEXT6, CUST_DATE4, CUST_TEXT7, CUST_DATE3, CUST_TEXT4, CUST_DATE2, CUST_TEXT5, CUST_DATE1, CUST_TEXT2, CUST_TEXT3, CUST_DATE7, CUST_DATE6, CUST_TEXT1, CUST_DATE5, CUST_DECIMAL6, CUST_DECIMAL7, CUST_NUMBER3, CUST_NUMBER2, CUST_NUMBER1, CUST_NUMBER7, CUST_NUMBER6, CUST_NUMBER5, CUST_NUMBER4, CUST_DECIMAL1, CUST_DECIMAL2, CUST_DECIMAL3, CUST_DECIMAL4, CUST_DECIMAL5) 
SELECT (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), DOMAINID, 'CodelistBook', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_NUMBER2, CUST_NUMBER3, CUST_NUMBER1, CUST_NUMBER6, CUST_NUMBER7, CUST_NUMBER4, CUST_NUMBER5, CUST_TEXT5, CUST_DECIMAL2, CUST_TEXT4, CUST_DECIMAL3, CUST_TEXT7, CUST_DECIMAL4, CUST_DECIMAL5, CUST_TEXT6, CUST_TEXT1, CUST_TEXT3, CUST_TEXT2, CUST_DECIMAL1, CUST_DATE3, CUST_DATE4, CUST_DATE1, CUST_DATE2, CUST_DECIMAL6, CUST_DATE7, CUST_DECIMAL7, CUST_DATE5, CUST_DATE6) 
SELECT (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')), DOMAINID, 'Codelist', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_NUMBER2, CUST_NUMBER3, CUST_NUMBER1, CUST_NUMBER6, CUST_NUMBER7, CUST_NUMBER4, CUST_NUMBER5, CUST_TEXT5, CUST_DECIMAL2, CUST_TEXT4, CUST_DECIMAL3, CUST_TEXT7, CUST_DECIMAL4, CUST_DECIMAL5, CUST_TEXT6, CUST_TEXT1, CUST_TEXT3, CUST_TEXT2, CUST_DECIMAL1, CUST_DATE3, CUST_DATE4, CUST_DATE1, CUST_DATE2, CUST_DECIMAL6, CUST_DATE7, CUST_DECIMAL7, CUST_DATE5, CUST_DATE6) 
SELECT (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')), DOMAINID, 'Codelist', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE  DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')));


END;
$body$ LANGUAGE PLPGSQL;
-----------------------------END CTC-7 00030.root_CODELIST_V15_10_0.sql------------------------------


-----------------------------BEGIN CTC-8 00050.root_LOOKUPLIST.sql--------------------------------
-- JiRa No.: CTC-8
-- Description: add lookup list 'IMPORT_KEY'
-- Input parameter:'/'
-- Scripts re-runnable: Yes
DO $body$
DECLARE
DOMAINID VARCHAR(200) := '/';
BEGIN
  UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID 
  AND not exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and REF_NO LIKE 'LKU%' and DOMAIN_ID = DOMAINID);

  INSERT INTO CNT_LOOKUP_BOOK(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, DOC_STATUS, EDITING_STATUS, CREATE_USER, CREATE_USER_NAME, UPDATE_USER, UPDATE_USER_NAME, CREATED_ON, UPDATED_ON, IS_CPM_INITIALIZED, IS_LATEST, NAME, DESCRIPTION, DATA_LIST_TYPE_ID, REF_NO, BUSINESS_REF_NO) 
  select LOWER(SYS_GUID()), 1, 1, DOMAINID, DOMAINID, false, 1, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, false, true, 'IMPORT_KEY', 'IMPORT_KEY', (select id from CNT_DATA_LIST_TYPE where name='IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID) 
  where not exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID); 

  INSERT INTO CNT_LOOKUP_BOOK_M(id, revision, domain_id, hub_domain_id, VERSION, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, is_cpm_initialized, is_latest, ref_no) 
  select (select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), 1, DOMAINID, DOMAINID, 1, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, false, true, (select ref_no from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  where exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  and not exists(select 1 from CNT_LOOKUP_BOOK_M where id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest));

  INSERT INTO CNT_LOOKUP(id, revision, entity_version, domain_id, hub_domain_id, is_for_reference, internal_seq_no, duid, parent_id, disabled) 
  select LOWER(SYS_GUID()), 1, 1, DOMAINID, DOMAINID, false, 1, '1', (select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), false 
  where exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  and not exists(select 1 from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest));

  INSERT INTO CTM_LOOKUP_BOOK(id, domain_id, ref_entity_name, cust_text1, cust_text2) 
  select (select id from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)), DOMAINID, 'Lookup', 'item','itemName' 
  where exists(select 1 from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)) 
  and not exists(select 1 from CTM_LOOKUP_BOOK where id=(select id from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)));

  delete from cnt_serialized_entity where target_entity = 'LookupBook';
END;
$body$ LANGUAGE PLPGSQL;

-----------------------------END CTC-8 00050.root_LOOKUPLIST.sql------------------------------


----------------------------- BEGIN CTC-26 cbx-biz-15.10.0-update-06-dml-postdeploy.sql---------------------------
update cnt_domain_attribute set value=value||',import' where key='cbx.elasticsearch.enabled.modules' and domain_id ='/' and value not like '%import%';

update cnt_domain_attribute set value=value||',import' where key='cbx.concurrent.edit.enabled.modules' and domain_id ='/' and value not like '%import%';

update cnt_domain_attribute set value=value||',import.importMappingList:100' where key='ui.grid.paging.config' and value not like '%import.importMappingList%';

update cnt_domain_attribute set value=value||',import' where key='cbx.concurrent.edit.enabled.modules' and domain_id ='QA01' and value not like '%import%';

-- INSERT INTO cnt_seq_def(id, revision, entity_version, domain_id, hub_domain_id, is_for_reference, internal_seq_no, duid, seq_id, start_with, max_value, increment_by, "cycle", cache_size, updated_on, next_val, cycle_started_on) 
-- select SYS_GUID(),1, 1,'QA01','QA01',false,NULL,NULL,'CBX_SEQ_IMPORT_NO',1,999999,1, 'NULL' ,0,'2025-04-07 03:46:52.000',1,'2025-04-07 03:46:52.000' 
-- WHERE NOT EXISTS(SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID='QA01' AND SEQ_ID='CBX_SEQ_IMPORT_NO');
-----------------------------END CTC-26 cbx-biz-15.10.0-update-06-dml-postdeploy.sql------------------------------


-----------------------------BEGIN CTC-8 CTC-26_IMPORT_KEY_datalist_type.sql--------------------------------
-- JiRa No.: CTC-8
-- Description: Create new datalist type IMPORT_KEY
-- Input parameter:'/'

do $BODY$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
begin

    DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_SEQ_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_DATA_LIST_TYPE where name = 'IMPORT_KEY' and REF_NO LIKE 'DLT%'));
    UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_DATA_LIST_TYPE where name = 'IMPORT_KEY' and REF_NO LIKE 'DLT%');

    INSERT INTO CNT_DATA_LIST_TYPE(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, NAME, APPLY_TO_ENTITY, REMARKS, REF_NO, LABEL_KEY_PREFIX, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, HUB_DOMAIN_ID, IS_FOR_REFERENCE, IS_LATEST, CREATE_USER_NAME, UPDATE_USER_NAME, IS_CPM_INITIALIZED) SELECT LOWER(SYS_GUID()), 1, 1, DOMAINID, 1, null, 'active', 'confirmed', 'system', 'system', TO_TIMESTAMP('2025-04-08 14:17:29', 'YYYY-MM-DD HH24:MI:SS'), null, 'IMPORT_KEY', 'lookup', 'IMPORT_KEY', (SELECT 'DLT' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID), 'lbl.lookup.tabHeader', null, null, null, DOMAINID, '0', '1', 'system', 'system', null  
    WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE WHERE  DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 0,  'custText1',      'Text',     'Module', 'lbl.lookup.tabHeader.IMPORT_KEY.custText1',    '1', '0', 0,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 1,  'custText2',      'Text',     'Key', 'lbl.lookup.tabHeader.IMPORT_KEY.custText2',    '1', '0', 1,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 2,  'custText3',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText3',    '0', '0', 2,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 3,  'custText4',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText4',    '0', '0', 3,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 4,  'custText5',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText5',    '0', '0', 4,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 5,  'custText6',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText6',    '0', '0', 5,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 6,  'custText7',      'Text',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custText7',    '0', '0', 6,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custText7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 7,  'custNumber1',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber1',  '0', '0', 7,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 8,  'custNumber2',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber2',  '0', '0', 8,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 9,  'custNumber3',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber3',  '0', '0', 9,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 10, 'custNumber4',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber4',  '0', '0', 10, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 11, 'custNumber5',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber5',  '0', '0', 11, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 12, 'custNumber6',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber6',  '0', '0', 12, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 13, 'custNumber7',    'Number',   null, 'lbl.lookup.tabHeader.IMPORT_KEY.custNumber7',  '0', '0', 13, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 14, 'custDate1',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate1',    '0', '0', 14, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 15, 'custDate2',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate2',    '0', '0', 15, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 16, 'custDate3',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate3',    '0', '0', 16, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 17, 'custDate4',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate4',    '0', '0', 17, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 18, 'custDate5',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate5',    '0', '0', 18, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 19, 'custDate6',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate6',    '0', '0', 19, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 20, 'custDate7',      'Date',     null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDate7',    '0', '0', 20, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 21, 'custDecimal1',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal1', '0', '0', 21, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 22, 'custDecimal2',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal2', '0', '0', 22, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 23, 'custDecimal3',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal3', '0', '0', 23, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 24, 'custDecimal4',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal4', '0', '0', 24, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 25, 'custDecimal5',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal5', '0', '0', 25, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 26, 'custDecimal6',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal6', '0', '0', 26, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 27, 'custDecimal7',   'Decimal',  null, 'lbl.lookup.tabHeader.IMPORT_KEY.custDecimal7', '0', '0', 27, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 28, 'custCodelist1',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist1', '0', '0', 28, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 29, 'custCodelist2',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist2', '0', '0', 29, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 30, 'custCodelist3',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist3', '0', '0', 30, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 31, 'custCodelist4',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist4', '0', '0', 31, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 32, 'custCodelist5',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist5', '0', '0', 32, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 33, 'custCodelist6',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist6', '0', '0', 33, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 34, 'custCodelist7',  'Codelist', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCodelist7', '0', '0', 34, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 35, 'custMemoText1',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText1', '0', '0', 35, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 36, 'custMemoText2',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText2', '0', '0', 36, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 37, 'custMemoText3',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText3', '0', '0', 37, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 38, 'custMemoText4',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText4', '0', '0', 38, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 39, 'custMemoText5',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText5', '0', '0', 39, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 40, 'custMemoText6',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText6', '0', '0', 40, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 41, 'custMemoText7',  'TextArea', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custMemoText7', '0', '0', 41, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 42, 'custHcl1', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl1', '0', '0', 42, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 43, 'custHcl2', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl2', '0', '0', 43, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 44, 'custHcl3', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl3', '0', '0', 44, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 45, 'custHcl4', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl4', '0', '0', 45, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 46, 'custHcl5', 	   'HclGroup', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custHcl5', '0', '0', 46, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custHcl5');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 47, 'custCheckbox1',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox1', '0', '0', 47, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 48, 'custCheckbox2',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox2', '0', '0', 48, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 49, 'custCheckbox3',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox3', '0', '0', 49, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 50, 'custCheckbox4',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox4', '0', '0', 50, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 51, 'custCheckbox5',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox5', '0', '0', 51, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 52, 'custCheckbox6',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox6', '0', '0', 52, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1'), 53, 'custCheckbox7',  'Checkbox', null, 'lbl.lookup.tabHeader.IMPORT_KEY.custCheckbox7', '0', '0', 53, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_KEY' AND IS_LATEST = '1') AND  FIELD_ID = 'custCheckbox7');

END;
$BODY$ LANGUAGE PLPGSQL;

-----------------------------END CTC-8 CTC-26_IMPORT_KEY_datalist_type.sql--------------------------------


-----------------------------BEGIN CTC-8 CTC-26_IMPORT_KEY_lookuplist.sql--------------------------------
-- JiRa No.: CTC-8
-- Description: add lookup list 'IMPORT_KEY'
-- Input parameter:'/'
-- Scripts re-runnable: Yes
DO $body$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
BEGIN
  UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID 
  AND not exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and REF_NO LIKE 'LKU%' and DOMAIN_ID = DOMAINID);

  INSERT INTO CNT_LOOKUP_BOOK(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, DOC_STATUS, EDITING_STATUS, CREATE_USER, CREATE_USER_NAME, UPDATE_USER, UPDATE_USER_NAME, CREATED_ON, UPDATED_ON, IS_CPM_INITIALIZED, IS_LATEST, NAME, DESCRIPTION, DATA_LIST_TYPE_ID, REF_NO, BUSINESS_REF_NO) 
  select LOWER(SYS_GUID()), 1, 1, DOMAINID, DOMAINID, false, 1, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, false, true, 'IMPORT_KEY', 'IMPORT_KEY', (select id from CNT_DATA_LIST_TYPE where name='IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID) 
  where not exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID); 

  INSERT INTO CNT_LOOKUP_BOOK_M(id, revision, domain_id, hub_domain_id, VERSION, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, is_cpm_initialized, is_latest, ref_no) 
  select (select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), 1, DOMAINID, DOMAINID, 1, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, false, true, (select ref_no from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  where exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  and not exists(select 1 from CNT_LOOKUP_BOOK_M where id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest));

  INSERT INTO CNT_LOOKUP(id, revision, entity_version, domain_id, hub_domain_id, is_for_reference, internal_seq_no, duid, parent_id, disabled) 
  select LOWER(SYS_GUID()), 1, 1, DOMAINID, DOMAINID, false, 1, '1', (select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), false 
  where exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  and not exists(select 1 from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest));

  INSERT INTO CTM_LOOKUP_BOOK(id, domain_id, ref_entity_name, cust_text1, cust_text2) 
  select (select id from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)), DOMAINID, 'Lookup', 'item','itemName' 
  where exists(select 1 from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)) 
  and not exists(select 1 from CTM_LOOKUP_BOOK where id in (select id from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)));

  delete from cnt_serialized_entity where target_entity = 'LookupBook';
END;
$body$ LANGUAGE PLPGSQL;

-----------------------------END CTC-8 CTC-26_IMPORT_KEY_lookuplist.sql------------------------------


----------------------------- BEGIN CTC-7 CTC-26_IMPORT_MODULE_codelist.sql-----------------------------
-- JiRa No.: CTC-7
-- Description: add codelist book 'IMPORT_MODULE'
-- Input parameter:'/'
-- Scripts re-runnable: Yes
BEGIN;
DO $body$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
BEGIN

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_CODELIST_BOOK where name = 'IMPORT_MODULE' and REF_NO LIKE 'CL%');

INSERT INTO CNT_CODELIST_BOOK(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, IS_LATEST, NAME, TYPE_ID, EFFECTIVE_FROM, EFFECTIVE_TO, DESCRIPTION, REF_NO, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER_NAME, UPDATE_USER_NAME, IS_CPM_INITIALIZED) 
SELECT SYS_GUID(), 1, 1, DOMAINID, 1, null, 'active', 'confirmed', 'system', null, CURRENT_TIMESTAMP, null, '1', 'IMPORT_MODULE', (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'Default' AND IS_LATEST = '1'), CURRENT_TIMESTAMP, null, 'IMPORT_MODULE', (SELECT 'CL' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = DOMAINID), null, null, null, DOMAINID, '0', 'system', null, null 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1');

INSERT INTO CNT_CODELIST(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, SEQ_NO, CODE, NAME, DISABLED, INTERNAL_SEQ_NO, REF_NO, business_ref_no, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
SELECT SYS_GUID(), 1, 1, DOMAINID, (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), 10, 'item', 'Item', '0', 1, 'item', 'item', DOMAINID, '0' 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CNT_CODELIST(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, SEQ_NO, CODE, NAME, DISABLED, INTERNAL_SEQ_NO, REF_NO, business_ref_no, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
SELECT SYS_GUID(), 1, 1, DOMAINID, (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), 20, 'project', 'Project', '0', 1, 'project', 'project', DOMAINID, '0' 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_TEXT6, CUST_DATE4, CUST_TEXT7, CUST_DATE3, CUST_TEXT4, CUST_DATE2, CUST_TEXT5, CUST_DATE1, CUST_TEXT2, CUST_TEXT3, CUST_DATE7, CUST_DATE6, CUST_TEXT1, CUST_DATE5, CUST_DECIMAL6, CUST_DECIMAL7, CUST_NUMBER3, CUST_NUMBER2, CUST_NUMBER1, CUST_NUMBER7, CUST_NUMBER6, CUST_NUMBER5, CUST_NUMBER4, CUST_DECIMAL1, CUST_DECIMAL2, CUST_DECIMAL3, CUST_DECIMAL4, CUST_DECIMAL5) 
SELECT (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), DOMAINID, 'CodelistBook', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_NUMBER2, CUST_NUMBER3, CUST_NUMBER1, CUST_NUMBER6, CUST_NUMBER7, CUST_NUMBER4, CUST_NUMBER5, CUST_TEXT5, CUST_DECIMAL2, CUST_TEXT4, CUST_DECIMAL3, CUST_TEXT7, CUST_DECIMAL4, CUST_DECIMAL5, CUST_TEXT6, CUST_TEXT1, CUST_TEXT3, CUST_TEXT2, CUST_DECIMAL1, CUST_DATE3, CUST_DATE4, CUST_DATE1, CUST_DATE2, CUST_DECIMAL6, CUST_DATE7, CUST_DECIMAL7, CUST_DATE5, CUST_DATE6) 
SELECT (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')), DOMAINID, 'Codelist', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_NUMBER2, CUST_NUMBER3, CUST_NUMBER1, CUST_NUMBER6, CUST_NUMBER7, CUST_NUMBER4, CUST_NUMBER5, CUST_TEXT5, CUST_DECIMAL2, CUST_TEXT4, CUST_DECIMAL3, CUST_TEXT7, CUST_DECIMAL4, CUST_DECIMAL5, CUST_TEXT6, CUST_TEXT1, CUST_TEXT3, CUST_TEXT2, CUST_DECIMAL1, CUST_DATE3, CUST_DATE4, CUST_DATE1, CUST_DATE2, CUST_DECIMAL6, CUST_DATE7, CUST_DECIMAL7, CUST_DATE5, CUST_DATE6) 
SELECT (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')), DOMAINID, 'Codelist', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE  DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')));


END;
$body$ LANGUAGE PLPGSQL;
-----------------------------END CTC-7 CTC-26_IMPORT_MODULE_codelist.sql------------------------------

-----------------------------BEGIN others.txt ------------------------------
INSERT INTO public.cnt_label
(id, revision, entity_version, hub_domain_id, is_for_reference, "version", status, doc_status, editing_status, create_user_name, update_user_name, integration_source, integration_status, integration_note, is_cpm_initialized, is_latest, label_id, locale, "type", module_code, "label", ref_no, business_ref_no, domain_id, create_user, update_user, created_on, updated_on)
select LOWER(SYS_GUID()), NULL, NULL, '/', false, 1, NULL, 'active', 'confirmed', NULL, NULL, NULL, NULL, NULL, NULL, true, 'lbl.status.import.new', 'en_US', 'label', 'import', 'New', NULL, NULL, '/', NULL, NULL, NULL, NULL
where not exists (select 1 from cnt_label where label_id = 'lbl.status.import.new' and domain_id= '/' and locale = 'en_US');
-----------------------------END others.txt------------------------------