-- default profile
update cnt_seq_def 
	set next_val = (select LTRIM(split_part(ref_no, '-', 2), '0') :: numeric + 1 from cnt_default_profile cdp where is_latest and domain_id  = 'PEPL' and ref_no like 'DF%' order by ref_no desc limit 1),
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_DEFAULT_PROFILE_REF_NO';

-- lookup
update cnt_seq_def 
	set next_val = (select LTRIM(split_part(ref_no, '-', 2), '0') :: numeric + 1 from cnt_lookup_book where is_latest and domain_id  = 'PEPL' and ref_no like 'LKU%' order by ref_no desc limit 1),
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_LOOKUP_BOOK_REF_NO';

-- data list type
update cnt_seq_def 
	set next_val = (select LTRIM(split_part(ref_no, '-', 2), '0') :: numeric + 1 from cnt_data_list_type where is_latest and domain_id  = 'PEPL' and ref_no like 'DLT%' order by ref_no desc limit 1),
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO';

-- codelist
update cnt_seq_def 
	set next_val = (select LTRIM(split_part(ref_no, '-', 2), '0') :: numeric + 1 from cnt_codelist_book where is_latest and domain_id  = 'PEPL' and ref_no like 'CL%' order by ref_no desc limit 1),
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_CODE_LIST_REF_NO';

-- notification profile
update cnt_seq_def 
	set next_val = (select LTRIM(split_part(ref_no, '-', 2), '0') :: numeric + 1 from cnt_notification_profile where is_latest and domain_id  = 'PEPL' and ref_no like 'NTF%' order by ref_no desc limit 1),
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_NOTIFICATION_REF_NO';

-- trigger
update cnt_seq_def 
	set next_val = (select LTRIM(split_part(ref_no, '-', 2), '0') :: numeric + 1 from cnt_trigger where is_latest and domain_id  = 'PEPL' and ref_no like 'EVT%' order by ref_no desc limit 1),
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_EVENT_REF_NO';



commit;