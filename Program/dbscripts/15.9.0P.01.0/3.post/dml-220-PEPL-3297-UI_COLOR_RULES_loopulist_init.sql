-----------------------------BEGIN CTC-49--------------------------------
-- JiRa No.: CTC-49
-- Description: Create lookuplist UI_COLOR_RULES and update
-- Input parameter: <DOMAIN_ID>
-- Scripts re-runnable: Yes

do $BODY$
DECLARE
  V_DOMAIN_ID VARCHAR(200) := 'PEPL';
  V_LOOKUP_BOOK_NAME VARCHAR(800) := 'UI_COLOR_RULES';
  V_INTERNAL_SEQ_NO numeric(20,0);
BEGIN

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = V_DOMAIN_ID AND not exists(select 1 from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and REF_NO LIKE 'LKU%' and DOMAIN_ID = V_DOMAIN_ID);

INSERT INTO CNT_LOOKUP_BOOK
(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, CREATE_USER_NAME, UPDATE_USER, UPDATE_USER_NAME, CREATED_ON, UPDATED_ON, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, IS_CPM_INITIALIZED, IS_LATEST, NAME, DESCRIPTION, DATA_LIST_TYPE_ID, REF_NO, BUSINESS_REF_NO) 
select LOWER(SYS_GUID()), 1, 1, V_DOMAIN_ID, V_DOMAIN_ID, false, 1, NULL, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, NULL, NULL, NULL, false, true, V_LOOKUP_BOOK_NAME, NULL, (select id from CNT_DATA_LIST_TYPE where name = V_LOOKUP_BOOK_NAME  and DOMAIN_ID = V_DOMAIN_ID and is_latest), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = V_DOMAIN_ID), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = V_DOMAIN_ID)
where not exists (select 1 from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest);

INSERT INTO CNT_LOOKUP_BOOK_M
(id, revision, domain_id, hub_domain_id, "version", status, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, integration_source, integration_status, integration_note, is_cpm_initialized, is_latest, ref_no, party_template_ref, party_template_ver, party_template_name, party_name1, party_name2, party_name3, party_name4, party_name5) 
select (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest), 1, V_DOMAIN_ID, V_DOMAIN_ID, 1, NULL, 'active', 'confirmed', 'system', 'system', null, null, current_timestamp, null, NULL, NULL, NULL, false, true, (select ref_no from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest), NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL 
where not exists (select 1 from CNT_LOOKUP_BOOK_M where id = (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest));

select COALESCE(MAX(internal_seq_no), 1) into V_INTERNAL_SEQ_NO from cnt_lookup cl where cl.parent_id = (select id from cnt_lookup_book where domain_id = V_DOMAIN_ID and is_latest and name = V_LOOKUP_BOOK_NAME );


insert into cnt_lookup (id,revision ,entity_version ,domain_id ,parent_id ,disabled ,internal_seq_no ,hub_domain_id ,is_for_reference) 
select SYS_GUID(), 1, 1, V_DOMAIN_ID, (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest), false, (V_INTERNAL_SEQ_NO+1), V_DOMAIN_ID, false 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Pending''');

insert into ctm_lookup_book (id,domain_id,ref_entity_name,cust_text1,cust_text2)
select (select id from cnt_lookup cl where cl.internal_seq_no =(V_INTERNAL_SEQ_NO+1) and cl.parent_id = (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest)) , V_DOMAIN_ID, 'Lookup', 'color-status-pale-blue','import.importMappingList.importStatus=''Pending''' 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Pending''');


insert into cnt_lookup (id,revision ,entity_version ,domain_id ,parent_id ,disabled ,internal_seq_no ,hub_domain_id ,is_for_reference) 
select SYS_GUID(), 1, 1, V_DOMAIN_ID, (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest), false, (V_INTERNAL_SEQ_NO+2), V_DOMAIN_ID, false 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Created''');

insert into ctm_lookup_book (id,domain_id,ref_entity_name,cust_text1,cust_text2)
select (select id from cnt_lookup cl where cl.internal_seq_no =(V_INTERNAL_SEQ_NO+2) and cl.parent_id = (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest)) , V_DOMAIN_ID, 'Lookup', 'color-status-green','import.importMappingList.importStatus=''Created''' 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Created''');


insert into cnt_lookup (id,revision ,entity_version ,domain_id ,parent_id ,disabled ,internal_seq_no ,hub_domain_id ,is_for_reference) 
select SYS_GUID(), 1, 1, V_DOMAIN_ID, (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest), false, (V_INTERNAL_SEQ_NO+3), V_DOMAIN_ID, false 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Updated''');

insert into ctm_lookup_book (id,domain_id,ref_entity_name,cust_text1,cust_text2)
select (select id from cnt_lookup cl where cl.internal_seq_no =(V_INTERNAL_SEQ_NO+3) and cl.parent_id = (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest)) , V_DOMAIN_ID, 'Lookup', 'color-status-green','import.importMappingList.importStatus=''Updated''' 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Updated''');


insert into cnt_lookup (id,revision ,entity_version ,domain_id ,parent_id ,disabled ,internal_seq_no ,hub_domain_id ,is_for_reference) 
select SYS_GUID(), 1, 1, V_DOMAIN_ID, (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest), false, (V_INTERNAL_SEQ_NO+4), V_DOMAIN_ID, false 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Failed''');

insert into ctm_lookup_book (id,domain_id,ref_entity_name,cust_text1,cust_text2)
select (select id from cnt_lookup cl where cl.internal_seq_no =(V_INTERNAL_SEQ_NO+4) and cl.parent_id = (select id from CNT_LOOKUP_BOOK where name = V_LOOKUP_BOOK_NAME and DOMAIN_ID = V_DOMAIN_ID and is_latest)) , V_DOMAIN_ID, 'Lookup', 'color-status-red','import.importMappingList.importStatus=''Failed''' 
where not exists(select 1 from ctm_lookup_book where cust_text2='import.importMappingList.importStatus=''Failed''');

END;
$BODY$ LANGUAGE plpgsql;

-------------------------------END CTC-49--------------------------------

