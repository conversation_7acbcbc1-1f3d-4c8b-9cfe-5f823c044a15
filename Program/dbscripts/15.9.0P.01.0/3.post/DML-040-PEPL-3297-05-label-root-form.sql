--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import', 'en_US', '\', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LA<PERSON>L_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.docStatus', 'en_US', '\', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.header.docStatus' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.headerIntegration', 'en_US', '\', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.header.headerIntegration' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.headerName', 'en_US', '\', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.header.headerName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.linkbar', 'en_US', '\', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.header.linkbar' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.status', 'en_US', '\', 'Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.header.status' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.version', 'en_US', '\', 'Version', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.header.version' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.actionsGroup', 'en_US', '\', 'Actions', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.actionsGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.actionsGroup.copyDoc', 'en_US', '\', 'Copy', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.actionsGroup.copyDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.amendDoc', 'en_US', '\', 'Amend', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.amendDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.createGroup', 'en_US', '\', 'Create', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.createGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.createGroup.openImportPopupWin', 'en_US', '\', 'New Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.createGroup.openImportPopupWin' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup', 'en_US', '\', 'Mark as', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.activateDoc', 'en_US', '\', 'Active', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.activateDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.cancelDoc', 'en_US', '\', 'Canceled', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.cancelDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.deactivateDoc', 'en_US', '\', 'Inactive', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.deactivateDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus01', 'en_US', '\', 'Custom Status 1', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus01' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus02', 'en_US', '\', 'Custom Status 2', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus02' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus03', 'en_US', '\', 'Custom Status 3', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus03' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus04', 'en_US', '\', 'Custom Status 4', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus04' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus05', 'en_US', '\', 'Custom Status 5', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus05' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus06', 'en_US', '\', 'Custom Status 6', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus06' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus07', 'en_US', '\', 'Custom Status 7', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus07' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus08', 'en_US', '\', 'Custom Status 8', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus08' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus09', 'en_US', '\', 'Custom Status 9', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus09' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus10', 'en_US', '\', 'Custom Status 10', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus10' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.toolsGroup', 'en_US', '\', 'Tools', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.toolsGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.toolsGroup.importRawData', 'en_US', '\', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.importMenubar.toolsGroup.importRawData' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader', 'en_US', '\', 'Header', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection', 'en_US', '\', 'Data', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.dataSection' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection.importKeyDisplay', 'en_US', '\', 'Key', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.dataSection.importKeyDisplay' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection.importModule', 'en_US', '\', 'Module', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.dataSection.importModule' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection.importTable', 'en_US', '\', 'Table', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.dataSection.importTable' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection', 'en_US', '\', 'General Information', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.fileId', 'en_US', '\', 'File', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.fileId' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.headerRow', 'en_US', '\', 'Header Row', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.headerRow' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.importNo', 'en_US', '\', 'Import No.', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.importNo' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.startRow', 'en_US', '\', 'Start Row', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.startRow' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings', 'en_US', '\', 'Mapping & Validation', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.importMappings' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.error', 'en_US', '\', 'Error', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.importMappings.error' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.excelLine', 'en_US', '\', 'Excel Line', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.importMappings.excelLine' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.importStatus', 'en_US', '\', 'Import Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.importMappings.importStatus' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.startImport', 'en_US', '\', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.import.tabHeader.importMappings.startImport' AND LOCALE='en_US');
