create table if not exists bak_cnt_look_up_book_m_pepl_3300_20250729 as (
    select clb.id, clb.ref_no, clb."version"  
    from cnt_lookup_book clb 
    where clb.domain_id = 'PEPL' 
      and clb.is_latest 
      and not exists (
          select 1 
          from cnt_lookup_book_m clbm 
          where clbm.id = clb.id
      )
);

INSERT INTO cnt_lookup_book_m
(id, revision, domain_id, hub_domain_id, "version", status, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, integration_source, integration_status, integration_note, is_cpm_initialized, is_latest, ref_no, party_template_ref, party_template_ver, party_template_name, party_name1, party_name2, party_name3, party_name4, party_name5)
select clb.id, clb.revision, clb.domain_id, clb.hub_domain_id, clb."version", NULL, clb.doc_status, clb.editing_status, clb.create_user, clb.create_user_name, clb.update_user, clb.update_user_name, clb.created_on, clb.updated_on, NULL, NULL, NULL, NULL, clb.is_latest, clb.ref_no, NULL, NULL, NULL, NULL, NULL, NULL, NULL, null
from cnt_lookup_book clb 
where clb.id in (select id from bak_cnt_look_up_book_m_pepl_3300_20250729)
  and not exists (
      select 1 
      from cnt_lookup_book_m clbm 
      where clbm.id = clb.id
  );
 
delete from cnt_serialized_entity where target_id in (select id from bak_cnt_look_up_book_m_pepl_3300_20250729);
