DO $BODY$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';

BEGIN

--// FILE: DML_ACL_import_view.sql

--// ACL: import_view

INSERT INTO CNT_ACCESS_OBJECT(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, OBJECT_ID, REF_NO, OBJECT_TYPE, OBJECT_VERSION, DOMAIN_ID, DESCN, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import', 'system', 'system', TO_TIMESTAMP('2025-07-06 15:00:06', 'YYYY-MM-DD HH24:MI:SS'), 'import', 'view' || ':' || 'import', 'view', 1, DOMAINID, NULL, '1', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ACCESS_OBJECT') AND TARGET_ID IN (SELECT ID FROM CNT_ACCESS_OBJECT WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));

UPDATE CNT_ACCESS_OBJECT SET REVISION = REVISION+1,ENTITY_VERSION = 1,DOC_STATUS = 'active',NAME = 'import',OBJECT_ID = 'import',REF_NO = 'view' || ':' || 'import',OBJECT_TYPE = 'view',OBJECT_VERSION = 1,DOMAIN_ID = DOMAINID,DESCN = NULL,IS_LATEST = '1',CREATE_USER = 'system',CREATE_USER_NAME = 'system',CREATED_ON = TO_TIMESTAMP('2025-07-06 15:00:06', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');





DELETE FROM CNT_ACCESS_OBJECT_ACTION WHERE DOMAIN_ID = DOMAINID AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'searchActivateDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'searchCancelDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'searchDeactivateDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'openImportPopupWin');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'importRawData');




DELETE FROM CNT_ACCESS_OBJ_ACT_CONDITION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') ;




DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;






DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchActivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchCancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'searchDeactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'importRawData', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '67aaff0acfc14f728bb812d3a4be4ab0', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='67aaff0acfc14f728bb812d3a4be4ab0');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '67aaff0acfc14f728bb812d3a4be4ab0', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='67aaff0acfc14f728bb812d3a4be4ab0');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '67aaff0acfc14f728bb812d3a4be4ab0', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='67aaff0acfc14f728bb812d3a4be4ab0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '67aaff0acfc14f728bb812d3a4be4ab0', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='67aaff0acfc14f728bb812d3a4be4ab0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchActivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '67aaff0acfc14f728bb812d3a4be4ab0', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='67aaff0acfc14f728bb812d3a4be4ab0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchCancelDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '67aaff0acfc14f728bb812d3a4be4ab0', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='67aaff0acfc14f728bb812d3a4be4ab0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchDeactivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '67aaff0acfc14f728bb812d3a4be4ab0', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='67aaff0acfc14f728bb812d3a4be4ab0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '67aaff0acfc14f728bb812d3a4be4ab0', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='67aaff0acfc14f728bb812d3a4be4ab0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='importRawData');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT 'cd2457e47f184f55a060e5717847c4c5', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='cd2457e47f184f55a060e5717847c4c5');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT 'cd2457e47f184f55a060e5717847c4c5', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='cd2457e47f184f55a060e5717847c4c5');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cd2457e47f184f55a060e5717847c4c5', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='cd2457e47f184f55a060e5717847c4c5' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cd2457e47f184f55a060e5717847c4c5', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='cd2457e47f184f55a060e5717847c4c5' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchActivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cd2457e47f184f55a060e5717847c4c5', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='cd2457e47f184f55a060e5717847c4c5' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchCancelDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cd2457e47f184f55a060e5717847c4c5', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='cd2457e47f184f55a060e5717847c4c5' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchDeactivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cd2457e47f184f55a060e5717847c4c5', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='cd2457e47f184f55a060e5717847c4c5' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cd2457e47f184f55a060e5717847c4c5', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='cd2457e47f184f55a060e5717847c4c5' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='importRawData');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '9577b6830c8043b58753106e0b3dbf9d', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='9577b6830c8043b58753106e0b3dbf9d');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '9577b6830c8043b58753106e0b3dbf9d', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='9577b6830c8043b58753106e0b3dbf9d');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9577b6830c8043b58753106e0b3dbf9d', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='9577b6830c8043b58753106e0b3dbf9d' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9577b6830c8043b58753106e0b3dbf9d', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='9577b6830c8043b58753106e0b3dbf9d' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchActivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9577b6830c8043b58753106e0b3dbf9d', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='9577b6830c8043b58753106e0b3dbf9d' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchCancelDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9577b6830c8043b58753106e0b3dbf9d', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='9577b6830c8043b58753106e0b3dbf9d' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchDeactivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9577b6830c8043b58753106e0b3dbf9d', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='9577b6830c8043b58753106e0b3dbf9d' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9577b6830c8043b58753106e0b3dbf9d', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='9577b6830c8043b58753106e0b3dbf9d' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='importRawData');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '1c001f5d070747328404b85586734a88', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='1c001f5d070747328404b85586734a88');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '1c001f5d070747328404b85586734a88', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='1c001f5d070747328404b85586734a88');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c001f5d070747328404b85586734a88', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c001f5d070747328404b85586734a88' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c001f5d070747328404b85586734a88', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c001f5d070747328404b85586734a88' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchActivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c001f5d070747328404b85586734a88', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c001f5d070747328404b85586734a88' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchCancelDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c001f5d070747328404b85586734a88', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c001f5d070747328404b85586734a88' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchDeactivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c001f5d070747328404b85586734a88', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c001f5d070747328404b85586734a88' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c001f5d070747328404b85586734a88', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c001f5d070747328404b85586734a88' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='importRawData');



DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '0a4c4c8539d3435ebf0759f9f5c56d0c', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'view', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'view' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='0a4c4c8539d3435ebf0759f9f5c56d0c');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '0a4c4c8539d3435ebf0759f9f5c56d0c', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='0a4c4c8539d3435ebf0759f9f5c56d0c');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0a4c4c8539d3435ebf0759f9f5c56d0c', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='0a4c4c8539d3435ebf0759f9f5c56d0c' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0a4c4c8539d3435ebf0759f9f5c56d0c', 'RuleActionAdmin', 'actionId', null, null, 'searchActivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='0a4c4c8539d3435ebf0759f9f5c56d0c' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchActivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0a4c4c8539d3435ebf0759f9f5c56d0c', 'RuleActionAdmin', 'actionId', null, null, 'searchCancelDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='0a4c4c8539d3435ebf0759f9f5c56d0c' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchCancelDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0a4c4c8539d3435ebf0759f9f5c56d0c', 'RuleActionAdmin', 'actionId', null, null, 'searchDeactivateDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='0a4c4c8539d3435ebf0759f9f5c56d0c' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='searchDeactivateDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0a4c4c8539d3435ebf0759f9f5c56d0c', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='0a4c4c8539d3435ebf0759f9f5c56d0c' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '0a4c4c8539d3435ebf0759f9f5c56d0c', 'RuleActionAdmin', 'actionId', null, null, 'importRawData' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='0a4c4c8539d3435ebf0759f9f5c56d0c' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='importRawData');



DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='view';


END;
$BODY$ LANGUAGE PLPGSQL;

delete from cnt_serialized_entity cse where target_entity like 'Role%';

