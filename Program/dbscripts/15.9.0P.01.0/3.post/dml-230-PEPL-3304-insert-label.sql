--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'fact', 'lbl.fact.tabEnergyConsumption', 'en_US', 'PEPL', 'Energy Consumption', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.fact.tabEnergyConsumption' AND LOCALE='en_US');


--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'fact', 'lbl.fact.tabEnergyConsumption', 'en_US', '\', 'Energy Consumption', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.fact.tabEnergyConsumption' AND LOCALE='en_US');
