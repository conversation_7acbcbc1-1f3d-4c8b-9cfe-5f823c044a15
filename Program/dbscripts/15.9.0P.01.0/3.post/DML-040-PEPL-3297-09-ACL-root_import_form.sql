--// FILE: DML_ACL_import_form.sql

--// ACL: import_form

INSERT INTO CNT_ACCESS_OBJECT(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, OBJECT_ID, REF_NO, OBJECT_TYPE, OBJECT_VERSION, DOMAIN_ID, DESCN, SYSBASE_RULE_CHECKSUM, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:52:07', 'YYYY-MM-DD HH24:MI:SS'), 'import', 'form' || ':' || 'import', 'form', 1, '/', NULL, 'cdafda13e12c9d4558c063747cacee38e70708ecc29d3948c4d4abd51cf1940d', '1', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ACCESS_OBJECT') AND TARGET_ID IN (SELECT ID FROM CNT_ACCESS_OBJECT WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'));

UPDATE CNT_ACCESS_OBJECT SET REVISION = REVISION+1,ENTITY_VERSION = 1,DOC_STATUS = 'active',NAME = 'import',OBJECT_ID = 'import',REF_NO = 'form' || ':' || 'import',OBJECT_TYPE = 'form',OBJECT_VERSION = 1,DOMAIN_ID = '/',DESCN = NULL,SYSBASE_RULE_CHECKSUM = 'cdafda13e12c9d4558c063747cacee38e70708ecc29d3948c4d4abd51cf1940d',IS_LATEST = '1',CREATE_USER = 'system',CREATE_USER_NAME = 'system',CREATED_ON = TO_TIMESTAMP('2025-07-30 14:52:07', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1');





DELETE FROM CNT_ACCESS_OBJECT_ACTION WHERE DOMAIN_ID = '/' AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'openImportPopupWin');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'amendDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'saveDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveAndConfirm', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'saveAndConfirm');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'doneDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'SnapshotDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'SnapshotDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'discardDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'activateDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'deactivateDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'cancelDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, '/', (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', '/', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ACTION_ID = 'loadDoc');




DELETE FROM CNT_ACCESS_OBJECT_CONDITION WHERE DOMAIN_ID = '/' AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', '/', '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', '/', '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', '/', '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', '/', '0' ;




DELETE FROM CNT_ACCESS_OBJ_ACT_CONDITION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, '/', '/', '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') ;


INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, '/', '/', '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') ;


INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, '/', '/', '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') ;


INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, '/', '/', '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') ;




DELETE FROM CNT_RULE_TEMPLATE_ACL WHERE PARENT_ID IN (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME LIKE 'import-workflowStatus%');

DELETE FROM CNT_RULE_TEMPLATE WHERE NAME LIKE 'import-workflowStatus%';

DELETE FROM CNT_RULE_ACTION_TEMPLATE WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-1', 'workflowStatus', '1', '1', '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveAndConfirm', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-2', 'workflowStatus', '1', '1', '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 2, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 2, '/', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-3', 'workflowStatus', '1', '1', '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 2, '/', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'SnapshotDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-4', 'workflowStatus', '1', '1', '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 2, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = '/'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1, '/', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = '/'), '/', 'workflowStatus', '/', '0' ;








DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '2245ef228e454425b5b32db508a9a10e', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '2245ef228e454425b5b32db508a9a10e', '/', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2245ef228e454425b5b32db508a9a10e', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'e5763b3621a64aef8724a5c79892c7ac', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'e5763b3621a64aef8724a5c79892c7ac', '/', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'e5763b3621a64aef8724a5c79892c7ac', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '59261d4932aa4bfca0a9f13c85fe2c1a', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '59261d4932aa4bfca0a9f13c85fe2c1a', '/', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '59261d4932aa4bfca0a9f13c85fe2c1a', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '7afa64007c1b4b25914e0ec4326921be', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '7afa64007c1b4b25914e0ec4326921be', '/', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7afa64007c1b4b25914e0ec4326921be', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '767e3a26c3c14996a6387845e53042ba', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '767e3a26c3c14996a6387845e53042ba', '/', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '767e3a26c3c14996a6387845e53042ba', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'a31fd718c9414afd89fa996bd1d6c868', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'a31fd718c9414afd89fa996bd1d6c868', '/', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a31fd718c9414afd89fa996bd1d6c868', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a31fd718c9414afd89fa996bd1d6c868', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;






DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1');

INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '/', 1, '/', '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) SELECT '5f38bd5c9fcb41faaf9f69c0bafd419a', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1 ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '5f38bd5c9fcb41faaf9f69c0bafd419a', '/', 'RuleUiAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5f38bd5c9fcb41faaf9f69c0bafd419a', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5f38bd5c9fcb41faaf9f69c0bafd419a', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5f38bd5c9fcb41faaf9f69c0bafd419a', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) SELECT 'cee78b4acf5b4cf8b9da53aef72c5a42', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1 ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'cee78b4acf5b4cf8b9da53aef72c5a42', '/', 'RuleUiAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cee78b4acf5b4cf8b9da53aef72c5a42', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cee78b4acf5b4cf8b9da53aef72c5a42', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'cee78b4acf5b4cf8b9da53aef72c5a42', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID ='/' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='/' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) SELECT '616cb6d9198b4d28b5a0f70e0dd13d3e', 0, 1, '/', '/', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = '/' AND IS_LATEST = '1'), 1 ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '616cb6d9198b4d28b5a0f70e0dd13d3e', '/', 'RuleUiAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '616cb6d9198b4d28b5a0f70e0dd13d3e', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = '/' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '616cb6d9198b4d28b5a0f70e0dd13d3e', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, '/', '/', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '616cb6d9198b4d28b5a0f70e0dd13d3e', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' ;






