--// FILE: DML_AUDIT_TEMPL.sql

DELETE FROM CNT_AUDIT_CONFIG_FIELD WHERE CONFIG_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE  DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.Login' AND AUDIT_MODULE_ID= '');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_AUDIT_CONFIG') AND TARGET_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.Login' AND AUDIT_MODULE_ID= '');

DELETE FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.Login' AND AUDIT_MODULE_ID= '';

INSERT INTO CNT_AUDIT_CONFIG(REVISION, EXTRA_HANDLER, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, UPDATE_USER, UPDATED_ON, ACTION_ID, TEMPLATE_ID, ENTITY_VERSION, CREATED_ON, CREATE_USER_NAME, ID, UPDATE_USER_NAME, AUDIT_MODULE_ID, IS_LATEST) SELECT '0', 'com.core.cbx.logging.handler.LoginAuditMessageHandler', '0', 'system', '/', '/', 'system', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'com.core.cbx.action.actionContext.Login', 'template_audit_login', '1', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'system', '68e3b93244b843029156437039364fa7', 'system', '', '1' ;


DELETE FROM CNT_AUDIT_CONFIG_FIELD WHERE CONFIG_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE  DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.ListDataView' AND AUDIT_MODULE_ID= '');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_AUDIT_CONFIG') AND TARGET_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.ListDataView' AND AUDIT_MODULE_ID= '');

DELETE FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.ListDataView' AND AUDIT_MODULE_ID= '';

INSERT INTO CNT_AUDIT_CONFIG(REVISION, EXTRA_HANDLER, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, UPDATE_USER, UPDATED_ON, ACTION_ID, TEMPLATE_ID, ENTITY_VERSION, CREATED_ON, CREATE_USER_NAME, ID, UPDATE_USER_NAME, AUDIT_MODULE_ID, IS_LATEST) SELECT '0', 'com.core.cbx.logging.handler.ListDataViewAuditMessageHandler', '0', 'system', '/', '/', 'system', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'com.core.cbx.action.actionContext.ListDataView', 'template_audit_listDataView', '1', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'system', '3c32273f5590485db4a023edd087613d', 'system', '', '1' ;


DELETE FROM CNT_AUDIT_CONFIG_FIELD WHERE CONFIG_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE  DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.workerAction.RemoveDocumentCpmWokerAction$RemoveDocumentCpmAuditContext' AND AUDIT_MODULE_ID= 'common');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_AUDIT_CONFIG') AND TARGET_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.workerAction.RemoveDocumentCpmWokerAction$RemoveDocumentCpmAuditContext' AND AUDIT_MODULE_ID= 'common');

DELETE FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.workerAction.RemoveDocumentCpmWokerAction$RemoveDocumentCpmAuditContext' AND AUDIT_MODULE_ID= 'common';

INSERT INTO CNT_AUDIT_CONFIG(REVISION, EXTRA_HANDLER, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, UPDATE_USER, UPDATED_ON, ACTION_ID, TEMPLATE_ID, ENTITY_VERSION, CREATED_ON, CREATE_USER_NAME, ID, UPDATE_USER_NAME, AUDIT_MODULE_ID, IS_LATEST) SELECT '0', '', '0', 'system', '/', '/', 'system', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'com.core.cbx.action.workerAction.RemoveDocumentCpmWokerAction$RemoveDocumentCpmAuditContext', 'template_audit_workerAction', '1', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'system', '2d2dd882ce1d4e9e853efcc8dd0ed1d3', 'system', 'common', '1' ;


DELETE FROM CNT_AUDIT_CONFIG_FIELD WHERE CONFIG_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE  DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.SaveAndConfirm' AND AUDIT_MODULE_ID= 'user');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_AUDIT_CONFIG') AND TARGET_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.SaveAndConfirm' AND AUDIT_MODULE_ID= 'user');

DELETE FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.SaveAndConfirm' AND AUDIT_MODULE_ID= 'user';

INSERT INTO CNT_AUDIT_CONFIG(REVISION, EXTRA_HANDLER, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, UPDATE_USER, UPDATED_ON, ACTION_ID, TEMPLATE_ID, ENTITY_VERSION, CREATED_ON, CREATE_USER_NAME, ID, UPDATE_USER_NAME, AUDIT_MODULE_ID, IS_LATEST) SELECT '0', 'com.core.cbx.logging.handler.UserSaveDocAuditMessageHandler', '0', 'system', '/', '/', 'system', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'com.core.cbx.action.actionContext.SaveAndConfirm', 'template_audit_userSaveDoc', '1', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'system', 'a9030efc956041b9845995c0fd2ca0a6', 'system', 'user', '1' ;


DELETE FROM CNT_AUDIT_CONFIG_FIELD WHERE CONFIG_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE  DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.SaveAndConfirm' AND AUDIT_MODULE_ID= 'group');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_AUDIT_CONFIG') AND TARGET_ID IN (SELECT ID FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.SaveAndConfirm' AND AUDIT_MODULE_ID= 'group');

DELETE FROM CNT_AUDIT_CONFIG WHERE DOMAIN_ID = '/' AND ACTION_ID= 'com.core.cbx.action.actionContext.SaveAndConfirm' AND AUDIT_MODULE_ID= 'group';

INSERT INTO CNT_AUDIT_CONFIG(REVISION, EXTRA_HANDLER, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, UPDATE_USER, UPDATED_ON, ACTION_ID, TEMPLATE_ID, ENTITY_VERSION, CREATED_ON, CREATE_USER_NAME, ID, UPDATE_USER_NAME, AUDIT_MODULE_ID, IS_LATEST) SELECT '0', 'com.core.cbx.logging.handler.GroupSaveDocAuditMessageHandler', '0', 'system', '/', '/', 'system', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'com.core.cbx.action.actionContext.SaveAndConfirm', 'template_audit_userSaveDoc', '1', TO_TIMESTAMP('2025-07-30 18:51:51', 'YYYY-MM-DD HH24:MI:SS'), 'system', 'ff11bffd157c435b96c95115cefbbaae', 'system', 'group', '1' ;


