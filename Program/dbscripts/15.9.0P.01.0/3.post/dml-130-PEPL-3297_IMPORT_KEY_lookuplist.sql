-----------------------------BEGIN CTC-8--------------------------------
-- JiRa No.: CTC-8
-- Description: add lookup list 'IMPORT_KEY'
-- Input parameter:<DOMAIN_ID>
-- Scripts re-runnable: Yes
DO $body$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
BEGIN
  UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID 
  AND not exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and REF_NO LIKE 'LKU%' and DOMAIN_ID = DOMAINID);

  INSERT INTO CNT_LOOKUP_BOOK(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, DOC_STATUS, EDITING_STATUS, CREATE_USER, CREATE_USER_NAME, UPDATE_USER, UPDATE_USER_NAME, CREATED_ON, UPDATED_ON, IS_CPM_INITIALIZED, IS_LATEST, NAME, DESCRIPTION, DATA_LIST_TYPE_ID, REF_NO, BUSINESS_REF_NO) 
  select LOWER(SYS_GUID()), 1, 1, DOMAINID, DOMAINID, false, 1, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, false, true, 'IMPORT_KEY', 'IMPORT_KEY', (select id from CNT_DATA_LIST_TYPE where name='IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID), (SELECT 'LKU' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::text, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_LOOKUP_BOOK_REF_NO' AND DOMAIN_ID = DOMAINID) 
  where not exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID); 

  INSERT INTO CNT_LOOKUP_BOOK_M(id, revision, domain_id, hub_domain_id, VERSION, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, is_cpm_initialized, is_latest, ref_no) 
  select (select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), 1, DOMAINID, DOMAINID, 1, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp, current_timestamp, false, true, (select ref_no from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  where exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  and not exists(select 1 from CNT_LOOKUP_BOOK_M where id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest));

  INSERT INTO CNT_LOOKUP(id, revision, entity_version, domain_id, hub_domain_id, is_for_reference, internal_seq_no, duid, parent_id, disabled) 
  select LOWER(SYS_GUID()), 1, 1, DOMAINID, DOMAINID, false, 1, '1', (select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest), false 
  where exists(select 1 from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest) 
  and not exists(select 1 from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest));

  INSERT INTO CTM_LOOKUP_BOOK(id, domain_id, ref_entity_name, cust_text1, cust_text2) 
  select (select id from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)), DOMAINID, 'Lookup', 'item','itemName' 
  where exists(select 1 from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)) 
  and not exists(select 1 from CTM_LOOKUP_BOOK where id in(select id from CNT_LOOKUP where parent_id=(select id from CNT_LOOKUP_BOOK where name = 'IMPORT_KEY' and DOMAIN_ID = DOMAINID and is_latest)));

  delete from cnt_serialized_entity where target_entity = 'LookupBook';
END;
$body$ LANGUAGE PLPGSQL;

-----------------------------END CTC-8------------------------------
