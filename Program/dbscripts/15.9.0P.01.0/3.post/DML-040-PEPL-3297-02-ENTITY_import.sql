--// FILE: DML_ENTITY_import.sql

DELETE FROM CNT_FIELD_DEFINITION WHERE PARENT_ID IN (SELECT ID FROM CNT_ENTITY_DEFINITION WHERE MODULE = 'import' AND ENTITY_VERSION = 1);
DELETE FROM CNT_ENTITY_DEFINITION WHERE MODULE = 'import' AND ENTITY_VERSION = 1;
INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODU<PERSON>, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('bcc4285c7224449d98a27a40b2d15dbe', 0, 'Import', 1, 'CNT_IMPORT', 'CNT_IMPORT_M', 'CTM_IMPORT', 'IMPORT', 'import', 'main', 'ALL', 'ALL', 'ImportHistory', '${importNo}', '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('412d679c036f417dac3f16ca063838a9', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('abfe22a305c74e22ba1e568c03407156', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6c30c7a03a344041a015baae6abcac9e', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7db862db2af047b0a46135ab5b8a1cd7', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('023250b4ab454364a7271ba7ac852abd', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5bf1551d53c74d5896007ff1b672b6ab', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4aeb88fc949c42fab5ef9c476c56123e', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'version', 'VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.version', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('79dd2d20345642f696128d1c822c1cd6', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'status', 'STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.status', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1cce0f764bce4989a70adb0f1cb0fd1f', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'docStatus', 'DOC_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.docStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ddf6a5aa0869452689426d7dc69187d1', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'editingStatus', 'EDITING_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.editingStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1c749c681f3c49ebb7520df3b30d318b', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7aa34ed03a634c81932ee0fc9e82b5ac', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e4e8703e7f4a4269a8a46ce3fbb968b0', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'updateUser', 'UPDATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.updateUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ecd48c6e1ae345b1a54903e7b5f81746', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.updateUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2927a9e127af4577ad9c464f05a432fa', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8788cd3544b749fcb9efef3e58f68d00', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.updatedOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('400fd0583f9d4249aec89c0509eb17cc', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'integrationSource', 'INTEGRATION_SOURCE', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.integrationSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('65500c05750a49a0b1becef6e758080c', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'integrationStatus', 'INTEGRATION_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.integrationStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f20e847cf6ef4285b3df4d0c44cb0b35', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'integrationNote', 'INTEGRATION_NOTE', 'VARCHAR(1000)', NULL, '2', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.integrationNote', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1e9b8f56003b4e7a8be9f94bb23d16fc', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'isCpmInitialized', 'IS_CPM_INITIALIZED', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.isCpmInitialized', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('03486378f7534c2e9eacd40f0abae65b', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'isLatest', 'IS_LATEST', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.isLatest', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('585ac71fadab4e818f64548a041a051f', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importNo', 'IMPORT_NO', 'VARCHAR(400)', 'IMPORT_NO', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.importNo', NULL, NULL, NULL, NULL, 'refNo', NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2ecf554b5bf04615a273d352e37c5ed0', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'headerRow', 'HEADER_ROW', 'NUMERIC(20, 0)', 'HEADER_ROW', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.headerRow', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4a51dccaa1e44c8a9d7e33e43c8f6c29', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'startRow', 'START_ROW', 'NUMERIC(20, 0)', 'START_ROW', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.startRow', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5305dbde3c5d4dad8bea5e17d3c0ef69', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'fileId', 'FILE_ID', 'VARCHAR(32)', 'ATTACHMENT_SID', '0', 'entity', 'Attachment', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.fileId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e4d753030c8e4a4db835dd2c32df8a77', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importModule', 'IMPORT_MODULE', 'VARCHAR(400)', 'IMPORT_MODULE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.importModule', 'codelist', 'IMPORT_MODULE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('355256cc70164aecaa7307581ac03c5b', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importTable', 'IMPORT_TABLE', 'VARCHAR(400)', 'IMPORT_TABLE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '26', '0', 'entity.importTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d09599d918cc4c80be57102156ba69e9', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importTableDisplay', 'IMPORT_TABLE_DISPLAY', 'VARCHAR(400)', 'IMPORT_TABLE_DISPLAY', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '27', '0', 'entity.importTableDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('380f4f1b3938401ca81d679e21261a2d', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importKey', 'IMPORT_KEY', 'VARCHAR(400)', 'IMPORT_KEY', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '28', '0', 'entity.importKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('662177789b20421f9169bf30760e3a05', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importKeyDisplay', 'IMPORT_KEY_DISPLAY', 'VARCHAR(400)', 'IMPORT_KEY_DISPLAY', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '29', '0', 'entity.importKeyDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e4d1642de97a463c81298e4dfc7d41af', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'checksum', 'CHECKSUM', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '30', '0', 'entity.checksum', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3d55c129a71a42ab911e991340da70ac', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importMappings', 'IMPORT_MAPPINGS', NULL, NULL, '0', 'collection', 'ImportMapping', 'importId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '31', '0', 'entity.importMappings', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f4d3ac21f4ba45e9a4e01b93f8b320d7', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'refNo', 'REF_NO', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, 'com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_IMPORT_NO","system.pattern.ImportNo","IMPT#{Date:YYMM}-#{Seq:6}")', NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '32', '0', 'entity.refNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d265b08cf5d849769702d64481783460', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'businessRefNo', 'BUSINESS_REF_NO', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '33', '0', 'entity.businessRefNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d8d8b112a2e94024b6c5d75c8df51ae9', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyTemplateRef', 'PARTY_TEMPLATE_REF', 'VARCHAR(400)', NULL, '3', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '34', '0', 'entity.partyTemplateRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('92641f13f94b4705a69b2099e13eef4e', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyTemplateVer', 'PARTY_TEMPLATE_VER', 'NUMERIC(20, 0)', NULL, '3', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '35', '0', 'entity.partyTemplateVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5651c391961e4af8af3d058435d7495e', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyTemplateName', 'PARTY_TEMPLATE_NAME', 'VARCHAR(400)', NULL, '3', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '36', '0', 'entity.partyTemplateName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c9f999e5462d4db4a627d10fdf79a71f', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyName1', 'PARTY_NAME1', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '37', '0', 'entity.partyName1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4e9ba36abc9e4dba96b5ec74d835647e', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyName2', 'PARTY_NAME2', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '38', '0', 'entity.partyName2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8d410fcaa0a348b29d5b829a0b5c1d5a', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyName3', 'PARTY_NAME3', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '39', '0', 'entity.partyName3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ad43cef85e4c43519830acd1272d7bf3', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyName4', 'PARTY_NAME4', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '40', '0', 'entity.partyName4', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a9c3a9f2f75f4d6ca9b766cd593e413a', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'partyName5', 'PARTY_NAME5', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '41', '0', 'entity.partyName5', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('95e9681ece484d0eacf20f6562fe0db0', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'parties', 'PARTIES', NULL, NULL, '2', 'collection', 'ImportParty', 'docId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '42', '0', 'entity.parties', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8a8159ff17ce4a60be18d93b4fe3a6c6', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'fileRef', 'FILE_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '43', '0', 'entity.fileRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a48026dbf02842f5a673daf3c4bee9a9', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'fileVer', 'FILE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '44', '0', 'entity.fileVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a17d44edab2a4820aeb78c7e7fa8e978', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'fileRef2', 'FILE_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '45', '0', 'entity.fileRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2664d983ec0549abb45ec0b17b13d1fa', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'fileDuid', 'FILE_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '46', '0', 'entity.fileDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e72d018e5fa54ab79b4cc50b628267d5', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importModuleName', 'IMPORT_MODULE_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '47', '0', 'entity.importModuleName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0411284197784b348b1644d3d7e51290', 0, 'bcc4285c7224449d98a27a40b2d15dbe', 'importModuleVer', 'IMPORT_MODULE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '48', '0', 'entity.importModuleVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('3db5e40d1b7e4d9c9bdc4ba712915d97', 0, 'ImportSelection', 1, 'CNT_IMPORT_SLN', NULL, 'CTM_IMPORT', 'N/A', 'import', NULL, NULL, NULL, NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('480798604b0a48509b2cb5ceec1a919f', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('71e9d39540cc45fcb33d96f2312aab73', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('759478c01c5a4648b1cb8e8b148452c9', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('78d6c2abc1ad45ec8a78a60699231da9', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c14f238e13d2454394cd07b506c61c20', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('96d0ec65e72546d196cdc6fabb20a86e', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1f387d4fbb9a460399331c1a003c9177', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'version', 'VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.version', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e7fb2eba17a64225ba8d55deab8c339e', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'status', 'STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.status', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e82d2bd1ffe848788b0c274fd61f9918', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'docStatus', 'DOC_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.docStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b67bd5b942664b7490136c6943ab87eb', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'editingStatus', 'EDITING_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.editingStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('23f3c9ddd8814ff1a6e5f592dcdd7828', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('da5d1d3852924afa9dbe5de2ae4fbc1f', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ad82e895ad074bda944e17938eda1f1f', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'updateUser', 'UPDATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.updateUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7d4116aeb39d42f0901543a13365c11d', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.updateUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ce4bad622726411d92247fa8ae966f96', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a77937a480404fb788f78e03062420d0', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.updatedOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1abf91d0ea2f48f2b614b08beec8d186', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'integrationSource', 'INTEGRATION_SOURCE', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.integrationSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a61e0ed5c9504637819fac715f91543e', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'integrationStatus', 'INTEGRATION_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.integrationStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7410fe6aa6244e8ca2c79c1fb9ec6237', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'integrationNote', 'INTEGRATION_NOTE', 'VARCHAR(1000)', NULL, '2', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.integrationNote', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3d8745e705914f35a185dcda82389a42', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'isCpmInitialized', 'IS_CPM_INITIALIZED', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.isCpmInitialized', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a922ceda46974259bb90b7735e3cafc2', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'isLatest', 'IS_LATEST', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.isLatest', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2c7db67aefe945249091566cc1f31038', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'parentId', 'PARENT_ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('39026eabb6154e2cb341a568d7845df0', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'parentEntity', 'PARENT_ENTITY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.parentEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cc145a519b304e6b8991ee1d9cd5f6bd', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'fieldId', 'FIELD_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ec57569f7e674b02a88bc4920a792fc0', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'ref', 'REF_ID', 'VARCHAR(32)', NULL, '0', 'entity-dynamic', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.ref', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('03e036941fd14d4e8cd24aa329ba4441', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'refEntity', 'REF_ENTITY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.refEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('10d18dab8fe6406aa3281720373f01d1', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'displayValue', 'DISPLAY_VALUE', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '26', '0', 'entity.displayValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3e834a1dc2d1432293176cb06f42c4d7', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '27', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e235db1bbbac4299946a4667bac98cb1', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'refRef', 'REF_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '28', '0', 'entity.refRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d154645b2f924d91aa42951608d7b2c6', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'refVer', 'REF_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '29', '0', 'entity.refVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5ad5098191ee49d6a8f7a69c492125cf', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'refRef2', 'REF_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '30', '0', 'entity.refRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('509fdf1d184844a09e41659d04b492bf', 0, '3db5e40d1b7e4d9c9bdc4ba712915d97', 'refDuid', 'REF_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '31', '0', 'entity.refDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('cf8b4161d2354bbb9f68b93c60cabbae', 0, 'ImportMapping', 1, 'CNT_IMPORT_MAPPING', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0f3cb3dd4b0a46a1909638349fe66f2c', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.importMappings.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8a95fa5b42d64cfe84af81c359ed449f', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.importMappings.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a78fc469534d4d40b5a8ef6b287d6950', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.importMappings.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a302826fde8f469581553a89df6b8bea', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.importMappings.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f7b05a90ea154d5292c3c9d22704bbad', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.importMappings.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5a51edf4306e430c8c8af3b7df3b9b83', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.importMappings.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('381dcfd29bfc4a8586f7b598105e3367', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.importMappings.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0c2d3ee4a79a4488a8d322a1ff4473c5', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.importMappings.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0c183a40ba324790ab2e88705ec9f0c3', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.importMappings.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ab54c35d8f1f45f49163be605dd86c83', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'importId', 'IMPORT_ID', 'VARCHAR(32)', 'IMPORT_SYS_ID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '1', 'entity.importMappings.importId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('196c4ee983e84266bc7173acc72b6582', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'excelLine', 'EXCEL_LINE', 'NUMERIC(20, 0)', 'ATTACHMENT_SYS_ID', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.importMappings.excelLine', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a58f885916d14f3194ca76928b530b4c', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'importStatus', 'IMPORT_STATUS', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.importMappings.importStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1a570048d00640a5ad69428e811bc23e', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'error', 'ERROR', 'VARCHAR(5000)', NULL, '0', 'string-xl', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 5000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.importMappings.error', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('25c7f4436e1e477d9e2dc01cb81f703c', 0, 'cf8b4161d2354bbb9f68b93c60cabbae', 'fieldDatas', 'FIELD_DATAS', NULL, NULL, '0', 'collection', 'ImportMappingFieldData', 'mappingId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.importMappings.fieldDatas', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('c96e112908aa4321815c1257c275f7b7', 0, 'ImportMappingFieldDef', 1, 'CNT_IMPORT_MAPPING_FIELD_DEF', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING_FIELD_DEF', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('520eb16221494f05afdd340fe469fca6', 0, 'c96e112908aa4321815c1257c275f7b7', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('40bf125528eb40a8a984004a25425324', 0, 'c96e112908aa4321815c1257c275f7b7', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('85726b4303a1422ca045e0fa62807739', 0, 'c96e112908aa4321815c1257c275f7b7', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c324973405da4b79a82cb9e09801f6b2', 0, 'c96e112908aa4321815c1257c275f7b7', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('999c8cabbc2e4bc1875c8d333f2eb7d2', 0, 'c96e112908aa4321815c1257c275f7b7', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('684fc873db594a6895c099ae43b95037', 0, 'c96e112908aa4321815c1257c275f7b7', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f2f600d17c4646529c1e380f5a944019', 0, 'c96e112908aa4321815c1257c275f7b7', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('830658b537644d569286684990a98630', 0, 'c96e112908aa4321815c1257c275f7b7', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('174f31f4b65a4fa1b6755cfbd408c651', 0, 'c96e112908aa4321815c1257c275f7b7', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b7fbdaf7503b40a6b341416214833d38', 0, 'c96e112908aa4321815c1257c275f7b7', 'excelHeader', 'EXCEL_HEADER', 'VARCHAR(400)', 'ATTACHMENT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.excelHeader', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bf1386e0ebb44157ad0a4891107e217d', 0, 'c96e112908aa4321815c1257c275f7b7', 'fieldMapping', 'FIELD_MAPPING', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.fieldMapping', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a17ca3b20bbc4a70962d43dc8bc71bb3', 0, 'c96e112908aa4321815c1257c275f7b7', 'fieldMappingDisplay', 'FIELD_MAPPING_DISPLAY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.fieldMappingDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1c1de05fba504a858e6adcf49f46917f', 0, 'c96e112908aa4321815c1257c275f7b7', 'dataType', 'DATA_TYPE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.dataType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('558c7f39978e44caabde847b98ba1405', 0, 'c96e112908aa4321815c1257c275f7b7', 'module', 'MODULE', 'VARCHAR(400)', 'IMPORT_MODULE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.module', 'codelist', 'IMPORT_MODULE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('37f2577cd9be40508e2b8f83fcdb5c21', 0, 'c96e112908aa4321815c1257c275f7b7', 'checksum', 'CHECKSUM', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.checksum', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('54c374a78cbe4ad690d40d7ea2e51f14', 0, 'c96e112908aa4321815c1257c275f7b7', 'templId', 'TEMPL_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.templId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7a718b030ea94ac99b7b873d904b9bda', 0, 'c96e112908aa4321815c1257c275f7b7', 'importId', 'IMPORT_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.importId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('db60e142ff7f4c9eb72f67eabc4907e9', 0, 'c96e112908aa4321815c1257c275f7b7', 'moduleName', 'MODULE_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.moduleName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3e906082897a4a36a56b7ac45118dc55', 0, 'c96e112908aa4321815c1257c275f7b7', 'moduleVer', 'MODULE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.moduleVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('73f84faeada04bd9b258a4ad964c40fb', 0, 'ImportMappingFieldDefTempl', 1, 'CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING_FIELD_DEF_TEMPL', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ecf984834e3f40649d8cb3405f40d740', 0, '73f84faeada04bd9b258a4ad964c40fb', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('713620ca487e4cd385240f7184eae3fe', 0, '73f84faeada04bd9b258a4ad964c40fb', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4d64b009d9ee457a803ff0d9ca451c48', 0, '73f84faeada04bd9b258a4ad964c40fb', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('92e2bfded9114c2a95d07df02dec0d13', 0, '73f84faeada04bd9b258a4ad964c40fb', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('27aeb193cdd84855b72cbcb987733ddf', 0, '73f84faeada04bd9b258a4ad964c40fb', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('66ddbbdef0e9496083a152a8980d31b3', 0, '73f84faeada04bd9b258a4ad964c40fb', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fc0a65dccd2843eaa76f7be6d3761885', 0, '73f84faeada04bd9b258a4ad964c40fb', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6a4e5262e70f4720b4e94f0202efd2d8', 0, '73f84faeada04bd9b258a4ad964c40fb', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d8950fa9cd7e440a880acad2e9481a32', 0, '73f84faeada04bd9b258a4ad964c40fb', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a51af292eae1443b9b5b9d75d80fd77a', 0, '73f84faeada04bd9b258a4ad964c40fb', 'excelHeader', 'EXCEL_HEADER', 'VARCHAR(400)', 'ATTACHMENT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.excelHeader', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7ce800b186c945c0adc9b50b2c2f2319', 0, '73f84faeada04bd9b258a4ad964c40fb', 'fieldMapping', 'FIELD_MAPPING', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.fieldMapping', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2d3b9527cedc459dadf095786064a7df', 0, '73f84faeada04bd9b258a4ad964c40fb', 'fieldMappingDisplay', 'FIELD_MAPPING_DISPLAY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.fieldMappingDisplay', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('62a2a83e58f641979afee74ad1ed00c3', 0, '73f84faeada04bd9b258a4ad964c40fb', 'dataType', 'DATA_TYPE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.dataType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('afe1b8021c744b97b151b6c9295f492f', 0, '73f84faeada04bd9b258a4ad964c40fb', 'module', 'MODULE', 'VARCHAR(400)', 'IMPORT_MODULE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.module', 'codelist', 'IMPORT_MODULE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ef391d58dd6e41ea9a460b5234e8c730', 0, '73f84faeada04bd9b258a4ad964c40fb', 'checksum', 'CHECKSUM', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.checksum', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('949b0a68e60a484a91f5ec85813430a4', 0, '73f84faeada04bd9b258a4ad964c40fb', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.updateUserName', NULL, NULL, 'UPDATE_USER_NAME', NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bb20ebe5270c4d268ca1035f7dd60a1f', 0, '73f84faeada04bd9b258a4ad964c40fb', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '0', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.updatedOn', NULL, NULL, 'UPDATED_ON', NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b0bb750212c94892b302ca8079a2a0f9', 0, '73f84faeada04bd9b258a4ad964c40fb', 'moduleName', 'MODULE_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.moduleName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7b71d6f17d8b4395bada01403c5702d4', 0, '73f84faeada04bd9b258a4ad964c40fb', 'moduleVer', 'MODULE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.moduleVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('af448555ee8b436ba65017311653cf56', 0, 'ImportMappingFieldData', 1, 'CNT_IMPORT_MAPPING_FIELD_DATA', NULL, 'CTM_IMPORT', 'IMPORT_MAPPING_FIELD_DATA', 'import', 'inner', 'ALL', 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6859bfe8e90f4b1b83168c0368e9ff1d', 0, 'af448555ee8b436ba65017311653cf56', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.importMappings.fieldDatas.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ef8c05a25fc84a279119651d1a20d9f7', 0, 'af448555ee8b436ba65017311653cf56', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.importMappings.fieldDatas.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c073531e3c6a4172b614a98737c8f667', 0, 'af448555ee8b436ba65017311653cf56', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.importMappings.fieldDatas.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6222c04748884a928a59b2d0200b2bc4', 0, 'af448555ee8b436ba65017311653cf56', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.importMappings.fieldDatas.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8be7cd01c2ea40be8c47c83ae816c7bc', 0, 'af448555ee8b436ba65017311653cf56', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.importMappings.fieldDatas.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ae0298a47f9749e1873a334c3caec7bd', 0, 'af448555ee8b436ba65017311653cf56', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.importMappings.fieldDatas.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('be6bf04a5dba4c8b9dfc72fabb4fcb9d', 0, 'af448555ee8b436ba65017311653cf56', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.importMappings.fieldDatas.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9dc706866d7b43999af061b0139ca224', 0, 'af448555ee8b436ba65017311653cf56', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.importMappings.fieldDatas.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5e2e987ecb0940c5b86fa351531a5723', 0, 'af448555ee8b436ba65017311653cf56', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.importMappings.fieldDatas.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2d49364c1abd45cfa6f4d2f1349ce9b8', 0, 'af448555ee8b436ba65017311653cf56', 'mappingId', 'MAPPING_ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.importMappings.fieldDatas.mappingId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5315c3ae121243e09e39b3544d9a1efe', 0, 'af448555ee8b436ba65017311653cf56', 'defId', 'DEF_ID', 'VARCHAR(400)', 'IMPORT_SYS_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '1', 'entity.importMappings.fieldDatas.defId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6a4b40b4e7ca4c9d9f4fecf334589d0e', 0, 'af448555ee8b436ba65017311653cf56', 'dataValue', 'DATA_VALUE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.importMappings.fieldDatas.dataValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('248ae9224dbf4f3ab531c09fc0ba62dc', 0, 'ImportParty', 1, 'CNT_IMPORT_PARTY', NULL, 'CTM_IMPORT', 'IMPORT_PARTY', 'import', NULL, NULL, NULL, NULL, '${partyName.code}-${contactUserRef}', '0', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '5', '5', '5', '5', '5', '5', '5', '5', '5', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cab9ad635bba420dbdfb9c5fa6d9ca36', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.parties.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c8dd670a134643eab9f341bc90cd93b3', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.parties.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b3fd32e1b25b4909908b83e1c60c327a', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.parties.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('eccb4468a1764646a674913b4503ae43', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.parties.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('833eda9f41fe4d1b85d6535d987c9c6f', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.parties.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3137f945166d49979ff0699abc7312ab', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.parties.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d5769aaefc4540ee8e9184964decdc4f', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.parties.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d19e2f1da80a40779446444b93533270', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.parties.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ba04225299e64689a986f309088cc329', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.parties.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d0056e43f0024820961fa9c121499760', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'docId', 'DOC_ID', 'VARCHAR(32)', 'DOC_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.parties.docId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0c23c96c92704ad5a0c5e7efbcc91940', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'partyName', 'PARTY_NAME', 'VARCHAR(400)', 'PARTY_NAME', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.parties.partyName', 'codelist', 'RESPONSIBLE_PARTIES_NAME', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b3bd384b0fa04a90a3850cc562166dff', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'partyNameSeqNo', 'PARTY_NAME_SEQ_NO', 'NUMERIC(20, 0)', 'PARTY_NAME_SEQ_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.parties.partyNameSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('22baeee9c80c42469d15412abeb584ad', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'contactUser', 'CONTACT_USER_ID', 'VARCHAR(32)', 'CONTACT_USER', '0', 'entity', 'User', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.parties.contactUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ba2bf8d6a24d40138148a95977bda412', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'contactUserSeqNo', 'CONTACT_USER_SEQ_NO', 'NUMERIC(20, 0)', 'CONTACT_USER_SEQ_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.parties.contactUserSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3fd087888f2040159073a3ea389b1f2d', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'isOwner', 'IS_OWNER', 'BOOLEAN', 'IS_OWNER', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.parties.isOwner', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('24f04f07b1044954a7862697c2402704', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'partyNameName', 'PARTY_NAME_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.parties.partyNameName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('03cf275cbef241cbb94057f0df8416b5', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'partyNameVer', 'PARTY_NAME_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.parties.partyNameVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('48ba58e6b191480985f040067bbd3643', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'contactUserRef', 'CONTACT_USER_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.parties.contactUserRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f3eff6a46a374f4fa4a0aa5919ed3a0c', 0, '248ae9224dbf4f3ab531c09fc0ba62dc', 'contactUserVer', 'CONTACT_USER_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.parties.contactUserVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('28ef78d076434bc69305c3511efb185e', 0, 'ImportHistory', 1, 'CNT_IMPORT_H', NULL, NULL, NULL, 'import', 'inner', NULL, NULL, NULL, NULL, '1', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('11951111bc034a12bb2e29b57ec11cfa', 0, '28ef78d076434bc69305c3511efb185e', 'parentId', 'PARENT_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('967fbb17887c49918ceaf6b6df0c91c7', 0, '28ef78d076434bc69305c3511efb185e', 'docVersion', 'DOC_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.docVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('884d8dd5b8384388b4f6b1b372896e04', 0, '28ef78d076434bc69305c3511efb185e', 'docRevision', 'DOC_REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.docRevision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('762cec2e4506489d8eb84d620b19518e', 0, '28ef78d076434bc69305c3511efb185e', 'itemId', 'ITEM_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.itemId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6e76d10a25bd4788a919577a34a8b76a', 0, '28ef78d076434bc69305c3511efb185e', 'itemRevision', 'ITEM_REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.itemRevision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1aad01d733954394bb356d66e3cd0cdb', 0, '28ef78d076434bc69305c3511efb185e', 'itemEntityName', 'ITEM_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.itemEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5e68bd6461024539adb8859933d4f766', 0, '28ef78d076434bc69305c3511efb185e', 'type', 'TYPE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.type', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('51b647ae853f4773a93fd44d9dbeb52a', 0, '28ef78d076434bc69305c3511efb185e', 'fieldId', 'FIELD_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('838310097dc14454afef92aff2ab6884', 0, '28ef78d076434bc69305c3511efb185e', 'path', 'PATH', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.path', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2c657a5ee0334dc78c6437e39fa6cf08', 0, '28ef78d076434bc69305c3511efb185e', 'refValue', 'REF_VALUE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.refValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('595b281f78794af4b9befb0b6ce0bdac', 0, '28ef78d076434bc69305c3511efb185e', 'operation', 'OPERATION', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.operation', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2cba2a2be22c4d649edfc4485d8d6568', 0, '28ef78d076434bc69305c3511efb185e', 'valueBefore', 'VALUE_BEFORE', 'TEXT', NULL, '0', 'clob', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.valueBefore', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3f3a787a90fa4aac9353b6212eec5d9c', 0, '28ef78d076434bc69305c3511efb185e', 'valueAfter', 'VALUE_AFTER', 'TEXT', NULL, '0', 'clob', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.valueAfter', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0889da187e4c4522a65a9fe629ae6aa7', 0, '28ef78d076434bc69305c3511efb185e', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('00dad1bfd0d746eb846ba7aa7987a085', 0, '28ef78d076434bc69305c3511efb185e', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ee04d652c46d4802977c2a46b4974a32', 0, '28ef78d076434bc69305c3511efb185e', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '0', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('01bff7fbded34649af0c1c6844346a5a', 0, '28ef78d076434bc69305c3511efb185e', 'trackingLevel', 'TRACKING_LEVEL', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.trackingLevel', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('eb76c2ccfdf84b6b8ac570d169151ebc', 0, '28ef78d076434bc69305c3511efb185e', 'id', 'ID', 'VARCHAR(32)', NULL, '2', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b31fc6a881344d1fb9e4a42c8aba03ba', 0, '28ef78d076434bc69305c3511efb185e', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b2d21eb2d6b04797b62d052b5f52a2df', 0, '28ef78d076434bc69305c3511efb185e', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b63e4bcc1d98491d9be286aa6383fa7f', 0, '28ef78d076434bc69305c3511efb185e', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0bd2baa65e984887bf58223b84cd25e2', 0, '28ef78d076434bc69305c3511efb185e', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('421727a799d64e4a96dcaa2f41f1e77c', 0, '28ef78d076434bc69305c3511efb185e', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5baf282a012c42edb32313c8a7255564', 0, '28ef78d076434bc69305c3511efb185e', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('04878470869c473e8d1ee7eb201e5260', 0, '28ef78d076434bc69305c3511efb185e', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fa2fb59948394bb782dbe443d09d1ad1', 0, '28ef78d076434bc69305c3511efb185e', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '2', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');




DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'Import' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'Import' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'Import' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportSelection' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportSelection' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportSelection' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMapping' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMapping' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMapping' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDef' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDef' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDef' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDefTempl' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDefTempl' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldDefTempl' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldData' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldData' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportMappingFieldData' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportParty' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportParty' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'ImportParty' AND ENTITY_VERSION = 1;

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('822abcbfb5204fe1b13da0c2dbca3482', 0, 'Import', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('11fe72b9ce724d4894d6ce108b4f4e9c', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('95ccbb4c57ca490e8f5a3c97b55b8c12', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5bfce7911f7c493e8f6be6e0c08bea62', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a20cbf0b4d1145e4b76296b9a58588f5', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dcad055f3eb64ba08f6aea3676bcaa3a', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8f823233b7cf414098019e81b2820481', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('599cf9cf2d924a70b143bc13e1889c56', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '6', 'version', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ba7177de23ae4dce8ff95f327bcd897a', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '7', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d39dca9065de40dd9a98f5c0722a3f43', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '8', 'docStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5914e6c366d04f19a73da75f3406b663', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '9', 'editingStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c1f3612596be4dd280d1d35acd0fb00a', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '10', 'createUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ef9ce0c1dc504009a33b6201a5f22e32', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '11', 'createUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cda75213be3545b1b25e21dda66af250', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '12', 'updateUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dc4331b166714cbdae5dba1831e12719', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '13', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fd447bef0d69433c9d1b485fecc6a73c', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '14', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1a645a9ee33c40d0bffd327cb17e6da7', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '15', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('934c3adb41de4c53bf071113b813f3ea', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '16', 'integrationSource', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3395f282c4fd4aecaf8c40274dcceede', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '17', 'integrationStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4e8ba81d053a4e7181a1d9ee279ced63', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '18', 'integrationNote', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('606414b922ed4fe6805c6947dfd26e74', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '19', 'isCpmInitialized', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ff612d5a2d8444678d23d68436373637', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '20', 'isLatest', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1d8706900b73415eb9edcf383051ff25', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '21', 'importNo', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3472387e3c8f44e095dd3be8e4f46121', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '22', 'headerRow', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c7001ea1257f46deadee6de2ad1a7695', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '23', 'startRow', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b5179af3351f4576b1055244aec137c0', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '24', 'fileId', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b622a9b3dd974c929f1440e969199dfb', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '25', 'importModule', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('150ebc45fa5b4645b9d8384033d7983b', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '26', 'importTable', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('62e60d88ee3e49b9acb8b58ea78cce8f', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '27', 'importTableDisplay', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('494e86df38eb4ad4981fd150c9eca4fe', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '28', 'importKey', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('11f9835153934351902248062daecc51', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '29', 'importKeyDisplay', 2, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dcf59394ba1c44e5b777cf7495d5a0c2', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '30', 'checksum', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ac7341d5ac0b4e21a678f587d821570a', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '31', 'importMappings', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('54d2bf6236d9480ba054e74c3ab08f32', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '32', 'refNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ca3fc3551e3443cc88daf08b5a5c24ed', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '33', 'businessRefNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c673e5a2c44a4514a18d801d36b6bf24', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '34', 'partyTemplateRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('af78a33a64a64e27ad8fb5ed4d7c0fa2', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '35', 'partyTemplateVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fd8092c8bf6b482fa00773461c2c9559', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '36', 'partyTemplateName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('269c8275ba8b4b689ed02fddb328d8dc', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '37', 'partyName1', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('32ccd3dc31b64282beba065445148aa7', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '38', 'partyName2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e33b2f3809874d63b921dcefeaa72505', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '39', 'partyName3', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bc83d52d1bbf42f5927aadd484c93996', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '40', 'partyName4', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('aa08f4b021044dc5863a0421d59c01dc', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '41', 'partyName5', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('675eadc4b94f47c58c27f2f37d1e2a30', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '42', 'parties', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f95e310f96e046878c0f026f25c9c4ef', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '43', 'fileRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b2d3f755dec24421be12f5c92b905f23', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '44', 'fileVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('81d7fcec6a2d4a718c2ad4a2781771f4', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '45', 'fileRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7ec77f8dce7c42c484d8caf38d1cf845', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '46', 'fileDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d4d5f73a432c456e9c0f267f3edc6aaa', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '47', 'importModuleName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fec59dba4753461e85099d4961a70fc2', 0, 1, '/', '/', '0', '822abcbfb5204fe1b13da0c2dbca3482', '48', 'importModuleVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('d217d43cd31a4b78a45fe54503ee5a46', 0, 'ImportSelection', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ec6a59e2b1b040eeb160fe0a6afc08cc', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('24f826bb1e2c43d6b7e158040f1d2d12', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c71a187ab55c45dbafdff1c0787e865c', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bd1cdd99b5724860bb3c2da254f8596c', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5e678033995c4c049c73e9c0abc0334f', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('86db86be30eb43cc9c2a227534cf5dbe', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3d968a7aa1d8463b8b0d1f6d636333b6', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '6', 'version', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('73f0d19077c14b7b8781ec5d1eb40f1a', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '7', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e44843ca175a4f988a6d4d4164c444b4', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '8', 'docStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1cfe80ebb9fc4e41838d99373ce630b4', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '9', 'editingStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4d42fe9740ec4c4f8637d26e7bf67a3c', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '10', 'createUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c5136a33bfbc467582cc18daceaeaa68', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '11', 'createUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('be01b0af360641739d2ac4ce2d423c91', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '12', 'updateUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5e67ea1f4d1f46889598b470afb8ad90', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '13', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3860edd32bd94d84b4c2903d8da648d5', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '14', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ecf26811569e4b33862f3b04c809f524', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '15', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d6366cd7c8294c44852b1aba319054d7', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '16', 'integrationSource', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e963ef55ca9b44a4b6aae08190c0dddf', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '17', 'integrationStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ba376bd36d7642d29253dc041221481a', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '18', 'integrationNote', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('34fe75beee9b46db88b5370e0e04d183', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '19', 'isCpmInitialized', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a6593651cf404293bbbb449583f2dc8c', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '20', 'isLatest', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4cd2983f94b04d37ba0fb2e4338a53e2', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '21', 'parentId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a7f31de1296545db8736c645cac8d31d', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '22', 'parentEntity', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('541f02443fc14e8bb5b513b8b5455537', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '23', 'fieldId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1255e2f2e72a444b8231108f0a6adab9', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '24', 'ref', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('93dbb860d272461ba6a710bbe32c0250', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '25', 'refEntity', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b9a9fade23884b4ba7cbe38c65b99b44', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '26', 'displayValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4a6279ec3ddf4c809bdae54638ff7126', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '27', 'internalSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0780354f4fc044a6a1bcd3d9b7136781', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '28', 'refRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ed53f4b20bea4fe192e8f1eb0842b236', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '29', 'refVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ac3d2bdbeb91463394a965c52b6200d8', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '30', 'refRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4607f65d7b1f4bda9b9d514cfb26f7d2', 0, 1, '/', '/', '0', 'd217d43cd31a4b78a45fe54503ee5a46', '31', 'refDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('4e533b6ce4794eb485e42e03cc2f17f2', 0, 'ImportMapping', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bce0a06595224fcb80a8d8f8f3922553', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('98b2ec257df24661b5bf1b8b7d927c68', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9b98ac46fb0941b2a114970e7b72f1de', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0b0f4ce7dbde47689c8e0c6633f3620a', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('95f814ec3568415883ab171bcc327ada', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('30d8dd581d9148eb84de4094e6c7f01a', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cf32187f8ee54006ade119bcc415316a', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f19338f962454834a2bc95e1ae4c2eb5', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9407c2751b3a453893038e4c6f234387', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1c63e3b6d0804cee921c59d28d298201', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '9', 'importId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7cab7cab391740a28a84aa910cce5482', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '10', 'excelLine', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('685e826f4f8741758d795e840e7f4197', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '11', 'importStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8b31273e80cd484a98560ddc59905072', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '12', 'error', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3aa23d1e976543cf8fa549ac89267d1f', 0, 1, '/', '/', '0', '4e533b6ce4794eb485e42e03cc2f17f2', '13', 'fieldDatas', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('06a9c6114f6d4ef9a611c0637bd58614', 0, 'ImportMappingFieldDef', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a617b3d8b3804dba8554453329f6b661', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8c1078f539db470d940da9cc01437b04', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1000a738f7714e7bbffbed71cfad6cce', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e4412962d3004fbcbfb41a970a917385', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9256a25f70e94070b89bc6448edb0714', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('91467ad36d2b4f02abb2f710880bd0fa', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c9b35e2efae54ea0a85723e48036eb18', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8f9c184119e54b369e99cb69d2c722b8', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b89654b9a01e496a81e48c313d7c0648', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('64c7e76f065945efbbe0d598d9938103', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '9', 'excelHeader', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a2e061f3165a4cefbe10b16463b56615', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '10', 'fieldMapping', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2caa0133dc434c5d92c5686f7368298e', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '11', 'fieldMappingDisplay', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f4336a8ccf074d169abde84f2dd89a83', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '12', 'dataType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3bb46dbdddb04b4abbf7a7f48ac4e991', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '13', 'module', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('702f8ac42e6646178cf9f9ace5244c2d', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '14', 'checksum', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8cf971cb76364b09be7268a97217ef4e', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '15', 'templId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('03cb69d6914a4239b569246e46c0e394', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '16', 'importId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b8085bc4647d41b5966a7f17037841c6', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '17', 'moduleName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8ebe3340f1564467b2b9ecd8be8cdb81', 0, 1, '/', '/', '0', '06a9c6114f6d4ef9a611c0637bd58614', '18', 'moduleVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('41039207ae5a4fa1a2df3f9688437d67', 0, 'ImportMappingFieldDefTempl', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bbb24d609250476b844ed80506f082c3', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d78bb42793f3426b8cc1b12dee31379f', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a5c515d03bc44f75b4a47daab4ba696e', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('258677a5553b4ae98e9d54b14df2e296', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('748873797fd345afb9bbbe062825a791', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ce3166d4a681479ca90ab1a9074a7ed9', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b88592f0742c40aeb1e2ecc21616f486', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0a25e43eacd841068152cd12989120fc', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('203c86bd74c2445a81dfb71a9d19e03a', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0fed430b037f45ce805addedb87f9815', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '9', 'excelHeader', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1656a0dd3adb487c9f4d708bce778ddd', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '10', 'fieldMapping', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d221379ef45d442aa2780f89c77ad174', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '11', 'fieldMappingDisplay', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('71b27147ffbc404097cd976d6ce1a220', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '12', 'dataType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b73869ed37f849f2a6162081ff8ed447', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '13', 'module', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('188793b108f34226aed2e5e36bb3f0ea', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '14', 'checksum', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('823f5a4c48064807a2d86f823a4b6d35', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '15', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('507d03e5bb474110b769b0f4a424ec1d', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '16', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b47833799d174befa904b42edbf178b5', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '17', 'moduleName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('de395e0a12c54d64aa56ebb4a46c7808', 0, 1, '/', '/', '0', '41039207ae5a4fa1a2df3f9688437d67', '18', 'moduleVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('c6ef701cab30478084787251e1c62fad', 0, 'ImportMappingFieldData', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0b1698a275d449098d785f99db3213a3', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('05a911a369a840d8a2453e86b4366e6c', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b9a537b57fcd4e27b2e0c3176c13e7c4', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('53e412a7a2e6414d8e589b0dc2c066c6', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0811f4e25c2547189a5faaf4893f0fb7', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('76ce3292a04d4d56bdbb44155e377c04', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d630e8c7441b4db8b4fd8110ee367173', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('736b3ec6924c4109a3a10e1da86454be', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('da99ccac52ad4984b5dbac657afb12da', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cb618d70e3ed4c99b7059902a9a6f7a2', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '9', 'mappingId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('08b68b5628c04565aaf9a6f848895f22', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '10', 'defId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5f71375812cb491cb24b68efe3dc9c4a', 0, 1, '/', '/', '0', 'c6ef701cab30478084787251e1c62fad', '11', 'dataValue', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('87d20478d7164717a3a4620863b3e5a0', 0, 'ImportParty', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-07-30 14:37:51', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('409cdf46faf44177adb28481952873d1', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4caed0265f274ca5bfa2cf6b0bee40fd', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('782b762914ed45e7b722cb54b6b2b4ea', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c1785d459c76434991c24a8ec06861ae', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('27c8d32f44f24967b0cee7f8ce362b73', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('45c9a3a3ca834c3c9b7308e0e25a0a8f', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('af013585471e49ada7b8aac13d8f423f', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ffe8cc162c0f4f8ab0796d21380cae55', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3b93bd6bbf9d4830acd684f2e946a6ae', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('762eb79062a347fd905b006da6e87db6', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '9', 'docId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b8fd28c55bd946eea53471d8a0a331d2', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '10', 'partyName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('00f1f1699d3744b381b68d1faa51e722', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '11', 'partyNameSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7b089034b28542c29584c26124296a5d', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '12', 'contactUser', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a16b52da2c0d46bb86d38968de574d0a', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '13', 'contactUserSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bd875f4277c448f6b2626b712f166e10', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '14', 'isOwner', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a9b89172d6f34e138b482fd1b00c00b3', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '15', 'partyNameName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c0a5bcd564b34c5cb025f7086d055a92', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '16', 'partyNameVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bcd53c03b383451f8cf4a38826fabd17', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '17', 'contactUserRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bb66c629db71408cbc478694e47ba422', 0, 1, '/', '/', '0', '87d20478d7164717a3a4620863b3e5a0', '18', 'contactUserVer', 0, 'PathRenderer', 'DetailRenderer');

