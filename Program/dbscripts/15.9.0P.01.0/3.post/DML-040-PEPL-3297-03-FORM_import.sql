--// FILE: DML_FORM_import.sql

DELETE FROM CNT_FIELD_PATH_ATTRIBUTES WHERE PARENT_ID IN (SELECT ID FROM CNT_FIELD_PATH WHERE FORM_ID = 'importForm' AND FORM_VERSION = 1);

DELETE FROM CNT_FIELD_PATH WHERE FORM_ID = 'importForm' AND FORM_VERSION = 1;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'ff423a68d9f6490891520440006e35d2', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'docStatus', 'Field', 'lbl.import.header.docStatus', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''docStatus'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''docStatus'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '826227b3d3a14f479dc69cb936391a03', 0, 1, '/', '/', '0', 'ff423a68d9f6490891520440006e35d2', 'align', 'left'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ff423a68d9f6490891520440006e35d2');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '511284b1a9084c999c60a9d550e14cda', 0, 1, '/', '/', '0', 'ff423a68d9f6490891520440006e35d2', 'format', 'inactive:(inactive),active:,canceled:(canceled)'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ff423a68d9f6490891520440006e35d2');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '64e6851aa1984fa6a924b84a7eea47db', 0, 1, '/', '/', '0', 'ff423a68d9f6490891520440006e35d2', 'hideLabel', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ff423a68d9f6490891520440006e35d2');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'cb6982504f2a4b269e97c5a26d13401a', 0, 1, '/', '/', '0', 'ff423a68d9f6490891520440006e35d2', 'id', 'docStatus'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ff423a68d9f6490891520440006e35d2');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'dfa1724e48d44495a6f7411589dd832e', 0, 1, '/', '/', '0', 'ff423a68d9f6490891520440006e35d2', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ff423a68d9f6490891520440006e35d2');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6e4af9d2a3984a01982140a91e3612b5', 0, 1, '/', '/', '0', 'ff423a68d9f6490891520440006e35d2', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ff423a68d9f6490891520440006e35d2');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'cfebe1306887489c85b24d2df6e7a3e9', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'headerName', 'Field', 'lbl.import.header.headerName', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerName'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerName'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '3a7ac879eadc4f45b2da07f27f2b1510', 0, 1, '/', '/', '0', 'cfebe1306887489c85b24d2df6e7a3e9', 'align', 'left'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cfebe1306887489c85b24d2df6e7a3e9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'c1dd747942d442038de2eb37b4aa857b', 0, 1, '/', '/', '0', 'cfebe1306887489c85b24d2df6e7a3e9', 'format', '{name}'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cfebe1306887489c85b24d2df6e7a3e9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e2aa15b5ad3a49ffbb7fdb190f4d0f4a', 0, 1, '/', '/', '0', 'cfebe1306887489c85b24d2df6e7a3e9', 'hideLabel', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cfebe1306887489c85b24d2df6e7a3e9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '18def4f17bf7434a9c1e5deb23324fd9', 0, 1, '/', '/', '0', 'cfebe1306887489c85b24d2df6e7a3e9', 'id', 'headerName'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cfebe1306887489c85b24d2df6e7a3e9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '79aea3cff9ff4060862f20b98e5ace9d', 0, 1, '/', '/', '0', 'cfebe1306887489c85b24d2df6e7a3e9', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cfebe1306887489c85b24d2df6e7a3e9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'd981e859f85641b1aa713c245f57ca97', 0, 1, '/', '/', '0', 'cfebe1306887489c85b24d2df6e7a3e9', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cfebe1306887489c85b24d2df6e7a3e9');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'bc6fd11076054057982bba434489a80b', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'status', 'Field', 'lbl.import.header.status', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''status'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''status'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ecbd366174ec4df78a4c8d59eaa0a662', 0, 1, '/', '/', '0', 'bc6fd11076054057982bba434489a80b', 'align', 'right'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bc6fd11076054057982bba434489a80b');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2935abe35d0d46e1a46307fcf3fb77dd', 0, 1, '/', '/', '0', 'bc6fd11076054057982bba434489a80b', 'id', 'status'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bc6fd11076054057982bba434489a80b');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '687da1efdace4a78ac99391889361839', 0, 1, '/', '/', '0', 'bc6fd11076054057982bba434489a80b', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bc6fd11076054057982bba434489a80b');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '641484c1481c43759f77d6c99ca81db8', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'version', 'Field', 'lbl.import.header.version', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''version'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''version'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '51b57feb07ed4663bfa5ceecc93f07f2', 0, 1, '/', '/', '0', '641484c1481c43759f77d6c99ca81db8', 'align', 'right'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '641484c1481c43759f77d6c99ca81db8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'cfef7d3dd09d4a4db27b5d261a18608c', 0, 1, '/', '/', '0', '641484c1481c43759f77d6c99ca81db8', 'format', '{version}'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '641484c1481c43759f77d6c99ca81db8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '32dfad8db8ec422e88967fb1175ab95a', 0, 1, '/', '/', '0', '641484c1481c43759f77d6c99ca81db8', 'id', 'version'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '641484c1481c43759f77d6c99ca81db8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'b2d37b6b9a7e4feba21981e4fa284d3b', 0, 1, '/', '/', '0', '641484c1481c43759f77d6c99ca81db8', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '641484c1481c43759f77d6c99ca81db8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6d540da6b62b4d18aeb4ae972692a271', 0, 1, '/', '/', '0', '641484c1481c43759f77d6c99ca81db8', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '641484c1481c43759f77d6c99ca81db8');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'cd31b9fd89724aea81aedca038c06863', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'headerIntegration', 'Field', 'lbl.import.header.headerIntegration', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerIntegration'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerIntegration'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f3faa65857d547aaaa278fc8ee0ecacc', 0, 1, '/', '/', '0', 'cd31b9fd89724aea81aedca038c06863', 'align', 'right'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cd31b9fd89724aea81aedca038c06863');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'a143cbf2ddef47ecbe582db6a3bbcc23', 0, 1, '/', '/', '0', 'cd31b9fd89724aea81aedca038c06863', 'hideLabel', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cd31b9fd89724aea81aedca038c06863');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'dd088854956641fea3f65ba6498f6fe3', 0, 1, '/', '/', '0', 'cd31b9fd89724aea81aedca038c06863', 'id', 'headerIntegration'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cd31b9fd89724aea81aedca038c06863');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '22f4a667ecdb4f8395604eae4c466236', 0, 1, '/', '/', '0', 'cd31b9fd89724aea81aedca038c06863', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cd31b9fd89724aea81aedca038c06863');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '7fccac121de64354bcbc33310bf994d1', 0, 1, '/', '/', '0', 'cd31b9fd89724aea81aedca038c06863', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cd31b9fd89724aea81aedca038c06863');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '5ccc174622f54a17a433f07f8fd88909', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'linkbar', 'Field', 'lbl.import.header.linkbar', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''linkbar'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''linkbar'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ee14799257d544968c45c9a9b58d348d', 0, 1, '/', '/', '0', '5ccc174622f54a17a433f07f8fd88909', 'id', 'linkbar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5ccc174622f54a17a433f07f8fd88909');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ab1c4152c32a44ebac4193e5be19c1d2', 0, 1, '/', '/', '0', '5ccc174622f54a17a433f07f8fd88909', 'type', 'Linkbar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5ccc174622f54a17a433f07f8fd88909');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '9fff2da3807a419bb979dcc6db0c1e67', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Header', NULL, NULL, '/Form[@id=''''importForm'''']/Header', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '8136dd79760643ec88db5239334d61e1', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.createGroup.openImportPopupWin', 'import.importMenubar.createGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']/MenuItem[@id=''''openImportPopupWin'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']/MenuItem[@id=''''openImportPopupWin'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '3ca3a32f5bb74284bceba1f08a1e65ac', 0, 1, '/', '/', '0', '8136dd79760643ec88db5239334d61e1', 'action', 'PopImportNewAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '8136dd79760643ec88db5239334d61e1');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'b307498ec87c47f79a7c21163d8f67ff', 0, 1, '/', '/', '0', '8136dd79760643ec88db5239334d61e1', 'id', 'openImportPopupWin'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '8136dd79760643ec88db5239334d61e1');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '3f496ccc6c0c4a3e84ee9860bdc03da0', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.createGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0d8045f0311a4a528aced0c9b1aa9364', 0, 1, '/', '/', '0', '3f496ccc6c0c4a3e84ee9860bdc03da0', 'id', 'createGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '3f496ccc6c0c4a3e84ee9860bdc03da0');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'cb892663e6d244a2ad0087f4b4db70b8', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.amendDoc', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuItem[@id=''''amendDoc'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuItem[@id=''''amendDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0e6d23bae44e45e390d0d6eacfb5dc02', 0, 1, '/', '/', '0', 'cb892663e6d244a2ad0087f4b4db70b8', 'action', 'AmendDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cb892663e6d244a2ad0087f4b4db70b8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6d0928809f5c448091277e4dffaa3b0a', 0, 1, '/', '/', '0', 'cb892663e6d244a2ad0087f4b4db70b8', 'id', 'amendDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cb892663e6d244a2ad0087f4b4db70b8');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '3768e17dff674187aba90a9a69dfe5f7', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.actionsGroup.copyDoc', 'import.importMenubar.actionsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']/MenuItem[@id=''''copyDoc'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']/MenuItem[@id=''''copyDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '099e415179214d5fa71b34e1a88510df', 0, 1, '/', '/', '0', '3768e17dff674187aba90a9a69dfe5f7', 'action', 'CopyDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '3768e17dff674187aba90a9a69dfe5f7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '476d0892d81f4413b03b7726d8b80ffe', 0, 1, '/', '/', '0', '3768e17dff674187aba90a9a69dfe5f7', 'id', 'copyDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '3768e17dff674187aba90a9a69dfe5f7');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'cbf501f9e45d41429df44fd3d72d0cbf', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.actionsGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '96a029399d6b4999addfde3b7cf1d38e', 0, 1, '/', '/', '0', 'cbf501f9e45d41429df44fd3d72d0cbf', 'id', 'actionsGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cbf501f9e45d41429df44fd3d72d0cbf');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'df1762089616432ebe8c6a195bfccc25', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.activateDoc', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''activateDoc'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''activateDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8a183c7e0399451fa5d4a99b5226680a', 0, 1, '/', '/', '0', 'df1762089616432ebe8c6a195bfccc25', 'action', 'ActivateDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'df1762089616432ebe8c6a195bfccc25');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '092fcafb18cb47818538b508ddf90ba0', 0, 1, '/', '/', '0', 'df1762089616432ebe8c6a195bfccc25', 'id', 'activateDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'df1762089616432ebe8c6a195bfccc25');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '2acddc8d7d9f4118827e0e91e96aa3bb', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.deactivateDoc', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''deactivateDoc'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''deactivateDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '3061d6c1c27f45bd9eea428477a554ac', 0, 1, '/', '/', '0', '2acddc8d7d9f4118827e0e91e96aa3bb', 'action', 'DeactivateDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '2acddc8d7d9f4118827e0e91e96aa3bb');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '374c85ddce6d4d0089c543851fb870b5', 0, 1, '/', '/', '0', '2acddc8d7d9f4118827e0e91e96aa3bb', 'id', 'deactivateDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '2acddc8d7d9f4118827e0e91e96aa3bb');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '0b858eca0275487c9266ef39e48a16a9', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.cancelDoc', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''cancelDoc'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''cancelDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f14de722ad6f4a78a37dcea2a1adfabe', 0, 1, '/', '/', '0', '0b858eca0275487c9266ef39e48a16a9', 'action', 'CancelDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '0b858eca0275487c9266ef39e48a16a9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'cf6cbb6039794c448182f2a307b58072', 0, 1, '/', '/', '0', '0b858eca0275487c9266ef39e48a16a9', 'id', 'cancelDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '0b858eca0275487c9266ef39e48a16a9');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '1b78e9ca6ed0440f85eb7a0f3fc4cd4d', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus01', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus01'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus01'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2b7c144a744f4fb9a45485ab1334eead', 0, 1, '/', '/', '0', '1b78e9ca6ed0440f85eb7a0f3fc4cd4d', 'action', 'MarkAsCustomStatus01DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '1b78e9ca6ed0440f85eb7a0f3fc4cd4d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2f0bd409a286446095a0955470dde3e5', 0, 1, '/', '/', '0', '1b78e9ca6ed0440f85eb7a0f3fc4cd4d', 'id', 'markAsCustomStatus01'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '1b78e9ca6ed0440f85eb7a0f3fc4cd4d');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '8ab7ce9952814ca2904fdb787f89819d', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus02', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus02'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus02'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'd41d30574e3f4ed59517e45c8d6aaa1b', 0, 1, '/', '/', '0', '8ab7ce9952814ca2904fdb787f89819d', 'action', 'MarkAsCustomStatus02DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '8ab7ce9952814ca2904fdb787f89819d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1d331bc33eae43d6b1beecd8372880e9', 0, 1, '/', '/', '0', '8ab7ce9952814ca2904fdb787f89819d', 'id', 'markAsCustomStatus02'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '8ab7ce9952814ca2904fdb787f89819d');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '1c94ee3d8e324327a9eb5fd389e0a3bb', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus03', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus03'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus03'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '82a6f0da0be44392ab067bf380a2c84e', 0, 1, '/', '/', '0', '1c94ee3d8e324327a9eb5fd389e0a3bb', 'action', 'MarkAsCustomStatus03DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '1c94ee3d8e324327a9eb5fd389e0a3bb');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0b11a8994bfa452293bd7deecc66fc65', 0, 1, '/', '/', '0', '1c94ee3d8e324327a9eb5fd389e0a3bb', 'id', 'markAsCustomStatus03'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '1c94ee3d8e324327a9eb5fd389e0a3bb');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'ba8dc169178c4aa68a0894a07792c239', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus04', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus04'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus04'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6bf1fc42d72a4a5b8d23165a4c7bc789', 0, 1, '/', '/', '0', 'ba8dc169178c4aa68a0894a07792c239', 'action', 'MarkAsCustomStatus04DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ba8dc169178c4aa68a0894a07792c239');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'b6165e64d6694e3b80c6e4d8dc306de2', 0, 1, '/', '/', '0', 'ba8dc169178c4aa68a0894a07792c239', 'id', 'markAsCustomStatus04'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ba8dc169178c4aa68a0894a07792c239');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '3d8bfcd1d64447febca4013dfb382764', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus05', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus05'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus05'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '14f8a9525431402c8944ab64c985384c', 0, 1, '/', '/', '0', '3d8bfcd1d64447febca4013dfb382764', 'action', 'MarkAsCustomStatus05DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '3d8bfcd1d64447febca4013dfb382764');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'fbd66cce4bd64b8c8c8e4263aa2883ae', 0, 1, '/', '/', '0', '3d8bfcd1d64447febca4013dfb382764', 'id', 'markAsCustomStatus05'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '3d8bfcd1d64447febca4013dfb382764');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'e221947645df4522bc5a9bc714a8ae70', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus06', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus06'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus06'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e773d79a24cf4f84bbfe9b9dd3291510', 0, 1, '/', '/', '0', 'e221947645df4522bc5a9bc714a8ae70', 'action', 'MarkAsCustomStatus06DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'e221947645df4522bc5a9bc714a8ae70');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '19edc5708bd14347b8063ec6b047123d', 0, 1, '/', '/', '0', 'e221947645df4522bc5a9bc714a8ae70', 'id', 'markAsCustomStatus06'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'e221947645df4522bc5a9bc714a8ae70');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'f88d558e0ce84e8e98a3aa8172ef88ec', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus07', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus07'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus07'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '722c9af52f5c481bb01328b8412ab065', 0, 1, '/', '/', '0', 'f88d558e0ce84e8e98a3aa8172ef88ec', 'action', 'MarkAsCustomStatus07DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'f88d558e0ce84e8e98a3aa8172ef88ec');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f13b006b0e264c3eb90a18aa07438672', 0, 1, '/', '/', '0', 'f88d558e0ce84e8e98a3aa8172ef88ec', 'id', 'markAsCustomStatus07'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'f88d558e0ce84e8e98a3aa8172ef88ec');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a2a1cab38bc64a7ab5a8fcc4003e6768', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus08', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus08'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus08'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8e62bb4cc0a641f3a9ac69f5d732d856', 0, 1, '/', '/', '0', 'a2a1cab38bc64a7ab5a8fcc4003e6768', 'action', 'MarkAsCustomStatus08DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a2a1cab38bc64a7ab5a8fcc4003e6768');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '670a0bb4d83f4c66bb4d4ef17faf7b45', 0, 1, '/', '/', '0', 'a2a1cab38bc64a7ab5a8fcc4003e6768', 'id', 'markAsCustomStatus08'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a2a1cab38bc64a7ab5a8fcc4003e6768');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '5b0720c9c04c41e9809438eb19bcbe28', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus09', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus09'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus09'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'aadb361e173d44ca9af5810fb0315f47', 0, 1, '/', '/', '0', '5b0720c9c04c41e9809438eb19bcbe28', 'action', 'MarkAsCustomStatus09DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5b0720c9c04c41e9809438eb19bcbe28');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2bdb9946180b47808b3e925e060c96e3', 0, 1, '/', '/', '0', '5b0720c9c04c41e9809438eb19bcbe28', 'id', 'markAsCustomStatus09'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5b0720c9c04c41e9809438eb19bcbe28');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'f785c3ca60404e3fbcc7a26e567a95aa', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus10', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus10'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus10'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4de131bdde304f95be949ab859117e6e', 0, 1, '/', '/', '0', 'f785c3ca60404e3fbcc7a26e567a95aa', 'action', 'MarkAsCustomStatus10DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'f785c3ca60404e3fbcc7a26e567a95aa');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '67bf128df9414b2d9540fea55c3a6a76', 0, 1, '/', '/', '0', 'f785c3ca60404e3fbcc7a26e567a95aa', 'id', 'markAsCustomStatus10'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'f785c3ca60404e3fbcc7a26e567a95aa');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'acc59c61a845446b84a32e70ac36bdfd', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.markAsGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8fb2f0adbbe24317877c61cad18bd40e', 0, 1, '/', '/', '0', 'acc59c61a845446b84a32e70ac36bdfd', 'id', 'markAsGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'acc59c61a845446b84a32e70ac36bdfd');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '440d47cac93249a9930096ef2ddbad1e', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.toolsGroup.importRawData', 'import.importMenubar.toolsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']/MenuItem[@id=''''importRawData'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']/MenuItem[@id=''''importRawData'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6a7b26c0da7e4611a7c5025df4c34ba8', 0, 1, '/', '/', '0', '440d47cac93249a9930096ef2ddbad1e', 'action', 'ImportRawData'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '440d47cac93249a9930096ef2ddbad1e');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8a9075005ea24f1b932077d02d738a9c', 0, 1, '/', '/', '0', '440d47cac93249a9930096ef2ddbad1e', 'id', 'importRawData'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '440d47cac93249a9930096ef2ddbad1e');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '2724d7fc5bfb428395ec06d35a228b6f', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.toolsGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f2ece6b24aeb45ac837e3ff87c59bf97', 0, 1, '/', '/', '0', '2724d7fc5bfb428395ec06d35a228b6f', 'id', 'toolsGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '2724d7fc5bfb428395ec06d35a228b6f');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'fdb80f16dce8418fbb857735187ff721', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Menubar', NULL, NULL, '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '5fdeb543eba14e5198aa4f308d41b9c4', 0, 1, '/', '/', '0', 'fdb80f16dce8418fbb857735187ff721', 'align', 'left'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'fdb80f16dce8418fbb857735187ff721');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '9637e4a1ba4c4b06bf9cb56ab6750574', 0, 1, '/', '/', '0', 'fdb80f16dce8418fbb857735187ff721', 'cssClass', 'cbx-importMenubar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'fdb80f16dce8418fbb857735187ff721');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '5798d5c119fc42aebe8d2292d44208ef', 0, 1, '/', '/', '0', 'fdb80f16dce8418fbb857735187ff721', 'id', 'importMenubar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'fdb80f16dce8418fbb857735187ff721');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '4f0306cf9fbd47468e21004106194faf', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Toolbar', NULL, NULL, '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '297d4be0d9884ad3aba25e5168fd58b2', 0, 1, '/', '/', '0', '4f0306cf9fbd47468e21004106194faf', 'id', 'importToolbar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '4f0306cf9fbd47468e21004106194faf');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '694c5b1bbb634c0dbbe66657bf800c15', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importNo', 'Field', 'lbl.import.tabHeader.generalInfoSection.importNo', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''importNo'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''importNo'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'dbce7ed2ec634fb29b0d2f8965571011', 0, 1, '/', '/', '0', '694c5b1bbb634c0dbbe66657bf800c15', 'id', 'importNo'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '694c5b1bbb634c0dbbe66657bf800c15');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '19e7639900f9463b878f6b0c72d943f2', 0, 1, '/', '/', '0', '694c5b1bbb634c0dbbe66657bf800c15', 'size', 'M'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '694c5b1bbb634c0dbbe66657bf800c15');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '277e949293c944a3bfb4748150db5106', 0, 1, '/', '/', '0', '694c5b1bbb634c0dbbe66657bf800c15', 'type', 'Text'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '694c5b1bbb634c0dbbe66657bf800c15');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '16980bd44bbc41d5b1dc45c4270cdfb4', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'headerRow', 'Field', 'lbl.import.tabHeader.generalInfoSection.headerRow', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''headerRow'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''headerRow'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e3b6b298bc7f4fcbbdd742389528488e', 0, 1, '/', '/', '0', '16980bd44bbc41d5b1dc45c4270cdfb4', 'id', 'headerRow'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '16980bd44bbc41d5b1dc45c4270cdfb4');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0e86412303ec4873a9e26e6b19fdafc9', 0, 1, '/', '/', '0', '16980bd44bbc41d5b1dc45c4270cdfb4', 'size', 'M'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '16980bd44bbc41d5b1dc45c4270cdfb4');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1f76f81e99614fecbc659ee390999235', 0, 1, '/', '/', '0', '16980bd44bbc41d5b1dc45c4270cdfb4', 'type', 'Number'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '16980bd44bbc41d5b1dc45c4270cdfb4');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '09707d11a9e141a19e274cbdd883ef5e', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'startRow', 'Field', 'lbl.import.tabHeader.generalInfoSection.startRow', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''startRow'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''startRow'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '64eae4f482c346b38823c51bbef6ff67', 0, 1, '/', '/', '0', '09707d11a9e141a19e274cbdd883ef5e', 'id', 'startRow'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '09707d11a9e141a19e274cbdd883ef5e');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f525886641a04360b0aa65143c8b6981', 0, 1, '/', '/', '0', '09707d11a9e141a19e274cbdd883ef5e', 'size', 'M'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '09707d11a9e141a19e274cbdd883ef5e');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '57322a9b11b4490d9ea6b9b11239252e', 0, 1, '/', '/', '0', '09707d11a9e141a19e274cbdd883ef5e', 'type', 'Number'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '09707d11a9e141a19e274cbdd883ef5e');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '133b835fe1d64ed889d8068a4ac00fc8', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'fileId', 'Field', 'lbl.import.tabHeader.generalInfoSection.fileId', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''fileId'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''fileId'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '821e75cc3d2a42228fe844324c6c70e7', 0, 1, '/', '/', '0', '133b835fe1d64ed889d8068a4ac00fc8', 'id', 'fileId'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '133b835fe1d64ed889d8068a4ac00fc8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0eff10fadedb4f498acfb38647b6ac10', 0, 1, '/', '/', '0', '133b835fe1d64ed889d8068a4ac00fc8', 'size', 'L'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '133b835fe1d64ed889d8068a4ac00fc8');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '74329e91179d48558a17417fa3932691', 0, 1, '/', '/', '0', '133b835fe1d64ed889d8068a4ac00fc8', 'type', 'Attach'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '133b835fe1d64ed889d8068a4ac00fc8');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '0e469a2f646347e9bf2c803dcac36fc2', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'fields', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'dddec8a3c091414ab8665f8a85ecbfb7', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Section', 'lbl.import.tabHeader.generalInfoSection', 'import.tabHeader', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'bfc1da8c899a41c09c28c2f0ed661666', 0, 1, '/', '/', '0', 'dddec8a3c091414ab8665f8a85ecbfb7', 'arrangement', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'dddec8a3c091414ab8665f8a85ecbfb7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '700aa42650324c9395a7b00b1057f67b', 0, 1, '/', '/', '0', 'dddec8a3c091414ab8665f8a85ecbfb7', 'id', 'generalInfoSection'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'dddec8a3c091414ab8665f8a85ecbfb7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '7e39f3b3dae949cf86c459a510980215', 0, 1, '/', '/', '0', 'dddec8a3c091414ab8665f8a85ecbfb7', 'ratio', '100%'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'dddec8a3c091414ab8665f8a85ecbfb7');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'de2d5464c6a84d5daa17fde689a5aefc', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importModule', 'Field', 'lbl.import.tabHeader.dataSection.importModule', 'import.tabHeader.dataSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importModule'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importModule'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '140f0b7a00c24afd91da3c9cc26b19f8', 0, 1, '/', '/', '0', 'de2d5464c6a84d5daa17fde689a5aefc', 'id', 'importModule'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'de2d5464c6a84d5daa17fde689a5aefc');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '7b7dee308da548f59f46a9bddc578b75', 0, 1, '/', '/', '0', 'de2d5464c6a84d5daa17fde689a5aefc', 'type', 'Dropdown'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'de2d5464c6a84d5daa17fde689a5aefc');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'd2feef75681e48faa1ad5aeac31cdf9a', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importTable', 'Field', 'lbl.import.tabHeader.dataSection.importTable', 'import.tabHeader.dataSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importTable'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importTable'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '88c8e65807f240308901a6ffdbf1e4bd', 0, 1, '/', '/', '0', 'd2feef75681e48faa1ad5aeac31cdf9a', 'id', 'importTable'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'd2feef75681e48faa1ad5aeac31cdf9a');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ea76ed2f946a47d3960259c61161a8cf', 0, 1, '/', '/', '0', 'd2feef75681e48faa1ad5aeac31cdf9a', 'type', 'Dropdown'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'd2feef75681e48faa1ad5aeac31cdf9a');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'c4f6c3d38eca4aa0bba125c5c303a324', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importKeyDisplay', 'Field', 'lbl.import.tabHeader.dataSection.importKeyDisplay', 'import.tabHeader.dataSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importKeyDisplay'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importKeyDisplay'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8cebf80f8f8545f183010976735ba2ca', 0, 1, '/', '/', '0', 'c4f6c3d38eca4aa0bba125c5c303a324', 'id', 'importKeyDisplay'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'c4f6c3d38eca4aa0bba125c5c303a324');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ea582511698e491f83a6104a3c1084a9', 0, 1, '/', '/', '0', 'c4f6c3d38eca4aa0bba125c5c303a324', 'type', 'Text'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'c4f6c3d38eca4aa0bba125c5c303a324');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '1abe780a45a94f20bb30023b79ea1b81', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'fields', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '23e15d27fbaa41f596741d2925d705b9', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Section', 'lbl.import.tabHeader.dataSection', 'import.tabHeader', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'aa425ef4aa7b44489d08b2ff0d1cfef3', 0, 1, '/', '/', '0', '23e15d27fbaa41f596741d2925d705b9', 'arrangement', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '23e15d27fbaa41f596741d2925d705b9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '766cd86322fd48389859dd09d8878fb8', 0, 1, '/', '/', '0', '23e15d27fbaa41f596741d2925d705b9', 'id', 'dataSection'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '23e15d27fbaa41f596741d2925d705b9');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '82327f9cdef94528852ee86b0850ca32', 0, 1, '/', '/', '0', '23e15d27fbaa41f596741d2925d705b9', 'ratio', '100%'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '23e15d27fbaa41f596741d2925d705b9');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '1df0f30e97d9429f879f72127a8c8d95', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'startImport', 'Field', 'lbl.import.tabHeader.importMappings.startImport', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar/Field[@id=''''startImport'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar/Field[@id=''''startImport'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2eef9fd6ed2c43ee846b6280d809e91f', 0, 1, '/', '/', '0', '1df0f30e97d9429f879f72127a8c8d95', 'action', 'importAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '1df0f30e97d9429f879f72127a8c8d95');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '46297725fd654e04947d6254d22b45e9', 0, 1, '/', '/', '0', '1df0f30e97d9429f879f72127a8c8d95', 'id', 'startImport'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '1df0f30e97d9429f879f72127a8c8d95');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '9cbb2b2ea4c943ea9c7b16e7aaea63d8', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Buttonbar', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'c31014ac0e04434fb2c3ccbd55cb92d4', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'excelLine', 'Column', 'lbl.import.tabHeader.importMappings.excelLine', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''excelLine'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''excelLine'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'a3bc89580c4d46c1834b168899a2bd4b', 0, 1, '/', '/', '0', 'c31014ac0e04434fb2c3ccbd55cb92d4', 'id', 'excelLine'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'c31014ac0e04434fb2c3ccbd55cb92d4');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'acf90affa17748d2b7bd87bac6609a5b', 0, 1, '/', '/', '0', 'c31014ac0e04434fb2c3ccbd55cb92d4', 'type', 'Number'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'c31014ac0e04434fb2c3ccbd55cb92d4');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a5ffa7c706f64fbd9628af1072e897b6', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'importStatus', 'Column', 'lbl.import.tabHeader.importMappings.importStatus', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''importStatus'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''importStatus'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '839921c0254746f5b4c62d315c87ca41', 0, 1, '/', '/', '0', 'a5ffa7c706f64fbd9628af1072e897b6', 'id', 'importStatus'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a5ffa7c706f64fbd9628af1072e897b6');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0e49a9121169496eac37c01e022ea268', 0, 1, '/', '/', '0', 'a5ffa7c706f64fbd9628af1072e897b6', 'type', 'Text'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a5ffa7c706f64fbd9628af1072e897b6');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '5363240d01b14067a1d2a553fbfdd3e1', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'error', 'Column', 'lbl.import.tabHeader.importMappings.error', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''error'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''error'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '01206c27b21045feb0566bb83d7b010e', 0, 1, '/', '/', '0', '5363240d01b14067a1d2a553fbfdd3e1', 'id', 'error'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5363240d01b14067a1d2a553fbfdd3e1');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6fbfbdcd6e514c208d26b01b16c8ddd3', 0, 1, '/', '/', '0', '5363240d01b14067a1d2a553fbfdd3e1', 'type', 'TextArea'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5363240d01b14067a1d2a553fbfdd3e1');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'e415f20ee4c345608d1136a72555adcf', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'columns', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '7e561d44cd0d4a21ba9f929f4b29af19', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'importMappings', 'Grid', 'lbl.import.tabHeader.importMappings', 'import.tabHeader', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '19f6f3c936924c5994bc21880f84b8e5', 0, 1, '/', '/', '0', '7e561d44cd0d4a21ba9f929f4b29af19', 'alwaysSelectable', 'false'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '7e561d44cd0d4a21ba9f929f4b29af19');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '63b26370e4874d5a8f38a88b12b2e30b', 0, 1, '/', '/', '0', '7e561d44cd0d4a21ba9f929f4b29af19', 'entityName', 'ImportMapping'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '7e561d44cd0d4a21ba9f929f4b29af19');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'abfa5f0fbdf1423fb0feaf0bdc98a71d', 0, 1, '/', '/', '0', '7e561d44cd0d4a21ba9f929f4b29af19', 'id', 'importMappings'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '7e561d44cd0d4a21ba9f929f4b29af19');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'c018c98c43f349f18c2f542baddb29f7', 0, 1, '/', '/', '0', '7e561d44cd0d4a21ba9f929f4b29af19', 'showHint', 'false'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '7e561d44cd0d4a21ba9f929f4b29af19');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '6f9978d67207406392285369e5a624b6', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Tab', 'lbl.import.tabHeader', 'import', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'fb16cf2466a84819bc246536fa9966a6', 0, 1, '/', '/', '0', '6f9978d67207406392285369e5a624b6', 'id', 'tabHeader'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6f9978d67207406392285369e5a624b6');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e7acf414081840e29fb8303d6b8d702f', 0, 1, '/', '/', '0', '6f9978d67207406392285369e5a624b6', 'ratio', '67%,33%'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6f9978d67207406392285369e5a624b6');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a5fb0a86a681405a911caa3fea29e849', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'tabs', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '56ac56f28e43474cac4720e3eb9f0348', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'TabGroup', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f94ac2427e374d57a3692de07700aaa9', 0, 1, '/', '/', '0', '56ac56f28e43474cac4720e3eb9f0348', 'id', 'importTabGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '56ac56f28e43474cac4720e3eb9f0348');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '7ef4706a1d7e431f89a6a73407e885af', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'inPopup', NULL, NULL, '/Form[@id=''''importForm'''']/inPopup', 'system', TO_TIMESTAMP('2025-07-30 14:44:16', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/inPopup');
;

