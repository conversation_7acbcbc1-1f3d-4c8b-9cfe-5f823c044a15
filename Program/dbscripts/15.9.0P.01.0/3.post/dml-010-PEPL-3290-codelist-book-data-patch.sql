
drop table if exists dp_pepl_3290_codelist_book;
create table dp_pepl_3290_codelist_book as
select id, ref_no, "name", doc_status, "version" from cnt_codelist_book ccb where  domain_id = 'PEPL'
and ref_no in (
select ref_no from cnt_codelist_book ccb where is_latest and domain_id = 'PEPL'
group by ref_no 
having count(id) > 1
)
order by ref_no;

update cnt_codelist_book book
	set (ref_no,business_ref_no) = (dp."name", dp."name")
from dp_pepl_3290_codelist_book dp
where dp.id = book.id;

update cnt_codelist_book_m book_m
	set ref_no = dp."name"
from dp_pepl_3290_codelist_book dp
where dp.id = book_m.id;

delete from cnt_serialized_entity where target_id in (select id from dp_pepl_3290_codelist_book);

commit;
