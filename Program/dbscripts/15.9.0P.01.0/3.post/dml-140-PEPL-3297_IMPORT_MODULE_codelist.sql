----------------------------- BEGIN CTC-7-----------------------------
-- JiRa No.: CTC-7
-- Description: add codelist book 'IMPORT_MODULE'
-- Input parameter:<DOMAIN_ID>
-- Scripts re-runnable: Yes
BEGIN;
DO $body$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
BEGIN

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_CODELIST_BOOK where name = 'IMPORT_MODULE' and REF_NO LIKE 'CL%');

INSERT INTO CNT_CODELIST_BOOK(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, IS_LATEST, NAME, TYPE_ID, EFFECTIVE_FROM, EFFECTIVE_TO, DESCRIPTION, REF_NO, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER_NAME, UPDATE_USER_NAME, IS_CPM_INITIALIZED) 
SELECT SYS_GUID(), 1, 1, DOMAINID, 1, null, 'active', 'confirmed', 'system', null, CURRENT_TIMESTAMP, null, '1', 'IMPORT_MODULE', (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'Default' AND IS_LATEST = '1'), CURRENT_TIMESTAMP, null, 'IMPORT_MODULE', (SELECT 'CL' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = DOMAINID), null, null, null, DOMAINID, '0', 'system', null, null 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1');

INSERT INTO CNT_CODELIST(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, SEQ_NO, CODE, NAME, DISABLED, INTERNAL_SEQ_NO, REF_NO, business_ref_no, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
SELECT SYS_GUID(), 1, 1, DOMAINID, (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), 10, 'item', 'Item', '0', 1, 'item', 'item', DOMAINID, '0' 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CNT_CODELIST(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, SEQ_NO, CODE, NAME, DISABLED, INTERNAL_SEQ_NO, REF_NO, business_ref_no, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
SELECT SYS_GUID(), 1, 1, DOMAINID, (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), 20, 'project', 'Project', '0', 1, 'project', 'project', DOMAINID, '0' 
WHERE NOT EXISTS (SELECT 1 FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_TEXT6, CUST_DATE4, CUST_TEXT7, CUST_DATE3, CUST_TEXT4, CUST_DATE2, CUST_TEXT5, CUST_DATE1, CUST_TEXT2, CUST_TEXT3, CUST_DATE7, CUST_DATE6, CUST_TEXT1, CUST_DATE5, CUST_DECIMAL6, CUST_DECIMAL7, CUST_NUMBER3, CUST_NUMBER2, CUST_NUMBER1, CUST_NUMBER7, CUST_NUMBER6, CUST_NUMBER5, CUST_NUMBER4, CUST_DECIMAL1, CUST_DECIMAL2, CUST_DECIMAL3, CUST_DECIMAL4, CUST_DECIMAL5) 
SELECT (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'), DOMAINID, 'CodelistBook', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1'));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_NUMBER2, CUST_NUMBER3, CUST_NUMBER1, CUST_NUMBER6, CUST_NUMBER7, CUST_NUMBER4, CUST_NUMBER5, CUST_TEXT5, CUST_DECIMAL2, CUST_TEXT4, CUST_DECIMAL3, CUST_TEXT7, CUST_DECIMAL4, CUST_DECIMAL5, CUST_TEXT6, CUST_TEXT1, CUST_TEXT3, CUST_TEXT2, CUST_DECIMAL1, CUST_DATE3, CUST_DATE4, CUST_DATE1, CUST_DATE2, CUST_DECIMAL6, CUST_DATE7, CUST_DECIMAL7, CUST_DATE5, CUST_DATE6) 
SELECT (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')), DOMAINID, 'Codelist', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'item' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')));

INSERT INTO CTM_CODELIST_BOOK(ID, DOMAIN_ID, REF_ENTITY_NAME, CUST_NUMBER2, CUST_NUMBER3, CUST_NUMBER1, CUST_NUMBER6, CUST_NUMBER7, CUST_NUMBER4, CUST_NUMBER5, CUST_TEXT5, CUST_DECIMAL2, CUST_TEXT4, CUST_DECIMAL3, CUST_TEXT7, CUST_DECIMAL4, CUST_DECIMAL5, CUST_TEXT6, CUST_TEXT1, CUST_TEXT3, CUST_TEXT2, CUST_DECIMAL1, CUST_DATE3, CUST_DATE4, CUST_DATE1, CUST_DATE2, CUST_DECIMAL6, CUST_DATE7, CUST_DECIMAL7, CUST_DATE5, CUST_DATE6) 
SELECT (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')), DOMAINID, 'Codelist', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null 
WHERE NOT EXISTS (SELECT 1 FROM CTM_CODELIST_BOOK WHERE  DOMAIN_ID = DOMAINID AND ID = (SELECT ID FROM CNT_CODELIST WHERE DOMAIN_ID = DOMAINID AND CODE = 'project' AND PARENT_ID = (SELECT ID FROM CNT_CODELIST_BOOK WHERE DOMAIN_ID = DOMAINID AND NAME = 'IMPORT_MODULE' AND IS_LATEST = '1')));

-- other updates
update cnt_domain_attribute set value=value||',import.importMappingList:100' where key='ui.grid.paging.config' and value not like '%import.importMappingList%';

update cnt_domain_attribute set value=value||',import' where key='cbx.concurrent.edit.enabled.modules' and domain_id=DOMAINID and value not like '%import%';

INSERT INTO cnt_seq_def(id, revision, entity_version, domain_id, hub_domain_id, is_for_reference, internal_seq_no, duid, seq_id, start_with, max_value, increment_by, "cycle", cache_size, updated_on, next_val, cycle_started_on) 
select SYS_GUID(),1, 1,DOMAINID,DOMAINID,false,NULL,NULL,'CBX_SEQ_IMPORT_NO',1,999999,1, 'NULL' ,0,'2025-04-07 03:46:52.000',1,'2025-04-07 03:46:52.000' 
WHERE NOT EXISTS(SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID=DOMAINID AND SEQ_ID='CBX_SEQ_IMPORT_NO');

END;
$body$ LANGUAGE PLPGSQL;
-----------------------------END CTC-7------------------------------

COMMIT;

