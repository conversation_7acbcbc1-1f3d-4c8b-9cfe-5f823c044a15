--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import', 'en_US', 'PEPL', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.docStatus', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.header.docStatus' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.headerIntegration', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.header.headerIntegration' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.headerName', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.header.headerName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.linkbar', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.header.linkbar' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.status', 'en_US', 'PEPL', 'Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.header.status' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.header.version', 'en_US', 'PEPL', 'Version', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.header.version' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.actionsGroup', 'en_US', 'PEPL', 'Actions', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.actionsGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.actionsGroup.copyDoc', 'en_US', 'PEPL', 'Copy', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.actionsGroup.copyDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.amendDoc', 'en_US', 'PEPL', 'Amend', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.amendDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.createGroup', 'en_US', 'PEPL', 'Create', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.createGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.createGroup.openImportPopupWin', 'en_US', 'PEPL', 'New Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.createGroup.openImportPopupWin' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup', 'en_US', 'PEPL', 'Mark as', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.activateDoc', 'en_US', 'PEPL', 'Active', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.activateDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.cancelDoc', 'en_US', 'PEPL', 'Canceled', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.cancelDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.deactivateDoc', 'en_US', 'PEPL', 'Inactive', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.deactivateDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus01', 'en_US', 'PEPL', 'Custom Status 1', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus01' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus02', 'en_US', 'PEPL', 'Custom Status 2', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus02' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus03', 'en_US', 'PEPL', 'Custom Status 3', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus03' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus04', 'en_US', 'PEPL', 'Custom Status 4', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus04' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus05', 'en_US', 'PEPL', 'Custom Status 5', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus05' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus06', 'en_US', 'PEPL', 'Custom Status 6', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus06' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus07', 'en_US', 'PEPL', 'Custom Status 7', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus07' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus08', 'en_US', 'PEPL', 'Custom Status 8', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus08' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus09', 'en_US', 'PEPL', 'Custom Status 9', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus09' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus10', 'en_US', 'PEPL', 'Custom Status 10', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.markAsGroup.markAsCustomStatus10' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.toolsGroup', 'en_US', 'PEPL', 'Tools', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.toolsGroup' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.importMenubar.toolsGroup.importRawData', 'en_US', 'PEPL', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.importMenubar.toolsGroup.importRawData' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader', 'en_US', 'PEPL', 'Header', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection', 'en_US', 'PEPL', 'Data', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.dataSection' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection.importKeyDisplay', 'en_US', 'PEPL', 'Key', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.dataSection.importKeyDisplay' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection.importModule', 'en_US', 'PEPL', 'Module', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.dataSection.importModule' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.dataSection.importTable', 'en_US', 'PEPL', 'Table', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.dataSection.importTable' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection', 'en_US', 'PEPL', 'General Information', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.fileId', 'en_US', 'PEPL', 'File', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.fileId' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.headerRow', 'en_US', 'PEPL', 'Header Row', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.headerRow' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.importNo', 'en_US', 'PEPL', 'Import No.', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.importNo' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.generalInfoSection.startRow', 'en_US', 'PEPL', 'Start Row', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.generalInfoSection.startRow' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings', 'en_US', 'PEPL', 'Mapping & Validation', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.importMappings' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.error', 'en_US', 'PEPL', 'Error', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.importMappings.error' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.excelLine', 'en_US', 'PEPL', 'Excel Line', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.importMappings.excelLine' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.importStatus', 'en_US', 'PEPL', 'Import Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.importMappings.importStatus' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.startImport', 'en_US', 'PEPL', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.import.tabHeader.importMappings.startImport' AND LOCALE='en_US');
