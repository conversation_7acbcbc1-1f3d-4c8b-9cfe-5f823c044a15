--// FILE: DML_FORM_import.sql

DELETE FROM CNT_FIELD_PATH_ATTRIBUTES WHERE PARENT_ID IN (SELECT ID FROM CNT_FIELD_PATH WHERE FORM_ID = 'importForm' AND FORM_VERSION = 1);

DELETE FROM CNT_FIELD_PATH WHERE FORM_ID = 'importForm' AND FORM_VERSION = 1;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '5c8a03ac321f4d4cbe954f59ea91de03', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'docStatus', 'Field', 'lbl.import.header.docStatus', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''docStatus'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''docStatus'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'caebfe07ae694e4cacc3473e878bc164', 0, 1, '/', '/', '0', '5c8a03ac321f4d4cbe954f59ea91de03', 'align', 'left'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5c8a03ac321f4d4cbe954f59ea91de03');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'b260240e060d4e569cd4888930237937', 0, 1, '/', '/', '0', '5c8a03ac321f4d4cbe954f59ea91de03', 'format', 'inactive:(inactive),active:,canceled:(canceled)'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5c8a03ac321f4d4cbe954f59ea91de03');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '398a084d204a422fb1f77e5f9eb145e0', 0, 1, '/', '/', '0', '5c8a03ac321f4d4cbe954f59ea91de03', 'hideLabel', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5c8a03ac321f4d4cbe954f59ea91de03');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '5920c6a6967a478abb9568c7a2baaab2', 0, 1, '/', '/', '0', '5c8a03ac321f4d4cbe954f59ea91de03', 'id', 'docStatus'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5c8a03ac321f4d4cbe954f59ea91de03');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'bbe02cf00be648778384a5ad5b9602ad', 0, 1, '/', '/', '0', '5c8a03ac321f4d4cbe954f59ea91de03', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5c8a03ac321f4d4cbe954f59ea91de03');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4da5a21266fe4c23991e823acfecce4a', 0, 1, '/', '/', '0', '5c8a03ac321f4d4cbe954f59ea91de03', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5c8a03ac321f4d4cbe954f59ea91de03');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '92affe7e6cc84ac2ac358017369dabe7', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'headerName', 'Field', 'lbl.import.header.headerName', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerName'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerName'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '794bce92378a4176aed77253bcbd150b', 0, 1, '/', '/', '0', '92affe7e6cc84ac2ac358017369dabe7', 'align', 'left'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '92affe7e6cc84ac2ac358017369dabe7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e173f1b1db374e379d31c1dfdb844173', 0, 1, '/', '/', '0', '92affe7e6cc84ac2ac358017369dabe7', 'format', '{name}'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '92affe7e6cc84ac2ac358017369dabe7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '17024958261a45ad9dc37ab99363c36d', 0, 1, '/', '/', '0', '92affe7e6cc84ac2ac358017369dabe7', 'hideLabel', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '92affe7e6cc84ac2ac358017369dabe7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8c87dfeb15a849bc950a71fb4fb2df6b', 0, 1, '/', '/', '0', '92affe7e6cc84ac2ac358017369dabe7', 'id', 'headerName'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '92affe7e6cc84ac2ac358017369dabe7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f2adde703d564a38a1c7038f179f5da6', 0, 1, '/', '/', '0', '92affe7e6cc84ac2ac358017369dabe7', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '92affe7e6cc84ac2ac358017369dabe7');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1b960096d4044ca7b80714a01e0b0574', 0, 1, '/', '/', '0', '92affe7e6cc84ac2ac358017369dabe7', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '92affe7e6cc84ac2ac358017369dabe7');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'eed0912b5c1a4a2691ddb69039af7128', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'status', 'Field', 'lbl.import.header.status', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''status'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''status'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '951516fe17c94c4dad19edd599f168b0', 0, 1, '/', '/', '0', 'eed0912b5c1a4a2691ddb69039af7128', 'align', 'right'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'eed0912b5c1a4a2691ddb69039af7128');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4f7db35a20034350946604b5366f6f76', 0, 1, '/', '/', '0', 'eed0912b5c1a4a2691ddb69039af7128', 'id', 'status'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'eed0912b5c1a4a2691ddb69039af7128');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '5ca1e2b286334a99bf678e33c79a048f', 0, 1, '/', '/', '0', 'eed0912b5c1a4a2691ddb69039af7128', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'eed0912b5c1a4a2691ddb69039af7128');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '36d34d0c01ff482798c83382cccbba47', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'version', 'Field', 'lbl.import.header.version', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''version'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''version'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8ae8764de7c947f98f1d36ff9b691edd', 0, 1, '/', '/', '0', '36d34d0c01ff482798c83382cccbba47', 'align', 'right'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '36d34d0c01ff482798c83382cccbba47');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'd94c589a4086463a89fab07c30111b8f', 0, 1, '/', '/', '0', '36d34d0c01ff482798c83382cccbba47', 'format', '{version}'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '36d34d0c01ff482798c83382cccbba47');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'c3cc08c131fb42aab49e88b49efc81e3', 0, 1, '/', '/', '0', '36d34d0c01ff482798c83382cccbba47', 'id', 'version'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '36d34d0c01ff482798c83382cccbba47');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '79d280c05fe44fa4855595d0487ed9be', 0, 1, '/', '/', '0', '36d34d0c01ff482798c83382cccbba47', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '36d34d0c01ff482798c83382cccbba47');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '75cd3e39499345f8a1ea539e649d6639', 0, 1, '/', '/', '0', '36d34d0c01ff482798c83382cccbba47', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '36d34d0c01ff482798c83382cccbba47');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'bb4492c29836452b89ab71939b69fe9d', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'headerIntegration', 'Field', 'lbl.import.header.headerIntegration', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerIntegration'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''headerIntegration'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '510ada669a4f4d97a41538816c6cf27c', 0, 1, '/', '/', '0', 'bb4492c29836452b89ab71939b69fe9d', 'align', 'right'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bb4492c29836452b89ab71939b69fe9d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f90f140de6f549b0ab1c3495b6f1b9df', 0, 1, '/', '/', '0', 'bb4492c29836452b89ab71939b69fe9d', 'hideLabel', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bb4492c29836452b89ab71939b69fe9d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f71685d7b8ca4dc7975101e02ca62a75', 0, 1, '/', '/', '0', 'bb4492c29836452b89ab71939b69fe9d', 'id', 'headerIntegration'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bb4492c29836452b89ab71939b69fe9d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '10cdefdc5de24e57ba72dcd8585f7a33', 0, 1, '/', '/', '0', 'bb4492c29836452b89ab71939b69fe9d', 'labelRenderer', 'com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bb4492c29836452b89ab71939b69fe9d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ab0abf8658154f9f94393aa69d70a0bb', 0, 1, '/', '/', '0', 'bb4492c29836452b89ab71939b69fe9d', 'type', 'Label'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'bb4492c29836452b89ab71939b69fe9d');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'd32fc416613e46159f90f4d604ee79d0', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'linkbar', 'Field', 'lbl.import.header.linkbar', 'import.header', '/Form[@id=''''importForm'''']/Header/Field[@id=''''linkbar'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header/Field[@id=''''linkbar'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '342c30d86d0846179d2700de0e207dc4', 0, 1, '/', '/', '0', 'd32fc416613e46159f90f4d604ee79d0', 'id', 'linkbar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'd32fc416613e46159f90f4d604ee79d0');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'aff45f4d20664cfcbc0d6eae9dabe181', 0, 1, '/', '/', '0', 'd32fc416613e46159f90f4d604ee79d0', 'type', 'Linkbar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'd32fc416613e46159f90f4d604ee79d0');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '7a034206334d41c59e0075a3a8b6f219', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Header', NULL, NULL, '/Form[@id=''''importForm'''']/Header', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Header');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '458838c6b4a74d0c82d231d40bd3e33e', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.createGroup.openImportPopupWin', 'import.importMenubar.createGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']/MenuItem[@id=''''openImportPopupWin'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']/MenuItem[@id=''''openImportPopupWin'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4ccd5a4e90a44f2882c7762194c0bf1a', 0, 1, '/', '/', '0', '458838c6b4a74d0c82d231d40bd3e33e', 'action', 'PopImportNewAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '458838c6b4a74d0c82d231d40bd3e33e');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '757809610f4744bdbe80bab60c8604c9', 0, 1, '/', '/', '0', '458838c6b4a74d0c82d231d40bd3e33e', 'id', 'openImportPopupWin'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '458838c6b4a74d0c82d231d40bd3e33e');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'e1192dfcdccc4d6cac2618babaeb7643', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.createGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''createGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6bfbd031ec2d4ce9bc2ddd24ad74b78d', 0, 1, '/', '/', '0', 'e1192dfcdccc4d6cac2618babaeb7643', 'id', 'createGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'e1192dfcdccc4d6cac2618babaeb7643');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'f0d8ca37ed414d2f91f6d2138fe8ded1', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.amendDoc', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuItem[@id=''''amendDoc'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuItem[@id=''''amendDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '05e574be8b634b0dbd8d7c9c05867662', 0, 1, '/', '/', '0', 'f0d8ca37ed414d2f91f6d2138fe8ded1', 'action', 'AmendDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'f0d8ca37ed414d2f91f6d2138fe8ded1');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '08d9991ced6d41f6aa0104f5800c9f83', 0, 1, '/', '/', '0', 'f0d8ca37ed414d2f91f6d2138fe8ded1', 'id', 'amendDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'f0d8ca37ed414d2f91f6d2138fe8ded1');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '8a929bd93bcf4785aaf92b0ad8e483fb', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.actionsGroup.copyDoc', 'import.importMenubar.actionsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']/MenuItem[@id=''''copyDoc'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']/MenuItem[@id=''''copyDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '447a1240c8174e67b8f84686a3e0b0b7', 0, 1, '/', '/', '0', '8a929bd93bcf4785aaf92b0ad8e483fb', 'action', 'CopyDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '8a929bd93bcf4785aaf92b0ad8e483fb');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '9afe9f260c834d4fa0d9bc5b3a9f44b9', 0, 1, '/', '/', '0', '8a929bd93bcf4785aaf92b0ad8e483fb', 'id', 'copyDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '8a929bd93bcf4785aaf92b0ad8e483fb');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '6cc7154d3b2c4a61bbf21061a703c4e0', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.actionsGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''actionsGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'a1b0ac588db645dfb304f46c3710f3ea', 0, 1, '/', '/', '0', '6cc7154d3b2c4a61bbf21061a703c4e0', 'id', 'actionsGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6cc7154d3b2c4a61bbf21061a703c4e0');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '306a90ae89904a7d842dfa737534f27f', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.activateDoc', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''activateDoc'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''activateDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'bc297a1b245847f2a708eadecd291067', 0, 1, '/', '/', '0', '306a90ae89904a7d842dfa737534f27f', 'action', 'ActivateDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '306a90ae89904a7d842dfa737534f27f');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '195b143c1de74f22bef721b685024d31', 0, 1, '/', '/', '0', '306a90ae89904a7d842dfa737534f27f', 'id', 'activateDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '306a90ae89904a7d842dfa737534f27f');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '7e5154645d754a898138d0fc663d3132', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.deactivateDoc', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''deactivateDoc'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''deactivateDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'd30fe801f0574bbab0413320d0ff5330', 0, 1, '/', '/', '0', '7e5154645d754a898138d0fc663d3132', 'action', 'DeactivateDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '7e5154645d754a898138d0fc663d3132');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f106aaab3ebc4a8c9230f70c043268c7', 0, 1, '/', '/', '0', '7e5154645d754a898138d0fc663d3132', 'id', 'deactivateDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '7e5154645d754a898138d0fc663d3132');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '0e510f1a4d6c4fc3b09d4366d2f400f5', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.cancelDoc', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''cancelDoc'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''cancelDoc'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f4bfd4ea10d0448f82327eaf3ad39e2d', 0, 1, '/', '/', '0', '0e510f1a4d6c4fc3b09d4366d2f400f5', 'action', 'CancelDocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '0e510f1a4d6c4fc3b09d4366d2f400f5');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'b201151a321a4761a5cc5704e54cbefb', 0, 1, '/', '/', '0', '0e510f1a4d6c4fc3b09d4366d2f400f5', 'id', 'cancelDoc'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '0e510f1a4d6c4fc3b09d4366d2f400f5');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '6a526b0ef925477fa37d86ffa55fef88', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus01', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus01'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus01'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '55ed1b406f1143ae862638f13916aa32', 0, 1, '/', '/', '0', '6a526b0ef925477fa37d86ffa55fef88', 'action', 'MarkAsCustomStatus01DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6a526b0ef925477fa37d86ffa55fef88');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'c8f581750cdc472c94f23018e8ebaaa4', 0, 1, '/', '/', '0', '6a526b0ef925477fa37d86ffa55fef88', 'id', 'markAsCustomStatus01'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6a526b0ef925477fa37d86ffa55fef88');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'd76e66f8627d4befb8b654e77440a407', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus02', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus02'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus02'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '61f2b6d220d04203aa8b82948f1063ea', 0, 1, '/', '/', '0', 'd76e66f8627d4befb8b654e77440a407', 'action', 'MarkAsCustomStatus02DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'd76e66f8627d4befb8b654e77440a407');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1a561ab7b29c4a9289283ead54c5d80d', 0, 1, '/', '/', '0', 'd76e66f8627d4befb8b654e77440a407', 'id', 'markAsCustomStatus02'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'd76e66f8627d4befb8b654e77440a407');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '55fdd7561094409caa15bc4c56a60302', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus03', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus03'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus03'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '374b305444b84248ab4f08f5bcd91701', 0, 1, '/', '/', '0', '55fdd7561094409caa15bc4c56a60302', 'action', 'MarkAsCustomStatus03DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '55fdd7561094409caa15bc4c56a60302');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0ccdf4ebe547437596d2ac735193c506', 0, 1, '/', '/', '0', '55fdd7561094409caa15bc4c56a60302', 'id', 'markAsCustomStatus03'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '55fdd7561094409caa15bc4c56a60302');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '9e3606b52c314693b8ce003cd8412819', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus04', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus04'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus04'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e6e006ef0d764c7086f519b419e6c6c8', 0, 1, '/', '/', '0', '9e3606b52c314693b8ce003cd8412819', 'action', 'MarkAsCustomStatus04DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '9e3606b52c314693b8ce003cd8412819');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2d8996115f954c6b9b41589d6e4877c2', 0, 1, '/', '/', '0', '9e3606b52c314693b8ce003cd8412819', 'id', 'markAsCustomStatus04'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '9e3606b52c314693b8ce003cd8412819');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '32c8970368574027a19df0b87e26d3be', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus05', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus05'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus05'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1db627594a7943c89500e48ec1a95bdd', 0, 1, '/', '/', '0', '32c8970368574027a19df0b87e26d3be', 'action', 'MarkAsCustomStatus05DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '32c8970368574027a19df0b87e26d3be');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '0ad13eace705442e8ff008333d63b516', 0, 1, '/', '/', '0', '32c8970368574027a19df0b87e26d3be', 'id', 'markAsCustomStatus05'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '32c8970368574027a19df0b87e26d3be');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '51446825434c4acaba6080850b0ec154', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus06', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus06'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus06'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '15f643c169904557a9e5356aa8cfdc77', 0, 1, '/', '/', '0', '51446825434c4acaba6080850b0ec154', 'action', 'MarkAsCustomStatus06DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '51446825434c4acaba6080850b0ec154');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'd5ac2ceacf454998b88c96f6f380d174', 0, 1, '/', '/', '0', '51446825434c4acaba6080850b0ec154', 'id', 'markAsCustomStatus06'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '51446825434c4acaba6080850b0ec154');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a542fba2a2bd4d9c9ac22f197727c935', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus07', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus07'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus07'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'fd8644c3e3ff4b12ab69ea1b38bf4d29', 0, 1, '/', '/', '0', 'a542fba2a2bd4d9c9ac22f197727c935', 'action', 'MarkAsCustomStatus07DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a542fba2a2bd4d9c9ac22f197727c935');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'dcb002558c164d9fb19c97d6d9936abb', 0, 1, '/', '/', '0', 'a542fba2a2bd4d9c9ac22f197727c935', 'id', 'markAsCustomStatus07'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a542fba2a2bd4d9c9ac22f197727c935');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '768a74c695954acaa7c0cd0b3f6e5500', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus08', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus08'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus08'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '88d3df2b5dee4075b47de70cfd3cdc3b', 0, 1, '/', '/', '0', '768a74c695954acaa7c0cd0b3f6e5500', 'action', 'MarkAsCustomStatus08DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '768a74c695954acaa7c0cd0b3f6e5500');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '056eef642acd472f89a4e8a92c6948a2', 0, 1, '/', '/', '0', '768a74c695954acaa7c0cd0b3f6e5500', 'id', 'markAsCustomStatus08'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '768a74c695954acaa7c0cd0b3f6e5500');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'dad6bd58e9f147fb9f425328deee6543', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus09', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus09'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus09'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '707ae72826784d7cb351a80a4b7edc71', 0, 1, '/', '/', '0', 'dad6bd58e9f147fb9f425328deee6543', 'action', 'MarkAsCustomStatus09DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'dad6bd58e9f147fb9f425328deee6543');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8a107ce2fcaf4338911ffcc7f4210efc', 0, 1, '/', '/', '0', 'dad6bd58e9f147fb9f425328deee6543', 'id', 'markAsCustomStatus09'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'dad6bd58e9f147fb9f425328deee6543');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '432b02f153b34d11b1821c7fd2ef91ce', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.markAsGroup.markAsCustomStatus10', 'import.importMenubar.markAsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus10'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']/MenuItem[@id=''''markAsCustomStatus10'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '993ac654835f46c08a85a6231bc70899', 0, 1, '/', '/', '0', '432b02f153b34d11b1821c7fd2ef91ce', 'action', 'MarkAsCustomStatus10DocAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '432b02f153b34d11b1821c7fd2ef91ce');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '53bd6b9e930043a088496694416224a1', 0, 1, '/', '/', '0', '432b02f153b34d11b1821c7fd2ef91ce', 'id', 'markAsCustomStatus10'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '432b02f153b34d11b1821c7fd2ef91ce');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '0e0237a631614198adfc0c64b01a949f', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.markAsGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''markAsGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '106afd186cf64fd4b9083f563de3198b', 0, 1, '/', '/', '0', '0e0237a631614198adfc0c64b01a949f', 'id', 'markAsGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '0e0237a631614198adfc0c64b01a949f');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '9c1f9aaa2c2443508fedbdb08b1f0218', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuItem', 'lbl.import.importMenubar.toolsGroup.importRawData', 'import.importMenubar.toolsGroup', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']/MenuItem[@id=''''importRawData'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']/MenuItem[@id=''''importRawData'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6afad357dcb344c9a49cae16d668f952', 0, 1, '/', '/', '0', '9c1f9aaa2c2443508fedbdb08b1f0218', 'action', 'ImportRawData'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '9c1f9aaa2c2443508fedbdb08b1f0218');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '7882a3965c5a4302b65298b3670c15a8', 0, 1, '/', '/', '0', '9c1f9aaa2c2443508fedbdb08b1f0218', 'id', 'importRawData'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '9c1f9aaa2c2443508fedbdb08b1f0218');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'c2c1e76b4b514e1488662b2706534b01', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'MenuGroup', 'lbl.import.importMenubar.toolsGroup', 'import.importMenubar', '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']/MenuGroup[@id=''''toolsGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'c1def0a70b274d80af9131f5c7646971', 0, 1, '/', '/', '0', 'c2c1e76b4b514e1488662b2706534b01', 'id', 'toolsGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'c2c1e76b4b514e1488662b2706534b01');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '34e185674e524fc58667a1008667bc5b', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Menubar', NULL, NULL, '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']/Menubar[@id=''''importMenubar'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '75dc67cbc7f441789662df59b435355a', 0, 1, '/', '/', '0', '34e185674e524fc58667a1008667bc5b', 'align', 'left'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '34e185674e524fc58667a1008667bc5b');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'fab8ea0d56d548f883a77fe6873f2fc9', 0, 1, '/', '/', '0', '34e185674e524fc58667a1008667bc5b', 'cssClass', 'cbx-importMenubar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '34e185674e524fc58667a1008667bc5b');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'fec59987630b4f038319a4f4760311be', 0, 1, '/', '/', '0', '34e185674e524fc58667a1008667bc5b', 'id', 'importMenubar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '34e185674e524fc58667a1008667bc5b');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'cdffd88ab50544069d0e4e532bb6e16a', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Toolbar', NULL, NULL, '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/Toolbar[@id=''''importToolbar'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '83739c303c5f4a2a9ec4abf563695ee3', 0, 1, '/', '/', '0', 'cdffd88ab50544069d0e4e532bb6e16a', 'id', 'importToolbar'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'cdffd88ab50544069d0e4e532bb6e16a');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '795ef8156dbc4d0aabb30a42c2a6581d', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importNo', 'Field', 'lbl.import.tabHeader.generalInfoSection.importNo', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''importNo'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''importNo'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'a1c952065074405ab2fb9d4f0731bc31', 0, 1, '/', '/', '0', '795ef8156dbc4d0aabb30a42c2a6581d', 'id', 'importNo'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '795ef8156dbc4d0aabb30a42c2a6581d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4af24ccb35864f649db676d0206ec1a9', 0, 1, '/', '/', '0', '795ef8156dbc4d0aabb30a42c2a6581d', 'size', 'M'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '795ef8156dbc4d0aabb30a42c2a6581d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1c4a5c242cab42b79567c41337fe8f09', 0, 1, '/', '/', '0', '795ef8156dbc4d0aabb30a42c2a6581d', 'type', 'Text'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '795ef8156dbc4d0aabb30a42c2a6581d');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '26f8b27556224e74bfc05c03a6cad4ff', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'headerRow', 'Field', 'lbl.import.tabHeader.generalInfoSection.headerRow', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''headerRow'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''headerRow'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '32c6071858024ea783e3a50d0729fbde', 0, 1, '/', '/', '0', '26f8b27556224e74bfc05c03a6cad4ff', 'id', 'headerRow'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '26f8b27556224e74bfc05c03a6cad4ff');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '97633517b3c74cc59dd3f716dbed545e', 0, 1, '/', '/', '0', '26f8b27556224e74bfc05c03a6cad4ff', 'size', 'M'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '26f8b27556224e74bfc05c03a6cad4ff');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '39b3227701e84858bebd1b69a0145c75', 0, 1, '/', '/', '0', '26f8b27556224e74bfc05c03a6cad4ff', 'type', 'Number'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '26f8b27556224e74bfc05c03a6cad4ff');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '65ecfef55bb34813a52fcfe3104fac8a', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'startRow', 'Field', 'lbl.import.tabHeader.generalInfoSection.startRow', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''startRow'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''startRow'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '35593abdf670433eb3112be2d5d70fa7', 0, 1, '/', '/', '0', '65ecfef55bb34813a52fcfe3104fac8a', 'id', 'startRow'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '65ecfef55bb34813a52fcfe3104fac8a');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'd4c732fde48b49f294c9eebc8f47793a', 0, 1, '/', '/', '0', '65ecfef55bb34813a52fcfe3104fac8a', 'size', 'M'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '65ecfef55bb34813a52fcfe3104fac8a');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '34f9fc709179413da9a08f33add8f9a3', 0, 1, '/', '/', '0', '65ecfef55bb34813a52fcfe3104fac8a', 'type', 'Number'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '65ecfef55bb34813a52fcfe3104fac8a');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '60feb9f96fbd4124bf64d3a0d8c1948c', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'fileId', 'Field', 'lbl.import.tabHeader.generalInfoSection.fileId', 'import.tabHeader.generalInfoSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''fileId'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields/Field[@id=''''fileId'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '61fdf97e143e4c6190dc1498bffd8691', 0, 1, '/', '/', '0', '60feb9f96fbd4124bf64d3a0d8c1948c', 'id', 'fileId'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '60feb9f96fbd4124bf64d3a0d8c1948c');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4fb56b5fecba45fa97560a5d5a512955', 0, 1, '/', '/', '0', '60feb9f96fbd4124bf64d3a0d8c1948c', 'size', 'L'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '60feb9f96fbd4124bf64d3a0d8c1948c');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2cbfcddcf5814cee9f6c7cc8bdce8f86', 0, 1, '/', '/', '0', '60feb9f96fbd4124bf64d3a0d8c1948c', 'type', 'Attach'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '60feb9f96fbd4124bf64d3a0d8c1948c');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '0f05482f53b345cba42c845b3029c969', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'fields', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']/fields');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a776ed51ffb64464b856fdbc6588c598', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Section', 'lbl.import.tabHeader.generalInfoSection', 'import.tabHeader', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''generalInfoSection'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '1d40d732f59d43df8d553f92f63a3ae8', 0, 1, '/', '/', '0', 'a776ed51ffb64464b856fdbc6588c598', 'arrangement', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a776ed51ffb64464b856fdbc6588c598');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'a3880e7e79a2470bbaf829d0f43111ce', 0, 1, '/', '/', '0', 'a776ed51ffb64464b856fdbc6588c598', 'id', 'generalInfoSection'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a776ed51ffb64464b856fdbc6588c598');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '481ebbc1346e4d048c6cf5ea0ccca75d', 0, 1, '/', '/', '0', 'a776ed51ffb64464b856fdbc6588c598', 'ratio', '100%'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a776ed51ffb64464b856fdbc6588c598');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '6fc99ed6b78647228250beebf6759579', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importModule', 'Field', 'lbl.import.tabHeader.dataSection.importModule', 'import.tabHeader.dataSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importModule'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importModule'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'ef7c66f7de1246048afd374e77f0ef8a', 0, 1, '/', '/', '0', '6fc99ed6b78647228250beebf6759579', 'id', 'importModule'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6fc99ed6b78647228250beebf6759579');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2512e42c8efc47dab03a2de23d7be405', 0, 1, '/', '/', '0', '6fc99ed6b78647228250beebf6759579', 'type', 'Dropdown'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6fc99ed6b78647228250beebf6759579');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '6195d0efd1a347b98e3cea512e8b3428', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importTable', 'Field', 'lbl.import.tabHeader.dataSection.importTable', 'import.tabHeader.dataSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importTable'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importTable'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '64b278f16a7d472bb7049043faaa7ad4', 0, 1, '/', '/', '0', '6195d0efd1a347b98e3cea512e8b3428', 'id', 'importTable'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6195d0efd1a347b98e3cea512e8b3428');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'b21e42303bf747a9b58240700cece8cc', 0, 1, '/', '/', '0', '6195d0efd1a347b98e3cea512e8b3428', 'type', 'Dropdown'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '6195d0efd1a347b98e3cea512e8b3428');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a4dfbe928a6c4ede88520a93e1934488', 0, 1, 'importForm', 1, '/', '/', '0', 'Import', 'importKeyDisplay', 'Field', 'lbl.import.tabHeader.dataSection.importKeyDisplay', 'import.tabHeader.dataSection', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importKeyDisplay'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields/Field[@id=''''importKeyDisplay'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '18df3bc1bfc94851a1e1180f9111a748', 0, 1, '/', '/', '0', 'a4dfbe928a6c4ede88520a93e1934488', 'id', 'importKeyDisplay'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a4dfbe928a6c4ede88520a93e1934488');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '4bb1742ea84c412ba7eeb5b460ffaaf7', 0, 1, '/', '/', '0', 'a4dfbe928a6c4ede88520a93e1934488', 'type', 'Text'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a4dfbe928a6c4ede88520a93e1934488');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '0c032d3e106a48a2be8f08b754c43271', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'fields', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']/fields');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'ca6b636c9ec149e4b931e41472a3da5d', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Section', 'lbl.import.tabHeader.dataSection', 'import.tabHeader', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Section[@id=''''dataSection'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f670ed74ba214d2ca24e1620412f95da', 0, 1, '/', '/', '0', 'ca6b636c9ec149e4b931e41472a3da5d', 'arrangement', 'true'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ca6b636c9ec149e4b931e41472a3da5d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '5af6c69d5e89470b93570ae0f3a4fb2b', 0, 1, '/', '/', '0', 'ca6b636c9ec149e4b931e41472a3da5d', 'id', 'dataSection'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ca6b636c9ec149e4b931e41472a3da5d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '2a4179c4b20d4101b4ccb01cec6075be', 0, 1, '/', '/', '0', 'ca6b636c9ec149e4b931e41472a3da5d', 'ratio', '100%'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'ca6b636c9ec149e4b931e41472a3da5d');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '56efbf4a99de4de5ae9a9e7c74bb9ebd', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'startImport', 'Field', 'lbl.import.tabHeader.importMappings.startImport', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar/Field[@id=''''startImport'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar/Field[@id=''''startImport'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '61f9f6c9a5774f8180874ea810ca7332', 0, 1, '/', '/', '0', '56efbf4a99de4de5ae9a9e7c74bb9ebd', 'action', 'importAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '56efbf4a99de4de5ae9a9e7c74bb9ebd');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '7069eb55bdbf41c1abf5e519ddb90645', 0, 1, '/', '/', '0', '56efbf4a99de4de5ae9a9e7c74bb9ebd', 'id', 'startImport'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '56efbf4a99de4de5ae9a9e7c74bb9ebd');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '16f180d639b949e4b0dd74842cd00f9e', 0, 1, 'importForm', 1, '/', '/', '0', NULL, 'showError', 'Field', 'lbl.import.tabHeader.importMappings.showError', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar/Field[@id=''''showError'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar/Field[@id=''''showError'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '64738389619e42e5a1ac7c7e6a8c3080', 0, 1, '/', '/', '0', '16f180d639b949e4b0dd74842cd00f9e', 'action', 'showErrorAction'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '16f180d639b949e4b0dd74842cd00f9e');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f3c974cebe1b4893b70a9d337d2937c6', 0, 1, '/', '/', '0', '16f180d639b949e4b0dd74842cd00f9e', 'id', 'showError'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '16f180d639b949e4b0dd74842cd00f9e');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '449fae9af35e4c57a480735aa96d875a', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Buttonbar', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/Buttonbar');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '5fb105a4b3df4cf49f966023f3e299f1', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'excelLine', 'Column', 'lbl.import.tabHeader.importMappings.excelLine', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''excelLine'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''excelLine'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'efa282dc1bcf47bcbfd957d263379a26', 0, 1, '/', '/', '0', '5fb105a4b3df4cf49f966023f3e299f1', 'id', 'excelLine'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5fb105a4b3df4cf49f966023f3e299f1');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '6d0f305a24ca4a0887a6b672b739ca28', 0, 1, '/', '/', '0', '5fb105a4b3df4cf49f966023f3e299f1', 'type', 'Number'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '5fb105a4b3df4cf49f966023f3e299f1');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '98149919bf4a40a88646f93231bd588d', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'importStatus', 'Column', 'lbl.import.tabHeader.importMappings.importStatus', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''importStatus'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''importStatus'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '557d5963fbb747af80ed24c51cf7533f', 0, 1, '/', '/', '0', '98149919bf4a40a88646f93231bd588d', 'id', 'importStatus'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '98149919bf4a40a88646f93231bd588d');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'c292406ef9cd487da1ac386e29648a23', 0, 1, '/', '/', '0', '98149919bf4a40a88646f93231bd588d', 'type', 'Text'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '98149919bf4a40a88646f93231bd588d');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '4751fcdbe4084d88afab8c8b6ce5d49b', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'error', 'Column', 'lbl.import.tabHeader.importMappings.error', 'import.tabHeader.importMappings', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''error'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns/Column[@id=''''error'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'f7b7a3ec4e5548a49cd960c490a7f15a', 0, 1, '/', '/', '0', '4751fcdbe4084d88afab8c8b6ce5d49b', 'id', 'error'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '4751fcdbe4084d88afab8c8b6ce5d49b');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '918b9110a5b546f2b0be349b8ac90373', 0, 1, '/', '/', '0', '4751fcdbe4084d88afab8c8b6ce5d49b', 'type', 'TextArea'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '4751fcdbe4084d88afab8c8b6ce5d49b');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '2bc70ad049a7461985d5b923792c2a2f', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'columns', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']/columns');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '80770fca3cd64ca8a37803f1dea32c44', 0, 1, 'importForm', 1, '/', '/', '0', 'ImportMapping', 'importMappings', 'Grid', 'lbl.import.tabHeader.importMappings', 'import.tabHeader', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']/Grid[@id=''''importMappings'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '8d6097aa53674ebeb6ea1a3b4b999b35', 0, 1, '/', '/', '0', '80770fca3cd64ca8a37803f1dea32c44', 'alwaysSelectable', 'false'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '80770fca3cd64ca8a37803f1dea32c44');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '957ea0aa73974f5c8dcf45161018af7e', 0, 1, '/', '/', '0', '80770fca3cd64ca8a37803f1dea32c44', 'entityName', 'ImportMapping'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '80770fca3cd64ca8a37803f1dea32c44');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '17518e76e418434f8acc99d896899d53', 0, 1, '/', '/', '0', '80770fca3cd64ca8a37803f1dea32c44', 'id', 'importMappings'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '80770fca3cd64ca8a37803f1dea32c44');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '638da33a858043b38c968193ec654238', 0, 1, '/', '/', '0', '80770fca3cd64ca8a37803f1dea32c44', 'showHint', 'false'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '80770fca3cd64ca8a37803f1dea32c44');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT '933afab903a1466ca99a5d8468024845', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'Tab', 'lbl.import.tabHeader', 'import', '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs/Tab[@id=''''tabHeader'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e37a1a982a5c4822ac8528ef8c159edc', 0, 1, '/', '/', '0', '933afab903a1466ca99a5d8468024845', 'id', 'tabHeader'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '933afab903a1466ca99a5d8468024845');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT 'e70acb7900b24c1c955a22a41cbf33eb', 0, 1, '/', '/', '0', '933afab903a1466ca99a5d8468024845', 'ratio', '67%,33%'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = '933afab903a1466ca99a5d8468024845');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'cc13963198084130a193459fce85195c', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'tabs', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']/tabs');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a549bab48837490a81d76d1e679ed9ca', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'TabGroup', NULL, NULL, '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/TabGroup[@id=''''importTabGroup'''']');
;

INSERT INTO CNT_FIELD_PATH_ATTRIBUTES(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PARENT_ID, KEY, VALUE) SELECT '62846b08758c455ebf044492b71ac102', 0, 1, '/', '/', '0', 'a549bab48837490a81d76d1e679ed9ca', 'id', 'importTabGroup'  WHERE  EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE ID = 'a549bab48837490a81d76d1e679ed9ca');
;

INSERT INTO CNT_FIELD_PATH(ID, REVISION, ENTITY_VERSION, FORM_ID, FORM_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ENTITY_NAME, FIELD_ID, TYPE, LABEL_KEY, UI_PATH, X_PATH, CREATE_USER, CREATED_ON) SELECT 'a1cb918c39ef41aaa1d95d1cdb75ca7e', 0, 1, 'importForm', 1, '/', '/', '0', NULL, NULL, 'inPopup', NULL, NULL, '/Form[@id=''''importForm'''']/inPopup', 'system', TO_TIMESTAMP('2025-07-31 17:39:33', 'YYYY-MM-DD HH24:MI:SS')  WHERE NOT EXISTS (SELECT 1 FROM CNT_FIELD_PATH WHERE X_PATH = '/Form[@id=''''importForm'''']/inPopup');
;

