--// FILE: DML_NAVI.sql

--//generate sql clear table cnt_navi_entry
DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'Cnt_navi_entry') AND TARGET_ID IN (SELECT ID FROM cnt_navi_entry WHERE DOMAIN_ID = 'PEPL');

DELETE FROM cnt_navi_entry WHERE DOMAIN_ID = 'PEPL';

--//generate sql clear table cnt_navi_module
DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'Cnt_navi_module') AND TARGET_ID IN (SELECT ID FROM cnt_navi_module WHERE DOMAIN_ID = 'PEPL');

DELETE FROM cnt_navi_module WHERE DOMAIN_ID = 'PEPL';

--//generate sql insert for table cnt_navi_module
INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '0', 'lbl.dashboard.module.home', '1', '1', 'home', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.home', 'system', 'home', '1', 1  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'home');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.plan', '1', '0', 'plan', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.plan', 'system', 'plan', '1', 2  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'plan');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.design', '1', '0', 'design', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.design', 'system', 'design', '1', 3  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'design');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.product', '1', '0', 'product', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.product', 'system', 'product', '1', 4  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'product');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.sourcing', '1', '0', 'sourcing', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.sourcing', 'system', 'sourcing', '1', 5  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'sourcing');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.order', '1', '0', 'order', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.order', 'system', 'order', '1', 6  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'order');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.compliance', '1', '0', 'compliance', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.compliance', 'system', 'compliance', '1', 7  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'compliance');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.quality', '1', '0', 'quality', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.quality', 'system', 'quality', '1', 8  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'quality');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.logistics', '1', '0', 'logistics', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.logistics', 'system', 'logistics', '1', 9  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'logistics');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.finance', '1', '0', 'finance', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.finance', 'system', 'finance', '1', 10  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'finance');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.master', '1', '0', 'master', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.master', 'system', 'master', '1', 11  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'master');


INSERT INTO cnt_navi_module(REVISION, IS_FOR_REFERENCE, CREATE_USER, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, show_In_Dashboard, dashboard_Label, ENTITY_VERSION, is_Default, REF_NO, CREATED_ON, action, LABEL, CREATE_USER_NAME, id, IS_LATEST, SEQ) SELECT '0', '0', 'system', 'PEPL', 'PEPL', NULL, '1', 'lbl.dashboard.module.setup', '1', '0', 'setup', TO_TIMESTAMP('2025-07-31 13:38:19', 'YYYY-MM-DD HH24:MI:SS'), NULL, 'lbl.navi.module.setup', 'system', 'setup', '1', 12  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_module WHERE  id = 'setup');


--//generate sql insert for table cnt_navi_entry
INSERT INTO cnt_navi_entry(REVISION, navi_Module_Id, IS_FOR_REFERENCE, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, view_Name, internal_Seq_No, show_In_Dashboard, NAVI_ENTRY_ID, type, entry_Group, dashboard_Label, dashboard_Seq_No, ENTITY_VERSION, is_Default, action, LABEL, id, IS_LATEST, SEQ) SELECT '0', 'product', '0', 'PEPL', 'PEPL', NULL, NULL, '2', '0', 'itemGroup', 'EntryGroup', NULL, 'lbl.dashboard.entry.itemGroup', '0', '1', '0', NULL, 'lbl.navi.entry.itemGroup', LOWER(SYS_GUID()), '1', 1  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_entry WHERE NAVI_ENTRY_ID = 'itemGroup' AND DOMAIN_ID = 'PEPL');


INSERT INTO cnt_navi_entry(REVISION, navi_Module_Id, IS_FOR_REFERENCE, HUB_DOMAIN_ID, DOMAIN_ID, action_Params, view_Name, internal_Seq_No, show_In_Dashboard, NAVI_ENTRY_ID, type, entry_Group, dashboard_Label, dashboard_Seq_No, ENTITY_VERSION, is_Default, action, LABEL, id, IS_LATEST, SEQ) SELECT '0', 'product', '0', 'PEPL', 'PEPL', NULL, 'itemConceptView', '2', '0', 'itemConcept', 'Entry', 'itemGroup', 'lbl.dashboard.entry.itemConcept', '0', '1', '1', NULL, 'lbl.navi.entry.itemConcept', LOWER(SYS_GUID()), '1', 2  WHERE NOT EXISTS (SELECT 1 FROM cnt_navi_entry WHERE NAVI_ENTRY_ID = 'itemConcept' AND DOMAIN_ID = 'PEPL');


--//generate sql clear table CNT_NAVI_DEFAULT
DELETE FROM CNT_NAVI_DEFAULT WHERE DOMAIN_ID = 'PEPL';

--//generate sql insert for table CNT_NAVI_DEFAULT
