--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, <PERSON><PERSON><PERSON>_ID, LOCALE, <PERSON>OMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'import', 'lbl.import.tabHeader.importMappings.showError', 'en_US', '/', 'Show Error', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='/' AND LABEL_ID='lbl.import.tabHeader.importMappings.showError' AND LOCALE='en_US');
