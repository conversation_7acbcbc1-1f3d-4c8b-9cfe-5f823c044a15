--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'system', 'lbl.navi.module.import', 'en_US', '\', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.navi.module.import' AND LOCALE='en_US');

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, <PERSON>BEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'system', 'lbl.navi.entry.import', 'en_US', '\', 'All Profiles', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.navi.entry.import' AND LOCALE='en_US');

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'system', 'lbl.navi.entry.importGroup', 'en_US', '\', 'Imports', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.navi.entry.importGroup' AND LOCALE='en_US');

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'system', 'lbl.dashboard.module.import', 'en_US', '\', 'Import', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.dashboard.module.import' AND LOCALE='en_US');

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'system', 'lbl.dashboard.entry.import', 'en_US', '\', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.dashboard.entry.import' AND LOCALE='en_US');

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'system', 'lbl.dashboard.entry.importGroup', 'en_US', '\', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='\' AND LABEL_ID='lbl.dashboard.entry.importGroup' AND LOCALE='en_US');


