--// FILE: DDL_ENTITY_import.sql

CREATE TABLE IF NOT EXISTS CNT_IMPORT_M (ID VARCHAR(32),REVISION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_<PERSON>OMAIN_ID VARCHAR(400),VERSION NUMERIC(20, 0),STATUS VARCHAR(400),DOC_STATUS VARCHAR(400),EDITING_STATUS VARCHAR(400),CREATE_USER VARCHAR(100),CREATE_USER_NAME VARCHAR(100),UPDATE_USER VARCHAR(100),UPDATE_USER_NAME VARCHAR(100),CREATED_ON TIMESTAMP,UPDATED_ON TIMESTAMP,INTEGRATION_SOURCE VARCHAR(400),INTEGRATION_STATUS VARCHAR(400),INTEGRATION_NOTE VARCHAR(1000),IS_CPM_INITIALIZED BOOLEAN,IS_LATEST BOOLEAN,REF_NO VARCHAR(1000),PARTY_TEMPLATE_REF VARCHAR(400),PARTY_TEMPLATE_VER NUMERIC(20, 0),PARTY_TEMPLATE_NAME VARCHAR(400),PARTY_NAME1 VARCHAR(1000),PARTY_NAME2 VARCHAR(1000),PARTY_NAME3 VARCHAR(1000),PARTY_NAME4 VARCHAR(1000),PARTY_NAME5 VARCHAR(1000));
CREATE TABLE IF NOT EXISTS CNT_IMPORT (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,VERSION NUMERIC(20, 0),STATUS VARCHAR(400),DOC_STATUS VARCHAR(400),EDITING_STATUS VARCHAR(400),CREATE_USER VARCHAR(100),CREATE_USER_NAME VARCHAR(100),UPDATE_USER VARCHAR(100),UPDATE_USER_NAME VARCHAR(100),CREATED_ON TIMESTAMP,UPDATED_ON TIMESTAMP,INTEGRATION_SOURCE VARCHAR(400),INTEGRATION_STATUS VARCHAR(400),INTEGRATION_NOTE VARCHAR(1000),IS_CPM_INITIALIZED BOOLEAN,IS_LATEST BOOLEAN,IMPORT_NO VARCHAR(400),HEADER_ROW NUMERIC(20, 0),START_ROW NUMERIC(20, 0),FILE_ID VARCHAR(32),IMPORT_MODULE VARCHAR(400),IMPORT_TABLE VARCHAR(400),IMPORT_TABLE_DISPLAY VARCHAR(400),IMPORT_KEY VARCHAR(400),IMPORT_KEY_DISPLAY VARCHAR(400),CHECKSUM VARCHAR(400),REF_NO VARCHAR(1000),BUSINESS_REF_NO VARCHAR(1000),FILE_REF VARCHAR(1000),FILE_VER NUMERIC(20, 0),FILE_REF2 VARCHAR(1000),FILE_DUID VARCHAR(100),
IMPORT_MODULE_NAME VARCHAR(400),IMPORT_MODULE_VER NUMERIC(20, 0));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_SLN (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,VERSION NUMERIC(20, 0),STATUS VARCHAR(400),DOC_STATUS VARCHAR(400),EDITING_STATUS VARCHAR(400),CREATE_USER VARCHAR(100),CREATE_USER_NAME VARCHAR(100),UPDATE_USER VARCHAR(100),UPDATE_USER_NAME VARCHAR(100),CREATED_ON TIMESTAMP,UPDATED_ON TIMESTAMP,INTEGRATION_SOURCE VARCHAR(400),INTEGRATION_STATUS VARCHAR(400),INTEGRATION_NOTE VARCHAR(1000),IS_CPM_INITIALIZED BOOLEAN,IS_LATEST BOOLEAN,PARENT_ID VARCHAR(32),PARENT_ENTITY VARCHAR(400),FIELD_ID VARCHAR(400),REF_ID VARCHAR(32),REF_ENTITY VARCHAR(400),DISPLAY_VALUE VARCHAR(1000),INTERNAL_SEQ_NO NUMERIC(20, 0),REF_REF VARCHAR(1000),REF_VER NUMERIC(20, 0),REF_REF2 VARCHAR(1000),REF_DUID VARCHAR(100));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_MAPPING (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,INTERNAL_SEQ_NO NUMERIC(20, 0),DUID VARCHAR(100),UNIQUE_KEY VARCHAR(32),IMPORT_ID VARCHAR(32),EXCEL_LINE NUMERIC(20, 0),IMPORT_STATUS VARCHAR(400),ERROR VARCHAR(5000));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_MAPPING_FIELD_DEF (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,INTERNAL_SEQ_NO NUMERIC(20, 0),DUID VARCHAR(100),UNIQUE_KEY VARCHAR(32),EXCEL_HEADER VARCHAR(400),FIELD_MAPPING VARCHAR(400),FIELD_MAPPING_DISPLAY VARCHAR(400),DATA_TYPE VARCHAR(400),MODULE VARCHAR(400),CHECKSUM VARCHAR(400),TEMPL_ID VARCHAR(400),IMPORT_ID VARCHAR(400),MODULE_NAME VARCHAR(400),MODULE_VER NUMERIC(20, 0));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,INTERNAL_SEQ_NO NUMERIC(20, 0),DUID VARCHAR(100),UNIQUE_KEY VARCHAR(32),EXCEL_HEADER VARCHAR(400),FIELD_MAPPING VARCHAR(400),FIELD_MAPPING_DISPLAY VARCHAR(400),DATA_TYPE VARCHAR(400),MODULE VARCHAR(400),CHECKSUM VARCHAR(400),UPDATE_USER_NAME VARCHAR(100),UPDATED_ON TIMESTAMP,MODULE_NAME VARCHAR(400),MODULE_VER NUMERIC(20, 0));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_MAPPING_FIELD_DATA (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,INTERNAL_SEQ_NO NUMERIC(20, 0),DUID VARCHAR(100),UNIQUE_KEY VARCHAR(32),MAPPING_ID VARCHAR(32),DEF_ID VARCHAR(400),DATA_VALUE VARCHAR(400));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_PARTY (ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,INTERNAL_SEQ_NO NUMERIC(20, 0),DUID VARCHAR(100),UNIQUE_KEY VARCHAR(32),DOC_ID VARCHAR(32),PARTY_NAME VARCHAR(400),PARTY_NAME_SEQ_NO NUMERIC(20, 0),CONTACT_USER_ID VARCHAR(32),CONTACT_USER_SEQ_NO NUMERIC(20, 0),IS_OWNER BOOLEAN,PARTY_NAME_NAME VARCHAR(400),PARTY_NAME_VER NUMERIC(20, 0),CONTACT_USER_REF VARCHAR(1000),CONTACT_USER_VER NUMERIC(20, 0));
CREATE TABLE IF NOT EXISTS CNT_IMPORT_H (PARENT_ID VARCHAR(400),DOC_VERSION NUMERIC(20, 0),DOC_REVISION NUMERIC(20, 0),ITEM_ID VARCHAR(400),ITEM_REVISION NUMERIC(20, 0),ITEM_ENTITY_NAME VARCHAR(400),TYPE NUMERIC(20, 0),FIELD_ID VARCHAR(400),PATH VARCHAR(1000),REF_VALUE VARCHAR(400),OPERATION VARCHAR(400),VALUE_BEFORE TEXT,VALUE_AFTER TEXT,CREATE_USER VARCHAR(100),CREATE_USER_NAME VARCHAR(100),CREATED_ON TIMESTAMP,TRACKING_LEVEL NUMERIC(20, 0),ID VARCHAR(32),REVISION NUMERIC(20, 0),ENTITY_VERSION NUMERIC(20, 0),DOMAIN_ID VARCHAR(400),HUB_DOMAIN_ID VARCHAR(400),IS_FOR_REFERENCE BOOLEAN,INTERNAL_SEQ_NO NUMERIC(20, 0),DUID VARCHAR(100),UNIQUE_KEY VARCHAR(32));
CREATE TABLE IF NOT EXISTS CTM_IMPORT (ID VARCHAR(32),CUST_TEXT1 VARCHAR(1000),CUST_TEXT2 VARCHAR(1000),CUST_TEXT3 VARCHAR(1000),CUST_TEXT4 VARCHAR(1000),CUST_TEXT5 VARCHAR(1000),CUST_DATE1 DATE,CUST_DATE2 DATE,CUST_DATE3 DATE,CUST_DATE4 DATE,CUST_DATE5 DATE,CUST_NUMBER1 NUMERIC(20, 0),CUST_NUMBER2 NUMERIC(20, 0),CUST_NUMBER3 NUMERIC(20, 0),CUST_NUMBER4 NUMERIC(20, 0),CUST_NUMBER5 NUMERIC(20, 0),CUST_DECIMAL1 NUMERIC(25, 5),CUST_DECIMAL2 NUMERIC(25, 5),CUST_DECIMAL3 NUMERIC(25, 5),CUST_DECIMAL4 NUMERIC(25, 5),CUST_DECIMAL5 NUMERIC(25, 5),CUST_MEMO_TEXT1 VARCHAR(5000),CUST_MEMO_TEXT2 VARCHAR(5000),CUST_MEMO_TEXT3 VARCHAR(5000),CUST_MEMO_TEXT4 VARCHAR(5000),CUST_MEMO_TEXT5 VARCHAR(5000),CUST_CHECKBOX1 BOOLEAN,CUST_CHECKBOX2 BOOLEAN,CUST_CHECKBOX3 BOOLEAN,CUST_CHECKBOX4 BOOLEAN,CUST_CHECKBOX5 BOOLEAN,CUST_CODELIST1 VARCHAR(400),CUST_CODELIST1_NAME VARCHAR(400),CUST_CODELIST1_VER NUMERIC(20, 0),CUST_CODELIST2 VARCHAR(400),CUST_CODELIST2_NAME VARCHAR(400),CUST_CODELIST2_VER NUMERIC(20, 0),
CUST_CODELIST3 VARCHAR(400),CUST_CODELIST3_NAME VARCHAR(400),CUST_CODELIST3_VER NUMERIC(20, 0),CUST_CODELIST4 VARCHAR(400),CUST_CODELIST4_NAME VARCHAR(400),CUST_CODELIST4_VER NUMERIC(20, 0),CUST_CODELIST5 VARCHAR(400),CUST_CODELIST5_NAME VARCHAR(400),CUST_CODELIST5_VER NUMERIC(20, 0),CUST_HCL1 VARCHAR(32),CUST_HCL1_FULL_CODE VARCHAR(1000),CUST_HCL1_FULL_NAME VARCHAR(1000),CUST_HCL1_HCL VARCHAR(400),CUST_HCL1_HCL_LEVEL NUMERIC(20, 0),CUST_HCL2 VARCHAR(32),CUST_HCL2_FULL_CODE VARCHAR(1000),CUST_HCL2_FULL_NAME VARCHAR(1000),CUST_HCL2_HCL VARCHAR(400),CUST_HCL2_HCL_LEVEL NUMERIC(20, 0),CUST_HCL3 VARCHAR(32),CUST_HCL3_FULL_CODE VARCHAR(1000),CUST_HCL3_FULL_NAME VARCHAR(1000),CUST_HCL3_HCL VARCHAR(400),CUST_HCL3_HCL_LEVEL NUMERIC(20, 0),CUST_HCL4 VARCHAR(32),CUST_HCL4_FULL_CODE VARCHAR(1000),CUST_HCL4_FULL_NAME VARCHAR(1000),CUST_HCL4_HCL VARCHAR(400),CUST_HCL4_HCL_LEVEL NUMERIC(20, 0),CUST_HCL5 VARCHAR(32),CUST_HCL5_FULL_CODE VARCHAR(1000),CUST_HCL5_FULL_NAME VARCHAR(1000),
CUST_HCL5_HCL VARCHAR(400),CUST_HCL5_HCL_LEVEL NUMERIC(20, 0),CUST_SELECTION1_VALUE VARCHAR(1000),CUST_SELECTION2_VALUE VARCHAR(1000),CUST_SELECTION3_VALUE VARCHAR(1000),CUST_SELECTION4_VALUE VARCHAR(1000),CUST_SELECTION5_VALUE VARCHAR(1000),CUST_TIMESTAMP1 TIMESTAMP,CUST_TIMESTAMP2 TIMESTAMP,DOMAIN_ID VARCHAR(400),REF_ENTITY_NAME VARCHAR(400));
ALTER TABLE CNT_IMPORT_M ADD CONSTRAINT CNT_IMPORT_M_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT ADD CONSTRAINT CNT_IMPORT_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_SLN ADD CONSTRAINT CNT_IMPORT_SLN_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_MAPPING ADD CONSTRAINT CNT_IMPORT_MAPPING_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF ADD CONSTRAINT CNT_IMPORT_MAPPING_FIELD_DEF_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL ADD CONSTRAINT CNT_IMPORT_MAPPING_FIELD_DEF_TEMPL_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_MAPPING_FIELD_DATA ADD CONSTRAINT CNT_IMPORT_MAPPING_FIELD_DATA_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_PARTY ADD CONSTRAINT CNT_IMPORT_PARTY_PK PRIMARY KEY (ID);
ALTER TABLE CNT_IMPORT_H ADD CONSTRAINT CNT_IMPORT_H_PK PRIMARY KEY (ID);
ALTER TABLE CTM_IMPORT ADD CONSTRAINT CTM_IMPORT_PK PRIMARY KEY (ID);

CREATE INDEX IF NOT EXISTS IDX_IMPORT_SLN_1 ON CNT_IMPORT_SLN(REF_ID);
CREATE INDEX IF NOT EXISTS IDX_IMPORT_SLN_2 ON CNT_IMPORT_SLN(PARENT_ID,FIELD_ID);
CREATE INDEX IF NOT EXISTS IDX_IMPORT_SLN_3 ON CNT_IMPORT_SLN(PARENT_ENTITY);
CREATE INDEX IF NOT EXISTS IDX_IMPORT_SLN_4 ON CNT_IMPORT_SLN(REF_ENTITY);







