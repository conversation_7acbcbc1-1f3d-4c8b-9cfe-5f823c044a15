--// FILE: DML_ACL_import_form.sql

--// ACL: import_form

INSERT INTO CNT_ACCESS_OBJECT(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, OBJECT_ID, REF_NO, OBJECT_TYPE, OBJECT_VERSION, DOMAIN_ID, DESCN, SYSBASE_RULE_CHECKSUM, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import', 'system', 'system', TO_TIMESTAMP('2025-07-30 15:14:56', 'YYYY-MM-DD HH24:MI:SS'), 'import', 'form' || ':' || 'import', 'form', 1, 'RD2', NULL, 'cdafda13e12c9d4558c063747cacee38e70708ecc29d3948c4d4abd51cf1940d', '1', 'RD2', '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ACCESS_OBJECT') AND TARGET_ID IN (SELECT ID FROM CNT_ACCESS_OBJECT WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'));

UPDATE CNT_ACCESS_OBJECT SET REVISION = REVISION+1,ENTITY_VERSION = 1,DOC_STATUS = 'active',NAME = 'import',OBJECT_ID = 'import',REF_NO = 'form' || ':' || 'import',OBJECT_TYPE = 'form',OBJECT_VERSION = 1,DOMAIN_ID = 'RD2',DESCN = NULL,SYSBASE_RULE_CHECKSUM = 'cdafda13e12c9d4558c063747cacee38e70708ecc29d3948c4d4abd51cf1940d',IS_LATEST = '1',CREATE_USER = 'system',CREATE_USER_NAME = 'system',CREATED_ON = TO_TIMESTAMP('2025-07-30 15:14:56', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');





DELETE FROM CNT_ACCESS_OBJECT_ACTION WHERE DOMAIN_ID = 'RD2' AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');



DELETE FROM CNT_ACCESS_OBJECT_CONDITION WHERE DOMAIN_ID = 'RD2' AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 'RD2', '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 'RD2', '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 'RD2', '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 'RD2', '0' ;








DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'efdc0b6888014aa6b94230a38d34e106', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'efdc0b6888014aa6b94230a38d34e106', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'efdc0b6888014aa6b94230a38d34e106', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '765e5d24eddc4f998820a2e30edfe5ed', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '765e5d24eddc4f998820a2e30edfe5ed', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '765e5d24eddc4f998820a2e30edfe5ed', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'dcde306d41184360b00b62b3fbd5d556', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'dcde306d41184360b00b62b3fbd5d556', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'dcde306d41184360b00b62b3fbd5d556', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '9bb89d99b76f49389788c4a02ad0f492', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '9bb89d99b76f49389788c4a02ad0f492', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '9bb89d99b76f49389788c4a02ad0f492', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT 'f3f3cd55aed3469aa9e958569553e083', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'f3f3cd55aed3469aa9e958569553e083', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'activateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'deactivateDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'cancelDoc' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f3f3cd55aed3469aa9e958569553e083', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) SELECT '120e2db70a6044f88e3e5bf5103d2240', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY', '0' ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '120e2db70a6044f88e3e5bf5103d2240', 'RD2', 'RuleActionAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '120e2db70a6044f88e3e5bf5103d2240', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '120e2db70a6044f88e3e5bf5103d2240', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' ;






DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1');

INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'RD2', 1, 'RD2', '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) SELECT '3c5426bab60047f2bf192c4b284bf7ec', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 1 ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT '3c5426bab60047f2bf192c4b284bf7ec', 'RD2', 'RuleUiAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '3c5426bab60047f2bf192c4b284bf7ec', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '3c5426bab60047f2bf192c4b284bf7ec', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '3c5426bab60047f2bf192c4b284bf7ec', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) SELECT 'f183f3d5f4e7430a9e19ca877eb797d7', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 1 ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'f183f3d5f4e7430a9e19ca877eb797d7', 'RD2', 'RuleUiAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f183f3d5f4e7430a9e19ca877eb797d7', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f183f3d5f4e7430a9e19ca877eb797d7', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'f183f3d5f4e7430a9e19ca877eb797d7', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' ;


DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID ='RD2' AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID ='RD2' AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) SELECT 'a7be247ba9c14dea99665dc0408b362d', 0, 1, 'RD2', 'RD2', '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), 1 ;


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) SELECT 'a7be247ba9c14dea99665dc0408b362d', 'RD2', 'RuleUiAdmin' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a7be247ba9c14dea99665dc0408b362d', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = 'RD2' AND IS_LATEST = '1'), '$ANY' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a7be247ba9c14dea99665dc0408b362d', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' ;


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) SELECT SYS_GUID(), 0, 1, 'RD2', 'RD2', '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a7be247ba9c14dea99665dc0408b362d', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' ;






