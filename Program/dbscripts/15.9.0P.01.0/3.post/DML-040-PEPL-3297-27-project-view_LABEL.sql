--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView', 'en_US', 'PEPL', 'Imports - All', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.actions', 'en_US', 'PEPL', 'Actions', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.actions' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.importRawData', 'en_US', 'PEPL', 'Import by legacy template', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.importRawData' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.markAs', 'en_US', 'PEPL', 'Mark as', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.markAs' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.openImportPopupWin', 'en_US', 'PEPL', 'Create', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.openImportPopupWin' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchActivateDoc', 'en_US', 'PEPL', 'Active', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchActivateDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchCancelDoc', 'en_US', 'PEPL', 'Canceled', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchCancelDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchDeactivateDoc', 'en_US', 'PEPL', 'Inactive', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchDeactivateDoc' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus01', 'en_US', 'PEPL', 'Custom Status 1', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus01' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus02', 'en_US', 'PEPL', 'Custom Status 2', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus02' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus03', 'en_US', 'PEPL', 'Custom Status 3', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus03' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus04', 'en_US', 'PEPL', 'Custom Status 4', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus04' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus05', 'en_US', 'PEPL', 'Custom Status 5', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus05' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus06', 'en_US', 'PEPL', 'Custom Status 6', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus06' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus07', 'en_US', 'PEPL', 'Custom Status 7', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus07' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus08', 'en_US', 'PEPL', 'Custom Status 8', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus08' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus09', 'en_US', 'PEPL', 'Custom Status 9', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus09' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.action.searchMarkAsCustomStatus10', 'en_US', 'PEPL', 'Custom Status 10', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.action.searchMarkAsCustomStatus10' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.Import', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.Import' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.Import.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.Import.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.createdOn', 'en_US', 'PEPL', 'Created on', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.createdOn' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.createdOn.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.createdOn.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.docRef', 'en_US', 'PEPL', 'Document Ref.', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.docRef' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.docRef.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.docRef.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.docStatus', 'en_US', 'PEPL', 'Doc. Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.docStatus' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.docStatus.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.docStatus.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.editingStatus', 'en_US', 'PEPL', 'Editing Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.editingStatus' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.editingStatus.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.editingStatus.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.headerRow', 'en_US', 'PEPL', 'Header Row', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.headerRow' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.headerRow.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.headerRow.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importKeyDisplay', 'en_US', 'PEPL', 'Key', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importKeyDisplay' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importKeyDisplay.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importKeyDisplay.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importModule', 'en_US', 'PEPL', 'Module (Code)', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importModule' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importModule.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importModule.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importModuleName', 'en_US', 'PEPL', 'Module', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importModuleName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importModuleName.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importModuleName.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importNo', 'en_US', 'PEPL', 'Import No.', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importNo' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importNo.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importNo.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importTable', 'en_US', 'PEPL', 'Table', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importTable' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.importTable.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.importTable.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.startRow', 'en_US', 'PEPL', 'Start Row', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.startRow' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.startRow.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.startRow.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.status', 'en_US', 'PEPL', 'Status', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.status' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.status.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.status.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.systemTag', 'en_US', 'PEPL', 'System Tag', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.systemTag' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.systemTag.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.systemTag.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.updateUserName', 'en_US', 'PEPL', 'Last Modified by', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.updateUserName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.updateUserName.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.updateUserName.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.updatedOn', 'en_US', 'PEPL', 'Last Modified on', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.updatedOn' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.updatedOn.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.updatedOn.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.version', 'en_US', 'PEPL', 'Version', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.version' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.importView.column.version.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.importView.column.version.block' AND LOCALE='en_US');
