DO $BODY$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';

BEGIN

--// FILE: DML_ACL_import_form.sql

--// ACL: import_form

INSERT INTO CNT_ACCESS_OBJECT(ID, REVISION, ENTITY_VERSION, VERSION, DOC_STATUS, NAME, CREATE_USER, CREATE_USER_NAME, CREATED_ON, OBJECT_ID, REF_NO, OBJECT_TYPE, OBJECT_VERSION, DOMAIN_ID, DESCN, SYSBASE_RULE_CHECKSUM, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 0, 'active', 'import', 'system', 'system', TO_TIMESTAMP('2025-07-06 15:00:06', 'YYYY-MM-DD HH24:MI:SS'), 'import', 'form' || ':' || 'import', 'form', 1, DOMAINID, NULL, '79aabd11500df419dc2d8ceb9a4bd72fe5b850a269c7164587f84469dd18cbfb', '1', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');


DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_ACCESS_OBJECT') AND TARGET_ID IN (SELECT ID FROM CNT_ACCESS_OBJECT WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'));

UPDATE CNT_ACCESS_OBJECT SET REVISION = REVISION+1,ENTITY_VERSION = 1,DOC_STATUS = 'active',NAME = 'import',OBJECT_ID = 'import',REF_NO = 'form' || ':' || 'import',OBJECT_TYPE = 'form',OBJECT_VERSION = 1,DOMAIN_ID = DOMAINID,DESCN = NULL,SYSBASE_RULE_CHECKSUM = '79aabd11500df419dc2d8ceb9a4bd72fe5b850a269c7164587f84469dd18cbfb',IS_LATEST = '1',CREATE_USER = 'system',CREATE_USER_NAME = 'system',CREATED_ON = TO_TIMESTAMP('2025-07-06 15:00:06', 'YYYY-MM-DD HH24:MI:SS') WHERE ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');





DELETE FROM CNT_ACCESS_OBJECT_ACTION WHERE DOMAIN_ID = DOMAINID AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'openImportPopupWin');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'amendDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'saveDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveAndConfirm', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'saveAndConfirm');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'doneDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'SnapshotDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'SnapshotDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'discardDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'activateDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'activateDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'deactivateDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'deactivateDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'cancelDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'cancelDoc');


INSERT INTO CNT_ACCESS_OBJECT_ACTION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, ACCESS_OBJECT_ID, ACTION_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, DOMAINID, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_ACCESS_OBJECT_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ACTION_ID = 'loadDoc');




DELETE FROM CNT_ACCESS_OBJECT_CONDITION WHERE DOMAIN_ID = DOMAINID AND ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_ACCESS_OBJECT_CONDITION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, CONDITION_ID, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, DOMAINID, '0' ;




DELETE FROM CNT_ACCESS_OBJ_ACT_CONDITION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') ;


INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') ;


INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') ;


INSERT INTO CNT_ACCESS_OBJ_ACT_CONDITION(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CONDITION_ID, ACCESS_OBJECT_ID) SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') ;




DELETE FROM CNT_RULE_TEMPLATE_ACL WHERE PARENT_ID IN (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME LIKE 'import-workflowStatus%');

DELETE FROM CNT_RULE_TEMPLATE WHERE NAME LIKE 'import-workflowStatus%';

DELETE FROM CNT_RULE_ACTION_TEMPLATE WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-1', 'workflowStatus', '1', '1', DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveAndConfirm', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-2', 'workflowStatus', '1', '1', DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-2' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-3', 'workflowStatus', '1', '1', DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'SnapshotDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-3' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-4', 'workflowStatus', '1', '1', DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-4' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE(ID, REVISION, ENTITY_VERSION, NAME, CATEGORY, RULE_TYPE, IS_SYSTEM, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, 'import-workflowStatus-5', 'workflowStatus', '1', '1', DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndExistDocument' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isConcurrentEditModeAndNewDoc' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_TEMPLATE_ACL(ID, REVISION, ENTITY_VERSION, PARENT_ID, CONDITION_ID, ACCESS_RIGHT, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), (SELECT ID FROM CNT_CONDITION WHERE NAME = 'isNotConcurrentEditMode' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 2, DOMAINID, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'activateDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'deactivateDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'cancelDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-5' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION_TEMPLATE(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, RULE_TEMPLATE_ID, DOMAIN_ID, RULE_TEMPLATE_CATEGORY, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = '$DEFAULT_ROLE' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_RULE_TEMPLATE WHERE NAME = 'import-workflowStatus-1' AND DOMAIN_ID = DOMAINID), DOMAINID, 'workflowStatus', DOMAINID, '0' ;








DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_ACTION WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'openImportPopupWin', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'amendDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'saveDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'doneDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'discardDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_ACTION(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, ACTION_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'loadDoc', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;






DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '23d3439c88364ce2af133e0695ced62f', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='23d3439c88364ce2af133e0695ced62f');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '23d3439c88364ce2af133e0695ced62f', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='23d3439c88364ce2af133e0695ced62f');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='amendDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='saveDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='doneDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='discardDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '23d3439c88364ce2af133e0695ced62f', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='23d3439c88364ce2af133e0695ced62f' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='loadDoc');



DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '703b1648b2224279a91b1a86c0c08be0', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='703b1648b2224279a91b1a86c0c08be0');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '703b1648b2224279a91b1a86c0c08be0', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='703b1648b2224279a91b1a86c0c08be0');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='amendDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='saveDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='doneDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='discardDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '703b1648b2224279a91b1a86c0c08be0', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='703b1648b2224279a91b1a86c0c08be0' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='loadDoc');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT 'a1b2ba1e161247a28cfd5514e4c98029', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='a1b2ba1e161247a28cfd5514e4c98029');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT 'a1b2ba1e161247a28cfd5514e4c98029', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='a1b2ba1e161247a28cfd5514e4c98029');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='amendDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='saveDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='doneDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='discardDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'a1b2ba1e161247a28cfd5514e4c98029', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='a1b2ba1e161247a28cfd5514e4c98029' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='loadDoc');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '1fac2068bc3d4a70af0eeb117654dbf2', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Author' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='1fac2068bc3d4a70af0eeb117654dbf2');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '1fac2068bc3d4a70af0eeb117654dbf2', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='1fac2068bc3d4a70af0eeb117654dbf2');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='amendDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='saveDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='doneDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='discardDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1fac2068bc3d4a70af0eeb117654dbf2', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1fac2068bc3d4a70af0eeb117654dbf2' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='loadDoc');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT '1c6cbd147b2643da822005d7086c85c1', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.Editor' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='1c6cbd147b2643da822005d7086c85c1');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '1c6cbd147b2643da822005d7086c85c1', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='1c6cbd147b2643da822005d7086c85c1');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'actionId', null, null, 'openImportPopupWin' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='openImportPopupWin');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'actionId', null, null, 'amendDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='amendDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'actionId', null, null, 'saveDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='saveDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'actionId', null, null, 'doneDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='doneDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'actionId', null, null, 'discardDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='discardDoc');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1c6cbd147b2643da822005d7086c85c1', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='1c6cbd147b2643da822005d7086c85c1' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='actionId' and DISPLAY_VALUE='loadDoc');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_ACTION_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, CONDITION_ID_VALUE, IS_DISALLOW) 
SELECT 'baafea08addb4d3794fed052879f8b51', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'import.ReadOnly' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY', '0' 
where not exists(select 1 from CNT_RULE_ACTION_ADMIN where id='baafea08addb4d3794fed052879f8b51');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT 'baafea08addb4d3794fed052879f8b51', DOMAINID, 'RuleActionAdmin' 
where not exists(select 1 from CTM_ROLE where id='baafea08addb4d3794fed052879f8b51');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'baafea08addb4d3794fed052879f8b51', 'RuleActionAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='baafea08addb4d3794fed052879f8b51' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'baafea08addb4d3794fed052879f8b51', 'RuleActionAdmin', 'actionId', null, null, 'loadDoc' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='baafea08addb4d3794fed052879f8b51' and PARENT_ENTITY='RuleActionAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='loadDoc');




DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

DELETE FROM CNT_RULE_UI WHERE ACCESS_OBJECT_ID = (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1')AND ROLE_ID = (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1');

INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'ui', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;


INSERT INTO CNT_RULE_UI(ID, REVISION, ENTITY_VERSION, ACCESS_OBJECT_ID, ROLE_ID, UI_ID, CONDITION_ID, DOMAIN_ID, ACCESS_RIGHT, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT SYS_GUID(), 0, 1, (SELECT ID FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'ui.tabHeader', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), DOMAINID, 1, DOMAINID, '0' ;





DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) 
SELECT 'fa5410b3725f4651948511b00e023e72', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'SuperAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1 
where not exists(select 1 from CNT_RULE_UI_ADMIN where id='fa5410b3725f4651948511b00e023e72');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT 'fa5410b3725f4651948511b00e023e72', DOMAINID, 'RuleUiAdmin' 
where not exists(select 1 from CTM_ROLE where id='fa5410b3725f4651948511b00e023e72');



INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'fa5410b3725f4651948511b00e023e72', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='fa5410b3725f4651948511b00e023e72' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'fa5410b3725f4651948511b00e023e72', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='fa5410b3725f4651948511b00e023e72' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='uiFieldId' and DISPLAY_VALUE='ui');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'fa5410b3725f4651948511b00e023e72', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='fa5410b3725f4651948511b00e023e72' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='uiFieldId' and DISPLAY_VALUE='ui.tabHeader');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) 
SELECT '2c167d4d2f4a41d49cee21ce7bcb54e3', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'GeneralAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1 
where not exists(select 1 from CNT_RULE_UI_ADMIN where id='2c167d4d2f4a41d49cee21ce7bcb54e3');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT '2c167d4d2f4a41d49cee21ce7bcb54e3', DOMAINID, 'RuleUiAdmin' 
where not exists(select 1 from CTM_ROLE where id='2c167d4d2f4a41d49cee21ce7bcb54e3');



INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2c167d4d2f4a41d49cee21ce7bcb54e3', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='2c167d4d2f4a41d49cee21ce7bcb54e3' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2c167d4d2f4a41d49cee21ce7bcb54e3', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='2c167d4d2f4a41d49cee21ce7bcb54e3' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='uiFieldId' and DISPLAY_VALUE='ui');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2c167d4d2f4a41d49cee21ce7bcb54e3', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='2c167d4d2f4a41d49cee21ce7bcb54e3' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='uiFieldId' and DISPLAY_VALUE='ui.tabHeader');




DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID =DOMAINID AND PARENT_ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CTM_ROLE WHERE ID IN (SELECT ID FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form');

DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID =DOMAINID AND ROLE_ID IN (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1') AND OBJ_ID = 'import' AND OBJ_TYPE ='form';

INSERT INTO CNT_RULE_UI_ADMIN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, ROLE_ID, OBJ_ID, OBJ_TYPE, CONDITION_ID_VALUE, OBJ_NAME, ACCESS_RIGHT) 
SELECT 'b859b91cb7164e3e8f6f316c507c367a', 0, 1, DOMAINID, DOMAINID, '0', (SELECT ID FROM CNT_ROLE WHERE NAME = 'ClientAdministratorRole' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 'import', 'form', '$ANY', (SELECT NAME FROM CNT_ACCESS_OBJECT WHERE OBJECT_ID = 'import' AND OBJECT_TYPE = 'form' AND OBJECT_VERSION = 1 AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), 1 
where not exists(select 1 from CNT_RULE_UI_ADMIN where id='b859b91cb7164e3e8f6f316c507c367a');


INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME) 
SELECT 'b859b91cb7164e3e8f6f316c507c367a', DOMAINID, 'RuleUiAdmin' 
where not exists(select 1 from CTM_ROLE where id='b859b91cb7164e3e8f6f316c507c367a');



INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b859b91cb7164e3e8f6f316c507c367a', 'RuleUiAdmin', 'conditionId', 'Condition', (SELECT ID FROM CNT_CONDITION WHERE NAME = '$ANY' AND DOMAIN_ID = DOMAINID AND IS_LATEST = '1'), '$ANY' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='b859b91cb7164e3e8f6f316c507c367a' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='conditionId' and DISPLAY_VALUE='$ANY');



INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b859b91cb7164e3e8f6f316c507c367a', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='b859b91cb7164e3e8f6f316c507c367a' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='uiFieldId' and DISPLAY_VALUE='ui');


INSERT INTO CNT_ROLE_SLN(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE) 
SELECT SYS_GUID(), 0, 1, DOMAINID, DOMAINID, '0', 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'b859b91cb7164e3e8f6f316c507c367a', 'RuleUiAdmin', 'uiFieldId', null, null, 'ui.tabHeader' 
where not exists(select 1 from CNT_ROLE_SLN where PARENT_ID='b859b91cb7164e3e8f6f316c507c367a' and PARENT_ENTITY='RuleUiAdmin' and FIELD_ID='uiFieldId' and DISPLAY_VALUE='ui.tabHeader');

END;
$BODY$ LANGUAGE PLPGSQL;

delete from cnt_serialized_entity cse where target_entity like 'Role%';
