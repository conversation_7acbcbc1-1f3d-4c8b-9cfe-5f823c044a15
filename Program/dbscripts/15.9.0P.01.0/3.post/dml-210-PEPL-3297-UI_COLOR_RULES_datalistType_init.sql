-----------------------------BEGIN CTC-49--------------------------------
-- JiRa No.: CTC-49
-- Description: Create datalist type UI_COLOR_RULES if not exist
-- Input parameter: <DOMAIN_ID>
-- Scripts re-runnable: Yes

do $BODY$
DECLARE
DOMAINID VARCHAR(200) := 'PEPL';
begin

    DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_SEQ_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_DATA_LIST_TYPE where name = 'UI_COLOR_RULES' and REF_NO LIKE 'DLT%'));
    UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID AND not exists (select 1 from CNT_DATA_LIST_TYPE where name = 'UI_COLOR_RULES' and REF_NO LIKE 'DLT%');

    INSERT INTO CNT_DATA_LIST_TYPE(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON, NAME, APPLY_TO_ENTITY, REMARKS, REF_NO, LABEL_KEY_PREFIX, INTEGRATION_SOURCE, INTEGRATION_STATUS, INTEGRATION_NOTE, HUB_DOMAIN_ID, IS_FOR_REFERENCE, IS_LATEST, CREATE_USER_NAME, UPDATE_USER_NAME, IS_CPM_INITIALIZED) SELECT LOWER(SYS_GUID()), 1, 1, DOMAINID, 1, null, 'active', 'confirmed', 'system', 'system', TO_TIMESTAMP('2025-07-04 14:17:29', 'YYYY-MM-DD HH24:MI:SS'), null, 'UI_COLOR_RULES', 'lookup', 'UI_COLOR_RULES', (SELECT 'DLT' || to_char(now(), 'YY') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_DATA_LIST_TYPE_REF_NO' AND DOMAIN_ID = DOMAINID), 'lbl.codelist.tabHeader', null, null, null, DOMAINID, '0', '1', 'system', 'system', null  
    WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE WHERE  DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES');

    INSERT INTO cnt_data_list_type_m
    (id, revision, domain_id, hub_domain_id, "version", status, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, integration_source, integration_status, integration_note, is_cpm_initialized, is_latest, ref_no, party_template_ref, party_template_ver, party_template_name, party_name1, party_name2, party_name3, party_name4, party_name5) 
    select id, 1, DOMAINID, DOMAINID, 1, NULL, 'active', 'confirmed', 'system', 'system', 'system', 'system', current_timestamp , current_timestamp, NULL, NULL, NULL, false, true, ref_no , NULL, NULL, NULL, NULL, NULL, NULL, NULL, null 
    from cnt_data_list_type where domain_id = DOMAINID and is_latest and name='UI_COLOR_RULES' and not exists (select 1 from cnt_data_list_type_m where id = (select id from cnt_data_list_type where domain_id = DOMAINID and is_latest and name='UI_COLOR_RULES'));

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 0,  'custText1', 'Text', 'Color(HEX)', 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText1', '1', '0', 0,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 1,  'custText2', 'Text', 'Rules', 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText2', '1', '0', 1,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 2,  'custText3', 'Text', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText3', '0', '0', 2,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 3,  'custText4', 'Text', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText4', '0', '0', 3,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 4,  'custText5', 'Text', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText5', '0', '0', 4,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 5,  'custText6', 'Text', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText6', '0', '0', 5,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 6,  'custText7', 'Text', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custText7', '0', '0', 6,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custText7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 7,  'custNumber1', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber1', '0', '0', 7,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 8,  'custNumber2', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber2', '0', '0', 8,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 9,  'custNumber3', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber3', '0', '0', 9,  DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 10, 'custNumber4', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber4', '0', '0', 10, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 11, 'custNumber5', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber5', '0', '0', 11, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 12, 'custNumber6', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber6', '0', '0', 12, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 13, 'custNumber7', 'Number', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custNumber7', '0', '0', 13, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custNumber7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 14, 'custDate1', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate1', '0', '0', 14, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 15, 'custDate2', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate2', '0', '0', 15, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 16, 'custDate3', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate3', '0', '0', 16, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 17, 'custDate4', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate4', '0', '0', 17, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 18, 'custDate5', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate5', '0', '0', 18, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 19, 'custDate6', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate6', '0', '0', 19, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 20, 'custDate7', 'Date', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDate7', '0', '0', 20, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDate7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 21, 'custDecimal1', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal1', '0', '0', 21, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 22, 'custDecimal2', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal2', '0', '0', 22, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 23, 'custDecimal3', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal3', '0', '0', 23, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 24, 'custDecimal4', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal4', '0', '0', 24, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 25, 'custDecimal5', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal5', '0', '0', 25, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 26, 'custDecimal6', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal6', '0', '0', 26, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 27, 'custDecimal7', 'Decimal',  null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custDecimal7', '0', '0', 27, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custDecimal7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 28, 'custCodelist1', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist1', '0', '0', 28, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 29, 'custCodelist2', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist2', '0', '0', 29, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 30, 'custCodelist3', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist3', '0', '0', 30, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 31, 'custCodelist4', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist4', '0', '0', 31, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 32, 'custCodelist5', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist5', '0', '0', 32, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 33, 'custCodelist6', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist6', '0', '0', 33, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 34, 'custCodelist7', 'Codelist', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCodelist7', '0', '0', 34, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custCodelist7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 35, 'custMemoText1', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText1', '0', '0', 35, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 36, 'custMemoText2', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText2', '0', '0', 36, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 37, 'custMemoText3', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText3', '0', '0', 37, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 38, 'custMemoText4', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText4', '0', '0', 38, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 39, 'custMemoText5', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText5', '0', '0', 39, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 40, 'custMemoText6', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText6', '0', '0', 40, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 41, 'custMemoText7', 'TextArea', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custMemoText7', '0', '0', 41, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND  FIELD_ID = 'custMemoText7');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 42, 'custHcl1', 'HclGroup', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custHcl1', '0', '0', 42, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custHcl1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 43, 'custHcl2', 'HclGroup', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custHcl2', '0', '0', 43, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custHcl2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 44, 'custHcl3', 'HclGroup', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custHcl3', '0', '0', 44, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custHcl3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 45, 'custHcl4', 'HclGroup', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custHcl4', '0', '0', 45, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custHcl4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 46, 'custHcl5', 'HclGroup', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custHcl5', '0', '0', 46, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custHcl5');

    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 47, 'custCheckbox1', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox1', '0', '0', 47, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox1');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 48, 'custCheckbox2', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox2', '0', '0', 48, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox2');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 49, 'custCheckbox3', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox3', '0', '0', 49, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox3');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 50, 'custCheckbox4', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox4', '0', '0', 50, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox4');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 51, 'custCheckbox5', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox5', '0', '0', 51, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox5');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 52, 'custCheckbox6', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox6', '0', '0', 52, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox6');
    INSERT INTO CNT_DATA_LIST_TYPE_ITEM(ID, REVISION, ENTITY_VERSION, DOMAIN_ID, PARENT_ID, LINE_NO, FIELD_ID, FIELD_TYPE, FIELD_LABEL, FIELD_LABEL_KEY, ENABLED, MANDATORY, INTERNAL_SEQ_NO, HUB_DOMAIN_ID, IS_FOR_REFERENCE) SELECT sys_guid(), 1, 1, DOMAINID, (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1'), 53, 'custCheckbox7', 'Checkbox', null, 'lbl.codelist.tabHeader.UI_COLOR_RULES.custCheckbox7', '0', '0', 53, DOMAINID, '0'  WHERE NOT EXISTS (SELECT 1 FROM CNT_DATA_LIST_TYPE_ITEM WHERE  DOMAIN_ID = DOMAINID AND  PARENT_ID = (SELECT ID FROM CNT_DATA_LIST_TYPE WHERE DOMAIN_ID = DOMAINID AND NAME = 'UI_COLOR_RULES' AND IS_LATEST = '1') AND FIELD_ID = 'custCheckbox7');
END;
$BODY$ LANGUAGE PLPGSQL;

COMMIT;

-------------------------------END CTC-49---------------------------------

