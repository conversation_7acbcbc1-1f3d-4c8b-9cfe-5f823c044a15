
README for this folder
======================

This folder contains all the SQL scripts that will be run in the post process.
When release, we will concat all the scripts according to the filename ascending order.
Concated files will be:

* &lt;version&gt;-<SEQ>-DDL-postdeploy-&lt;fileName&gt;.sql
* &lt;version&gt;-<SEQ>-DML-postdeploy-&lt;fileName&gt;.sql

Name format of this file should be:

```
<ddl/dml>-<Sequence Number>-<JIRA no>.sql
```

e.g.
```
ddl-010-pts-12345.sql
ddl-020-pts-21412.sql
ddl-030-pts-22412.sql
dml-010-pts-12345.sql
dml-020-pts-22412.sql
```

Note:
	1. Sequence number should be incremented by 10. e.g. 010, 020, 030, 040... 990
	2. File name should be in lower case
	3. There is no need to add the "BEGIN;" and "COMMIT;". This will be added when these scripts are concated.

