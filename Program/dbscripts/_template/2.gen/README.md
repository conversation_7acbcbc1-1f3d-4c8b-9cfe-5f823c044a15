
README for this folder
======================

This folder contains all the SQL scripts that will be manually generated.
When release, we will concat all the scripts according to the filename ascending order.

Concated files will be:

* &lt;version&gt;-<SEQ>-DDL-concated_generated.sql
* &lt;version&gt;-<SEQ>-DML-concated_generated.sql

This folder should store the generated script according to the builder generated structure.

e.g.
view/DML_VIEW_sourcingRecord.sql
validation/DML_VALIDATION_inspectReport.sql

Note:
	1. There is no need to add the "BEGIN;" and "COMMIT;". This will be added when these scripts are concated.

