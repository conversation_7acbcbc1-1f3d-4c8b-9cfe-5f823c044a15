update cnt_codelist_book ccb
	set ref_no = (SELECT 'CL' || to_char(now(), 'YY') || '-' || LPAD(NEXT_VAL::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = 'PEPL'),
	business_ref_no = (SELECT 'CL' || to_char(now(), 'YY') || '-' || LPAD(NEXT_VAL::TEXT, 6, '0') from CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_CODE_LIST_REF_NO' AND DOMAIN_ID = 'PEPL')
where ccb.is_latest and ccb.domain_id = 'PEPL' and ccb."name" = 'IMPORT_MODULE';

update cnt_codelist_book_m m
	set ref_no = ccb.ref_no
from cnt_codelist_book ccb where m.id = ccb.id and ccb.is_latest and ccb.domain_id = 'PEPL' and ccb."name" = 'IMPORT_MODULE';


update cnt_seq_def 
	set next_val = next_val + 1,
	cycle_started_on = current_timestamp,
	updated_on = current_timestamp
where domain_id = 'PEPL' and seq_id = 'CBX_SEQ_CODE_LIST_REF_NO';

delete from cnt_serialized_entity where target_id = (select ccb.id from cnt_codelist_book ccb where ccb.is_latest and ccb.domain_id = 'PEPL' and ccb."name" = 'IMPORT_MODULE');



drop table if exists dp_pepl_3383_codelist_book;
create table dp_pepl_3383_codelist_book as
select * from cnt_codelist_book ccb
where ccb.is_latest and ccb.domain_id = 'PEPL'
and not exists (select 1 from cnt_codelist_book_m m where m.id = ccb.id);


insert into cnt_codelist_book_m (id, revision, domain_id, hub_domain_id, "version", status, doc_status, editing_status, create_user, create_user_name, update_user, update_user_name, created_on, updated_on, is_cpm_initialized, is_latest, ref_no)
select dp.id, dp.revision, dp.domain_id, dp.hub_domain_id, dp."version", dp.status, dp.doc_status, dp.editing_status, dp.create_user, dp.create_user_name, dp.update_user, dp.update_user_name, dp.created_on, dp.updated_on, dp.is_cpm_initialized, dp.is_latest, dp.ref_no  from dp_pepl_3383_codelist_book dp 
where not exists (select 1 from cnt_codelist_book_m m where m.id = dp.id);

delete from cnt_serialized_entity where target_id in (select dp.id from dp_pepl_3383_codelist_book dp);

commit;
