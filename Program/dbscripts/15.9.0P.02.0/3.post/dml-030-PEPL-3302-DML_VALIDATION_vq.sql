--// FILE: DML_VALIDATION_vq.sql

DELETE FROM CNT_VALIDATION_FIELD_PARAM WHERE VALIDATION_FIELD_ID IN
(SELECT VF.ID FROM CNT_VALIDATION_FIELD VF JOIN CNT_VALIDATION_RULE VR
ON VF.VALIDATION_RULE_ID = VR.ID JOIN CNT_VALIDATION_PROFILE VP
ON VR.PROFILE_ID = VP.ID WHERE VP.REF_ENTITY_NAME = 'Vq' AND VP.DOMAIN_ID = 'PEPL');

DELETE FROM CNT_VALIDATION_FIELD WHERE VALIDATION_RULE_ID IN
(SELECT VR.ID FROM CNT_VALIDATION_RULE VR
JOIN CNT_VALIDATION_PROFILE VP
ON VR.PROFILE_ID = VP.ID WHERE VP.REF_ENTITY_NAME = 'Vq' AND VP.DOMAIN_ID = 'PEPL');

DELETE FROM CNT_VALIDATION_RULE WHERE PROFILE_ID IN
(SELECT VP.ID FROM CNT_VALIDATION_PROFILE VP
WHERE VP.REF_ENTITY_NAME = 'Vq' AND VP.DOMAIN_ID = 'PEPL');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_VALIDATION_PROFILE') AND TARGET_ID IN (SELECT ID FROM CNT_VALIDATION_PROFILE WHERE REF_ENTITY_NAME = 'Vq' AND DOMAIN_ID = 'PEPL');

DELETE FROM CNT_VALIDATION_PROFILE WHERE REF_ENTITY_NAME = 'Vq' AND DOMAIN_ID = 'PEPL';

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL';

INSERT INTO CNT_VALIDATION_PROFILE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, UPDATE_USER, UPDATED_ON, IS_LATEST, MAX_ERROR, CONSOLIDATE_ERROR, PRIORITY, IGNORE_CUSTOM_FIELD, PROFILE_NAME, INHERIT_FROM, REF_NO, REF_ENTITY_NAME, REF_ENTITY_VERSION, ACTION, ENABLED, CREATE_USER, CREATED_ON)
VALUES ('fdf152439b744c549b8bc3d2c8771823', 0, 1, 'PEPL', 'PEPL', '0', '1', NULL, NULL, 'confirmed', NULL, TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'), '1', 0, '0', 0, '0', 'Default Data Validation Profile Vq[ver:1]', NULL, (SELECT 'VAL' || to_char(now(), 'YYMM') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL'), 'Vq', 1, 'Quoted,MarkShortListed,RejectToBuy,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,MarkAsCustomStatus06Doc,MarkAsCustomStatus07Doc,MarkAsCustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc', '1', 'system', TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('c361a9ec3dcd4f15843f9849bf517aab', 0, 1, 'PEPL', 'PEPL', '0', 'fdf152439b744c549b8bc3d2c8771823', 1, 'MandatoryValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryValidator', '1', 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a0794ab10f5e4274a77a038d580bcdbe', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('310c6bd3f4494c5bb212baf7744d1cc2', 0, 1, 'PEPL', 'PEPL', '0', 'a0794ab10f5e4274a77a038d580bcdbe', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4707e6afe30a4bb1b1a3f6be144d3cd1', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'vqType', 'entity.vqType', 2, '0', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cfccceebd1c34e73b303b1dbe2ada64c', 0, 1, 'PEPL', 'PEPL', '0', '4707e6afe30a4bb1b1a3f6be144d3cd1', 'LABEL_FIELD_ID', 'vqType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5f0d18879a89436a8cf55ad7cf973af6', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'currency', 'entity.currency', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('261e22456e8b41aeb1b96b86d3eb5d48', 0, 1, 'PEPL', 'PEPL', '0', '5f0d18879a89436a8cf55ad7cf973af6', 'LABEL_FIELD_ID', 'currency');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('dc88fba551924fe98502afef6874d606', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'unitCost', 'entity.unitCost', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e715677dbf04481c994992349e6ae4c9', 0, 1, 'PEPL', 'PEPL', '0', 'dc88fba551924fe98502afef6874d606', 'LABEL_FIELD_ID', 'unitCost');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2e3ca79081574cd4aeb711da8714d398', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'incoterm', 'entity.incoterm', 5, '0', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('170ba926b5bc4732b1435c8ea9262b89', 0, 1, 'PEPL', 'PEPL', '0', '2e3ca79081574cd4aeb711da8714d398', 'LABEL_FIELD_ID', 'incoterm');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('fac16d1eb01741a68b58bf98c549e9bc', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'shipmentMethod', 'entity.shipmentMethod', 6, '0', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('318d6b7faa014bb2b7679263fe8dc1b8', 0, 1, 'PEPL', 'PEPL', '0', 'fac16d1eb01741a68b58bf98c549e9bc', 'LABEL_FIELD_ID', 'shipmentMethod');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ba3c84f2eb6648d2bb792156c6c6ebde', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'countryOfOrigin', 'entity.countryOfOrigin', 7, '1', NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fb2f735d0578431c8dabe8e61138253e', 0, 1, 'PEPL', 'PEPL', '0', 'ba3c84f2eb6648d2bb792156c6c6ebde', 'LABEL_FIELD_ID', 'countryOfOrigin');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('3a5e6e65b1b04387ae3b039974b42660', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'countryOfShipment', 'entity.countryOfShipment', 8, '1', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('88a6c231cf2f4cfc8e0f7c8c00a8dd80', 0, 1, 'PEPL', 'PEPL', '0', '3a5e6e65b1b04387ae3b039974b42660', 'LABEL_FIELD_ID', 'countryOfShipment');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4ca6194737ed4249abdff5ab7d7bb97f', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'portOfLoading', 'entity.portOfLoading', 9, '0', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('37d358a4db914f178d1c1bfcc767c7e5', 0, 1, 'PEPL', 'PEPL', '0', '4ca6194737ed4249abdff5ab7d7bb97f', 'LABEL_FIELD_ID', 'portOfLoading');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ae15b08ea05040bcb502ded1abc36adb', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'hierarchy', 'entity.hierarchy', 10, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('afefbbbaf2bb4619a0c4042bafb25015', 0, 1, 'PEPL', 'PEPL', '0', 'ae15b08ea05040bcb502ded1abc36adb', 'LABEL_FIELD_ID', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('50eb90fc012941b78911392fc7946a19', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqCarton', 'cartonType', 'entity.vqCarton.cartonType', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4958480ec7ba4fa29306b16050cc7c1a', 0, 1, 'PEPL', 'PEPL', '0', '50eb90fc012941b78911392fc7946a19', 'GRID_ID', 'vqCarton');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('919ef86e9e214e6b9148b1937ddeba4c', 0, 1, 'PEPL', 'PEPL', '0', '50eb90fc012941b78911392fc7946a19', 'LABEL_FIELD_ID', 'cartonType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('e68df6d6969f4fe8b3fccf4418532901', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqImage', 'fileId', 'entity.vqImage.fileId', 12, '1', NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6e933fcea1ea44ffb82c35ba9b3810e7', 0, 1, 'PEPL', 'PEPL', '0', 'e68df6d6969f4fe8b3fccf4418532901', 'GRID_ID', 'vqImage');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c7ea66eb9b5e411c8a9b00f7039d62c8', 0, 1, 'PEPL', 'PEPL', '0', 'e68df6d6969f4fe8b3fccf4418532901', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d90549dad03f4c989fb1974fcd97bf8d', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqContact', 'contactTypeId', 'entity.vqContact.contactTypeId', 13, '0', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8c3963f300114edab817977f0e29a3fa', 0, 1, 'PEPL', 'PEPL', '0', 'd90549dad03f4c989fb1974fcd97bf8d', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3a17fa8f6e7b443798cda903267c7f59', 0, 1, 'PEPL', 'PEPL', '0', 'd90549dad03f4c989fb1974fcd97bf8d', 'LABEL_FIELD_ID', 'contactTypeId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f2bd57119cdb42289bee00e829958af1', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqContact', 'firstName', 'entity.vqContact.firstName', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e5add1ee16664e948975fc52120547fc', 0, 1, 'PEPL', 'PEPL', '0', 'f2bd57119cdb42289bee00e829958af1', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('af8386d37eb741d19bb2e903aacc9b20', 0, 1, 'PEPL', 'PEPL', '0', 'f2bd57119cdb42289bee00e829958af1', 'LABEL_FIELD_ID', 'firstName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('534cf34761734539b309ea4adac014ff', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqContact', 'email', 'entity.vqContact.email', 15, '1', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a1829121260249239ff907902de1c635', 0, 1, 'PEPL', 'PEPL', '0', '534cf34761734539b309ea4adac014ff', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('72ebd8e6f67547cdac4ffc8f102b8492', 0, 1, 'PEPL', 'PEPL', '0', '534cf34761734539b309ea4adac014ff', 'LABEL_FIELD_ID', 'email');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('218385b9218b46f7b2523c47e2f5479c', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqAddress', 'addressTypeId', 'entity.vqAddress.addressTypeId', 16, '0', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4dd59c3ad1d8413989f976b35d91677a', 0, 1, 'PEPL', 'PEPL', '0', '218385b9218b46f7b2523c47e2f5479c', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f90bb0c517b84891abee75c3b2487914', 0, 1, 'PEPL', 'PEPL', '0', '218385b9218b46f7b2523c47e2f5479c', 'LABEL_FIELD_ID', 'addressTypeId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('62a14388bb5143e0acd4c5bb3d718e1e', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqAddress', 'address1', 'entity.vqAddress.address1', 17, '1', NULL, 17);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c4fb8c7be0b64319b92630a735db5c81', 0, 1, 'PEPL', 'PEPL', '0', '62a14388bb5143e0acd4c5bb3d718e1e', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('eca5deda8f98409a9568a7977b5e3416', 0, 1, 'PEPL', 'PEPL', '0', '62a14388bb5143e0acd4c5bb3d718e1e', 'LABEL_FIELD_ID', 'address1');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4f24c0fde00e48b7ba6897ee4718a54c', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'VqAddress', 'country', 'entity.vqAddress.country', 18, '1', NULL, 18);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e2084daa821143299d94f549ff2ecee1', 0, 1, 'PEPL', 'PEPL', '0', '4f24c0fde00e48b7ba6897ee4718a54c', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3f0755cb975d41e69cfaf653f4599c67', 0, 1, 'PEPL', 'PEPL', '0', '4f24c0fde00e48b7ba6897ee4718a54c', 'LABEL_FIELD_ID', 'country');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2a9275d0e49e41fdb4792dc6833d448d', 0, 1, 'PEPL', 'PEPL', '0', 'c361a9ec3dcd4f15843f9849bf517aab', 'Vq', 'vqNo', 'entity.vqNo', 19, '0', NULL, 19);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9fe1173e33834a508d34ace1fd5b3652', 0, 1, 'PEPL', 'PEPL', '0', '2a9275d0e49e41fdb4792dc6833d448d', 'LABEL_FIELD_ID', 'vqNo');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('4a7991c2113e4251807b50d8d3b8c3dc', 0, 1, 'PEPL', 'PEPL', '0', 'fdf152439b744c549b8bc3d2c8771823', 2, 'UniqueInModuleValidator', 'com.core.cbx.validation.validator.UniqueInModuleValidator', 'UniqueInModuleValidator', '1', 2);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b266030245d94c6981f764a06fbe0356', 0, 1, 'PEPL', 'PEPL', '0', '4a7991c2113e4251807b50d8d3b8c3dc', 'Vq', 'vqNo', 'entity.vqNo', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('3ddbd971f851429dbdcfaf68bb15b398', 0, 1, 'PEPL', 'PEPL', '0', 'fdf152439b744c549b8bc3d2c8771823', 3, 'NumericRangeValidator', 'com.core.cbx.validation.validator.NumericRangeValidator', 'NumericRangeValidator', '1', 3);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('11252d2944834e1abd1e9f803d549c46', 0, 1, 'PEPL', 'PEPL', '0', '3ddbd971f851429dbdcfaf68bb15b398', 'VqCarton', 'cartonQty', 'entity.vqCarton.cartonQty', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('09132e404fbd47908ecca26aaa491bbc', 0, 1, 'PEPL', 'PEPL', '0', '11252d2944834e1abd1e9f803d549c46', 'MIN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4e155b344cd148c6aeedd419b345d614', 0, 1, 'PEPL', 'PEPL', '0', '11252d2944834e1abd1e9f803d549c46', 'GRID_ID', 'vqCarton');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ff268cac3e3340b29facf96c3a8e4c11', 0, 1, 'PEPL', 'PEPL', '0', '11252d2944834e1abd1e9f803d549c46', 'LABEL_FIELD_ID', 'cartonQty');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('14b2547d6ec948c5be6a69be0498fd11', 0, 1, 'PEPL', 'PEPL', '0', 'fdf152439b744c549b8bc3d2c8771823', 4, 'EmailValidator', 'com.core.cbx.validation.validator.EmailValidator', 'EmailValidator', '1', 4);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ded24c073e7044d9b3fa81b9c6b2c5a8', 0, 1, 'PEPL', 'PEPL', '0', '14b2547d6ec948c5be6a69be0498fd11', 'Vq', 'contactEmail', 'entity.contactEmail', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('379ce857829245e8a4a8e28275d712a8', 0, 1, 'PEPL', 'PEPL', '0', 'ded24c073e7044d9b3fa81b9c6b2c5a8', 'LABEL_FIELD_ID', 'contactEmail');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('468df92e916b43ceba1f93ac34bc8714', 0, 1, 'PEPL', 'PEPL', '0', '14b2547d6ec948c5be6a69be0498fd11', 'VqContact', 'email', 'entity.vqContact.email', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1dd9f5dfb0ff4186b32ee11038efa4be', 0, 1, 'PEPL', 'PEPL', '0', '468df92e916b43ceba1f93ac34bc8714', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2d8dbab00f124aba9b313b7bcde70a0b', 0, 1, 'PEPL', 'PEPL', '0', '468df92e916b43ceba1f93ac34bc8714', 'LABEL_FIELD_ID', 'email');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('9c011a1be7f3419c9a2d6bfe9050292a', 0, 1, 'PEPL', 'PEPL', '0', 'fdf152439b744c549b8bc3d2c8771823', 5, 'ClassificationValidator', 'com.core.cbx.validation.validator.ClassificationValidator', 'ClassificationValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 5);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4a424ee999d4482aaeef9fcea8e3e135', 0, 1, 'PEPL', 'PEPL', '0', '9c011a1be7f3419c9a2d6bfe9050292a', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4b61f66736c0499ea614241c68808bc1', 0, 1, 'PEPL', 'PEPL', '0', '4a424ee999d4482aaeef9fcea8e3e135', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('018f576473ef43f0a108fb0cf16616ca', 0, 1, 'PEPL', 'PEPL', '0', '4a424ee999d4482aaeef9fcea8e3e135', 'DOCUMENT_NO', 'vendorCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('39be0cf7b3294d4b863b9932e56d811a', 0, 1, 'PEPL', 'PEPL', '0', 'fdf152439b744c549b8bc3d2c8771823', 6, 'HCLValidator', 'com.core.cbx.validation.validator.HCLValidator', 'HCLValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), 6);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c487ee883c664ee093aeab556940a64c', 0, 1, 'PEPL', 'PEPL', '0', '39be0cf7b3294d4b863b9932e56d811a', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8d9da50d20cf4c5391e1f2e9b323cbba', 0, 1, 'PEPL', 'PEPL', '0', 'c487ee883c664ee093aeab556940a64c', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('249d663a5ace4eb48aed305f9d838791', 0, 1, 'PEPL', 'PEPL', '0', 'c487ee883c664ee093aeab556940a64c', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8b6558449f8943eaae6b9d1b3bbe0d91', 0, 1, 'PEPL', 'PEPL', '0', 'c487ee883c664ee093aeab556940a64c', 'TARGET_FIELD', 'hcs');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('07a39a7169944227a267d569cf384396', 0, 1, 'PEPL', 'PEPL', '0', 'c487ee883c664ee093aeab556940a64c', 'TYPE', 'isMasterSelection');

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL';

INSERT INTO CNT_VALIDATION_PROFILE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, UPDATE_USER, UPDATED_ON, IS_LATEST, MAX_ERROR, CONSOLIDATE_ERROR, PRIORITY, IGNORE_CUSTOM_FIELD, PROFILE_NAME, INHERIT_FROM, REF_NO, REF_ENTITY_NAME, REF_ENTITY_VERSION, ACTION, ENABLED, CREATE_USER, CREATED_ON)
VALUES ('92cbe03625e9480caab2ea5d1d53d124', 0, 1, 'PEPL', 'PEPL', '0', '1', NULL, NULL, 'confirmed', NULL, TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'), '1', 0, '0', 0, '0', 'PopupCostBreakdown validation', NULL, (SELECT 'VAL' || to_char(now(), 'YYMM') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL'), 'Vq', 1, 'QuotationCostBreakdownRecalculate', '1', 'system', TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('e2b854e1903b4235aa22494fcc085500', 0, 1, 'PEPL', 'PEPL', '0', '92cbe03625e9480caab2ea5d1d53d124', 1, 'MandatoryValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryValidator', '1', 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('bcb4ddca77a349a08d42a6958c978802', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqOtherCharge', 'type', 'entity.vqOtherCharges.type', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('af647e326d854961b83986c70ab10396', 0, 1, 'PEPL', 'PEPL', '0', 'bcb4ddca77a349a08d42a6958c978802', 'GRID_ID', 'vqOtherCharges');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bd87ae81ada7422c9678546a96b86dee', 0, 1, 'PEPL', 'PEPL', '0', 'bcb4ddca77a349a08d42a6958c978802', 'LABEL_FIELD_ID', 'type');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('23dae61e25344022bef846fc53eb2601', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqOtherCharge', 'description', 'entity.vqOtherCharges.description', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('63037d39f1af46c4ade41b3a28ef69ab', 0, 1, 'PEPL', 'PEPL', '0', '23dae61e25344022bef846fc53eb2601', 'GRID_ID', 'vqOtherCharges');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f14ba7c351a44cd392705ba7b59f2caa', 0, 1, 'PEPL', 'PEPL', '0', '23dae61e25344022bef846fc53eb2601', 'LABEL_FIELD_ID', 'description');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('e676bf5a7d2149928791839e75ab1edb', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqOtherCharge', 'basis', 'entity.vqOtherCharges.basis', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('703c0a84040c44d88e4e573f4c6de22b', 0, 1, 'PEPL', 'PEPL', '0', 'e676bf5a7d2149928791839e75ab1edb', 'GRID_ID', 'vqOtherCharges');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cf7180f798434dc081cec5b429c946e5', 0, 1, 'PEPL', 'PEPL', '0', 'e676bf5a7d2149928791839e75ab1edb', 'LABEL_FIELD_ID', 'basis');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ca687fc15fd74e1b8f7d75c4ed586ce0', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqOtherCharge', 'rate', 'entity.vqOtherCharges.rate', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e99e636dcba3419bac48cbf32fde8fe3', 0, 1, 'PEPL', 'PEPL', '0', 'ca687fc15fd74e1b8f7d75c4ed586ce0', 'GRID_ID', 'vqOtherCharges');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a50e46cdbda34087b3ad73f4878a23fd', 0, 1, 'PEPL', 'PEPL', '0', 'ca687fc15fd74e1b8f7d75c4ed586ce0', 'LABEL_FIELD_ID', 'rate');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('99088c67fc6b419caa227d6723cb7e7d', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqOtherCharge', 'currency', 'entity.vqOtherCharges.currency', 5, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'basisMatchToFixed' AND IS_LATEST = '1'), 1, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('152335a686db4798be14728135905647', 0, 1, 'PEPL', 'PEPL', '0', '99088c67fc6b419caa227d6723cb7e7d', 'GRID_ID', 'vqOtherCharges');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b875b2f164ad44a28e5096dba4665aec', 0, 1, 'PEPL', 'PEPL', '0', '99088c67fc6b419caa227d6723cb7e7d', 'LABEL_FIELD_ID', 'currency');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9127e0e6f804446dabeaadc57f1756c2', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqComponentCost', 'consumption', 'entity.vqComponentCosts.consumption', 6, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'rfqRequiresOpenCosting' AND IS_LATEST = '1'), 0, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('000852a54dab4a409770d10a21cc7b0a', 0, 1, 'PEPL', 'PEPL', '0', '9127e0e6f804446dabeaadc57f1756c2', 'GRID_ID', 'vqComponentCosts');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c871638f09334a97ab90cd732c28de63', 0, 1, 'PEPL', 'PEPL', '0', '9127e0e6f804446dabeaadc57f1756c2', 'LABEL_FIELD_ID', 'consumption');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('14a326ad629644a582e3408b6e104c53', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqComponentCost', 'unitCost', 'entity.vqComponentCosts.unitCost', 7, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'rfqRequiresOpenCosting' AND IS_LATEST = '1'), 0, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('42781f2b38014ece93f81f946b4c7903', 0, 1, 'PEPL', 'PEPL', '0', '14a326ad629644a582e3408b6e104c53', 'GRID_ID', 'vqComponentCosts');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f67987e24b1e47469b343cf9b173aca1', 0, 1, 'PEPL', 'PEPL', '0', '14a326ad629644a582e3408b6e104c53', 'LABEL_FIELD_ID', 'unitCost');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('20388f0e9007450fa9531236bfe105d8', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqComponentCost', 'currency', 'entity.vqComponentCosts.currency', 8, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'rfqRequiresOpenCosting' AND IS_LATEST = '1'), 0, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4b4753de32bb4f50aa5d7b1b51060213', 0, 1, 'PEPL', 'PEPL', '0', '20388f0e9007450fa9531236bfe105d8', 'GRID_ID', 'vqComponentCosts');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('323ae1200bb741108ad5993536bb9b53', 0, 1, 'PEPL', 'PEPL', '0', '20388f0e9007450fa9531236bfe105d8', 'LABEL_FIELD_ID', 'currency');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b0f7d266147d43f6b30f356eb0779b00', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqAdditionalCost', 'consumption', 'entity.vqAdditionalCosts.consumption', 9, '1', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5dfa7b54d9194a95a69cd0cf33204b6d', 0, 1, 'PEPL', 'PEPL', '0', 'b0f7d266147d43f6b30f356eb0779b00', 'GRID_ID', 'vqAdditionalCosts');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3d03842859c44be48568787136939a49', 0, 1, 'PEPL', 'PEPL', '0', 'b0f7d266147d43f6b30f356eb0779b00', 'LABEL_FIELD_ID', 'consumption');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4fc791b0e7ec43ff8058463fca9044a8', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqAdditionalCost', 'unitCost', 'entity.vqAdditionalCosts.unitCost', 10, '1', NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7a2caad12016424fbf8132452bf80a3f', 0, 1, 'PEPL', 'PEPL', '0', '4fc791b0e7ec43ff8058463fca9044a8', 'GRID_ID', 'vqAdditionalCosts');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('60b4d13c9147498b844fd48d38baac0a', 0, 1, 'PEPL', 'PEPL', '0', '4fc791b0e7ec43ff8058463fca9044a8', 'LABEL_FIELD_ID', 'unitCost');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('692ef40bc3314077af13337778dadc4b', 0, 1, 'PEPL', 'PEPL', '0', 'e2b854e1903b4235aa22494fcc085500', 'VqAdditionalCost', 'currency', 'entity.vqAdditionalCosts.currency', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('de883e39d6684ab6bae35e582121b506', 0, 1, 'PEPL', 'PEPL', '0', '692ef40bc3314077af13337778dadc4b', 'GRID_ID', 'vqAdditionalCosts');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9fc1409e526b4db1a5d14e1524155723', 0, 1, 'PEPL', 'PEPL', '0', '692ef40bc3314077af13337778dadc4b', 'LABEL_FIELD_ID', 'currency');

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL';

INSERT INTO CNT_VALIDATION_PROFILE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, UPDATE_USER, UPDATED_ON, IS_LATEST, MAX_ERROR, CONSOLIDATE_ERROR, PRIORITY, IGNORE_CUSTOM_FIELD, PROFILE_NAME, INHERIT_FROM, REF_NO, REF_ENTITY_NAME, REF_ENTITY_VERSION, ACTION, ENABLED, CREATE_USER, CREATED_ON)
VALUES ('78d7bb861082430586893ea05648d0ac', 0, 1, 'PEPL', 'PEPL', '0', '1', NULL, NULL, 'confirmed', NULL, TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'), '1', 0, '0', 0, '0', 'Default Data Validation Profile Vq[ver:1] SendToVendor', NULL, (SELECT 'VAL' || to_char(now(), 'YYMM') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL'), 'Vq', 1, 'SendToVendor', '1', 'system', TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('d9ec4148037746d9afe4160d29bcdfbc', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 1, 'MandatoryValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryValidator', '1', 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('3666aab699f741ee8885546543f51487', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6b55f5b38cb640eb855a18cac7c40117', 0, 1, 'PEPL', 'PEPL', '0', '3666aab699f741ee8885546543f51487', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('aeb53ca9e14a42a2bc1b953a85137665', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'vqType', 'entity.vqType', 2, '0', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('027e087fea4a49629a7fa0ff3033c923', 0, 1, 'PEPL', 'PEPL', '0', 'aeb53ca9e14a42a2bc1b953a85137665', 'LABEL_FIELD_ID', 'vqType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c788c996e78f4f5f87a4c96945a92327', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'currency', 'entity.currency', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('43472f801b3a45c5a4bc00664db77805', 0, 1, 'PEPL', 'PEPL', '0', 'c788c996e78f4f5f87a4c96945a92327', 'LABEL_FIELD_ID', 'currency');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('732354329f1549f5a03302b960edf72a', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'unitCost', 'entity.unitCost', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6f6b03cc8a9c4104be82eb7c6cbd7d45', 0, 1, 'PEPL', 'PEPL', '0', '732354329f1549f5a03302b960edf72a', 'LABEL_FIELD_ID', 'unitCost');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d0722c69668c4089b12ad9fa05d2a55f', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'incoterm', 'entity.incoterm', 5, '0', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2bb81d5b59294cfb88eb2494905ff364', 0, 1, 'PEPL', 'PEPL', '0', 'd0722c69668c4089b12ad9fa05d2a55f', 'LABEL_FIELD_ID', 'incoterm');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('73fe806ec111477d92d0c7c9e45eae07', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'shipmentMethod', 'entity.shipmentMethod', 6, '0', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e28e7815f9fe4164bb6f808afac170c1', 0, 1, 'PEPL', 'PEPL', '0', '73fe806ec111477d92d0c7c9e45eae07', 'LABEL_FIELD_ID', 'shipmentMethod');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('0f2b94c3fb4644108bb33ccc7929e131', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'countryOfOrigin', 'entity.countryOfOrigin', 7, '1', NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('22dd63a0eb394cc291aba201da0470e7', 0, 1, 'PEPL', 'PEPL', '0', '0f2b94c3fb4644108bb33ccc7929e131', 'LABEL_FIELD_ID', 'countryOfOrigin');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('23c6475711b9449db82e6f7fbcf8f301', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'countryOfShipment', 'entity.countryOfShipment', 8, '1', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('36a8e07b339246f6a58cab8d6212baf8', 0, 1, 'PEPL', 'PEPL', '0', '23c6475711b9449db82e6f7fbcf8f301', 'LABEL_FIELD_ID', 'countryOfShipment');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('bbe360f26ddc4c84bfa5a2875c17fdac', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'portOfLoading', 'entity.portOfLoading', 9, '0', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8a437496324e413280356a1dc47a21ba', 0, 1, 'PEPL', 'PEPL', '0', 'bbe360f26ddc4c84bfa5a2875c17fdac', 'LABEL_FIELD_ID', 'portOfLoading');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('56bf3b78943d4d2c8d3a40aaced02aca', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'Vq', 'hierarchy', 'entity.hierarchy', 10, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cdd0b5cb2d10438689cd782026a66189', 0, 1, 'PEPL', 'PEPL', '0', '56bf3b78943d4d2c8d3a40aaced02aca', 'LABEL_FIELD_ID', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('25d476b7bbfd45d6969b0302a1688738', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'VqCarton', 'cartonType', 'entity.vqCarton.cartonType', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('58d4eb0e1a2445eb8b8a079294237d25', 0, 1, 'PEPL', 'PEPL', '0', '25d476b7bbfd45d6969b0302a1688738', 'GRID_ID', 'vqCarton');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2489ca6d6f8a44439fd78f3d61c5997d', 0, 1, 'PEPL', 'PEPL', '0', '25d476b7bbfd45d6969b0302a1688738', 'LABEL_FIELD_ID', 'cartonType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2f7d3e53d0214bb390c9a0a32cd933e0', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'VqImage', 'fileId', 'entity.vqImage.fileId', 12, '1', NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cb2aedf6508748148459e43934345cf3', 0, 1, 'PEPL', 'PEPL', '0', '2f7d3e53d0214bb390c9a0a32cd933e0', 'GRID_ID', 'vqImage');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('83854ad269634b318adf5e0c6bf954e7', 0, 1, 'PEPL', 'PEPL', '0', '2f7d3e53d0214bb390c9a0a32cd933e0', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('31c419e55cf24f3495dfebdbeb8dd566', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'VqContact', 'firstName', 'entity.vqContact.firstName', 13, '1', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('682d65635f7448e5976884b49b902d62', 0, 1, 'PEPL', 'PEPL', '0', '31c419e55cf24f3495dfebdbeb8dd566', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('06b055b67c634339980e9a671bc1f0ed', 0, 1, 'PEPL', 'PEPL', '0', '31c419e55cf24f3495dfebdbeb8dd566', 'LABEL_FIELD_ID', 'firstName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('eb2c9973245f47e09ee720df24a0d841', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'VqContact', 'email', 'entity.vqContact.email', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6415030528764499ae89310d01766395', 0, 1, 'PEPL', 'PEPL', '0', 'eb2c9973245f47e09ee720df24a0d841', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('625d37b3563b4e41baeda595da97de84', 0, 1, 'PEPL', 'PEPL', '0', 'eb2c9973245f47e09ee720df24a0d841', 'LABEL_FIELD_ID', 'email');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('670fb67a9c5b4d67b73ab5a255cef338', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'VqAddress', 'address1', 'entity.vqAddress.address1', 15, '1', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7885ee2bd48c413e87df9480e133f5f5', 0, 1, 'PEPL', 'PEPL', '0', '670fb67a9c5b4d67b73ab5a255cef338', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a00ee9cb6ab74989a82a9255584dab8c', 0, 1, 'PEPL', 'PEPL', '0', '670fb67a9c5b4d67b73ab5a255cef338', 'LABEL_FIELD_ID', 'address1');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6ca98d7147db48569d1b86520c5ab6c5', 0, 1, 'PEPL', 'PEPL', '0', 'd9ec4148037746d9afe4160d29bcdfbc', 'VqAddress', 'country', 'entity.vqAddress.country', 16, '1', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('57ff896cca5c4e6e9166919edf483322', 0, 1, 'PEPL', 'PEPL', '0', '6ca98d7147db48569d1b86520c5ab6c5', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5513db4931d346a29206fe0130e17cf7', 0, 1, 'PEPL', 'PEPL', '0', '6ca98d7147db48569d1b86520c5ab6c5', 'LABEL_FIELD_ID', 'country');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('f5707b63f3894c86bb6362c43acdcdc3', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 2, 'UniqueInModuleValidator', 'com.core.cbx.validation.validator.UniqueInModuleValidator', 'UniqueInModuleValidator', '1', 2);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5337b35bf2ac4990ae98cd7eb45d016f', 0, 1, 'PEPL', 'PEPL', '0', 'f5707b63f3894c86bb6362c43acdcdc3', 'Vq', 'vqNo', 'entity.vqNo', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('20758aeb65e24a24b23c5e4c1014f7fe', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 3, 'NumericRangeValidator', 'com.core.cbx.validation.validator.NumericRangeValidator', 'NumericRangeValidator', '1', 3);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('fa7f8dc01cdd48ff92a143568c353423', 0, 1, 'PEPL', 'PEPL', '0', '20758aeb65e24a24b23c5e4c1014f7fe', 'Vq', 'prodWeight', 'entity.prodWeight', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6d277a2bd09a4edcb56dbaa3c2816e93', 0, 1, 'PEPL', 'PEPL', '0', 'fa7f8dc01cdd48ff92a143568c353423', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fbeef9bde1cc45bbb8c068823e7498fb', 0, 1, 'PEPL', 'PEPL', '0', 'fa7f8dc01cdd48ff92a143568c353423', 'LABEL_FIELD_ID', 'prodWeight');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2919665fa59645ac98442aa4155d6fc6', 0, 1, 'PEPL', 'PEPL', '0', '20758aeb65e24a24b23c5e4c1014f7fe', 'VqCarton', 'cartonQty', 'entity.vqCarton.cartonQty', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f9ec83fe5a8947e79214f4f4061f005b', 0, 1, 'PEPL', 'PEPL', '0', '2919665fa59645ac98442aa4155d6fc6', 'MIN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0b9607830db8419bb8247b99fce8f27a', 0, 1, 'PEPL', 'PEPL', '0', '2919665fa59645ac98442aa4155d6fc6', 'GRID_ID', 'vqCarton');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bbd0641de4274238b3f673a7a0571f90', 0, 1, 'PEPL', 'PEPL', '0', '2919665fa59645ac98442aa4155d6fc6', 'LABEL_FIELD_ID', 'cartonQty');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('0768c0542f7c44bc866d9294d350c56f', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 4, 'EmailValidator', 'com.core.cbx.validation.validator.EmailValidator', 'EmailValidator', '1', 4);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4434f232b6064c38b5345498da86c0c8', 0, 1, 'PEPL', 'PEPL', '0', '0768c0542f7c44bc866d9294d350c56f', 'Vq', 'contactEmail', 'entity.contactEmail', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3a7a4c6518994ac5ae2e25898fbecec3', 0, 1, 'PEPL', 'PEPL', '0', '4434f232b6064c38b5345498da86c0c8', 'LABEL_FIELD_ID', 'contactEmail');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8be2c8d12d4a4973a3ffc56fc10f16d1', 0, 1, 'PEPL', 'PEPL', '0', '0768c0542f7c44bc866d9294d350c56f', 'VqContact', 'email', 'entity.vqContact.email', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('16938a662d2b451a8750f867347b84c9', 0, 1, 'PEPL', 'PEPL', '0', '8be2c8d12d4a4973a3ffc56fc10f16d1', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7bcdc9194b6848409c0847247f35ab8f', 0, 1, 'PEPL', 'PEPL', '0', '8be2c8d12d4a4973a3ffc56fc10f16d1', 'LABEL_FIELD_ID', 'email');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('d0ab583f34d24a8dbbf162f4e6974839', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 5, 'ClassificationValidator', 'com.core.cbx.validation.validator.ClassificationValidator', 'ClassificationValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 5);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('119cb0dbf41247108e980b6eb15ef3ab', 0, 1, 'PEPL', 'PEPL', '0', 'd0ab583f34d24a8dbbf162f4e6974839', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6b30773d896840869e15d12f18288ac4', 0, 1, 'PEPL', 'PEPL', '0', '119cb0dbf41247108e980b6eb15ef3ab', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('457bdf2959e64ad1a23404375bbd2c51', 0, 1, 'PEPL', 'PEPL', '0', '119cb0dbf41247108e980b6eb15ef3ab', 'DOCUMENT_NO', 'vendorCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('1e5488999abf4c588ac3815a5dc528be', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 6, 'HCLValidator', 'com.core.cbx.validation.validator.HCLValidator', 'HCLValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), 6);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('fc5c247a85904aaab99c3ac2e5bb1a56', 0, 1, 'PEPL', 'PEPL', '0', '1e5488999abf4c588ac3815a5dc528be', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e815ef17ad584d30844cf5426b11dab9', 0, 1, 'PEPL', 'PEPL', '0', 'fc5c247a85904aaab99c3ac2e5bb1a56', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('53593b1f1aa14f48a25c0ee27cf90ab6', 0, 1, 'PEPL', 'PEPL', '0', 'fc5c247a85904aaab99c3ac2e5bb1a56', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('50b67eb0d40e4cb88eac61a349f792ad', 0, 1, 'PEPL', 'PEPL', '0', 'fc5c247a85904aaab99c3ac2e5bb1a56', 'TARGET_FIELD', 'hcs');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2bf6f798f1a44f258d31cfde9a641521', 0, 1, 'PEPL', 'PEPL', '0', 'fc5c247a85904aaab99c3ac2e5bb1a56', 'TYPE', 'isMasterSelection');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('53ff9c9703d14dba9a6cefb72be10019', 0, 1, 'PEPL', 'PEPL', '0', '78d7bb861082430586893ea05648d0ac', 7, 'ExternalActiveValidator', 'com.core.cbx.validation.validator.ExternalActiveValidator', 'ExternalActiveValidator', '1', 7);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('61fbb6a89b8442bdb630bd54147b349f', 0, 1, 'PEPL', 'PEPL', '0', '53ff9c9703d14dba9a6cefb72be10019', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b4091fc0b4cc4022b3760d979f102524', 0, 1, 'PEPL', 'PEPL', '0', '61fbb6a89b8442bdb630bd54147b349f', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5065517545c845bd9bee2835149a24e8', 0, 1, 'PEPL', 'PEPL', '0', '61fbb6a89b8442bdb630bd54147b349f', 'DOCUMENT_NO', 'vendorCode');

UPDATE CNT_SEQ_DEF SET NEXT_VAL = NEXT_VAL + 1 WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL';

INSERT INTO CNT_VALIDATION_PROFILE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, UPDATE_USER, UPDATED_ON, IS_LATEST, MAX_ERROR, CONSOLIDATE_ERROR, PRIORITY, IGNORE_CUSTOM_FIELD, PROFILE_NAME, INHERIT_FROM, REF_NO, REF_ENTITY_NAME, REF_ENTITY_VERSION, ACTION, ENABLED, CREATE_USER, CREATED_ON)
VALUES ('a29332a131634f9f9d05c91efbf5349e', 0, 1, 'PEPL', 'PEPL', '0', '1', NULL, NULL, 'confirmed', NULL, TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'), '1', 0, '0', 0, '0', 'Default Data Validation Profile Vq[ver:1] SaveDoc,SaveAndConfirm,SubmitVq,ConfirmToBuy', NULL, (SELECT 'VAL' || to_char(now(), 'YYMM') || '-' || LPAD((NEXT_VAL - 1)::TEXT, 6, '0') FROM CNT_SEQ_DEF WHERE SEQ_ID = 'CBX_SEQ_VALIDATION_PROFILE_CODE' AND DOMAIN_ID = 'PEPL'), 'Vq', 1, 'SaveDoc,SaveAndConfirm,SubmitVq,ConfirmToBuy', '1', 'system', TO_TIMESTAMP('2025-08-11 16:39:03', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('699e619d0d1344e2bb7fa49c92cd31c0', 0, 1, 'PEPL', 'PEPL', '0', 'a29332a131634f9f9d05c91efbf5349e', 1, 'MandatoryValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryValidator', '1', 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6eed38cdfb7547619238332527e6b673', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f1d88a133b4a4cd68510d2ce17fa9e93', 0, 1, 'PEPL', 'PEPL', '0', '6eed38cdfb7547619238332527e6b673', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2ffb7d348a0c4a919d1d935cde30a977', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'vqType', 'entity.vqType', 2, '0', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('601e4323a1c54c0e8cbdcba1228e7002', 0, 1, 'PEPL', 'PEPL', '0', '2ffb7d348a0c4a919d1d935cde30a977', 'LABEL_FIELD_ID', 'vqType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4f52ecd7a5b1455e9650d9f085397899', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'currency', 'entity.currency', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2c76576b7dba441d96bdc5572b14669d', 0, 1, 'PEPL', 'PEPL', '0', '4f52ecd7a5b1455e9650d9f085397899', 'LABEL_FIELD_ID', 'currency');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('dcfaf64e415444aead18a61ad9eac7d5', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'unitCost', 'entity.unitCost', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3d5078a1ed1e457884c228068cee45b2', 0, 1, 'PEPL', 'PEPL', '0', 'dcfaf64e415444aead18a61ad9eac7d5', 'LABEL_FIELD_ID', 'unitCost');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9c10a47cec3340e0afc74c835f05d808', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'incoterm', 'entity.incoterm', 5, '0', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('82806344e8bc4eb4841b52586bf32f70', 0, 1, 'PEPL', 'PEPL', '0', '9c10a47cec3340e0afc74c835f05d808', 'LABEL_FIELD_ID', 'incoterm');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('105c322629bd43b7bf7731481cd04f20', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'shipmentMethod', 'entity.shipmentMethod', 6, '0', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7e0a350fc5654dd8bf18bb92fee511d8', 0, 1, 'PEPL', 'PEPL', '0', '105c322629bd43b7bf7731481cd04f20', 'LABEL_FIELD_ID', 'shipmentMethod');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('824b0aa1ee8c4b2e96861b114d87b9b4', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'countryOfOrigin', 'entity.countryOfOrigin', 7, '1', NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('935d4b69b143422d98bb53b609ef87a7', 0, 1, 'PEPL', 'PEPL', '0', '824b0aa1ee8c4b2e96861b114d87b9b4', 'LABEL_FIELD_ID', 'countryOfOrigin');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ce684c1f0cae4fafacf2a2f430f8222d', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'countryOfShipment', 'entity.countryOfShipment', 8, '1', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('27e639f20b10469985f94b24dbe39edf', 0, 1, 'PEPL', 'PEPL', '0', 'ce684c1f0cae4fafacf2a2f430f8222d', 'LABEL_FIELD_ID', 'countryOfShipment');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d85f6108fbcf44fe9159625aa07a7f03', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'portOfLoading', 'entity.portOfLoading', 9, '0', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('04990d20782f46919e81a562e5c9b646', 0, 1, 'PEPL', 'PEPL', '0', 'd85f6108fbcf44fe9159625aa07a7f03', 'LABEL_FIELD_ID', 'portOfLoading');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('47b71be1afd545909e5fe65256e7bda3', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'hierarchy', 'entity.hierarchy', 10, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('45155f027ee848ea93decc2dcf63c43a', 0, 1, 'PEPL', 'PEPL', '0', '47b71be1afd545909e5fe65256e7bda3', 'LABEL_FIELD_ID', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8175283e298e42ff905e6263fcb98465', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqCarton', 'cartonType', 'entity.vqCarton.cartonType', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3e6a82c8062b4322ab14c759753bdac8', 0, 1, 'PEPL', 'PEPL', '0', '8175283e298e42ff905e6263fcb98465', 'GRID_ID', 'vqCarton');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d31a07f682554cb0b9eab75596cf213e', 0, 1, 'PEPL', 'PEPL', '0', '8175283e298e42ff905e6263fcb98465', 'LABEL_FIELD_ID', 'cartonType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5fa7bea38ce64cb28c21ad8e39a28c08', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqImage', 'fileId', 'entity.vqImage.fileId', 12, '1', NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('80403b2db5034a5b867221064c089a08', 0, 1, 'PEPL', 'PEPL', '0', '5fa7bea38ce64cb28c21ad8e39a28c08', 'GRID_ID', 'vqImage');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('170869ef803443149b1235c15c416d43', 0, 1, 'PEPL', 'PEPL', '0', '5fa7bea38ce64cb28c21ad8e39a28c08', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('75f53d57a06a406b8158fb044e6f2294', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqContact', 'contactTypeId', 'entity.vqContact.contactTypeId', 13, '0', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0dfc133d3f514bc596de230191eba784', 0, 1, 'PEPL', 'PEPL', '0', '75f53d57a06a406b8158fb044e6f2294', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fd1525fab31344fdb4b377d410865197', 0, 1, 'PEPL', 'PEPL', '0', '75f53d57a06a406b8158fb044e6f2294', 'LABEL_FIELD_ID', 'contactTypeId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ce29292c88a6482480df976bad84b7c0', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqContact', 'firstName', 'entity.vqContact.firstName', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4af78b38192546eaba4097d4fbada8db', 0, 1, 'PEPL', 'PEPL', '0', 'ce29292c88a6482480df976bad84b7c0', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2746bb6128ba4eebbbd34c073a3929b2', 0, 1, 'PEPL', 'PEPL', '0', 'ce29292c88a6482480df976bad84b7c0', 'LABEL_FIELD_ID', 'firstName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ed63b014592a4704b8c35570912390d3', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqContact', 'email', 'entity.vqContact.email', 15, '1', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6eb047c6e17444f585dc2a7636853280', 0, 1, 'PEPL', 'PEPL', '0', 'ed63b014592a4704b8c35570912390d3', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('70753cf3959c461ba41e3a83f1544cdb', 0, 1, 'PEPL', 'PEPL', '0', 'ed63b014592a4704b8c35570912390d3', 'LABEL_FIELD_ID', 'email');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c2299802408942aebebfea393ee13759', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqAddress', 'addressTypeId', 'entity.vqAddress.addressTypeId', 16, '0', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6a39b820ab6f472b8a32140b3f8e7ee2', 0, 1, 'PEPL', 'PEPL', '0', 'c2299802408942aebebfea393ee13759', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('52ecf39b29f74e4c95d35e1a811ad439', 0, 1, 'PEPL', 'PEPL', '0', 'c2299802408942aebebfea393ee13759', 'LABEL_FIELD_ID', 'addressTypeId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('750a9566ec4b4da1926f906ee5f41bda', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqAddress', 'address1', 'entity.vqAddress.address1', 17, '1', NULL, 17);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('edbef7494c1c4b1e89dae0ae6e4723a7', 0, 1, 'PEPL', 'PEPL', '0', '750a9566ec4b4da1926f906ee5f41bda', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9399deaa6947480dbf400a74307eba90', 0, 1, 'PEPL', 'PEPL', '0', '750a9566ec4b4da1926f906ee5f41bda', 'LABEL_FIELD_ID', 'address1');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('fdd08f36c36940c18531dd3b526951ab', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'VqAddress', 'country', 'entity.vqAddress.country', 18, '1', NULL, 18);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3758157a13b74d5bb9569bb53c9c6a01', 0, 1, 'PEPL', 'PEPL', '0', 'fdd08f36c36940c18531dd3b526951ab', 'GRID_ID', 'vqAddress');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7ea8be0d10294ae88d6fa8f7a00cd24a', 0, 1, 'PEPL', 'PEPL', '0', 'fdd08f36c36940c18531dd3b526951ab', 'LABEL_FIELD_ID', 'country');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ac5d7fc0202746cd9886197feeff890a', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'vqNo', 'entity.vqNo', 19, '0', NULL, 19);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a2f1cc60e2ae40698fe55262740afab4', 0, 1, 'PEPL', 'PEPL', '0', 'ac5d7fc0202746cd9886197feeff890a', 'LABEL_FIELD_ID', 'vqNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('99d2d464d17e4e7f9b29da9fea52c8a6', 0, 1, 'PEPL', 'PEPL', '0', '699e619d0d1344e2bb7fa49c92cd31c0', 'Vq', 'prodWeight', 'entity.prodWeight', 20, '1', NULL, 20);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0efd3c72f7d344c885511756f53051eb', 0, 1, 'PEPL', 'PEPL', '0', '99d2d464d17e4e7f9b29da9fea52c8a6', 'LABEL_FIELD_ID', 'prodWeight');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('aa06f41014f54e2999c9d1b4db0f62b1', 0, 1, 'PEPL', 'PEPL', '0', 'a29332a131634f9f9d05c91efbf5349e', 2, 'UniqueInModuleValidator', 'com.core.cbx.validation.validator.UniqueInModuleValidator', 'UniqueInModuleValidator', '1', 2);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c408a9df350e4fe0bb397075f50455a6', 0, 1, 'PEPL', 'PEPL', '0', 'aa06f41014f54e2999c9d1b4db0f62b1', 'Vq', 'vqNo', 'entity.vqNo', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('fdfec93abe7e4d159a7aae4247d695bb', 0, 1, 'PEPL', 'PEPL', '0', 'a29332a131634f9f9d05c91efbf5349e', 3, 'NumericRangeValidator', 'com.core.cbx.validation.validator.NumericRangeValidator', 'NumericRangeValidator', '1', 3);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('97533a4daa1347218fddfaef5cd090a3', 0, 1, 'PEPL', 'PEPL', '0', 'fdfec93abe7e4d159a7aae4247d695bb', 'Vq', 'prodWeight', 'entity.prodWeight', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('59e623a4d8fc48c4a6fde544e5ac320a', 0, 1, 'PEPL', 'PEPL', '0', '97533a4daa1347218fddfaef5cd090a3', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('73404f355b6643b7886bfb2078c9f306', 0, 1, 'PEPL', 'PEPL', '0', '97533a4daa1347218fddfaef5cd090a3', 'LABEL_FIELD_ID', 'prodWeight');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5a68124fde2b4b759a39895bd4ed443e', 0, 1, 'PEPL', 'PEPL', '0', 'fdfec93abe7e4d159a7aae4247d695bb', 'VqCarton', 'cartonQty', 'entity.vqCarton.cartonQty', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('624689753e224245b0665b0a4c199ff5', 0, 1, 'PEPL', 'PEPL', '0', '5a68124fde2b4b759a39895bd4ed443e', 'MIN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('749b548bc7d8422fbfa6401006bf5a52', 0, 1, 'PEPL', 'PEPL', '0', '5a68124fde2b4b759a39895bd4ed443e', 'GRID_ID', 'vqCarton');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b3ad86670cd740efb4753dc93ea095d4', 0, 1, 'PEPL', 'PEPL', '0', '5a68124fde2b4b759a39895bd4ed443e', 'LABEL_FIELD_ID', 'cartonQty');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('8e208cc71e534b239be91af2e8196fe4', 0, 1, 'PEPL', 'PEPL', '0', 'a29332a131634f9f9d05c91efbf5349e', 4, 'EmailValidator', 'com.core.cbx.validation.validator.EmailValidator', 'EmailValidator', '1', 4);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f0a76cce22a4491bbd641078a6842908', 0, 1, 'PEPL', 'PEPL', '0', '8e208cc71e534b239be91af2e8196fe4', 'Vq', 'contactEmail', 'entity.contactEmail', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('abb744c57d1948699ae9d05ecac426b4', 0, 1, 'PEPL', 'PEPL', '0', 'f0a76cce22a4491bbd641078a6842908', 'LABEL_FIELD_ID', 'contactEmail');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ebd0a3178719421d8a130f4e3c679ddd', 0, 1, 'PEPL', 'PEPL', '0', '8e208cc71e534b239be91af2e8196fe4', 'VqContact', 'email', 'entity.vqContact.email', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6307214de4ee4c97ac7d35c5343af3b2', 0, 1, 'PEPL', 'PEPL', '0', 'ebd0a3178719421d8a130f4e3c679ddd', 'GRID_ID', 'vqContact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('eed9f77192ae4d95a666f792cef11678', 0, 1, 'PEPL', 'PEPL', '0', 'ebd0a3178719421d8a130f4e3c679ddd', 'LABEL_FIELD_ID', 'email');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('8c4842599c9a4e2e9f1bff452cf2fd5c', 0, 1, 'PEPL', 'PEPL', '0', 'a29332a131634f9f9d05c91efbf5349e', 5, 'ClassificationValidator', 'com.core.cbx.validation.validator.ClassificationValidator', 'ClassificationValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 5);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('e21cd2af21d04b828500a9018e3783a8', 0, 1, 'PEPL', 'PEPL', '0', '8c4842599c9a4e2e9f1bff452cf2fd5c', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e968f27199d94dde82ac8cbca3c25f2b', 0, 1, 'PEPL', 'PEPL', '0', 'e21cd2af21d04b828500a9018e3783a8', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7064c7a694fc405a8171053f884cde28', 0, 1, 'PEPL', 'PEPL', '0', 'e21cd2af21d04b828500a9018e3783a8', 'DOCUMENT_NO', 'vendorCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('4f18e95621dd41dc98383ea375d141ac', 0, 1, 'PEPL', 'PEPL', '0', 'a29332a131634f9f9d05c91efbf5349e', 6, 'HCLValidator', 'com.core.cbx.validation.validator.HCLValidator', 'HCLValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), 6);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('950fa36dd1e64c96a8ecb3837c50e1a8', 0, 1, 'PEPL', 'PEPL', '0', '4f18e95621dd41dc98383ea375d141ac', 'Vq', 'vendor', 'entity.vendor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6aca61c89af246dda5fb68987b9409c5', 0, 1, 'PEPL', 'PEPL', '0', '950fa36dd1e64c96a8ecb3837c50e1a8', 'LABEL_FIELD_ID', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8405563d726541a699a385d2a6c25b99', 0, 1, 'PEPL', 'PEPL', '0', '950fa36dd1e64c96a8ecb3837c50e1a8', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('53e1d98785474ace97e92d6e95758548', 0, 1, 'PEPL', 'PEPL', '0', '950fa36dd1e64c96a8ecb3837c50e1a8', 'TARGET_FIELD', 'hcs');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a5562cbae0ce438d8e82b9ec86a4fdd7', 0, 1, 'PEPL', 'PEPL', '0', '950fa36dd1e64c96a8ecb3837c50e1a8', 'TYPE', 'isMasterSelection');

COMMIT;