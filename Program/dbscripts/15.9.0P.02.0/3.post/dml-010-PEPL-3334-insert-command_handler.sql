INSERT INTO cnt_command_handler (id, revision, entity_version, hub_domain_id, is_for_reference, internal_seq_no, duid, command_type, "implementation", priority, enable, is_async_retry, max_retry, retry_interval, create_user, update_user, created_on, updated_on, route_to_domain_server, domain_id,category)
select sys_guid(), 1, 1, 'PEPL', false, NULL, NULL, 'UserLoginToken', 'com.core.pepl.command.handler.UserLoginTokenCommandHandler', 1, true, NULL, NULL, NULL, 'admin@/', 'admin@/', now(), now(), NULL, 'PEPL','Common'
where not exists (select 1 from cnt_command_handler where command_type = 'UserLoginToken');


INSERT INTO cnt_command_handler (id, revision, entity_version, hub_domain_id, is_for_reference, internal_seq_no, duid, command_type, "implementation", priority, "enable", is_async_retry, max_retry, retry_interval, create_user, update_user, created_on, updated_on, route_to_domain_server, domain_id, category) 
select SYS_guid(), 1, 1, 'PEPL', false, NULL, NULL, 'ExecuteAPIAction', 'com.core.pepl.command.handler.ExecuteAPIActionCommandHandler', 1, true, NULL, NULL, NULL, 'admin@/', 'admin@/', now(), now(), NULL, 'PEPL', 'Common' 
where not exists (select 1 from cnt_command_handler where command_type = 'ExecuteAPIAction');
