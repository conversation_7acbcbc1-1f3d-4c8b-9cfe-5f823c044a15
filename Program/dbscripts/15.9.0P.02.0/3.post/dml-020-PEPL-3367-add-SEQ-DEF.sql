INSERT INTO CNT_SEQ_DEF (ID, REVISI<PERSON>, ENTITY_VERSION, <PERSON><PERSON><PERSON><PERSON>_ID, SEQ_ID, START_WITH, MAX_VALUE, INCREMENT_BY, <PERSON><PERSON><PERSON><PERSON>, CACHE_SIZE, NEXT_VAL, HUB_DOMAIN_ID, IS_FOR_REFERENCE, CY<PERSON>E_STARTED_ON, UPDATED_ON) 
SELECT SYS_GUID(), 1, 1, 'PEPL', 'CBX_SEQ_INSPT_RPT_TMPL_REF_NO', 1, 999999, 1, 'Y', 0, 1, 'PEPL', '0', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM CNT_SEQ_DEF WHERE DOMAIN_ID = 'PEPL' AND SEQ_ID = 'CBX_SEQ_INSPT_RPT_TMPL_REF_NO');

COMMIT;