--// FILE: DDL_ENTITY_inspectReport.sql

ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS CARTON_DETAIL_SIZE VARCHAR(400);
ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS CARTON_DETAIL_COLOR VARCHAR(400);
ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS NO_OF_CARTONS NUMERIC(20, 0);
ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS DELIVERED_QUANTITY NUMERIC(20, 0);
ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS INSPECTED_CARTON_QUANTITY NUMERIC(20, 0);
ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS ACTUAL_INSPECTED_QUANTITY NUMERIC(20, 0);
ALTER TABLE CNT_INSPECT_REPORT_CARTON_DETAIL ADD COLUMN IF NOT EXISTS SORT_OUT_QUANTITY NUMERIC(20, 0);

COMMIT;