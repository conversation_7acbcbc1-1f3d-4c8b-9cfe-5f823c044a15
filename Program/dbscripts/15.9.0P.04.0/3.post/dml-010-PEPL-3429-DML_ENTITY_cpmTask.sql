--// FILE: DML_ENTITY_cpmTask.sql

DELETE FROM CNT_FIELD_DEFINITION WHERE PARENT_ID IN (SELECT ID FROM CNT_ENTITY_DEFINITION WHERE MODULE = 'cpmTask' AND ENTITY_VERSION = 1);
DELETE FROM CNT_ENTITY_DEFINITION WHERE MODULE = 'cpmTask' AND ENTITY_VERSION = 1;
DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = ('CpmTask');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('c5e10b42b3d84271a874adf5f8602d2e', 0, 'CpmTaskParty', 1, 'CNT_CPM_TASK_PARTY', NULL, 'CTM_CPM_TASK', 'CPM_TASK_PARTY', 'cpmTask', NULL, NULL, NULL, NULL, '${partyName.code}-${contactUserRef}', '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fcf7746cefbd47ceb887d7594a6c077c', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.parties.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dcd112fe87e444b7a15b2f913bfb036a', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.parties.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5a0e5dfdfce442ab84f423ccb403446b', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.parties.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ee225a2143b947ea9135c93da0f7f999', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.parties.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a16cf38176f24e5c956e0af1d35410ec', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.parties.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a2b07a1d4570422fb87cb9015a2e9813', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.parties.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4315b4d426744e2a8f7df5aca84e58ff', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.parties.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('de309d59f6b94367ad822d65d703b702', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.parties.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bb6c9eed0f9347a6aca8e87027def079', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.parties.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('acc40c7d3c9549c09a85f10d6d0d0ee6', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'docId', 'DOC_ID', 'VARCHAR(32)', 'DOC_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.parties.docId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2b1a08b32a7d44ea81c956abb6324ac1', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'partyName', 'PARTY_NAME', 'VARCHAR(400)', 'PARTY_NAME', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.parties.partyName', 'codelist', 'RESPONSIBLE_PARTIES_NAME', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d01da5723f7441b3a9cd2218cdab0c06', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'partyNameSeqNo', 'PARTY_NAME_SEQ_NO', 'NUMERIC(20, 0)', 'PARTY_NAME_SEQ_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.parties.partyNameSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('66256f01ec984e768241d4863824e9a6', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'contactUser', 'CONTACT_USER_ID', 'VARCHAR(32)', 'CONTACT_USER', '0', 'entity', 'User', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.parties.contactUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('942fc61119c44f24a8b38f676f69849e', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'contactUserSeqNo', 'CONTACT_USER_SEQ_NO', 'NUMERIC(20, 0)', 'CONTACT_USER_SEQ_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.parties.contactUserSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('31437d8470e541e5991e7ef1e45e2b21', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'isOwner', 'IS_OWNER', 'BOOLEAN', 'IS_OWNER', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.parties.isOwner', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1ab411b083024b1c884f64bd6895c3fc', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'partyNameName', 'PARTY_NAME_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.parties.partyNameName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c19d3768f4b84c0384d0ebbf7772ab9f', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'partyNameVer', 'PARTY_NAME_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.parties.partyNameVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bded9c4bc00a462987bd476eea176e7c', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'contactUserRef', 'CONTACT_USER_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.parties.contactUserRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2377e6b9b9b14dc3a2a66a7812829edc', 0, 'c5e10b42b3d84271a874adf5f8602d2e', 'contactUserVer', 'CONTACT_USER_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.parties.contactUserVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('2188ed3de5d6401d81641806e45beaec', 0, 'CpmTask', 1, 'CNT_CPM_TASK', 'CNT_CPM_TASK_M', 'CTM_CPM_TASK', 'CPM_TASK', 'cpmTask', 'main', NULL, 'ALL', 'CpmTaskHistory', NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0d1abe7b380f4b61acd95bdd11b10286', 0, '2188ed3de5d6401d81641806e45beaec', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5c7c882c12224eccaf52b03556df2e78', 0, '2188ed3de5d6401d81641806e45beaec', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c26df438f6db4e429fa4ea78852e334e', 0, '2188ed3de5d6401d81641806e45beaec', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4cc39482632f41e09e54a841e7110207', 0, '2188ed3de5d6401d81641806e45beaec', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0ca3ed1b2b8848de89010199a020c2c7', 0, '2188ed3de5d6401d81641806e45beaec', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('303b2b2634dc47c189dd9085ba091ecf', 0, '2188ed3de5d6401d81641806e45beaec', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7925cc0764454e03bb9120fe8b3cb904', 0, '2188ed3de5d6401d81641806e45beaec', 'version', 'VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.version', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1a683eec670f400d870c5a6bfb11e9b3', 0, '2188ed3de5d6401d81641806e45beaec', 'docStatus', 'DOC_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.docStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7a94b72c254a49ffb55f4d77e990c7ad', 0, '2188ed3de5d6401d81641806e45beaec', 'editingStatus', 'EDITING_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.editingStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3e165bc6640e43deb4709256d7d81079', 0, '2188ed3de5d6401d81641806e45beaec', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fc55dff138f048f2b7668f74563b33d0', 0, '2188ed3de5d6401d81641806e45beaec', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('db877fffb52c4d43bec7275980c6c98b', 0, '2188ed3de5d6401d81641806e45beaec', 'updateUser', 'UPDATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.updateUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3562ddd206bc41dbba51055422726e57', 0, '2188ed3de5d6401d81641806e45beaec', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.updateUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8397560b08374f02b02bc5ffd6a8401a', 0, '2188ed3de5d6401d81641806e45beaec', 'integrationSource', 'INTEGRATION_SOURCE', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.integrationSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('045ebeb320454545855d426074d99e67', 0, '2188ed3de5d6401d81641806e45beaec', 'integrationStatus', 'INTEGRATION_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.integrationStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3d42afec6f1043c7917bd381307e2c73', 0, '2188ed3de5d6401d81641806e45beaec', 'integrationNote', 'INTEGRATION_NOTE', 'VARCHAR(1000)', NULL, '2', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.integrationNote', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9713ac6bfd1c4c7fba9e65977976016d', 0, '2188ed3de5d6401d81641806e45beaec', 'isCpmInitialized', 'IS_CPM_INITIALIZED', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.isCpmInitialized', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('af666a9eca4d405783f51f992c40d96c', 0, '2188ed3de5d6401d81641806e45beaec', 'isLatest', 'IS_LATEST', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.isLatest', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a7b9d5358a9443a983a2e23be0fbb68f', 0, '2188ed3de5d6401d81641806e45beaec', 'parentId', 'PARENT_ID', 'VARCHAR(32)', 'CPM_DOC_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2691c33d39174cefaf4ef756be365ca2', 0, '2188ed3de5d6401d81641806e45beaec', 'taskTemplId', 'TASK_TEMPL_ID', 'VARCHAR(32)', 'TASK_TEMPLATE_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.taskTemplId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7bc1f8bf537441dbb0adb56443d91930', 0, '2188ed3de5d6401d81641806e45beaec', 'refDocId', 'REF_DOC_ID', 'VARCHAR(32)', 'REF_DOC_ID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.refDocId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('927cb5671c4d4295b6c09bd9b6bbb525', 0, '2188ed3de5d6401d81641806e45beaec', 'refDocRefNo', 'REF_DOC_REF_NO', 'VARCHAR(100)', 'REF_DOC_REF_NO', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.refDocRefNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('65beb46bb23a4f33b3f91592bff6a8e6', 0, '2188ed3de5d6401d81641806e45beaec', 'refEntityName', 'REF_ENTITY_NAME', 'VARCHAR(100)', 'REF_ENTITY_NAME', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.refEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b00c4075fd044abfa9eebb8c671e6c9c', 0, '2188ed3de5d6401d81641806e45beaec', 'taskId', 'TASK_ID', 'VARCHAR(100)', 'TASK_SID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.taskId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3b5d6fe034984651978a8498433ba2c3', 0, '2188ed3de5d6401d81641806e45beaec', 'taskName', 'TASK_NAME', 'VARCHAR(100)', 'TASK_NAME', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.taskName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('83a5c58543a948d89ba34962bca0cae5', 0, '2188ed3de5d6401d81641806e45beaec', 'taskType', 'TASK_TYPE', 'VARCHAR(400)', 'TASK_TYPE', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.taskType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('db6b6682d64a497b9acf758d5b6ffee4', 0, '2188ed3de5d6401d81641806e45beaec', 'description', 'DESCRIPTION', 'VARCHAR(400)', 'DESCRIPTION', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '26', '0', 'entity.description', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('586f5acdc51047f9a73309d0035f905c', 0, '2188ed3de5d6401d81641806e45beaec', 'planStart', 'PLAN_START', 'DATE', 'PLAN_START_DATE', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '27', '0', 'entity.planStart', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1bb6e700159a4208a21dbf48184cef84', 0, '2188ed3de5d6401d81641806e45beaec', 'planEnd', 'PLAN_END', 'DATE', 'PLAN_END_DATE', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '28', '0', 'entity.planEnd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('43c1dd52b17141e0a199dcdbfe999501', 0, '2188ed3de5d6401d81641806e45beaec', 'actualStart', 'ACTUAL_START', 'DATE', 'ACTUAL_START_DATE', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '29', '0', 'entity.actualStart', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('58f274dd78f74533a3bdc2bac404f843', 0, '2188ed3de5d6401d81641806e45beaec', 'actualEnd', 'ACTUAL_END', 'DATE', 'ACTUAL_END_DATE', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '30', '0', 'entity.actualEnd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('80d212acb0d649fa94476db468f6efe8', 0, '2188ed3de5d6401d81641806e45beaec', 'reasonId', 'REASON_ID', 'VARCHAR(400)', 'REASON_SID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '31', '0', 'entity.reasonId', 'codelist', 'CPM_TASK_REASON', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e96c1b2f291044848654c0ec40839dbd', 0, '2188ed3de5d6401d81641806e45beaec', 'externalDomainId', 'EXTERNAL_DOMAIN_ID', 'VARCHAR(400)', 'EXTERNAL_DOMAIN_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '32', '0', 'entity.externalDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f9ffd71b41084a728dd602ec36c6e978', 0, '2188ed3de5d6401d81641806e45beaec', 'masterRefNo', 'MASTER_REF_NO', 'VARCHAR(400)', 'MASTER_REF_NO', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '33', '0', 'entity.masterRefNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bff32471a4ee4b6d8f0999d92b3329dc', 0, '2188ed3de5d6401d81641806e45beaec', 'reasonDescription', 'REASON_DESCRIPTION', 'VARCHAR(400)', 'REASON_DESC', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '34', '0', 'entity.reasonDescription', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2879608d52674f1f81d020c15d5c5c13', 0, '2188ed3de5d6401d81641806e45beaec', 'revisedPlanEnd', 'REVISED_PLAN_END', 'DATE', 'REVISEd_PLAN_END', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '35', '0', 'entity.revisedPlanEnd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b5a03984791b4de7babe340d1a81854a', 0, '2188ed3de5d6401d81641806e45beaec', 'daysBeforePlanStartDate', 'DAYS_BEFORE_PLAN_START_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '36', '0', 'entity.daysBeforePlanStartDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cbe67583f34f48aba8fbc8d84f68a011', 0, '2188ed3de5d6401d81641806e45beaec', 'daysBeforePlanEndDate', 'DAYS_BEFORE_PLAN_END_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '37', '0', 'entity.daysBeforePlanEndDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c91b73e5e7014b2d9ce80effa42b8301', 0, '2188ed3de5d6401d81641806e45beaec', 'daysBeforeActualStartDate', 'DAYS_BEFORE_ACTUAL_START_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '38', '0', 'entity.daysBeforeActualStartDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7b99a4b5b56b41c49f4bfe32e0213de6', 0, '2188ed3de5d6401d81641806e45beaec', 'daysBeforeActualEndDate', 'DAYS_BEFORE_ACTUAL_END_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '39', '0', 'entity.daysBeforeActualEndDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('65534941fd6c43e8aeffed07a13aa0a8', 0, '2188ed3de5d6401d81641806e45beaec', 'daysAfterPlanStartDate', 'DAYS_AFTER_PLAN_START_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '40', '0', 'entity.daysAfterPlanStartDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('86c556f777f44b4a8902d725cac9c72e', 0, '2188ed3de5d6401d81641806e45beaec', 'daysAfterPlanEndDate', 'DAYS_AFTER_PLAN_END_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '41', '0', 'entity.daysAfterPlanEndDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bdf8c182e3ea4835809f06a197bdf348', 0, '2188ed3de5d6401d81641806e45beaec', 'daysAfterActualStartDate', 'DAYS_AFTER_ACTUAL_START_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '42', '0', 'entity.daysAfterActualStartDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f1215077878e41b6a65a6629fc55aa62', 0, '2188ed3de5d6401d81641806e45beaec', 'daysAfterActualEndDate', 'DAYS_AFTER_ACTUAL_END_DATE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '43', '0', 'entity.daysAfterActualEndDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0d139313a71c43c9ad4aca6184397cf1', 0, '2188ed3de5d6401d81641806e45beaec', 'custText1LineNo', 'CUST_TEXT1_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT1_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '44', '0', 'entity.custText1LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1f76eff5dafb433c8ef5f8d1edfe0b38', 0, '2188ed3de5d6401d81641806e45beaec', 'custText1Label', 'CUST_TEXT1_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT1_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '45', '0', 'entity.custText1Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7fc580fed22b48588bb5851e6df4f536', 0, '2188ed3de5d6401d81641806e45beaec', 'custText1Enabled', 'CUST_TEXT1_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT1_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '46', '0', 'entity.custText1Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e632cea28d1543b2a8abaeffc7cc2942', 0, '2188ed3de5d6401d81641806e45beaec', 'custText1Mandatory', 'CUST_TEXT1_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT1_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '47', '0', 'entity.custText1Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dd8e4724b1894e64a51c83cb458e7593', 0, '2188ed3de5d6401d81641806e45beaec', 'custText2LineNo', 'CUST_TEXT2_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT2_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '48', '0', 'entity.custText2LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d5c1c99c6610446a809ff870b1565b6a', 0, '2188ed3de5d6401d81641806e45beaec', 'custText2Label', 'CUST_TEXT2_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT2_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '49', '0', 'entity.custText2Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('39c858a0086b48a1be7c02e44077d3bb', 0, '2188ed3de5d6401d81641806e45beaec', 'custText2Enabled', 'CUST_TEXT2_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT2_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '50', '0', 'entity.custText2Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('556ae6604e064b959a2b191145ebee78', 0, '2188ed3de5d6401d81641806e45beaec', 'custText2Mandatory', 'CUST_TEXT2_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT2_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '51', '0', 'entity.custText2Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('88ff30384c7c4350a1c33d461f34a554', 0, '2188ed3de5d6401d81641806e45beaec', 'custText3LineNo', 'CUST_TEXT3_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT3_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '52', '0', 'entity.custText3LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6316763b2d67455a8f2a03462df4fe15', 0, '2188ed3de5d6401d81641806e45beaec', 'custText3Label', 'CUST_TEXT3_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT3_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '53', '0', 'entity.custText3Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('487a61d505a946fab10d8acdfed7cef7', 0, '2188ed3de5d6401d81641806e45beaec', 'custText3Enabled', 'CUST_TEXT3_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT3_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '54', '0', 'entity.custText3Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e5a18f078d1744e9b3d5ed23a0dc3732', 0, '2188ed3de5d6401d81641806e45beaec', 'custText3Mandatory', 'CUST_TEXT3_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT3_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '55', '0', 'entity.custText3Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a20476d42f1d492c85a0ca16f47b21f2', 0, '2188ed3de5d6401d81641806e45beaec', 'custText4LineNo', 'CUST_TEXT4_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT4_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '56', '0', 'entity.custText4LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0fc6236b60ff465da62d9df381fdde58', 0, '2188ed3de5d6401d81641806e45beaec', 'custText4Label', 'CUST_TEXT4_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT4_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '57', '0', 'entity.custText4Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('76d3bbe186794abcaed66afe02ae261e', 0, '2188ed3de5d6401d81641806e45beaec', 'custText4Enabled', 'CUST_TEXT4_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT4_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '58', '0', 'entity.custText4Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dee6d14ffe8947cfaec4c6990d476410', 0, '2188ed3de5d6401d81641806e45beaec', 'custText4Mandatory', 'CUST_TEXT4_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT4_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '59', '0', 'entity.custText4Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bdfbac9415404de8ba7c83f35e1ca2e6', 0, '2188ed3de5d6401d81641806e45beaec', 'custText5LineNo', 'CUST_TEXT5_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT5_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '60', '0', 'entity.custText5LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1bf389a7bf4849599204b207856d299d', 0, '2188ed3de5d6401d81641806e45beaec', 'custText5Label', 'CUST_TEXT5_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT5_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '61', '0', 'entity.custText5Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2bcb6ed987494d3a9d615b7394692cf5', 0, '2188ed3de5d6401d81641806e45beaec', 'custText5Enabled', 'CUST_TEXT5_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT5_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '62', '0', 'entity.custText5Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f8b59da63761438a8f18b804030b8a35', 0, '2188ed3de5d6401d81641806e45beaec', 'custText5Mandatory', 'CUST_TEXT5_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT5_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '63', '0', 'entity.custText5Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e7a2d1d84a7444e0952bf134f3103010', 0, '2188ed3de5d6401d81641806e45beaec', 'custText6LineNo', 'CUST_TEXT6_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT6_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '64', '0', 'entity.custText6LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d8af26aa2d884210bfc024515c1ec66e', 0, '2188ed3de5d6401d81641806e45beaec', 'custText6Label', 'CUST_TEXT6_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT6_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '65', '0', 'entity.custText6Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2de751683a744471b7f28507f4b0fbe2', 0, '2188ed3de5d6401d81641806e45beaec', 'custText6Enabled', 'CUST_TEXT6_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT6_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '66', '0', 'entity.custText6Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c5c34e32ccb84cd38b90227ad74a4a16', 0, '2188ed3de5d6401d81641806e45beaec', 'custText6Mandatory', 'CUST_TEXT6_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT6_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '67', '0', 'entity.custText6Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f72901dd5546438bbbfeaf91ca95e740', 0, '2188ed3de5d6401d81641806e45beaec', 'custText7LineNo', 'CUST_TEXT7_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_TEXT7_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '68', '0', 'entity.custText7LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5e280cbe07194fddb7e73915e482f925', 0, '2188ed3de5d6401d81641806e45beaec', 'custText7Label', 'CUST_TEXT7_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_TEXT7_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '69', '0', 'entity.custText7Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bfd9962d063d421d902bdfcc09cad6b1', 0, '2188ed3de5d6401d81641806e45beaec', 'custText7Enabled', 'CUST_TEXT7_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_TEXT7_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '70', '0', 'entity.custText7Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('facd27a02f1945cd94d62a771daa6bc8', 0, '2188ed3de5d6401d81641806e45beaec', 'custText7Mandatory', 'CUST_TEXT7_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_TEXT7_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '71', '0', 'entity.custText7Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('60b1d6f1432e487c9bdf31a9dcf2a3ca', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate1LineNo', 'CUST_DATE1_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE1_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '72', '0', 'entity.custDate1LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8c185d093cf148219221f5e77d5a7dde', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate1Label', 'CUST_DATE1_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE1_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '73', '0', 'entity.custDate1Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b9224a227e7d4576a728ff56bb6f944d', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate1Enabled', 'CUST_DATE1_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE1_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '74', '0', 'entity.custDate1Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1eb0e42e62ca4e6eac9bc911a44c2ac2', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate1Mandatory', 'CUST_DATE1_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE1_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '75', '0', 'entity.custDate1Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a73704d8cd6e4165b84dc245cee9bc85', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate2LineNo', 'CUST_DATE2_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE2_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '76', '0', 'entity.custDate2LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a734bf7d78aa4d5b838e6c99f7947946', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate2Label', 'CUST_DATE2_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE2_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '77', '0', 'entity.custDate2Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d9fe001e92274a18a5e210a2818d0ab5', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate2Enabled', 'CUST_DATE2_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE2_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '78', '0', 'entity.custDate2Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2e1c9da33ca346ad813d9a3b0a758ff2', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate2Mandatory', 'CUST_DATE2_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE2_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '79', '0', 'entity.custDate2Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('85ff8b26af354adea1022da2128a13b5', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate3LineNo', 'CUST_DATE3_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE3_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '80', '0', 'entity.custDate3LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('eb8856e688d54edcb43ded13a4c9fd04', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate3Label', 'CUST_DATE3_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE3_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '81', '0', 'entity.custDate3Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3e4dd2614b544bc48158b92bd5399dd6', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate3Enabled', 'CUST_DATE3_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE3_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '82', '0', 'entity.custDate3Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0dfba187662441e48588c45139512d0f', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate3Mandatory', 'CUST_DATE3_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE3_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '83', '0', 'entity.custDate3Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e110590dce674d37bccb48f63dac4f72', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate4LineNo', 'CUST_DATE4_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE4_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '84', '0', 'entity.custDate4LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b8781ce464154048bf7f8587cda47928', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate4Label', 'CUST_DATE4_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE4_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '85', '0', 'entity.custDate4Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('93e0f48db7d84b05a957235b905d2427', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate4Enabled', 'CUST_DATE4_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE4_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '86', '0', 'entity.custDate4Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2bbeffd4b7414166a9797a5a162c03a0', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate4Mandatory', 'CUST_DATE4_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE4_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '87', '0', 'entity.custDate4Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9626ccf5c5e04235a2bd957c8cfbf2f2', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate5LineNo', 'CUST_DATE5_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE5_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '88', '0', 'entity.custDate5LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e996ef6012bd49cd90951ea74443a99e', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate5Label', 'CUST_DATE5_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE5_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '89', '0', 'entity.custDate5Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('42eea88b5ad04e87bebbdd029f6c49f9', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate5Enabled', 'CUST_DATE5_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE5_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '90', '0', 'entity.custDate5Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8d5e9bc2a76f4f4ebcfad47ab0df7ed3', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate5Mandatory', 'CUST_DATE5_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE5_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '91', '0', 'entity.custDate5Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fec25d481c20499d961d1699f68a682a', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate6LineNo', 'CUST_DATE6_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE6_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '92', '0', 'entity.custDate6LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ca54a88378a144efb3f6ba25276b5c71', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate6Label', 'CUST_DATE6_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE6_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '93', '0', 'entity.custDate6Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('970d222e21fe475196dd53c9d10db068', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate6Enabled', 'CUST_DATE6_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE6_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '94', '0', 'entity.custDate6Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a6c30c459c254d09a545a0fcb4ba9cb0', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate6Mandatory', 'CUST_DATE6_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE6_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '95', '0', 'entity.custDate6Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ceedfc6287a24aa7bf9221954180ff86', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate7LineNo', 'CUST_DATE7_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_DATE7_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '96', '0', 'entity.custDate7LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3d634eb748ab47c9bcff2906ce7485d0', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate7Label', 'CUST_DATE7_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_DATE7_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '97', '0', 'entity.custDate7Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c136f1d3dd3d4aaf809c8e8eddb2962c', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate7Enabled', 'CUST_DATE7_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_DATE7_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '98', '0', 'entity.custDate7Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8f33fbec796f4fe7876bb2d661494d49', 0, '2188ed3de5d6401d81641806e45beaec', 'custDate7Mandatory', 'CUST_DATE7_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_DATE7_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '99', '0', 'entity.custDate7Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3ea12d47c16b430c8b228d13e4a113a4', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber1LineNo', 'CUST_NUMBER1_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_NUM1_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '100', '0', 'entity.custNumber1LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f84d458f3bb74a119b3eff885b1121bc', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber1Label', 'CUST_NUMBER1_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_NUM1_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '101', '0', 'entity.custNumber1Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('520568dd459e4827adfc53cbd53333dd', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber1Enabled', 'CUST_NUMBER1_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_NUM1_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '102', '0', 'entity.custNumber1Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0fbc6cecdf9f463096178e84b7de0c3f', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber1Mandatory', 'CUST_NUMBER1_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_NUM1_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '103', '0', 'entity.custNumber1Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('965b602cc77e4a34b9ada96f66db708e', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber2LineNo', 'CUST_NUMBER2_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_NUM2_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '104', '0', 'entity.custNumber2LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d668153c8b564c1382dd6878320b1c82', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber2Label', 'CUST_NUMBER2_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_NUM2_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '105', '0', 'entity.custNumber2Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('86d60e6e6ff54d2c92ecc155bbe989e6', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber2Enabled', 'CUST_NUMBER2_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_NUM2_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '106', '0', 'entity.custNumber2Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('61af5a29a48b45998ea5965a0968b8da', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber2Mandatory', 'CUST_NUMBER2_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_NUM2_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '107', '0', 'entity.custNumber2Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2cd23390b6fc4b1daa5597fcd8d1268f', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber3LineNo', 'CUST_NUMBER3_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_NUM3_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '108', '0', 'entity.custNumber3LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ff471c01b14b4e9dae6a0cdba9cba141', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber3Label', 'CUST_NUMBER3_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_NUM3_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '109', '0', 'entity.custNumber3Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('65515c4e835c4f9bb0458999c1c3d5d1', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber3Enabled', 'CUST_NUMBER3_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_NUM3_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '110', '0', 'entity.custNumber3Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a0f4ad598abf4777ad802be57cf5f62f', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber3Mandatory', 'CUST_NUMBER3_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_NUM3_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '111', '0', 'entity.custNumber3Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8083467dbfb944edb189075225bdd2cc', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber4LineNo', 'CUST_NUMBER4_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_NUM4_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '112', '0', 'entity.custNumber4LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cb25df54f5f74b8d9d9e4943de0c1270', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber4Label', 'CUST_NUMBER4_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_NUM4_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '113', '0', 'entity.custNumber4Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('08c098e681d845aeac211291438f244a', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber4Enabled', 'CUST_NUMBER4_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_NUM4_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '114', '0', 'entity.custNumber4Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ed935228dffb4379ae8f65695ed500aa', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber4Mandatory', 'CUST_NUMBER4_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_NUM4_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '115', '0', 'entity.custNumber4Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('73ee23c0a47b4af8a4c8ef4ff76cf188', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber5LineNo', 'CUST_NUMBER5_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_NUM5_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '116', '0', 'entity.custNumber5LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f52263e551d84b0baab32a40db5ead2a', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber5Label', 'CUST_NUMBER5_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_NUM5_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '117', '0', 'entity.custNumber5Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('99de02d16eec4bad9ff66fb718589375', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber5Enabled', 'CUST_NUMBER5_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_NUM5_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '118', '0', 'entity.custNumber5Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b63b7fecc1844c0cb1f4a63873bcea27', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber5Mandatory', 'CUST_NUMBER5_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_NUM5_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '119', '0', 'entity.custNumber5Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('99ab81c99ac14a85b5c17cfde64ac157', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber6LineNo', 'CUST_NUMBER6_LINE_NO', 'NUMERIC(20, 0)', 'CUSTOM_FIELD_NUM6_LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '120', '0', 'entity.custNumber6LineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('47c422e6f4a74d7ea86148beecac1b2c', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber6Label', 'CUST_NUMBER6_LABEL', 'VARCHAR(1000)', 'CUSTOM_FIELD_NUM6_LABEL', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '121', '0', 'entity.custNumber6Label', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2e05c135b366429a9e910d908544d892', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber6Enabled', 'CUST_NUMBER6_ENABLED', 'BOOLEAN', 'CUSTOM_FIELD_NUM6_ENABLED', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '122', '0', 'entity.custNumber6Enabled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e7a1bbeb4d794e41bbc0ac4d3f3006b9', 0, '2188ed3de5d6401d81641806e45beaec', 'custNumber6Mandatory', 'CUST_NUMBER6_MANDATORY', 'BOOLEAN', 'CUSTOM_FIELD_NUM6_MANDATORY', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '123', '0', 'entity.custNumber6Mandatory', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('973a82a0384942579c0d9bd87c567c27', 0, '2188ed3de5d6401d81641806e45beaec', 'createdBy', 'CREATED_BY', 'VARCHAR(100)', 'CREATED_BY', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '124', '0', 'entity.createdBy', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('46ba1666dfe24ad88a5f8e2237de141f', 0, '2188ed3de5d6401d81641806e45beaec', 'updatedBy', 'UPDATED_BY', 'VARCHAR(100)', 'UPDATED_BY', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '125', '0', 'entity.updatedBy', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4e656834728441f091fed56d08e615aa', 0, '2188ed3de5d6401d81641806e45beaec', 'sequence', 'SEQUENCE', 'NUMERIC(20, 0)', 'SEQUENCE', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '126', '0', 'entity.sequence', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('66359f38c5fc493ba1a48c5bb173c11a', 0, '2188ed3de5d6401d81641806e45beaec', 'notificationSentOn', 'NOTIFICATION_SENT_ON', 'DATE', NULL, '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '127', '0', 'entity.notificationSentOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('162cfa430966472b84fc57b37646c944', 0, '2188ed3de5d6401d81641806e45beaec', 'colorCode', 'COLOR_CODE', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '128', '0', 'entity.colorCode', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4498f11f7a23436f938023f965106a67', 0, '2188ed3de5d6401d81641806e45beaec', 'fieldId', 'FIELD_ID', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '129', '0', 'entity.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e9378fd5cf8a43dfbc59cfd8cd1cd119', 0, '2188ed3de5d6401d81641806e45beaec', 'fieldValue', 'FIELD_VALUE', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '130', '0', 'entity.fieldValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('83ccc84ce59e42a2bb96f0d267a9308c', 0, '2188ed3de5d6401d81641806e45beaec', 'refEntity', 'REF_ENTITY', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '131', '0', 'entity.refEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e7ebc64a4289423791be8c9aa0ae31db', 0, '2188ed3de5d6401d81641806e45beaec', 'refFieldId', 'REF_FIELD_ID', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '132', '0', 'entity.refFieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c114a54fbb424572a9ca147e164915e8', 0, '2188ed3de5d6401d81641806e45beaec', 'percentage', 'PERCENTAGE', 'NUMERIC(25, 5)', 'PERCENTAGE', '0', 'decimal-percentage', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 20, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '133', '0', 'entity.percentage', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d72fb292d25a421da0fed870163511ab', 0, '2188ed3de5d6401d81641806e45beaec', 'quantity', 'QUANTITY', 'NUMERIC(20, 0)', 'QUANTITY', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '134', '0', 'entity.quantity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b8a4d886e01e46a4b1081795814460d7', 0, '2188ed3de5d6401d81641806e45beaec', 'attachment', 'ATTACHMENT_ID', 'VARCHAR(32)', 'ATTACHMENT', '0', 'entity', 'Attachment', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '135', '0', 'entity.attachment', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('de36ef8c37ef411993f549b6cd2c3b60', 0, '2188ed3de5d6401d81641806e45beaec', 'duration', 'DURATION', 'NUMERIC(20, 0)', 'DURATION', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '136', '0', 'entity.duration', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3718ecfc7d28434f986d34740ab82dfa', 0, '2188ed3de5d6401d81641806e45beaec', 'plannedDuration', 'PLANNED_DURATION', 'NUMERIC(20, 0)', 'PLANNED_DURATION', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '137', '0', 'entity.plannedDuration', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f76804384fd841598d7f0e8bcb334c16', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskAlerts', 'CPM_TASK_ALERTS', NULL, NULL, '0', 'collection', 'CpmTaskAlert', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '138', '0', 'entity.cpmTaskAlerts', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4ef825f73c63458580628e1fcb5fbbee', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskActivities', 'CPM_TASK_ACTIVITIES', NULL, NULL, '0', 'collection', 'CpmTaskActivity', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '139', '0', 'entity.cpmTaskActivities', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a71caaa04f40436b8a26216a18035229', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmImages', 'CPM_IMAGES', NULL, NULL, '0', 'collection', 'CpmImage', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '140', '0', 'entity.cpmImages', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('18ab3866572b4418a1c11d42f2cae85d', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmAttachment', 'CPM_ATTACHMENT', NULL, NULL, '0', 'collection', 'CpmAttachment', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '141', '0', 'entity.cpmAttachment', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('93bbf73e46eb484b92d2f1e081086329', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskAssignees', 'CPM_TASK_ASSIGNEES', NULL, NULL, '0', 'selection', 'CpmTaskSelection', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '142', '0', 'entity.cpmTaskAssignees', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3e101f01ce8c4cccb15fd2ec86c7f679', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskNotifications', 'CPM_TASK_NOTIFICATIONS', NULL, NULL, '0', 'collection', 'CpmAllNotification', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '143', '0', 'entity.cpmTaskNotifications', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a8f61f73ad0a4f9d95b8e90d44fc995f', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskAdvSecuritys', 'CPM_TASK_ADV_SECURITYS', NULL, NULL, '0', 'collection', 'CpmTaskAdvSecurity', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '144', '0', 'entity.cpmTaskAdvSecuritys', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('deac2af7bee34b40b3c0122aa6d0ffe2', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskPredecessors', 'CPM_TASK_PREDECESSORS', NULL, NULL, '0', 'collection', 'CpmTaskPredecessor', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '145', '0', 'entity.cpmTaskPredecessors', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('72e97da3ed5840c4a4458e84de119b12', 0, '2188ed3de5d6401d81641806e45beaec', 'partyTemplateRef', 'PARTY_TEMPLATE_REF', 'VARCHAR(400)', NULL, '3', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '146', '0', 'entity.partyTemplateRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8af9fb8d3ea8426b92d1e5dc7e2f9f64', 0, '2188ed3de5d6401d81641806e45beaec', 'partyTemplateVer', 'PARTY_TEMPLATE_VER', 'NUMERIC(20, 0)', NULL, '3', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '147', '0', 'entity.partyTemplateVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('38ef3fc0cf0d4614ac071b868002fa4d', 0, '2188ed3de5d6401d81641806e45beaec', 'partyTemplateName', 'PARTY_TEMPLATE_NAME', 'VARCHAR(400)', NULL, '3', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '148', '0', 'entity.partyTemplateName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('056fcfe6b8f84a3190bb520682cc1776', 0, '2188ed3de5d6401d81641806e45beaec', 'partyName1', 'PARTY_NAME1', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '149', '0', 'entity.partyName1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ad6423e1258c423a95a4ae5d5c2b9cf9', 0, '2188ed3de5d6401d81641806e45beaec', 'partyName2', 'PARTY_NAME2', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '150', '0', 'entity.partyName2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a0cddfca90114c9ca9f144e57e83072d', 0, '2188ed3de5d6401d81641806e45beaec', 'partyName3', 'PARTY_NAME3', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '151', '0', 'entity.partyName3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('26d27186daa347cb9a02c79775f1508e', 0, '2188ed3de5d6401d81641806e45beaec', 'partyName4', 'PARTY_NAME4', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '152', '0', 'entity.partyName4', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5df403e3674447cbb79e2f9f5ed90623', 0, '2188ed3de5d6401d81641806e45beaec', 'partyName5', 'PARTY_NAME5', 'VARCHAR(1000)', NULL, '3', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '153', '0', 'entity.partyName5', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4909d51edb004700b530ccc90f1ef4af', 0, '2188ed3de5d6401d81641806e45beaec', 'parties', 'PARTIES', NULL, NULL, '2', 'collection', 'CpmTaskParty', 'docId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '154', '0', 'entity.parties', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('87ffa1898376425bb96559629a2d6235', 0, '2188ed3de5d6401d81641806e45beaec', 'status', 'STATUS', 'VARCHAR(100)', 'STATUS', '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '155', '0', 'entity.status', 'codelist', 'CPM_TASK_STATUS', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('84b306533f54476d95568bcf00a30c08', 0, '2188ed3de5d6401d81641806e45beaec', 'createdOn', 'CREATED_ON', 'TIMESTAMP', 'CREATED_ON', '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '156', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('25475e6d7e484b33a68ecbbd8520bfff', 0, '2188ed3de5d6401d81641806e45beaec', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', 'UPDATED_ON', '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '157', '0', 'entity.updatedOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ac8aa6dfb3a24e5ebd236394806256a5', 0, '2188ed3de5d6401d81641806e45beaec', 'reasonIdName', 'REASON_ID_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '158', '0', 'entity.reasonIdName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6975ff15d72c415db26364e7a00ff8e7', 0, '2188ed3de5d6401d81641806e45beaec', 'reasonIdVer', 'REASON_ID_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '159', '0', 'entity.reasonIdVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('34520a727fba493cba56dcd241e97b10', 0, '2188ed3de5d6401d81641806e45beaec', 'attachmentRef', 'ATTACHMENT_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '160', '0', 'entity.attachmentRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('16b137556e004731b3e0ca63cc0d1e42', 0, '2188ed3de5d6401d81641806e45beaec', 'attachmentVer', 'ATTACHMENT_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '161', '0', 'entity.attachmentVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('34505701441c457184d5e301edbcf604', 0, '2188ed3de5d6401d81641806e45beaec', 'attachmentRef2', 'ATTACHMENT_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '162', '0', 'entity.attachmentRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('51ae06fb15484ab69c4d02aff36dcd26', 0, '2188ed3de5d6401d81641806e45beaec', 'attachmentDuid', 'ATTACHMENT_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '163', '0', 'entity.attachmentDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('70e7bb06895649a4ac1b68743e306536', 0, '2188ed3de5d6401d81641806e45beaec', 'cpmTaskAssigneesValue', 'CPM_TASK_ASSIGNEES_VALUE', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '164', '0', 'entity.cpmTaskAssigneesValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bc0d5d6f6ab14e0b81cf936ee162e0cd', 0, '2188ed3de5d6401d81641806e45beaec', 'statusName', 'STATUS_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '165', '0', 'entity.statusName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7069afbaf9254c828ef88ca9f747a82b', 0, '2188ed3de5d6401d81641806e45beaec', 'statusVer', 'STATUS_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '166', '0', 'entity.statusVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('9ce8f364658d47f281e496ed55ad2e55', 0, 'CpmTaskSelection', 1, 'CNT_CPM_TASK_SLN', NULL, 'CTM_CPM_TASK', 'N/A', 'cpmTask', NULL, NULL, NULL, NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1e96b5f5d80b40f6ad1bf5db0cec394d', 0, '9ce8f364658d47f281e496ed55ad2e55', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmTaskAssignees.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('abb0880b55ce4f138bd97d5bb8b88c58', 0, '9ce8f364658d47f281e496ed55ad2e55', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmTaskAssignees.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('018d450e128d40ec881ad1892124751f', 0, '9ce8f364658d47f281e496ed55ad2e55', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmTaskAssignees.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e0409fa931564144a015709ca0827a7d', 0, '9ce8f364658d47f281e496ed55ad2e55', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmTaskAssignees.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cc1c4e964fd9485b96af5ecac97ab0a3', 0, '9ce8f364658d47f281e496ed55ad2e55', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmTaskAssignees.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7c545702eb0d4896bfe6ee11b447b01c', 0, '9ce8f364658d47f281e496ed55ad2e55', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmTaskAssignees.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('84fbe07f7c044affb672d4d717562093', 0, '9ce8f364658d47f281e496ed55ad2e55', 'version', 'VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmTaskAssignees.version', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6eb22005f8e74370831a0337e3c11dd6', 0, '9ce8f364658d47f281e496ed55ad2e55', 'status', 'STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmTaskAssignees.status', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cf6d6283dd404385b188dcc03992d81b', 0, '9ce8f364658d47f281e496ed55ad2e55', 'docStatus', 'DOC_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmTaskAssignees.docStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a7a1dab370dc4859b9d8c623d6e0a4be', 0, '9ce8f364658d47f281e496ed55ad2e55', 'editingStatus', 'EDITING_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmTaskAssignees.editingStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('72e11298300f4fe288b5b31fc60c2ed3', 0, '9ce8f364658d47f281e496ed55ad2e55', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmTaskAssignees.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a4996a75df454f3c81947e7cd683bd77', 0, '9ce8f364658d47f281e496ed55ad2e55', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmTaskAssignees.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('098df2aff28b47a48be040f97a8d4fcc', 0, '9ce8f364658d47f281e496ed55ad2e55', 'updateUser', 'UPDATE_USER', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmTaskAssignees.updateUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4e1353cb1b63463980d1bee4fa5b9fbf', 0, '9ce8f364658d47f281e496ed55ad2e55', 'updateUserName', 'UPDATE_USER_NAME', 'VARCHAR(100)', NULL, '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.cpmTaskAssignees.updateUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9103e9caccc64c2a9f789f7f2b00bdfe', 0, '9ce8f364658d47f281e496ed55ad2e55', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.cpmTaskAssignees.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dac34bcf9f194f1cb1db4a8e065fe38d', 0, '9ce8f364658d47f281e496ed55ad2e55', 'updatedOn', 'UPDATED_ON', 'TIMESTAMP', NULL, '2', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.cpmTaskAssignees.updatedOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3f2882cab9ac4d289a8315b60c25e603', 0, '9ce8f364658d47f281e496ed55ad2e55', 'integrationSource', 'INTEGRATION_SOURCE', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.cpmTaskAssignees.integrationSource', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b05cf386aa5545148c804b94b546d2d3', 0, '9ce8f364658d47f281e496ed55ad2e55', 'integrationStatus', 'INTEGRATION_STATUS', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.cpmTaskAssignees.integrationStatus', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4a33a20b00474eac99f62fd297a0583e', 0, '9ce8f364658d47f281e496ed55ad2e55', 'integrationNote', 'INTEGRATION_NOTE', 'VARCHAR(1000)', NULL, '2', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.cpmTaskAssignees.integrationNote', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1f92d29149b24e51b21299c037346f91', 0, '9ce8f364658d47f281e496ed55ad2e55', 'isCpmInitialized', 'IS_CPM_INITIALIZED', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.cpmTaskAssignees.isCpmInitialized', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8b67a7d65f0c42a39b78c1299ba212a7', 0, '9ce8f364658d47f281e496ed55ad2e55', 'isLatest', 'IS_LATEST', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.cpmTaskAssignees.isLatest', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fb1a6522495f43cfb6fe72da33dfb9f7', 0, '9ce8f364658d47f281e496ed55ad2e55', 'parentId', 'PARENT_ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.cpmTaskAssignees.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c9bf9ca7f98c45d88731feb9f07d7225', 0, '9ce8f364658d47f281e496ed55ad2e55', 'parentEntity', 'PARENT_ENTITY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.cpmTaskAssignees.parentEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('999eaddd7d7b455d80b68a849fc40dfc', 0, '9ce8f364658d47f281e496ed55ad2e55', 'fieldId', 'FIELD_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.cpmTaskAssignees.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('56557b88a7544ae0927b5b11f6a44e0e', 0, '9ce8f364658d47f281e496ed55ad2e55', 'ref', 'REF_ID', 'VARCHAR(32)', NULL, '0', 'entity-dynamic', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.cpmTaskAssignees.ref', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('35aa9ccbcba64792894ceeceadf96856', 0, '9ce8f364658d47f281e496ed55ad2e55', 'refEntity', 'REF_ENTITY', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.cpmTaskAssignees.refEntity', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('edfeff5d0e88465db4e789ddf51ab7b7', 0, '9ce8f364658d47f281e496ed55ad2e55', 'displayValue', 'DISPLAY_VALUE', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '26', '0', 'entity.cpmTaskAssignees.displayValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('843da6e82b004e1db1b6d8ffb294e2b4', 0, '9ce8f364658d47f281e496ed55ad2e55', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '27', '0', 'entity.cpmTaskAssignees.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1d5a518505294924afcac775ed4aa82a', 0, '9ce8f364658d47f281e496ed55ad2e55', 'refRef', 'REF_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '28', '0', 'entity.cpmTaskAssignees.refRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bb03a8f600884fc8bac7126996c0be16', 0, '9ce8f364658d47f281e496ed55ad2e55', 'refVer', 'REF_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '29', '0', 'entity.cpmTaskAssignees.refVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5396f80b70a3483a8314ab565a2e639b', 0, '9ce8f364658d47f281e496ed55ad2e55', 'refRef2', 'REF_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '30', '0', 'entity.cpmTaskAssignees.refRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0ed81841670a4e0483d88790898851b6', 0, '9ce8f364658d47f281e496ed55ad2e55', 'refDuid', 'REF_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '31', '0', 'entity.cpmTaskAssignees.refDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('1ee34de7616f45aea67dbaadd7066cdf', 0, 'CpmTaskAlert', 1, 'CNT_CPM_TASK_ALERT', NULL, 'CTM_CPM_TASK', 'CPM_TASK_ALERT', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('20144eb3964049ba8ee1109978abe027', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmTaskAlerts.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d2bd615f67bb4646a2af791dbcc2f7fd', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmTaskAlerts.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2ca7ebd45a2c4c1e87467227dafab036', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmTaskAlerts.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7711d92358184cf5ac8ba23db6c4bff1', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmTaskAlerts.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('30c9832872824ca1ad5fc5a2a8160fe4', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmTaskAlerts.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b238f3771c55435b83282c36114ba20c', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmTaskAlerts.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c6a8b1253daf4af5ad37d7dcba230e67', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmTaskAlerts.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e1cab273b1574e628cd10cc408930727', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmTaskAlerts.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('7d2ae2c964fb4479b2f45f626b27b235', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmTaskAlerts.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b0b8fab76fff42d2bc9e7361b7331bb5', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'parentId', 'PARENT_ID', 'VARCHAR(32)', 'CPM_TASK_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmTaskAlerts.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c114a6750c2944c28f679b80c9e4ec13', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'lineNo', 'LINE_NO', 'NUMERIC(20, 0)', 'LINE_NO', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmTaskAlerts.lineNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('127b2af8550d43e19bfb7fd3f71cbe54', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'alertType', 'ALERT_TYPE', 'NUMERIC(20, 0)', 'ALERT_TYPE', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmTaskAlerts.alertType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('09161b34a29f4718887d45b4d5cecaf7', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'conditionId', 'CONDITION_ID', 'VARCHAR(32)', 'CONDITION_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmTaskAlerts.conditionId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('caf6264ef15d4e9297ea700251c826d0', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'frequenceType', 'FREQUENCE_TYPE', 'NUMERIC(20, 0)', 'FREQUENCE_TYPE', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.cpmTaskAlerts.frequenceType', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('05478b415e074b76aaf4b7b548a4f616', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'frequence', 'FREQUENCE', 'NUMERIC(20, 0)', 'FREQUENCE', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.cpmTaskAlerts.frequence', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e9bb4963f3d549b1b1699a555720a753', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'cpmTaskAlertTos', 'CPM_TASK_ALERT_TOS', NULL, NULL, '0', 'selection', 'CpmTaskSelection', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.cpmTaskAlerts.cpmTaskAlertTos', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c5485dc51ebc4cdcb7acddd4b350e115', 0, '1ee34de7616f45aea67dbaadd7066cdf', 'cpmTaskAlertTosValue', 'CPM_TASK_ALERT_TOS_VALUE', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.cpmTaskAlerts.cpmTaskAlertTosValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('c82482e75a7f40b08186e78a3ebbf027', 0, 'CpmTaskAdvSecurity', 1, 'CNT_CPM_Task_Adv_Security', NULL, 'CTM_CPM_TASK', 'N/A', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8d5baeb9c776473baaea84479a95624b', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmTaskAdvSecuritys.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e9ffc361d905432e9cbc7a84c3edcca4', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmTaskAdvSecuritys.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('aa9def4e70d54eda85f111e037573a76', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmTaskAdvSecuritys.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d18bc4f681f24cfcbc35e73f003080eb', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmTaskAdvSecuritys.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2f10578d6f6a4ab297d1e5f4abbbb764', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmTaskAdvSecuritys.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8968145a91074cad84504f540115bbb3', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmTaskAdvSecuritys.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c781dcb9475146f8b2c14f709d0b736e', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmTaskAdvSecuritys.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dfb47730fdaa424daf39bc522c52e680', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmTaskAdvSecuritys.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('dee56f919dba413e8a6cb8d171fd649e', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmTaskAdvSecuritys.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3beec166f1994138b048c359624704b9', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'parentId', 'PARENT_ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmTaskAdvSecuritys.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5d2b5cfa9d1d42dcaa2a9f8ae4e9e4b2', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'accessRight', 'ACCESS_RIGHT', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmTaskAdvSecuritys.accessRight', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d4d5f0d232cb4a91a9513713c05a28bc', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'assigneeGroup', 'ASSIGNEE_GROUP', NULL, NULL, '0', 'selection', 'CpmTaskSelection', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmTaskAdvSecuritys.assigneeGroup', 'selection-entity', 'Role', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5ce21bafe379489a9a4a60f60143ea1e', 0, 'c82482e75a7f40b08186e78a3ebbf027', 'assigneeGroupValue', 'ASSIGNEE_GROUP_VALUE', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmTaskAdvSecuritys.assigneeGroupValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('78cee82e09a34fbf8dd80493951bc7b7', 0, 'CpmDocOwner', 1, 'CNT_CPM_DOC_OWNER', NULL, 'CTM_CPM_TASK', 'CNT_CPM_DOC_OWNER', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b1f4dc8b71ae46df8a168c79aa8823e6', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e30acafc4bd3456286ec9beeb4ff3182', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2c360d6e9d8841129f4e09ca1fafc0dd', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('802d177a4444444a8e99002444110d77', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('99709e01005b455787df30313da61a07', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('eabcde0ec273450db833f8a239180cd2', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a5bb1992810c49899b512a6cb03223b8', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('33711773ec264f469276a2013890fa6f', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9800e73237834d5dabb1364d7121907c', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e7b0184225054ab3a72a5047581551b1', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'ownerDomainId', 'OWNER_DOMAIN_ID', 'VARCHAR(400)', 'OWNER_DOMAIN_ID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.ownerDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d73d12a862fd4647a6cf9ce513f7e823', 0, '78cee82e09a34fbf8dd80493951bc7b7', 'cpmDocId', 'CPM_DOC_ID', 'VARCHAR(32)', 'CPM_DOC_ID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmDocId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('9abe7bd3bf27434f9a0b4b9a68eb8a22', 0, 'CpmTaskActivity', 1, 'CNT_CPM_TASK_ACTIVITY', NULL, 'CTM_CPM_TASK', 'CPM_TASK_ACTIVITY', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8195956a0ea8494cb62df3a36a3e76a1', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmTaskActivities.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e781616cb9124096b1a526e5be718058', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmTaskActivities.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f9a73b07065545ec8fdf352d19579617', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmTaskActivities.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('da8fe64b97f541bba38929eabb408d85', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmTaskActivities.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fca8f9a794dc4b09ae49ca0733aacebc', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmTaskActivities.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4d5bfd28bd4a4c5c9bcd91686917ce67', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmTaskActivities.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d7ceee538a9e404f994869b778018bd7', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmTaskActivities.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6e1bb5c045c7474f886d5a3832acb627', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmTaskActivities.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f8132d2f246649288a262ebde9f4943d', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmTaskActivities.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('01e2601eaa8c43e2bab674698f89b078', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'parentId', 'PARENT_ID', 'VARCHAR(32)', 'CPM_TASK_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmTaskActivities.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('39b0683ffa1a4889a3add3def9d6f7db', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'activityVer', 'ACTIVITY_VER', 'VARCHAR(100)', 'ACTIVITY_VERSION', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmTaskActivities.activityVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a87f61ec38ca444595d258a0da2e20e1', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'endDate', 'END_DATE', 'DATE', 'END_DATE', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmTaskActivities.endDate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('47e293daa7ed4c43b6b05f8b83078627', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'isLate', 'IS_LATE', 'BOOLEAN', 'IS_LATEST', '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmTaskActivities.isLate', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f719fa4a724c4ad495cbc2231a4ee317', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'status', 'STATUS', 'VARCHAR(100)', 'STATUS', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.cpmTaskActivities.status', 'codelist', 'CPM_TASK_STATUS', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('89e9f9db437c4da69b94041c8246b7f5', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'reasonId', 'REASON_ID', 'VARCHAR(400)', 'REASON_SID', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.cpmTaskActivities.reasonId', 'codelist', 'CPM_TASK_REASON', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9a23230a38aa4377972f4638040caa69', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'reasonDescription', 'REASON_DESCRIPTION', 'VARCHAR(400)', 'REASON_DESC', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.cpmTaskActivities.reasonDescription', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('36ce29d298e34f318a4e69e3af2387c5', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'statusChangeTime', 'STATUS_CHANGE_TIME', 'DATE', 'STATUS_CHANGE_TIME', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.cpmTaskActivities.statusChangeTime', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('61d6a69d870545a6962701a1afced765', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'createdOn', 'CREATED_ON', 'DATE', 'CREATED_ON', '0', 'date', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.cpmTaskActivities.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4122aed433b14bf4880275274e1a79b5', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'statusName', 'STATUS_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.cpmTaskActivities.statusName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('2b087962ea6e45b781a6829255909b19', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'statusVer', 'STATUS_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.cpmTaskActivities.statusVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('748f19dcc2564923bda6053f4bc271bf', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'reasonIdName', 'REASON_ID_NAME', 'VARCHAR(400)', 'N/A', '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.cpmTaskActivities.reasonIdName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0e7e8b65fd4f4c6383bc8a92af0150da', 0, '9abe7bd3bf27434f9a0b4b9a68eb8a22', 'reasonIdVer', 'REASON_ID_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.cpmTaskActivities.reasonIdVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('a26f602a166e4d0abc0af99238428a6e', 0, 'CpmImage', 1, 'CNT_CPM_IMAGE', NULL, 'CTM_CPM_TASK', 'CPM_IMAGE', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f507119595f1452c9304c92ea7cbccd4', 0, 'a26f602a166e4d0abc0af99238428a6e', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmImages.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6639a89eb6024b2c8348bb32cefce54e', 0, 'a26f602a166e4d0abc0af99238428a6e', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmImages.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4b5daef8fc3044268a917fdd8f48f029', 0, 'a26f602a166e4d0abc0af99238428a6e', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmImages.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b1eff2bed72b43ebb6343cbbc8e40596', 0, 'a26f602a166e4d0abc0af99238428a6e', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmImages.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ab9b3e6f2cb84ab6a6c849c9a56b775a', 0, 'a26f602a166e4d0abc0af99238428a6e', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmImages.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('52fe7cf8febf471b8301ad00b54bece4', 0, 'a26f602a166e4d0abc0af99238428a6e', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmImages.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5927bab23aff433bb14939dd46e5510c', 0, 'a26f602a166e4d0abc0af99238428a6e', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmImages.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4f7418e342eb40f98f56f3b1db7e0cdc', 0, 'a26f602a166e4d0abc0af99238428a6e', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmImages.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c7420359168e4287a011a32856b7cea3', 0, 'a26f602a166e4d0abc0af99238428a6e', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmImages.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d9cc0280468649aebe5c0d077dee2810', 0, 'a26f602a166e4d0abc0af99238428a6e', 'parentId', 'PARENT_ID', 'VARCHAR(32)', 'CPM_TASK_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmImages.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('697c7a253c7a4f5e86860bd6f953fcf2', 0, 'a26f602a166e4d0abc0af99238428a6e', 'description', 'DESCRIPTION', 'VARCHAR(1000)', 'DESCRIPTION', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmImages.description', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('340e1b0899784f25a12c13a467856eb0', 0, 'a26f602a166e4d0abc0af99238428a6e', 'fileId', 'FILE_ID', 'VARCHAR(32)', 'IMAGE_SID', '0', 'entity', 'Image', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmImages.fileId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6292707039a440e08c5bd1c8796612d3', 0, 'a26f602a166e4d0abc0af99238428a6e', 'imageTypeId', 'IMAGE_TYPE_ID', NULL, NULL, '0', 'selection', 'CpmTaskSelection', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmImages.imageTypeId', 'selection-codelist', 'IMAGE_TYPE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('48dabbc5227b41ae9b7424fef92b5f10', 0, 'a26f602a166e4d0abc0af99238428a6e', 'fileRef', 'FILE_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.cpmImages.fileRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6f7aaf53a4614bb1960b462004d6fa64', 0, 'a26f602a166e4d0abc0af99238428a6e', 'fileVer', 'FILE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.cpmImages.fileVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b09af7082a144309800fc6f314b2e350', 0, 'a26f602a166e4d0abc0af99238428a6e', 'fileRef2', 'FILE_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.cpmImages.fileRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c34aa05b33034c9eb563920cfff14a05', 0, 'a26f602a166e4d0abc0af99238428a6e', 'fileDuid', 'FILE_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.cpmImages.fileDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('23a5cc69d3cc46268fa5551a8f242117', 0, 'a26f602a166e4d0abc0af99238428a6e', 'imageTypeIdValue', 'IMAGE_TYPE_ID_VALUE', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.cpmImages.imageTypeIdValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('f7fdfed293a444d0a7c48c00c3050c50', 0, 'CpmTaskHistory', 1, 'CNT_CPM_TASK_H', NULL, NULL, NULL, 'cpmTask', 'inner', NULL, NULL, NULL, NULL, '1', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', NULL, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c390447a86714dbb806415a7b5771f96', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'parentId', 'PARENT_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('3c36a7b5b6094731bd9c7a5bd800ab38', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'docVersion', 'DOC_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.docVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f07528db21f045c7885ccf3887454a9f', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'docRevision', 'DOC_REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.docRevision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e611fc6955da41e88e0ea5da8a07970b', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'itemId', 'ITEM_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.itemId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('be1c5065a90346909869aa713714f8a1', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'itemRevision', 'ITEM_REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.itemRevision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('832ff65c2f8a4f439d85357005c2864d', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'itemEntityName', 'ITEM_ENTITY_NAME', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.itemEntityName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ab049497b2b24bcd9c6859da9d496ab0', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'type', 'TYPE', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.type', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9944d660ff424900bfaac480388534e9', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'fieldId', 'FIELD_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.fieldId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ca907b0faf604c5080a9c4b1d5f5ac85', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'path', 'PATH', 'VARCHAR(1000)', NULL, '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.path', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a8fe07403ca04daf9419046f074ceac7', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'refValue', 'REF_VALUE', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.refValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('108e1c1c6d604f029c53682e85549f42', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'operation', 'OPERATION', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.operation', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9f0216746506429d93448665d425765b', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'valueBefore', 'VALUE_BEFORE', 'TEXT', NULL, '0', 'clob', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.valueBefore', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('cc1b2f30a73d45d68aec35bb281fa76e', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'valueAfter', 'VALUE_AFTER', 'TEXT', NULL, '0', 'clob', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.valueAfter', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5ff04c91a613424687006a367cfc19cf', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'createUser', 'CREATE_USER', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.createUser', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1524793e19da4cd68eaa1637aff15530', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'createUserName', 'CREATE_USER_NAME', 'VARCHAR(100)', NULL, '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.createUserName', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ac1b2838c7ad46c09b845fe8780041bf', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'createdOn', 'CREATED_ON', 'TIMESTAMP', NULL, '0', 'timestamp', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.createdOn', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('a5a77cb3c0dc45ae9b6b10b3be25d688', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'trackingLevel', 'TRACKING_LEVEL', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.trackingLevel', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('0385f9ee746b456ea68107d59a2e1413', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'id', 'ID', 'VARCHAR(32)', NULL, '2', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b2413d8e196d4df1abe8d6edbf142b43', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '18', '0', 'entity.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('23ff8ddbcb7c4d45a14a005bfe8361f3', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '19', '0', 'entity.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e80f9708a3b14777bc7e32fcf1f0c3a0', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '20', '0', 'entity.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('08cc43546ecc45a98c508e8082d3b8a8', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '2', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '21', '0', 'entity.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8bbb8bc219d144beb7a1464098106b24', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '2', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '22', '0', 'entity.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ca5f31d46bea499f9b462b4a0cd8c22f', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '2', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '23', '0', 'entity.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('d3ccf44f881240938cf88663b237bccf', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '2', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '24', '0', 'entity.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6b318e10c321402e9020673dd535045a', 0, 'f7fdfed293a444d0a7c48c00c3050c50', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '2', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '25', '0', 'entity.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('1efa67f3e67747af933a9a1728f8387a', 0, 'CpmTaskPredecessor', 1, 'CNT_CPM_TASK_PREDECESSOR', NULL, 'CTM_CPM_TASK', 'CPM_TASK_PREDECESSOR', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('e27fa4f898a94ea2a33c736a4c866043', 0, '1efa67f3e67747af933a9a1728f8387a', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmTaskPredecessors.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('fc325165c4a249248ebb3bfd74ad7447', 0, '1efa67f3e67747af933a9a1728f8387a', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmTaskPredecessors.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('15ab4b71692f4d2eadd4a4d363418cac', 0, '1efa67f3e67747af933a9a1728f8387a', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmTaskPredecessors.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('796d9eea41984706a5b5dee603bb41ff', 0, '1efa67f3e67747af933a9a1728f8387a', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmTaskPredecessors.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('59eedf8922ac4e598cb1f0d5b9afd69b', 0, '1efa67f3e67747af933a9a1728f8387a', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmTaskPredecessors.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('86590b3195174f01bcd70eb4c8e89b29', 0, '1efa67f3e67747af933a9a1728f8387a', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmTaskPredecessors.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8e10cbdaa51c4579bd25b369aa112367', 0, '1efa67f3e67747af933a9a1728f8387a', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmTaskPredecessors.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9176d404c8a5455a8dba250c5cbb4155', 0, '1efa67f3e67747af933a9a1728f8387a', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmTaskPredecessors.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8e7447e979de4dd592b21c08be7dbb3f', 0, '1efa67f3e67747af933a9a1728f8387a', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmTaskPredecessors.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('4584807953144c13aa9f0009c3c269c1', 0, '1efa67f3e67747af933a9a1728f8387a', 'parentId', 'PARENT_ID', 'VARCHAR(32)', 'CPM_TEMPLATE_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, '1', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmTaskPredecessors.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ddcce3aa427242ddb1c85c8940a4fd92', 0, '1efa67f3e67747af933a9a1728f8387a', 'taskTempl', 'TASK_TEMPL_ID', 'VARCHAR(32)', 'CPM_TASK_TEMPLATE_SID', '0', 'entity', 'CpmTaskTempl', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmTaskPredecessors.taskTempl', NULL, NULL, NULL, NULL, NULL, 'Snapshot', NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('9af08358e3cf411db049c17bcb4ee39b', 0, '1efa67f3e67747af933a9a1728f8387a', 'lag', 'LAG', 'NUMERIC(20, 0)', 'LAG', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmTaskPredecessors.lag', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('23a4378cf3e64cd990e25d6275ed7b80', 0, '1efa67f3e67747af933a9a1728f8387a', 'taskTemplRef', 'TASK_TEMPL_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmTaskPredecessors.taskTemplRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('bc6a0a938dbb4efc897320a7472b2e5d', 0, '1efa67f3e67747af933a9a1728f8387a', 'taskTemplVer', 'TASK_TEMPL_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.cpmTaskPredecessors.taskTemplVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_ENTITY_DEFINITION
 (ID, REVISION, ENTITY_NAME, ENTITY_VERSION, PRODUCT_TABLE_NAME, META_TABLE_NAME, CUSTOM_TABLE_NAME, REPORT_TABLE_NAME, MODULE, TYPE, REVISION_TRACKING, VERSION_TRACKING, HISTORY_ENTITY_NAME, REF_PATTERN, SYSTEM_ENTITY, CREATE_USER, CREATED_ON, IS_FOR_EXTERNAL, IS_SKIP_ACL, UNIQUE_FIELD_ID, CUSTOM_TEXT_COUNT, CUSTOM_DATE_COUNT, CUSTOM_NUMBER_COUNT, CUSTOM_DECIMAL_COUNT, CUSTOM_CODELIST_COUNT, CUSTOM_MEMOTEXT_COUNT, CUSTOM_HCL_COUNT, CUSTOM_CHECKBOX_COUNT, CUSTOM_SELECTION_COUNT, CUSTOM_TIMESTAMP_COUNT)
VALUES ('f55df8b2a3924f098147292c91f1c1e5', 0, 'CpmAttachment', 1, 'CNT_CPM_ATTACHMENT', NULL, 'CTM_CPM_TASK', 'CPM_ATTACHMENT', 'cpmTask', 'inner', NULL, 'ALL', NULL, NULL, '0', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '1', NULL, '7', '7', '7', '7', '7', '7', '5', '10', '0', '2');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8732c1023d0d4d88830c1216e1808bd0', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'id', 'ID', 'VARCHAR(32)', NULL, '0', 'id', NULL, NULL, NULL, 'com.core.cbx.data.generator.UUIDGenerator', NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '0', '0', 'entity.cpmAttachment.id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('756b129858754c2aab953726d9e97a6f', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'revision', 'REVISION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1', '0', 'entity.cpmAttachment.revision', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('30eeaf24df754f128ae14b076d3f9f85', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'entityVersion', 'ENTITY_VERSION', 'NUMERIC(20, 0)', NULL, '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '2', '0', 'entity.cpmAttachment.entityVersion', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6a506b26bf7449e0a9275f5aecd59d76', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'domainId', 'DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '3', '0', 'entity.cpmAttachment.domainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('c81a62a579f4445394f766b260cf4e4a', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'hubDomainId', 'HUB_DOMAIN_ID', 'VARCHAR(400)', NULL, '0', 'string-m', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 400, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '4', '0', 'entity.cpmAttachment.hubDomainId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('ec2a3af337ac45abb4c7fdec57b5df61', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'isForReference', 'IS_FOR_REFERENCE', 'BOOLEAN', NULL, '0', 'boolean', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '5', '0', 'entity.cpmAttachment.isForReference', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('20bb4c83078f49b192466ddb3febe059', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'internalSeqNo', 'INTERNAL_SEQ_NO', 'NUMERIC(20, 0)', 'n/a', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '6', '0', 'entity.cpmAttachment.internalSeqNo', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f4112daea2a64b159bb4566178317d9f', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'duid', 'DUID', 'VARCHAR(100)', 'DUID', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '7', '0', 'entity.cpmAttachment.duid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('6b3363a25f2d4d27866bde06ba170c4d', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'uniqueKey', 'UNIQUE_KEY', 'VARCHAR(32)', 'UNIQUE_KEY', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '8', '0', 'entity.cpmAttachment.uniqueKey', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('1a18f283768e41908d3c14369de1bb59', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'parentId', 'PARENT_ID', 'VARCHAR(32)', 'CPM_TASK_SID', '0', 'id', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '9', '0', 'entity.cpmAttachment.parentId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('594918ef361949919248d757512f1c33', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'description', 'DESCRIPTION', 'VARCHAR(1000)', 'DESCRIPTION', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '10', '0', 'entity.cpmAttachment.description', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('8142fa67e7284c32a7946b3be1b57c82', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'fileId', 'FILE_ID', 'VARCHAR(32)', 'ATTACHMENT_SID', '0', 'entity', 'Attachment', 'id', NULL, NULL, NULL, NULL, NULL, '0', 32, 32, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '11', '0', 'entity.cpmAttachment.fileId', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('b2ccefdba131486fa60d245cb62ee991', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'attachTypeId', 'ATTACH_TYPE_ID', NULL, NULL, '0', 'selection', 'CpmTaskSelection', 'parentId', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '12', '0', 'entity.cpmAttachment.attachTypeId', 'selection-codelist', 'ATTACHMENT_TYPE', NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('36a280026c10461f873fd367f3eb58ac', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'fileRef', 'FILE_REF', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '13', '0', 'entity.cpmAttachment.fileRef', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('5f0679b8a9b243b9a260b9b58c808b22', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'fileVer', 'FILE_VER', 'NUMERIC(20, 0)', 'N/A', '0', 'integer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 18, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '14', '0', 'entity.cpmAttachment.fileVer', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('38a933b4197c482aa070df97667fc6a7', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'fileRef2', 'FILE_REF2', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '15', '0', 'entity.cpmAttachment.fileRef2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('42dae40c32ff4d66b2f3ab7963eebc55', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'fileDuid', 'FILE_DUID', 'VARCHAR(100)', 'N/A', '0', 'string-s', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 100, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '16', '0', 'entity.cpmAttachment.fileDuid', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');

INSERT INTO CNT_FIELD_DEFINITION
 (ID, REVISION, PARENT_ID, FIELD_ID, COLUMN_NAME, COLUMN_TYPE, REPORT_COLUMN_NAME, CATEGORY, FIELD_TYPE, ENTITY_LOOKUP, ENTITY_LOOKUP_KEY, ORDER_BY, GENERATOR, REFER_TABLE, KEY_COLUMN, LOOKUP_COLUMN, MANDATORY, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, CONSTANT_VALUE, UNIQUE_KEY, CREATE_USER, CREATED_ON, LINE_NO, MANAGED, FIELD_PATH, DATA_TYPE, DATA1, DATA2, TRANSITIVE_FIELDS, FORMULA, ENTITY_LOOKUP_TYPE, SNAPSHOT_REF_KEY, SNAPSHOT_REF_FIELD, REFER_OTHER_DOC)
VALUES ('f85d4b81ae644fc9948958b1c23cff5a', 0, 'f55df8b2a3924f098147292c91f1c1e5', 'attachTypeIdValue', 'ATTACH_TYPE_ID_VALUE', 'VARCHAR(1000)', 'N/A', '0', 'string-l', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 1000, NULL, NULL, NULL, NULL, 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '17', '0', 'entity.cpmAttachment.attachTypeIdValue', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0');




DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskParty' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskParty' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskParty' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTask' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTask' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTask' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskSelection' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskSelection' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskSelection' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskAlert' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskAlert' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskAlert' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskAdvSecurity' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskAdvSecurity' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskAdvSecurity' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmDocOwner' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmDocOwner' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmDocOwner' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskActivity' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskActivity' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskActivity' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmImage' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmImage' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmImage' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskPredecessor' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskPredecessor' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmTaskPredecessor' AND ENTITY_VERSION = 1;

DELETE FROM CNT_HISTORY_DEF_ITEM WHERE PARENT_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmAttachment' AND ENTITY_VERSION = 1);

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_HISTORY_DEF') AND TARGET_ID IN (SELECT ID FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmAttachment' AND ENTITY_VERSION = 1);

DELETE FROM CNT_HISTORY_DEF WHERE DOMAIN_ID = '/' AND APPLY_ENTITY = 'CpmAttachment' AND ENTITY_VERSION = 1;

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('253bedc75b50497a9481edd6bffce351', 0, 'CpmTaskParty', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c79e5b4b3c5e412698f9cc8d760d7e0d', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b2c511c126b640a0a46f0d3533af4301', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4341d6f6bec740ffa8e7f420c05cc536', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bf3a08389cec4daeb280dc0a3a63ebb7', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('96a0c02a47964a7bb6723ca0519ca718', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ef1c45a04ad042cb87a357d2efff3538', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('de8aeb57f8794958b2716f43955a7e85', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b27950f0bfb64452b3bc3b5a5790926c', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b8a2400755ec47458b4fc82d0bcfb698', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5c5359e9c2b14d07924a6660d72bf7e0', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '9', 'docId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('36b847dd55114103a896f3cdf83cd4ef', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '10', 'partyName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('473246014ccf4f6c8df072523781714c', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '11', 'partyNameSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7ac7269f40ed43d0b9569c92a2e81a22', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '12', 'contactUser', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('87c4605d8f304799866e69fbea5a6120', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '13', 'contactUserSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ac68aa511a6148f0b68343b867cabf49', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '14', 'isOwner', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0ec7a1f20ad14003a8367c84912ddfb2', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '15', 'partyNameName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('239a914036a8403a9b49abea153b578c', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '16', 'partyNameVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fe5854226ac34a669c4d5f992991e30e', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '17', 'contactUserRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2921d6451ab34a2385eb60fa2193e861', 0, 1, '/', '/', '0', '253bedc75b50497a9481edd6bffce351', '18', 'contactUserVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('2a2cacb288e64169bb4447d0c605dfee', 0, 'CpmTask', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('33648795e70b409397da883077e6b9fb', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a86b8f1f8ed342249b62cb765cace8ed', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3d6fab9069484e3d9f923bf285d2d6c5', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('49ec70cd141e4067a0587e2e52799203', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9eec12be05b34c5bb60ebe273f3c6836', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b72351a5855e4df2a817d322a60dfd01', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1f1fdbe53fb142f79638ad4e47dc24c5', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '6', 'version', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cf3153a982e8469f961cdb431427ecd0', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '7', 'docStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('604a5c15ed4e49b4ada844e8410da82a', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '8', 'editingStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('41ff8d449d3d47e98b86fb1322223912', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '9', 'createUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('76aa413c66534cd1b932fb41e6f1249c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '10', 'createUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d0ff208daaec4888b721441949619354', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '11', 'updateUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0733ee6a99da476dad59908ff91f8e72', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '12', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d0db53cf8b7147cfacc6892984f9fddc', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '13', 'integrationSource', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d214a84156fb42f5b233ae18a0f8011e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '14', 'integrationStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('431ee0bb6040406082a6c988efd254fe', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '15', 'integrationNote', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4a7e0acb5f6443099d872e3f51e58090', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '16', 'isCpmInitialized', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('69dd6227a822454f852c0a4ee0813972', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '17', 'isLatest', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('186fecb391ec4f13a4d9fe984459b980', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '18', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2627def041bc4fb08de70cf69985370c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '19', 'taskTemplId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3ab9310395044b7282f4c215039723e7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '20', 'refDocId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ae13b08d9a404a8eaf38639e56be88b7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '21', 'refDocRefNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('07f020b58d564142b289485a9ef28f01', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '22', 'refEntityName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('166cbda7c90240a0a7f373e780f39a6b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '23', 'taskId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1248d8f325e2488a82bb6bd6da5d5a9a', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '24', 'taskName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d26a48bffe4540be9491871d56ee96a7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '25', 'taskType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8f419ef24bbb4280b255e14ce1253d65', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '26', 'description', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('559a273322c341cdb11a4c26a85ec199', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '27', 'planStart', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('13fe8b847dee433ea97ed4144c63bfb7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '28', 'planEnd', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1f17f1bdec854362ac0689a3982096ff', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '29', 'actualStart', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('60d406ecaec546c0bf1498d6e65da9da', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '30', 'actualEnd', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('729afb16c32c4b82a1e46db320907f8a', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '31', 'reasonId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cec03652fbf14518b12d8f9b5551e6d0', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '32', 'externalDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f5d92e3809104dbda12add5043f35bf7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '33', 'masterRefNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a2452ee607b143fc8a6c2263a1e6e8ee', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '34', 'reasonDescription', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('90177889d15e40229e2112c0d7c7996e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '35', 'revisedPlanEnd', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('764aa0e3897749adba0e8a1ee02e58a2', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '36', 'daysBeforePlanStartDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1bb44e83334041da8a4290c135038ae0', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '37', 'daysBeforePlanEndDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('161ae8a5d9174946a898ef959f7cc037', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '38', 'daysBeforeActualStartDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f9f6be8150e04d34968bddca791a525c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '39', 'daysBeforeActualEndDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c6d328762ed44c1593637132779d1899', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '40', 'daysAfterPlanStartDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9fcc40bc27944c11a281e88d03a8774d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '41', 'daysAfterPlanEndDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bd81bf1c60ac4fd4a7462df24eb212ee', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '42', 'daysAfterActualStartDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ae2fcdff6c5a49638bfa438d20cb8b7a', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '43', 'daysAfterActualEndDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5dc7f4b92cfa4dfa81696789d487db76', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '44', 'custText1LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e031c91702af463a823fa1c04f6cd0f4', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '45', 'custText1Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('96b52eae07ab42e999592000be6fc8fc', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '46', 'custText1Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3cc29535c3f2460cbadefceb2a0ba638', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '47', 'custText1Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6944febb445c4918977573f15c388d41', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '48', 'custText2LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c60335cf147f43309b0c899b5c60c92b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '49', 'custText2Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cc1aa9d76204488f88435f19cc650a1e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '50', 'custText2Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('22144481c90046ddb895ae75b6ff6d6a', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '51', 'custText2Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('407f55df25a644a4bf20c7aefb0fe7c1', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '52', 'custText3LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d4c649a247774506a0ab5a11ac089f3f', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '53', 'custText3Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e88ff483a46744cd94c393ea18b8306e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '54', 'custText3Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('de748174eb5f425ba15a41ded73575db', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '55', 'custText3Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fa36d349154241beb1d1a3bb22f3a29d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '56', 'custText4LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fa0fb01dd2274e66b7aee1ede5d51d49', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '57', 'custText4Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8b57c2da9fd44dd69a2e3528d9f39045', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '58', 'custText4Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('98656652dbbb43f1a86dd3a0c31ee6a7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '59', 'custText4Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('835a563c75234f5a9e7ccb9b0702acd3', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '60', 'custText5LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('784b0dba04c946e2bc3e13d673cd5cc8', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '61', 'custText5Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0d0fb0e030c547a1bb10a8bbe7fa5934', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '62', 'custText5Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b435fd57a7b34c1691e0d48396c01a68', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '63', 'custText5Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5ae5ddb0b24f4f11ab6a2b8e4f5c7fc5', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '64', 'custText6LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('514f40f2ddd04b3a982be66743d5d890', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '65', 'custText6Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b3e768790a954f21b4ba0db1a2d1313f', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '66', 'custText6Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1628a575c4de49e6aa97a88fe1db682e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '67', 'custText6Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8ece58d01123441786870d2fb3d45d05', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '68', 'custText7LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4b4606fd38a44be590aa2872dbb30bef', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '69', 'custText7Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('956ef443d7de4bb4aa9613f0a32ada00', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '70', 'custText7Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1cfef0566b984aae897c05a4ad1eefbc', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '71', 'custText7Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9e855ea093ec476fa1f3038ba1ed77c7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '72', 'custDate1LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1da784c19bca4ea3a860eddec8142fe2', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '73', 'custDate1Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('130e883f45c241d8ab79946bbd6005a5', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '74', 'custDate1Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a1ea8f18feeb48d68d255c90ee81dc33', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '75', 'custDate1Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('adeb47268cf04b72b0b274200353bfdb', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '76', 'custDate2LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('839bbf201f71436ca3393244da41ba30', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '77', 'custDate2Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1ccfcb9a43154889a2149f554a367e77', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '78', 'custDate2Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2a25a251706d4b87ab016bab6bad33da', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '79', 'custDate2Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('64c4c493639a408da72b9b3a167a1349', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '80', 'custDate3LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('39d563af2a4b40539f070b7a65832b8d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '81', 'custDate3Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5f904804c1bb4977a028645f80d2050b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '82', 'custDate3Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2ab46773db7949a0b27f93a058d3b970', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '83', 'custDate3Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('063ecb5810e64efd89919d2f347f60a2', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '84', 'custDate4LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a41e6d6a01e84a9d8ca166d80f67a19e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '85', 'custDate4Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ea320e1e98ca4fc4ba49c2b5f3d50e2b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '86', 'custDate4Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d878b2e82f5d442ebdb795a1f8fc070b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '87', 'custDate4Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('732bd552904348118a4e4c998e3c3bbc', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '88', 'custDate5LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4482a903e64e4afebc7fdd3be16198a7', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '89', 'custDate5Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('db1f8b3791df4c8688a09b5c152742a5', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '90', 'custDate5Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5fa5c41732264f6ca54514d3b5c05d06', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '91', 'custDate5Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0028f6148b6d437a8e26bbbf8204dd9f', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '92', 'custDate6LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('393f700d37a14b119d84cd7a7417189d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '93', 'custDate6Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('00614b0e3baf4c34a43560b61600942e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '94', 'custDate6Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('067608b1917f4a06ad51c3bae1c99384', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '95', 'custDate6Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ecb1280b03e24b00af961009044dd478', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '96', 'custDate7LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a8df30cbda4746778e181ecb80487795', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '97', 'custDate7Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7045618eb2b74279bc8612e824ee10d2', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '98', 'custDate7Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('02acecf4f300431c823807e85acf86ea', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '99', 'custDate7Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a1d55bd2d7eb4633a8c3e52f252aab12', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '100', 'custNumber1LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c3f353d7105448469fe3b3757b4f9a79', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '101', 'custNumber1Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('90dc0dcbaa9a40b48fc67d40f14e2376', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '102', 'custNumber1Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dcd7bd2bc4e94fa18e43b23ae4110503', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '103', 'custNumber1Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7bdf74f7c79a4c4a86ee7d82eb7d309d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '104', 'custNumber2LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cbd4ebc20fa44c9fa7dafda6f3ab91fe', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '105', 'custNumber2Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('20a9153cb652458b97b299125dfa30d3', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '106', 'custNumber2Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f54e25a0f3a34ee68cca09af3ccaa0c1', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '107', 'custNumber2Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e6253d43c90141f1b69fd37fa8cb6c1e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '108', 'custNumber3LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2458d2f912094f1199978f48306f7246', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '109', 'custNumber3Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2dc0f1b6b35d4c9da64d7d25ff1b4b4b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '110', 'custNumber3Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e2008e08a0e548728ce444780c0d0344', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '111', 'custNumber3Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d8a5211c3b2e48adb4aca3df64d4e8dd', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '112', 'custNumber4LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8b7bdf6b75bd42aebed535a910887025', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '113', 'custNumber4Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bb7efbd265574b49a11b8f72e680aa6c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '114', 'custNumber4Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2f062e8520e643b29aa2eb74ddbffb20', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '115', 'custNumber4Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b0430b72cc794c709d1e01276989d460', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '116', 'custNumber5LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('12213a8f7ab8450e8fc55e937fc43c82', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '117', 'custNumber5Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5549911af5b5429a9a6ca906588c674f', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '118', 'custNumber5Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f78ca794445f41dd9255866d671efda9', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '119', 'custNumber5Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c5a682c410064dbdb0b09d61e0fa8f2c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '120', 'custNumber6LineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cd7b80ed1b76486b950b27d89b7f9be8', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '121', 'custNumber6Label', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('66d2a77ab5a2467187f93397c17c6cde', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '122', 'custNumber6Enabled', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4f17aa3ec29a4f6f871617aa364deca3', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '123', 'custNumber6Mandatory', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('664ab0e1c0044fe0896a24b435a85df5', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '124', 'createdBy', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7c4bf921f8c048e89f12a43121172c35', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '125', 'updatedBy', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('50f950638d264aa9811cbdd60081056e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '126', 'sequence', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('90cccb41737a46939100c4a9c6fe075c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '127', 'notificationSentOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5df1a56f1f4142bbadc86edeeb6eb69e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '128', 'colorCode', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8858512d7c58420989d85e219a5f6396', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '129', 'fieldId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('91899ff727924c799afdefe334ebae09', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '130', 'fieldValue', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('33284a535ce8426e8e5ce5b166c92134', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '131', 'refEntity', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('684917fea909488b99191dc1518779ef', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '132', 'refFieldId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0470f197d463482087289e27b4673cd2', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '133', 'percentage', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6b7d1cc43e2d4921a34d3a6475611471', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '134', 'quantity', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5a8d26d015394158a7be23994b11c3da', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '135', 'attachment', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('20d55ea6e41e425385f621d90977974d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '136', 'duration', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('da9f50939a904ceeb95752fd8a808110', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '137', 'plannedDuration', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('debd158ddda84f4bb610e3ace8696aa1', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '138', 'cpmTaskAlerts', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('379ce7663c1d4ce886a14993b1625583', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '139', 'cpmTaskActivities', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('68016334858c410c96083bb01e796701', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '140', 'cpmImages', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('29656064fcc446ed89e204b9d5d675e0', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '141', 'cpmAttachment', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f3c18f52fe4a40e1b9d50a630c2fec6c', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '142', 'cpmTaskAssignees', 1, 'PathRenderer', 'SelectionDetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f3a3d73c09eb400d8e56cce7ec54003d', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '143', 'cpmTaskNotifications', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('af814100885f414fa9bcd78b9cdb6625', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '144', 'cpmTaskAdvSecuritys', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('50d86286000c4293905b2c7fccafc96e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '145', 'cpmTaskPredecessors', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('179192fc6b1743dfb7cd4fe309fa5f6b', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '146', 'partyTemplateRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2e3a809ebb2a4a749f6e4cd6479dcc77', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '147', 'partyTemplateVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('635fd59101524f808a1741424f95e9a1', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '148', 'partyTemplateName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7dd8c8e1750d4aa0a862bc7597c33725', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '149', 'partyName1', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c075882b0c324128ba3efc81e8c9ddba', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '150', 'partyName2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('22c24c794c3d44dd95c7a4e7aa7c21f0', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '151', 'partyName3', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ae8cb5192ed54529a34659ca8afbbd32', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '152', 'partyName4', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bd38ef62854c4e469e6b0b44da74a254', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '153', 'partyName5', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c0e38096237c457c97af46151cf16ecd', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '154', 'parties', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2c7cb1aff1fc4ac9b9f5a108b7be9335', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '155', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dd823198aeb044a792382b0efeb415a6', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '156', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('00911cca06394245ae7cee02bfbdf65e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '157', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('eca052aeca9e44e1a86956bccecdf3d1', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '158', 'reasonIdName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c853dac0abdc48cfa87b790685144787', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '159', 'reasonIdVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cceda0faba344e3bae3722edfeff84db', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '160', 'attachmentRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d94c5ab9bd57422ba37a183219fd0034', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '161', 'attachmentVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('39b66270500a4328a717fe99d7db1a4e', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '162', 'attachmentRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7bb80fdc582a48859508251b1d4d0afa', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '163', 'attachmentDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7fdf853c885741dc80d8f81fc484d369', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '164', 'cpmTaskAssigneesValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4cd4745e36d5441d8f005bff18a37e88', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '165', 'statusName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a9ef108a5cd144dda6b885bab752d422', 0, 1, '/', '/', '0', '2a2cacb288e64169bb4447d0c605dfee', '166', 'statusVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('88f0016f514a4826b5f93689828ff399', 0, 'CpmTaskSelection', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('189cb45e09f8462cac348be5b2845dc4', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d747966f225e4407a999b40f1a9c4762', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8b04e5f0475243a4bea6951a7d537569', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3b9572f1559b471aafe5c12be1b43892', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('db9347a795c543c4a305d33bc1f9a983', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b99ec208cc824388af27ba73faf00462', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8eadfff9ef784df5ac471691140c9582', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '6', 'version', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fda373d6e460477c95341a1e4a6b01c3', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '7', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4aa7182b7ede4163a3e7ff2bf5aff08c', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '8', 'docStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('aa8543de2ebb43cea62f6e2d31250aa6', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '9', 'editingStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('80afe42e0aad4e17992aac3fa212b72e', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '10', 'createUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('00bcbaa098144f418885ae3a19e982d5', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '11', 'createUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('157787fa57a24f3989cd8ac1978af408', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '12', 'updateUser', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7d69bfb0f1e3419aa60505f397c18ecb', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '13', 'updateUserName', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('25d42dee06a04fb3a2dcf915c27a44a0', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '14', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a3262fbc70a4406a9379e551e1997f41', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '15', 'updatedOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5b3a2a46251a4be981fa6a203f901b49', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '16', 'integrationSource', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d5d41753e0114878b9f2cd94ba645211', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '17', 'integrationStatus', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e8edee61cdee44668cb15e82db6a1826', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '18', 'integrationNote', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9d92fff633da46d0974cc0ef7aed4e08', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '19', 'isCpmInitialized', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a180da98bb04429ba6d5260177813926', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '20', 'isLatest', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e728175f89a143869aa053820a3c1614', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '21', 'parentId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3bd9c81ddcef4ab7bcf2104d7cff1941', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '22', 'parentEntity', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3ac3d7ad83f64637b40638094c730ed6', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '23', 'fieldId', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('477ff152d5cf4c16be4c9a4b1c09646d', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '24', 'ref', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('82e7986a6dae420aa92b55a72606a1e9', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '25', 'refEntity', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('496cb87efb3b497b9a2c9f03d99f55ab', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '26', 'displayValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8c036673d6fb4d93aca6148db0fc68f6', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '27', 'internalSeqNo', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2544d8b52333491896a1cf2586905836', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '28', 'refRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b1c4f49b77a04a0eb877992892cf2a4b', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '29', 'refVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('06190cf61b6a400585cc9d208c44e7fd', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '30', 'refRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('27f41e61987144acb64ee61a8774be34', 0, 1, '/', '/', '0', '88f0016f514a4826b5f93689828ff399', '31', 'refDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('9067c22c9a854242bf8c6482a30a052a', 0, 'CpmTaskAlert', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fca1e51aa4614d7196a72908ceb0121c', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b66f63fe0c65437293f2bf6240979e22', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('aaa87a2ad65b4e399a0cbe83a71c29f7', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('74b7b0c918ac40df8dfef565611182bc', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('930b1db3fff0443a9e5b1a7467a149cf', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8a479fdd140e46e5b0619db89e4474c2', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8789fe9261864deea59c42a4753b5ad0', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('98c444452b1c49139fa61bfbc9d8d89f', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dd88195293d94b4c97fa4a174debbd46', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('08a3ded3352d42ab9bd4f6742adf026f', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '9', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9074471d5d7f426b8f4e5ea1b3a394b0', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '10', 'lineNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c925dda0eeb14e1c8871330f2eac0db6', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '11', 'alertType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0c40a4a04e994cfd85abdb420d0f83cf', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '12', 'conditionId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('645a388b7c924dcc94e2de6ac37a6f3f', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '13', 'frequenceType', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f337d2cbd39c448c899a496dc0b0b8ec', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '14', 'frequence', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bfc6149af2e14e83999549596eafb78c', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '15', 'cpmTaskAlertTos', 1, 'PathRenderer', 'SelectionDetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fc14e4dd531749ebaec6cd23d80cca5a', 0, 1, '/', '/', '0', '9067c22c9a854242bf8c6482a30a052a', '16', 'cpmTaskAlertTosValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('10f57178ef3f490eb66d1d513c3c1c33', 0, 'CpmTaskAdvSecurity', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4abb6fb85cbf4c3e860dfa3957019447', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('88b692ecc479441ba6ada41bd50a27d8', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cf369435a5ba4328870a4f0720191a1d', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('35ecb9bfe118431d95f117df0d87f925', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fc4329297a6243bfa4a0fc26d7cbea11', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b68837fdee954efd98eb427e400b7d85', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6375bb9b443c42778b8c10dcde21b5a3', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('96f9f1e771c04d4e9ba2c1c10b211583', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4025fec2fd3b47099179064e84052ca5', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6e25a741119e445cbe942768e5c38b9c', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '9', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('f3fe25051247488bb62ca5c0e48277a8', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '10', 'accessRight', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('fdf5924913774d9db5cc6f65dc583b4f', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '11', 'assigneeGroup', 1, 'PathRenderer', 'SelectionDetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3e446838b4f243f486a84b08e5b96caa', 0, 1, '/', '/', '0', '10f57178ef3f490eb66d1d513c3c1c33', '12', 'assigneeGroupValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('9037e02261124bcd92851f8e658aa71e', 0, 'CpmDocOwner', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a7790d53e87d4725972cdadc232f8576', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('715233ad79ab4dc28ee664f95f07f176', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('c411dfb88d754d3b925c35d6dfaacb88', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('db1207f76e6241bfb69b97e9ee11ce15', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('09b645f4d83148e5a25f734b05ac6a05', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b510471a66d04b769104f309b92aef04', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4d9456e09ad94ed4bdada52764546f29', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bf03030716fb40b198d792806852baa1', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0df5f9c66c2348089ad767478c770ef7', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8b515ff131b44e2aad18a005de528f3e', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '9', 'ownerDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b22fd5ef2cd440069b26d8fb7809fb06', 0, 1, '/', '/', '0', '9037e02261124bcd92851f8e658aa71e', '10', 'cpmDocId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('fe317b8822204599a0025528e58a5364', 0, 'CpmTaskActivity', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('998d446366194a1982a57cdffed65d77', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('694352fd8f724527828d01c91649cf38', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('cf32e0564bb743f08c56e1fecf0c688a', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('963692b1763b44dcac1aca0103909b67', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8a65bdb0356e4de8b1a2476a3eccbde1', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6da2fa92745248d29e3b6cc6f9f2ece4', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('65e1d6b85f214c0492791dcfedc659a0', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9962493756fb4d9eba7a0910b3089baa', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4aa7b895528c476fa6d45754e3dfd2b6', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bd9689b9be8f437b94a6c5593d99a5dc', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '9', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('78c1622d45724df5884e0f1601508781', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '10', 'activityVer', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('74ea971623a048428e9f5ff3b0d135ca', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '11', 'endDate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b715686fb20d4f158b50aa063992683d', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '12', 'isLate', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2d50763e44e84dc689da9fedd5850b03', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '13', 'status', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8d3f4b058d834f14ba7e694425fd71e4', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '14', 'reasonId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('89f052155ef840619ce0ac34060cf6f1', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '15', 'reasonDescription', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1249f82074284ddb9fa86d6a47c39a11', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '16', 'statusChangeTime', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1d07a5a788b04c9a9487dd85b9a38a2d', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '17', 'createdOn', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('1094b33b20834c7da2fb96855cd5b1b5', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '18', 'statusName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7188fa77143c4938a6675c8d09aaf776', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '19', 'statusVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a7a9f729c0364a67bba5ec49d6385f34', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '20', 'reasonIdName', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b0f5ef35a6484c35a3a8c3c399074816', 0, 1, '/', '/', '0', 'fe317b8822204599a0025528e58a5364', '21', 'reasonIdVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('0250031800374727ad2cdc1d62ff870e', 0, 'CpmImage', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e0a6d42dac9641298964d55efe056cce', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('6cd620c78b414cce902b2743fc13b7cf', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7964600e181b4f3db8853a4307b8b3ce', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('aff7805173ba4ca7871b7dacd0b323c8', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('95a1f05190a24702aa7b86d7dfad253b', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8b5904934c3440e699ebc7ec28e5d251', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e03b0482111541e8a92e5f73f51682e7', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8be3050f16d140dabba12479c29e90ec', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('490a275c99b24318964eca2ca17ebc7f', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a7970e008edd4047b43abe75880b5097', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '9', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2315bb9f0b08405aa6e3181fd31c8989', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '10', 'description', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('27754477dfaa435eb692ea06f8858f1e', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '11', 'fileId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('ac678cce1b41418f938c9499c46d14a8', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '12', 'imageTypeId', 1, 'PathRenderer', 'SelectionDetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('3c08f727fba143dcb7a5ef17388fbb97', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '13', 'fileRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('601f88d426744be9a01aee836577e1d4', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '14', 'fileVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('b6ca8a300a9c4013b1d174c84d8cb35a', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '15', 'fileRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a7eb3c75170b41d793655b9f80bae1e8', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '16', 'fileDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('10c16ff459724d5cbe45ce3178325ba7', 0, 1, '/', '/', '0', '0250031800374727ad2cdc1d62ff870e', '17', 'imageTypeIdValue', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('0df99c3c22fc4cd885b754bb9e500da0', 0, 'CpmTaskPredecessor', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('42754ecd438c45398a96bdfde7d44e81', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d1c6e09de85545babcfb3fd1b5cafbf2', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('12ca457181844e668ed62e9fce90d182', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5ebebf146e964a5d8df3f31c676f0b06', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('687e4973a9024df097dfb1eeb52f0901', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('73d1b049e47046448cac5359bb27edee', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('770b6adbc2b84865b8fd6c7c94930515', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('de80dc7428c045e9bb61567e995949b0', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('98bb2d1e5a9c4ad4b8e702b8d1c65686', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5288a6cc85954bec9c3248852ae7fd6f', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '9', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('dac1a22be20a40f191ac70c26fe4d0c6', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '10', 'taskTempl', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d3562b46f63e4da881cd847f3e9963fc', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '11', 'lag', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('40e3da9b14f2435bb45059c2aefcf04f', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '12', 'taskTemplRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bdc5eec560714c5aac9525e172702db8', 0, 1, '/', '/', '0', '0df99c3c22fc4cd885b754bb9e500da0', '13', 'taskTemplVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF
 (ID, REVISION, APPLY_ENTITY, ENTITY_VERSION, DOMAIN_ID , HUB_DOMAIN_ID , IS_FOR_REFERENCE , GENERATED, CREATE_USER, CREATE_USER_NAME, CREATED_ON, IS_LATEST)
VALUES ('b45adec6151b49dfa1ac4e8383e740be', 0, 'CpmAttachment', 1, '/', '/', '0', '1', 'system', 'system', TO_TIMESTAMP('2025-09-01 10:04:21', 'YYYY-MM-DD HH24:MI:SS'), '1');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('845b9ff9f42941e4bda7e862b6dafebb', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '0', 'id', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('d4fcf660905047d18b2172924ac20747', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '1', 'revision', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('5fa61c902176469eaefcf629616cd06c', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '2', 'entityVersion', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8095628e61cf4c84ba9c1f00158c99b1', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '3', 'domainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a92711cad66e41518298b3723ae3eef6', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '4', 'hubDomainId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('e8f0d6e7b39240b5a4a92c14cee065e7', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '5', 'isForReference', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('9300f772b92448419236ddbe412cf122', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '6', 'internalSeqNo', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('18127c1b87be4b6c93a1be72e56967aa', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '7', 'duid', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('2900a867301f43b783db20eb4564151f', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '8', 'uniqueKey', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('24a76edbf759497a9842074d92cdcff8', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '9', 'parentId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('8584f10658b5491b87cea837dbabca76', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '10', 'description', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('386b4debb4684b7db5c8faf235405df4', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '11', 'fileId', 1, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('0edd620e35d947b58f37dc5cb68cbf90', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '12', 'attachTypeId', 1, 'PathRenderer', 'SelectionDetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('09a8275119d2425986ca100e5011ac64', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '13', 'fileRef', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('bd102570e8674d2d835f4dbe166e1244', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '14', 'fileVer', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('4acad326aacf40e5a381244b2c4948ce', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '15', 'fileRef2', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('7a8fccec3d5b4882901d6122658145a4', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '16', 'fileDuid', 0, 'PathRenderer', 'DetailRenderer');

INSERT INTO CNT_HISTORY_DEF_ITEM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID , IS_FOR_REFERENCE , PARENT_ID, LINE_NO, FIELD_ID, TRACKING_LEVEL, PATH_RENDERER_CLASS, DETAIL_RENDERER_CLASS)
VALUES ('a8c7858cc7a8466a882c0ed4bcfdab99', 0, 1, '/', '/', '0', 'b45adec6151b49dfa1ac4e8383e740be', '17', 'attachTypeIdValue', 0, 'PathRenderer', 'DetailRenderer');

