--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'vpo', 'lbl.vpo.tabItem.vpoItem.sellPrice', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.vpo.tabItem.vpoItem.sellPrice' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'vpo', 'lbl.vpo.tabHeader.orderAmtSection.currency', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.vpo.tabHeader.orderAmtSection.currency' AND LOCALE='en_US');

update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.vpo.tabItem.vpoItem.sellPrice' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.vpo.tabHeader.orderAmtSection.currency' and domain_id = 'PEPL' and locale = 'en_US';