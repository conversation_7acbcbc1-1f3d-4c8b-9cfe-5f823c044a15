--// FILE: DML_VALIDATION_item.sql

DELETE FROM CNT_VALIDATION_FIELD_PARAM WHERE VALIDATION_FIELD_ID IN
(SELECT VF.ID FROM CNT_VALIDATION_FIELD VF JOIN CNT_VALIDATION_RULE VR
ON VF.VALIDATION_RULE_ID = VR.ID JOIN CNT_VALIDATION_PROFILE VP
ON VR.PROFILE_ID = VP.ID WHERE VP.REF_ENTITY_NAME = 'Item' AND VP.DOMAIN_ID = 'PEPL');

DELETE FROM CNT_VALIDATION_FIELD WHERE VALIDATION_RULE_ID IN
(SELECT VR.ID FROM CNT_VALIDATION_RULE VR
JOIN CNT_VALIDATION_PROFILE VP
ON VR.PROFILE_ID = VP.ID WHERE VP.REF_ENTITY_NAME = 'Item' AND VP.DOMAIN_ID = 'PEPL');

DELETE FROM CNT_VALIDATION_RULE WHERE PROFILE_ID IN
(SELECT VP.ID FROM CNT_VALIDATION_PROFILE VP
WHERE VP.REF_ENTITY_NAME = 'Item' AND VP.DOMAIN_ID = 'PEPL');

DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY = (SELECT ENTITY_NAME FROM CNT_ENTITY_DEFINITION WHERE PRODUCT_TABLE_NAME = 'CNT_VALIDATION_PROFILE') AND TARGET_ID IN (SELECT ID FROM CNT_VALIDATION_PROFILE WHERE REF_ENTITY_NAME = 'Item' AND DOMAIN_ID = 'PEPL');

DELETE FROM CNT_VALIDATION_PROFILE WHERE REF_ENTITY_NAME = 'Item' AND DOMAIN_ID = 'PEPL';

INSERT INTO CNT_VALIDATION_PROFILE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, UPDATE_USER, UPDATED_ON, IS_LATEST, MAX_ERROR, CONSOLIDATE_ERROR, PRIORITY, IGNORE_CUSTOM_FIELD, PROFILE_NAME, INHERIT_FROM, REF_NO, REF_ENTITY_NAME, REF_ENTITY_VERSION, ACTION, ENABLED, CREATE_USER, CREATED_ON)
VALUES ('bf2f986f98924a79916c8a0b2faf46e5', 0, 1, 'PEPL', 'PEPL', '0', NULL, NULL, NULL, NULL, NULL, TO_TIMESTAMP('2025-08-28 14:58:38', 'YYYY-MM-DD HH24:MI:SS'), '1', 0, '0', 0, '0', 'Default Data Validation Profile Item[ver:1]', NULL, 'Default Data Validation Profile Item[ver:1]', 'Item', 1, 'MarkAsConcept,MarkAsCosting,MarkAsAdopted,MarkAsFinalized,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,MarkAsCustomStatus06Doc,MarkAsCustomStatus07Doc,MarkAsCustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc', '1', 'system', TO_TIMESTAMP('2025-08-28 14:58:38', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('6712bf49084f4d8b862e47c0c825b723', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 1, 'MandatoryValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryValidator', '1', 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ef079203a99b4940aed3a6b7088531f5', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'itemDesc', 'entity.itemDesc', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9b7b007870fa45eba3788168009da9b1', 0, 1, 'PEPL', 'PEPL', '0', 'ef079203a99b4940aed3a6b7088531f5', 'LABEL_FIELD_ID', 'itemDesc');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('af3bbdb03ae54acf9b8bda225b7dbc9e', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'hierarchy', 'entity.hierarchy', 2, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a8db6b3990a5452b9de6a76e79a5ea60', 0, 1, 'PEPL', 'PEPL', '0', 'af3bbdb03ae54acf9b8bda225b7dbc9e', 'LABEL_FIELD_ID', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b4b6e9971857441393968f164a5ea022', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'productCategory', 'entity.productCategory', 3, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fd62d37776274dda8ef4dc64ea6ad0a9', 0, 1, 'PEPL', 'PEPL', '0', 'b4b6e9971857441393968f164a5ea022', 'LABEL_FIELD_ID', 'productCategory');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4b482fe4a26c461abba14df471e8e908', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'itemNo', 'entity.itemNo', 4, '0', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6a280e1189244a8cb66643d4f7650eca', 0, 1, 'PEPL', 'PEPL', '0', '4b482fe4a26c461abba14df471e8e908', 'LABEL_FIELD_ID', 'itemNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4b7a0e856f8c4b17ad2bda7b8c33b7b3', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'season', 'entity.season', 5, '1', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('601dfe8e0bed43778fc836a091b34178', 0, 1, 'PEPL', 'PEPL', '0', '4b7a0e856f8c4b17ad2bda7b8c33b7b3', 'LABEL_FIELD_ID', 'season');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c18802ec6c6a41c3bbbbce5378842931', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'buyerItemNo', 'entity.buyerItemNo', 6, '1', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('41c3e47a677f458690cb8813b12563d9', 0, 1, 'PEPL', 'PEPL', '0', 'c18802ec6c6a41c3bbbbce5378842931', 'LABEL_FIELD_ID', 'buyerItemNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('e4c5de505d98449ba671cf144202cbaa', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'custHcl1', 'entity.custHcl1', 7, '0', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isNotNewDoc' AND IS_LATEST = '1'), NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ded5a482625b4cf28a5581f23bbf5c06', 0, 1, 'PEPL', 'PEPL', '0', 'e4c5de505d98449ba671cf144202cbaa', 'LABEL_FIELD_ID', 'custHcl1');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9470689ef0284a1f8bf49d393a03f89a', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemCustFinalDest', 'countryOfDestination', 'entity.itemCustFinalDest.countryOfDestination', 8, '0', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('706d526827a844408275033b2b7f2b58', 0, 1, 'PEPL', 'PEPL', '0', '9470689ef0284a1f8bf49d393a03f89a', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e766e76e629e4f1187a89ab9390bf566', 0, 1, 'PEPL', 'PEPL', '0', '9470689ef0284a1f8bf49d393a03f89a', 'LABEL_FIELD_ID', 'countryOfDestination');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('001b2704f15941a0bfcd035af4c33571', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemCustFinalDest', 'portOfDischarge', 'entity.itemCustFinalDest.portOfDischarge', 9, '0', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('005a882e6ff7422fb35b396bea209f6d', 0, 1, 'PEPL', 'PEPL', '0', '001b2704f15941a0bfcd035af4c33571', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('820deb2fa5834528bf8f9a045ffe3b8d', 0, 1, 'PEPL', 'PEPL', '0', '001b2704f15941a0bfcd035af4c33571', 'LABEL_FIELD_ID', 'portOfDischarge');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('7df722ff61b44b0681266705395245d5', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemCustFinalDest', 'finalDestination', 'entity.itemCustFinalDest.finalDestination', 10, '0', NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1ff865f731234a9eb4173c6f4e64d69c', 0, 1, 'PEPL', 'PEPL', '0', '7df722ff61b44b0681266705395245d5', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('29dc3575661f447293a74bf2f3d77b22', 0, 1, 'PEPL', 'PEPL', '0', '7df722ff61b44b0681266705395245d5', 'LABEL_FIELD_ID', 'finalDestination');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('454c07e725a64c49b2e0a577f60723cd', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemImage', 'fileId', 'entity.itemImage.fileId', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('202852b86782499d9b0caeffd1f8d3d0', 0, 1, 'PEPL', 'PEPL', '0', '454c07e725a64c49b2e0a577f60723cd', 'GRID_ID', 'itemImage');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3144c4cfa3264afd891c58b0dcd39a39', 0, 1, 'PEPL', 'PEPL', '0', '454c07e725a64c49b2e0a577f60723cd', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4792e262d7ab4d999bb57a5cf0179118', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemAttachment', 'fileId', 'entity.itemAttachment.fileId', 12, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isAttachTypeIdCER' AND IS_LATEST = '1'), NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8ce1cf47312d4539bdc62b282d3b8d1d', 0, 1, 'PEPL', 'PEPL', '0', '4792e262d7ab4d999bb57a5cf0179118', 'GRID_ID', 'itemAttachment');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('201460567a134010a06979c0f5008c35', 0, 1, 'PEPL', 'PEPL', '0', '4792e262d7ab4d999bb57a5cf0179118', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('25c57aebe3774152a0e32289d6e983aa', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecRequirement', 'seqNo', 'entity.specRequirement.seqNo', 13, '0', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ebb500c4d39c4b1f84cb565704062aec', 0, 1, 'PEPL', 'PEPL', '0', '25c57aebe3774152a0e32289d6e983aa', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1e7153103ec24603b5a42d325645580c', 0, 1, 'PEPL', 'PEPL', '0', '25c57aebe3774152a0e32289d6e983aa', 'LABEL_FIELD_ID', 'seqNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('936234d3fdea4a039ff9503f7ac38123', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecColorBom', 'specMaterial', 'entity.specColorBom.specMaterial', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('95d90131ac694ea8bae79436e049fcff', 0, 1, 'PEPL', 'PEPL', '0', '936234d3fdea4a039ff9503f7ac38123', 'GRID_ID', 'specColorBom');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e8cadc6571364527bf5dc259ff51d7a9', 0, 1, 'PEPL', 'PEPL', '0', '936234d3fdea4a039ff9503f7ac38123', 'LABEL_FIELD_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('22efc8655790436e83dc4f4ba1527d55', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecArtworkBom', 'artworkName', 'entity.specArtworkBom.artworkName', 15, '1', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('60e88c8db97b41fb88ac05a5b062d382', 0, 1, 'PEPL', 'PEPL', '0', '22efc8655790436e83dc4f4ba1527d55', 'GRID_ID', 'specArtworkBom');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2d3841fb2ce0420cbccd6d3ae70570ea', 0, 1, 'PEPL', 'PEPL', '0', '22efc8655790436e83dc4f4ba1527d55', 'LABEL_FIELD_ID', 'artworkName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('bc5056941a484656a459043022ee1780', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemColor', 'shortName', 'entity.itemColor.shortName', 16, '1', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b1e5531bb02444ed9ceed62e9ffc1b71', 0, 1, 'PEPL', 'PEPL', '0', 'bc5056941a484656a459043022ee1780', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f7968f4e22984a3182837b76632e05ae', 0, 1, 'PEPL', 'PEPL', '0', 'bc5056941a484656a459043022ee1780', 'LABEL_FIELD_ID', 'shortName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('3b21101c64b6404f949a774aa64257ab', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemSize', 'dimension', 'entity.itemSize.dimension', 17, '1', NULL, 17);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('02f83d1c35cb4d83a6107bc0e0eb1400', 0, 1, 'PEPL', 'PEPL', '0', '3b21101c64b6404f949a774aa64257ab', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0a3bfacc143a4932bf9b3f689d3a218f', 0, 1, 'PEPL', 'PEPL', '0', '3b21101c64b6404f949a774aa64257ab', 'LABEL_FIELD_ID', 'dimension');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('13986df0ad454e48acd7c467bbbb1ed0', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'ItemSize', 'sizeDisplayName', 'entity.itemSize.sizeDisplayName', 18, '1', NULL, 18);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('61a6537210724c43bfcf1993fb8a77c2', 0, 1, 'PEPL', 'PEPL', '0', '13986df0ad454e48acd7c467bbbb1ed0', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e4c4809c68654d51abbac4ad654bf955', 0, 1, 'PEPL', 'PEPL', '0', '13986df0ad454e48acd7c467bbbb1ed0', 'LABEL_FIELD_ID', 'sizeDisplayName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('611c44524f6848aead6457f2b20580fe', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'Item', 'itemSize', 'entity.itemSize', 19, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'validateClothingDivision' AND IS_LATEST = '1'), NULL, 19);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5b4a5cb438f54fb98d52530540eea189', 0, 1, 'PEPL', 'PEPL', '0', '611c44524f6848aead6457f2b20580fe', 'LABEL_FIELD_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5041d871838f4a2d86dc04169a6dd189', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecRequirement', 'details', 'entity.specRequirement.details', 20, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isSpecRequirementMandatory' AND IS_LATEST = '1'), 1, 20);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f63edb06fd9044db99f5748d847a25dc', 0, 1, 'PEPL', 'PEPL', '0', '5041d871838f4a2d86dc04169a6dd189', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e4cd2b886a814357b62980e7febd3e72', 0, 1, 'PEPL', 'PEPL', '0', '5041d871838f4a2d86dc04169a6dd189', 'LABEL_FIELD_ID', 'details');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c15e4c7075f34d62b41c3036fdcd91fb', 0, 1, 'PEPL', 'PEPL', '0', '5041d871838f4a2d86dc04169a6dd189', 'POSITION_LABEL_FIELD_ID', 'seqNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2908f97db9014ae0bbe6791d45fae3dc', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecDesign', 'seq', 'entity.specDesign.seq', 21, '0', NULL, 21);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('957f3939521042b18f73f6363370255a', 0, 1, 'PEPL', 'PEPL', '0', '2908f97db9014ae0bbe6791d45fae3dc', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5b1035b289cc45c48394ff10e67fb75c', 0, 1, 'PEPL', 'PEPL', '0', '2908f97db9014ae0bbe6791d45fae3dc', 'LABEL_FIELD_ID', 'seq');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('2cc291e62f52495bb308298077b6e6eb', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecDesign', 'imageType', 'entity.specDesign.imageType', 22, '0', NULL, 22);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2bc029897f4c4287bec1e2778a432cf0', 0, 1, 'PEPL', 'PEPL', '0', '2cc291e62f52495bb308298077b6e6eb', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7b41a9e3db934cf993bf9e82a4dd49e1', 0, 1, 'PEPL', 'PEPL', '0', '2cc291e62f52495bb308298077b6e6eb', 'LABEL_FIELD_ID', 'imageType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('dd872b39cf114a2e906357870cc9687f', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecDesign', 'imageId', 'entity.specDesign.imageId', 23, '0', NULL, 23);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('264727c6eea9477a8d1914750c5c343c', 0, 1, 'PEPL', 'PEPL', '0', 'dd872b39cf114a2e906357870cc9687f', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a04a8dda071247289844bc87cae2e713', 0, 1, 'PEPL', 'PEPL', '0', 'dd872b39cf114a2e906357870cc9687f', 'LABEL_FIELD_ID', 'imageId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f9714ddc156f400a8603c26a4d642f4d', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecMaterial', 'materialName', 'entity.specMaterial.materialName', 24, '1', NULL, 24);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('be59e0fb21a6446595ff6c52403a7656', 0, 1, 'PEPL', 'PEPL', '0', 'f9714ddc156f400a8603c26a4d642f4d', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0a18f7320874470d8a498482d243054c', 0, 1, 'PEPL', 'PEPL', '0', 'f9714ddc156f400a8603c26a4d642f4d', 'LABEL_FIELD_ID', 'materialName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1b179d8af26d4c4fb891aaf484795b3f', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecMaterial', 'materialType', 'entity.specMaterial.materialType', 25, '0', NULL, 25);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ef40d69ab5bb4353bfb48a24c5837990', 0, 1, 'PEPL', 'PEPL', '0', '1b179d8af26d4c4fb891aaf484795b3f', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('189388088ac9489397e5e55e28dea02e', 0, 1, 'PEPL', 'PEPL', '0', '1b179d8af26d4c4fb891aaf484795b3f', 'LABEL_FIELD_ID', 'materialType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('86aa7dcbcb6a4e9ab7193d2cd12d55e2', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecConstruction', 'constructionElement', 'entity.specConstruction.constructionElement', 26, '1', NULL, 26);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8e7edffb3c244d7e9b6f409f9fbbd07d', 0, 1, 'PEPL', 'PEPL', '0', '86aa7dcbcb6a4e9ab7193d2cd12d55e2', 'GRID_ID', 'specConstruction');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('91015d6f08ba4512816b10fae88ca5c4', 0, 1, 'PEPL', 'PEPL', '0', '86aa7dcbcb6a4e9ab7193d2cd12d55e2', 'LABEL_FIELD_ID', 'constructionElement');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c66964e7ffa24a75b2697b0a5577ac72', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecConstruction', 'type', 'entity.specConstruction.type', 27, '1', NULL, 27);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('be18bedb25684b2e8b9c9c6871e0e9f1', 0, 1, 'PEPL', 'PEPL', '0', 'c66964e7ffa24a75b2697b0a5577ac72', 'GRID_ID', 'specConstruction');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('68926ee0d31a4065b24e1aea0d038146', 0, 1, 'PEPL', 'PEPL', '0', 'c66964e7ffa24a75b2697b0a5577ac72', 'LABEL_FIELD_ID', 'type');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c2e1b7adf7a54b71bbc321c46ae4c2a5', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecGradingRule', 'seq', 'entity.specGradingRule.seq', 28, '1', NULL, 28);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6507bba20e6045f8bdb7597f26093611', 0, 1, 'PEPL', 'PEPL', '0', 'c2e1b7adf7a54b71bbc321c46ae4c2a5', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a0303a2437cd40328d120158a3654488', 0, 1, 'PEPL', 'PEPL', '0', 'c2e1b7adf7a54b71bbc321c46ae4c2a5', 'LABEL_FIELD_ID', 'seq');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d93a1006ad30473f9bc7ca16e21e60b0', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecGradingRule', 'code', 'entity.specGradingRule.code', 29, '1', NULL, 29);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('093fa27d1e2d45fbae03872c2679b75d', 0, 1, 'PEPL', 'PEPL', '0', 'd93a1006ad30473f9bc7ca16e21e60b0', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1b607b01cdd248cb9219c0dcc7901416', 0, 1, 'PEPL', 'PEPL', '0', 'd93a1006ad30473f9bc7ca16e21e60b0', 'LABEL_FIELD_ID', 'code');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('391285966d3f42d1bc4ca197bd5d0e65', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecGradingRule', 'description', 'entity.specGradingRule.description', 30, '1', NULL, 30);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cf2b55e82a5d4fdbbb919342fe571867', 0, 1, 'PEPL', 'PEPL', '0', '391285966d3f42d1bc4ca197bd5d0e65', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('49b6698a72214274af59c05511e93852', 0, 1, 'PEPL', 'PEPL', '0', '391285966d3f42d1bc4ca197bd5d0e65', 'LABEL_FIELD_ID', 'description');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4a7caa1c1bdd40f19f9ded8ff8578fa4', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecAccessoriesMeasurement', 'seq', 'entity.specAccessoriesMeasurement.seq', 31, '0', NULL, 31);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9a28aebca6c34d54aee1c8e6f831992a', 0, 1, 'PEPL', 'PEPL', '0', '4a7caa1c1bdd40f19f9ded8ff8578fa4', 'GRID_ID', 'specAccessoriesMeasurements');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2ff6030a2824447787d84f7b69f39dc0', 0, 1, 'PEPL', 'PEPL', '0', '4a7caa1c1bdd40f19f9ded8ff8578fa4', 'LABEL_FIELD_ID', 'seq');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('811bc59afcbf48dea4c0c9bdf1d1e76a', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecAccessoriesMeasurement', 'description', 'entity.specAccessoriesMeasurement.description', 32, '1', NULL, 32);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fbb27ed8f1834d9094de41a7dbd4cffb', 0, 1, 'PEPL', 'PEPL', '0', '811bc59afcbf48dea4c0c9bdf1d1e76a', 'GRID_ID', 'specAccessoriesMeasurements');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3c74e40d4d294fe7a18a523e3d0b139b', 0, 1, 'PEPL', 'PEPL', '0', '811bc59afcbf48dea4c0c9bdf1d1e76a', 'LABEL_FIELD_ID', 'description');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c3d604d39a5240778e33af9688bd37d8', 0, 1, 'PEPL', 'PEPL', '0', '6712bf49084f4d8b862e47c0c825b723', 'SpecInstruction', 'type', 'entity.specInstruction.type', 33, '0', NULL, 33);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6e9779471e914d5e887ab176e706046f', 0, 1, 'PEPL', 'PEPL', '0', 'c3d604d39a5240778e33af9688bd37d8', 'GRID_ID', 'specInstruction');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5966c82f8874400b816ae7bda910e623', 0, 1, 'PEPL', 'PEPL', '0', 'c3d604d39a5240778e33af9688bd37d8', 'LABEL_FIELD_ID', 'type');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('d756f889f44145ec9e276d26d74b3458', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 2, 'UniqueInModuleValidator', 'com.core.cbx.validation.validator.UniqueInModuleValidator', 'UniqueInModuleValidator', '1', 2);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c9779fbb5e344f7dbda8105f8f89b28b', 0, 1, 'PEPL', 'PEPL', '0', 'd756f889f44145ec9e276d26d74b3458', 'Item', 'itemNo', 'entity.itemNo', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('eb152b5e221542abadda964eb99e8477', 0, 1, 'PEPL', 'PEPL', '0', 'c9779fbb5e344f7dbda8105f8f89b28b', 'CHECK_LATEST_VERSION', 'Y');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('0efc4f5ba3f84234a3782340075b6fac', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 3, 'UniqueInSectionValidator', 'com.core.cbx.validation.validator.UniqueInSectionValidator', 'UniqueInSectionValidator', '1', 3);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('64399f9bda564681b6b2b788ebae215a', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemCust', 'cust', 'entity.itemCust.cust', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9a88618c98c74ce198b09f643c77cff0', 0, 1, 'PEPL', 'PEPL', '0', '64399f9bda564681b6b2b788ebae215a', 'ERROR_ID', '08010088');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b596d38035f747d5bb087cb66e5ed6d3', 0, 1, 'PEPL', 'PEPL', '0', '64399f9bda564681b6b2b788ebae215a', 'FIELD_GROUP', 'cust,market,channel');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('551c39c3f24445c2bc49430785170c9f', 0, 1, 'PEPL', 'PEPL', '0', '64399f9bda564681b6b2b788ebae215a', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d0d41de626954b70b727ababe22ccf7c', 0, 1, 'PEPL', 'PEPL', '0', '64399f9bda564681b6b2b788ebae215a', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('61480ff0add64dc2ab3251ff8503e668', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemCustFinalDest', 'cust', 'entity.itemCustFinalDest.cust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3a05fc268efe4250a5b4f5cb95dcb324', 0, 1, 'PEPL', 'PEPL', '0', '61480ff0add64dc2ab3251ff8503e668', 'ERROR_ID', '08010090');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('45b26a97aff04672841a0e79e7ca3cf8', 0, 1, 'PEPL', 'PEPL', '0', '61480ff0add64dc2ab3251ff8503e668', 'FIELD_GROUP', 'cust,portOfDischarge,finalDestination');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('225534e0014e4dd7ae5c3ccb4edd03fb', 0, 1, 'PEPL', 'PEPL', '0', '61480ff0add64dc2ab3251ff8503e668', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d839a105e76246cb92bfffcb58b847ef', 0, 1, 'PEPL', 'PEPL', '0', '61480ff0add64dc2ab3251ff8503e668', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('db255dc48cdb458492e93a64ab328257', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecAccessoriesMeasurement', 'refKey', 'entity.specAccessoriesMeasurement.refKey', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3d9c58dfc2a04c98b1145f0228057176', 0, 1, 'PEPL', 'PEPL', '0', 'db255dc48cdb458492e93a64ab328257', 'GRID_ID', 'specAccessoriesMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e0fb6d843d42466d90099373eb339efe', 0, 1, 'PEPL', 'PEPL', '0', 'db255dc48cdb458492e93a64ab328257', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a552514392bb45e88382036ca834715a', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecAccessoriesMeasurement', 'seq', 'entity.specAccessoriesMeasurement.seq', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ac526405592449c09b7a028580aee5f3', 0, 1, 'PEPL', 'PEPL', '0', 'a552514392bb45e88382036ca834715a', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('574e23e17fe1446ca27836b30048f444', 0, 1, 'PEPL', 'PEPL', '0', 'a552514392bb45e88382036ca834715a', 'GRID_ID', 'specAccessoriesMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9663c329a48544c087a1ab5ab0fe9e8f', 0, 1, 'PEPL', 'PEPL', '0', 'a552514392bb45e88382036ca834715a', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('05e6ae9186864c8e85a10abbcdf66997', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecGradingRule', 'code', 'entity.specGradingRule.code', 5, '1', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4d6dd652b8454a4b9abcaf65e9aca417', 0, 1, 'PEPL', 'PEPL', '0', '05e6ae9186864c8e85a10abbcdf66997', 'ERROR_ID', 'pepl_08010003');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1f17dfc7b0a2459fbd9c240497d9c628', 0, 1, 'PEPL', 'PEPL', '0', '05e6ae9186864c8e85a10abbcdf66997', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('be164e705b9d4e28b715800055395a9d', 0, 1, 'PEPL', 'PEPL', '0', '05e6ae9186864c8e85a10abbcdf66997', 'LABEL_FIELD_ID', 'code');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6838f578a9304e499697c4211d1c0730', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecGradingRule', 'refKey', 'entity.specGradingRule.refKey', 6, '1', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b473118fedf9414d858d37b04935e913', 0, 1, 'PEPL', 'PEPL', '0', '6838f578a9304e499697c4211d1c0730', 'GRID_ID', 'specGradingRule');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4d5d451453c74b059d40118d4b6f8336', 0, 1, 'PEPL', 'PEPL', '0', '6838f578a9304e499697c4211d1c0730', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d1e6858045c24ca8a9032abf0b22eaea', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecGradingRule', 'seq', 'entity.specGradingRule.seq', 7, '1', NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7eda3319160c499bbb9bd973588090d1', 0, 1, 'PEPL', 'PEPL', '0', 'd1e6858045c24ca8a9032abf0b22eaea', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1735c504ee3246299aa81231677c031a', 0, 1, 'PEPL', 'PEPL', '0', 'd1e6858045c24ca8a9032abf0b22eaea', 'GRID_ID', 'specGradingRule');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('42046b9c9c9d4a68b87d3c4bb33c81e5', 0, 1, 'PEPL', 'PEPL', '0', 'd1e6858045c24ca8a9032abf0b22eaea', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9967dd3bc8554988997f385e894374ce', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecMeasurement', 'refKey', 'entity.specMeasurement.refKey', 8, '1', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8ffcc2541c314328922f23f5cfaf2c53', 0, 1, 'PEPL', 'PEPL', '0', '9967dd3bc8554988997f385e894374ce', 'GRID_ID', 'specMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('506de8d15873443e8db94f4741f3d2a3', 0, 1, 'PEPL', 'PEPL', '0', '9967dd3bc8554988997f385e894374ce', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('03631eac8dfe4321aedbd44a375bafbd', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecOther', 'seq', 'entity.specOther.seq', 9, '1', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c7e31f7f4fb24d2588f1c67c6c5b4f49', 0, 1, 'PEPL', 'PEPL', '0', '03631eac8dfe4321aedbd44a375bafbd', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('780e5640638e494eb4d8a6068df926b8', 0, 1, 'PEPL', 'PEPL', '0', '03631eac8dfe4321aedbd44a375bafbd', 'GRID_ID', 'specOther');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0fbba4a615d6444ab63749adc2a8ddd3', 0, 1, 'PEPL', 'PEPL', '0', '03631eac8dfe4321aedbd44a375bafbd', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('57fd202eb8a24f0ba0e1723c3cb9a202', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecTreatment', 'seq', 'entity.specTreatment.seq', 10, '1', NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('61bf788030234fa6a627d52e83817e68', 0, 1, 'PEPL', 'PEPL', '0', '57fd202eb8a24f0ba0e1723c3cb9a202', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f90296ff6a2a40309a304fa7aade9268', 0, 1, 'PEPL', 'PEPL', '0', '57fd202eb8a24f0ba0e1723c3cb9a202', 'GRID_ID', 'specTreatment');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('51387db11d2d457eae3c6f9378cf56e3', 0, 1, 'PEPL', 'PEPL', '0', '57fd202eb8a24f0ba0e1723c3cb9a202', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('cddacd837cf6468b9cc89e114d2d7417', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecDesign', 'seq', 'entity.specDesign.seq', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3aaa1210b2c1405b9546ed658ad6ae5f', 0, 1, 'PEPL', 'PEPL', '0', 'cddacd837cf6468b9cc89e114d2d7417', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3f4b66ea35c64a00ae0ecb620aed8138', 0, 1, 'PEPL', 'PEPL', '0', 'cddacd837cf6468b9cc89e114d2d7417', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3fa121018a4e4d95844243f2d56b8a1f', 0, 1, 'PEPL', 'PEPL', '0', 'cddacd837cf6468b9cc89e114d2d7417', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('fb10d402f9c34abda8d6701c90a5baba', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecColorBom', 'seq', 'entity.specColorBom.seq', 12, '1', NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a82f8992b6d544ecbf47ba78198e811a', 0, 1, 'PEPL', 'PEPL', '0', 'fb10d402f9c34abda8d6701c90a5baba', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c51968e8aeb44c4e940c1cb82c781806', 0, 1, 'PEPL', 'PEPL', '0', 'fb10d402f9c34abda8d6701c90a5baba', 'GRID_ID', 'specColorBom');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('46cb56c17daa48b99c381e1c812aef0c', 0, 1, 'PEPL', 'PEPL', '0', 'fb10d402f9c34abda8d6701c90a5baba', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('49d75bdcf9684fa584b418d66abf94ca', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecMaterial', 'materialName', 'entity.specMaterial.materialName', 13, '1', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bc71e6b9c90f4cbc97e8b871fbe7fc95', 0, 1, 'PEPL', 'PEPL', '0', '49d75bdcf9684fa584b418d66abf94ca', 'ERROR_ID', '16052504');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d32fe862f8eb4301840e220b06722cda', 0, 1, 'PEPL', 'PEPL', '0', '49d75bdcf9684fa584b418d66abf94ca', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c46cf3c1d67e45faa26c9b1850f4abd4', 0, 1, 'PEPL', 'PEPL', '0', '49d75bdcf9684fa584b418d66abf94ca', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b251a08306ff40ad9c3cd170ea205358', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecMaterial', 'seq', 'entity.specMaterial.seq', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2ad9390af7ec43f18487ff14ed21b706', 0, 1, 'PEPL', 'PEPL', '0', 'b251a08306ff40ad9c3cd170ea205358', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9fbdbeaa4ebb431887601efbcf4185b0', 0, 1, 'PEPL', 'PEPL', '0', 'b251a08306ff40ad9c3cd170ea205358', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8730767750fd406db1d689f57d94791d', 0, 1, 'PEPL', 'PEPL', '0', 'b251a08306ff40ad9c3cd170ea205358', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('0636db9bcef74dcd9000582ce8b01639', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecRequirement', 'category', 'entity.specRequirement.category', 15, '0', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b7e74e70e9f549f599db44b87f2e11f9', 0, 1, 'PEPL', 'PEPL', '0', '0636db9bcef74dcd9000582ce8b01639', 'ERROR_ID', '08010100');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b2876ce5d1b142bdb8613c7b12272a9f', 0, 1, 'PEPL', 'PEPL', '0', '0636db9bcef74dcd9000582ce8b01639', 'FIELD_GROUP', 'category,type,description');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('27648fa9fb2744a59a984cf804d788de', 0, 1, 'PEPL', 'PEPL', '0', '0636db9bcef74dcd9000582ce8b01639', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fc2d15933e2841a8a954c0c3ca82a8ab', 0, 1, 'PEPL', 'PEPL', '0', '0636db9bcef74dcd9000582ce8b01639', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('13d31324735149308ced9988ab0ae352', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'SpecRequirement', 'seqNo', 'entity.specRequirement.seqNo', 16, '1', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f137e93453e04a6aa678425d3641706d', 0, 1, 'PEPL', 'PEPL', '0', '13d31324735149308ced9988ab0ae352', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7116c24f65fd47d78cb3c334c7979b3a', 0, 1, 'PEPL', 'PEPL', '0', '13d31324735149308ced9988ab0ae352', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7ba278dc85104434ab05605c7acfacdd', 0, 1, 'PEPL', 'PEPL', '0', '13d31324735149308ced9988ab0ae352', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5a711058c98c4da2a585a9e131dbfeea', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemColor', 'colorSeq', 'entity.itemColor.colorSeq', 17, '1', NULL, 17);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1c8ae0890940437d84feaa2799a74bb3', 0, 1, 'PEPL', 'PEPL', '0', '5a711058c98c4da2a585a9e131dbfeea', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('32c2ae3c265d41e8a028b429f29d36b1', 0, 1, 'PEPL', 'PEPL', '0', '5a711058c98c4da2a585a9e131dbfeea', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d959e48164c14e109035854284fc0b22', 0, 1, 'PEPL', 'PEPL', '0', '5a711058c98c4da2a585a9e131dbfeea', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a8eb613aab8d4b62ae7ab8f5a5afe58a', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemSize', 'sizeSeq', 'entity.itemSize.sizeSeq', 18, '1', NULL, 18);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a92c4a449c0f4f6b9a9702fb66216381', 0, 1, 'PEPL', 'PEPL', '0', 'a8eb613aab8d4b62ae7ab8f5a5afe58a', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5af5c39222194727bf232f772ec7297e', 0, 1, 'PEPL', 'PEPL', '0', 'a8eb613aab8d4b62ae7ab8f5a5afe58a', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('618b3a6458d04986a4e5b82c7e32b765', 0, 1, 'PEPL', 'PEPL', '0', 'a8eb613aab8d4b62ae7ab8f5a5afe58a', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f01de38c57d549fb8c7e25599fef86e1', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemVendorFact', 'vendor', 'entity.itemVendorFact.vendor', 19, '1', NULL, 19);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('85e2097180804176bb67b30f8a293b7b', 0, 1, 'PEPL', 'PEPL', '0', 'f01de38c57d549fb8c7e25599fef86e1', 'FIELD_GROUP', 'vendor,fact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8254f92950004ce9bfde9cde9c5d2d84', 0, 1, 'PEPL', 'PEPL', '0', 'f01de38c57d549fb8c7e25599fef86e1', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('64d610bbb96c4b06a4b19c7b6665bf1c', 0, 1, 'PEPL', 'PEPL', '0', 'f01de38c57d549fb8c7e25599fef86e1', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('0ef3598f43a74b1e8c8051fcf2c2653f', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemRelated', 'relatedItem', 'entity.itemRelated.relatedItem', 20, '1', NULL, 20);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('73a29e6091ed408e90502f2a2f067f50', 0, 1, 'PEPL', 'PEPL', '0', '0ef3598f43a74b1e8c8051fcf2c2653f', 'ERROR_ID', '08010098');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('27d6c1fb4ad44bcca5456983f21f2fc8', 0, 1, 'PEPL', 'PEPL', '0', '0ef3598f43a74b1e8c8051fcf2c2653f', 'FIELD_GROUP', 'relatedItem');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('95e524c5639f4dae8f7687e34646dc46', 0, 1, 'PEPL', 'PEPL', '0', '0ef3598f43a74b1e8c8051fcf2c2653f', 'GRID_ID', 'itemRelated');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a75a74f94135479e968f506532d7cd41', 0, 1, 'PEPL', 'PEPL', '0', '0ef3598f43a74b1e8c8051fcf2c2653f', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f1aff25176b549d2a0227469e68739aa', 0, 1, 'PEPL', 'PEPL', '0', '0efc4f5ba3f84234a3782340075b6fac', 'ItemSize', 'sizeCode', 'entity.itemSize.sizeCode', 21, '1', NULL, 21);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4ee2c33f9de345db8e7d107d0d290cad', 0, 1, 'PEPL', 'PEPL', '0', 'f1aff25176b549d2a0227469e68739aa', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e8bd048a6a4f4636a869c4ca239d7c5f', 0, 1, 'PEPL', 'PEPL', '0', 'f1aff25176b549d2a0227469e68739aa', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('da90df45e4f64e6f8d026c34105d2c64', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 4, 'ItemMarkAsDefaultValidator', 'com.core.cbx.item.action.ItemMarkAsDefaultValidator', 'ItemMarkAsDefaultValidator', '1', 4);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('10ce1dbaf14747aeb44ff5abc1633a5b', 0, 1, 'PEPL', 'PEPL', '0', 'da90df45e4f64e6f8d026c34105d2c64', 'Item', 'itemSourAgent', 'entity.itemSourAgent', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('3d04a45493a346abbf7a18e77d58c709', 0, 1, 'PEPL', 'PEPL', '0', 'da90df45e4f64e6f8d026c34105d2c64', 'Item', 'itemCust', 'entity.itemCust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('62235a5eb36f4350a902cdbe28e402f7', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 5, 'ItemDependenceValidator', 'com.core.cbx.validation.validator.ItemDependenceValidator', 'ItemDependenceValidator', '1', 5);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('7a175015e6e44397a58e8462d4d924c1', 0, 1, 'PEPL', 'PEPL', '0', '62235a5eb36f4350a902cdbe28e402f7', 'ItemCustFinalDest', 'businessName', 'entity.itemCustFinalDest.businessName', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('665a8b66855f4a85a849daeb0dc4dab1', 0, 1, 'PEPL', 'PEPL', '0', '7a175015e6e44397a58e8462d4d924c1', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('04d9128fb05a4e3caa90b0f35cae482b', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 6, 'ExpressionItemCountValidator', 'com.core.cbx.validation.validator.ExpressionItemCountValidator', 'ExpressionItemCountValidator', '1', 6);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('451e203a4fed4696a1553d10bc968673', 0, 1, 'PEPL', 'PEPL', '0', '04d9128fb05a4e3caa90b0f35cae482b', 'Item', 'itemColor', 'entity.itemColor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('414ad44c802b4739bb0ba59b7355be13', 0, 1, 'PEPL', 'PEPL', '0', '451e203a4fed4696a1553d10bc968673', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5d879a4ecd5b4c7d9f8bb05f74d5a777', 0, 1, 'PEPL', 'PEPL', '0', '451e203a4fed4696a1553d10bc968673', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7fe597aa37ce41a3a704b53bdcbb764a', 0, 1, 'PEPL', 'PEPL', '0', '451e203a4fed4696a1553d10bc968673', 'EXPRESSION', 'entity.isPrimary=true');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f1f929f696e04b52b166131d834b0d85', 0, 1, 'PEPL', 'PEPL', '0', '451e203a4fed4696a1553d10bc968673', 'MIN_COUNT', '1');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d25f7c4c5eb14868a79b4c24470237c2', 0, 1, 'PEPL', 'PEPL', '0', '451e203a4fed4696a1553d10bc968673', 'ERROR_ID', '08020009');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('429d47a5221f43cf9da52efeb56c59c8', 0, 1, 'PEPL', 'PEPL', '0', '04d9128fb05a4e3caa90b0f35cae482b', 'Item', 'itemCust', 'entity.itemCust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('82e57403abae4c98853f4822292ba916', 0, 1, 'PEPL', 'PEPL', '0', '429d47a5221f43cf9da52efeb56c59c8', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('23c34b96819744a7917a904f8753095a', 0, 1, 'PEPL', 'PEPL', '0', '429d47a5221f43cf9da52efeb56c59c8', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b65f1cf5aaed442e9a10344edf8c8716', 0, 1, 'PEPL', 'PEPL', '0', '429d47a5221f43cf9da52efeb56c59c8', 'EXPRESSION', 'entity.isDefault=true');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('96aaa52f4bd141e1bc2975f6327cf9f6', 0, 1, 'PEPL', 'PEPL', '0', '429d47a5221f43cf9da52efeb56c59c8', 'MIN_COUNT', '1');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('66a7a401232f453782896f8980d7a8ed', 0, 1, 'PEPL', 'PEPL', '0', '429d47a5221f43cf9da52efeb56c59c8', 'ERROR_ID', '8010091');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1f5fbf29ee7c440a9467422c8e8c4432', 0, 1, 'PEPL', 'PEPL', '0', '04d9128fb05a4e3caa90b0f35cae482b', 'Item', 'itemSourAgent', 'entity.itemSourAgent', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fe8b44174cec496498927e5a67a9f5d0', 0, 1, 'PEPL', 'PEPL', '0', '1f5fbf29ee7c440a9467422c8e8c4432', 'GRID_ID', 'itemSourAgent');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0224c3b39cfe40349899dbefe263fa42', 0, 1, 'PEPL', 'PEPL', '0', '1f5fbf29ee7c440a9467422c8e8c4432', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6244ee2f23414674bd0bc79b4a7ed299', 0, 1, 'PEPL', 'PEPL', '0', '1f5fbf29ee7c440a9467422c8e8c4432', 'EXPRESSION', 'entity.isDefault=true');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fee0d021d2f24e3b99c8c07c97ebc789', 0, 1, 'PEPL', 'PEPL', '0', '1f5fbf29ee7c440a9467422c8e8c4432', 'MIN_COUNT', '1');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b02f38a1137f485da3b360c803e712ea', 0, 1, 'PEPL', 'PEPL', '0', '1f5fbf29ee7c440a9467422c8e8c4432', 'ERROR_ID', '8010091');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('4560f97608f74122a1ca024bded54859', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 7, 'ClassificationValidator', 'com.core.cbx.validation.validator.ClassificationValidator', 'ClassificationValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 7);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4c858ccf679f4b9c8eef5bd5f781841a', 0, 1, 'PEPL', 'PEPL', '0', '4560f97608f74122a1ca024bded54859', 'Item', 'itemCust', 'entity.itemCust', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4d85d907a7b7412989c20ff2d65eb518', 0, 1, 'PEPL', 'PEPL', '0', '4c858ccf679f4b9c8eef5bd5f781841a', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('64ed5df12da14680892bbbbed691bc5c', 0, 1, 'PEPL', 'PEPL', '0', '4c858ccf679f4b9c8eef5bd5f781841a', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('90e75dc0112d446ab87f4978a5cf33dc', 0, 1, 'PEPL', 'PEPL', '0', '4c858ccf679f4b9c8eef5bd5f781841a', 'TARGET_FIELD', 'cust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6d8e11634c0a4f55b5564cd029e7fbe0', 0, 1, 'PEPL', 'PEPL', '0', '4c858ccf679f4b9c8eef5bd5f781841a', 'DOCUMENT_NO', 'custCode');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('08c58688a50a46379565c27c942e9994', 0, 1, 'PEPL', 'PEPL', '0', '4560f97608f74122a1ca024bded54859', 'Item', 'itemVendorFact', 'entity.itemVendorFact', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('62434d9bd27c44ffb79587de781a716c', 0, 1, 'PEPL', 'PEPL', '0', '08c58688a50a46379565c27c942e9994', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5ff8ed341acb47c9b97a247b0a7bdc36', 0, 1, 'PEPL', 'PEPL', '0', '08c58688a50a46379565c27c942e9994', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d9360e3070a14c6ab72a041b2a97db64', 0, 1, 'PEPL', 'PEPL', '0', '08c58688a50a46379565c27c942e9994', 'TARGET_FIELD', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4da62d8f030f4144b4ea495ce9b9e693', 0, 1, 'PEPL', 'PEPL', '0', '08c58688a50a46379565c27c942e9994', 'DOCUMENT_NO', 'vendorCode');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('eb4179ba74f14948a3693f053f92b045', 0, 1, 'PEPL', 'PEPL', '0', '4560f97608f74122a1ca024bded54859', 'Item', 'itemVendorFact', 'entity.itemVendorFact', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4dd362aee6244b978a9865d3ad69434a', 0, 1, 'PEPL', 'PEPL', '0', 'eb4179ba74f14948a3693f053f92b045', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a3665b8b75a348689290078865297e23', 0, 1, 'PEPL', 'PEPL', '0', 'eb4179ba74f14948a3693f053f92b045', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6f308c4af55642c8baa1ee7fa30da5cc', 0, 1, 'PEPL', 'PEPL', '0', 'eb4179ba74f14948a3693f053f92b045', 'TARGET_FIELD', 'fact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0c0cfbb4403b47029ecd8bdcde8c30bf', 0, 1, 'PEPL', 'PEPL', '0', 'eb4179ba74f14948a3693f053f92b045', 'DOCUMENT_NO', 'factCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('dda115bb8bd24f24a92663d64c28ee58', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 8, 'ItemValidatorForColor', 'com.core.cbx.item.validator.ItemValidatorForColorProductCategory', 'ItemValidatorForColorValidator', '0', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 8);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f7a03a0c69874f7f925118f84317e36a', 0, 1, 'PEPL', 'PEPL', '0', 'dda115bb8bd24f24a92663d64c28ee58', 'Item', 'itemColor', 'entity.itemColor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6b104346a8a444829a74c329e6a9880b', 0, 1, 'PEPL', 'PEPL', '0', 'f7a03a0c69874f7f925118f84317e36a', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('14f104bc93ec450f9698d416d00d475d', 0, 1, 'PEPL', 'PEPL', '0', 'f7a03a0c69874f7f925118f84317e36a', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4bb141668e0241f48973ebf6984ed1f6', 0, 1, 'PEPL', 'PEPL', '0', 'f7a03a0c69874f7f925118f84317e36a', 'TARGET_FIELD', 'color');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('edcea28b54864a089f30c264c05baa11', 0, 1, 'PEPL', 'PEPL', '0', 'f7a03a0c69874f7f925118f84317e36a', 'DOCUMENT_NO', 'colorCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('a790e018f0ce43cf8d55924b211f0685', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 9, 'HCLValidator', 'com.core.cbx.validation.validator.HCLValidator', 'HCLValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), 9);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('175273dccd4d42bd9af1151b3c696ae5', 0, 1, 'PEPL', 'PEPL', '0', 'a790e018f0ce43cf8d55924b211f0685', 'ItemCust', 'cust', 'entity.itemCust.cust', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('91b6444ff5134da8bb117dc16738b846', 0, 1, 'PEPL', 'PEPL', '0', '175273dccd4d42bd9af1151b3c696ae5', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('762aef43b75b475c96c7f4a594160f2d', 0, 1, 'PEPL', 'PEPL', '0', '175273dccd4d42bd9af1151b3c696ae5', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d9aaa653dca4495595d518fc3746dcdc', 0, 1, 'PEPL', 'PEPL', '0', '175273dccd4d42bd9af1151b3c696ae5', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('889639a5604f4bc280c3b580bc91e9ee', 0, 1, 'PEPL', 'PEPL', '0', '175273dccd4d42bd9af1151b3c696ae5', 'TARGET_FIELD', 'custHc');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ae57e0dddd0845d08cb2004f3eeea482', 0, 1, 'PEPL', 'PEPL', '0', '175273dccd4d42bd9af1151b3c696ae5', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f2c9ed3d4f2d46fcb59f268d4d84f2de', 0, 1, 'PEPL', 'PEPL', '0', 'a790e018f0ce43cf8d55924b211f0685', 'ItemCustFinalDest', 'cust', 'entity.itemCustFinalDest.cust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('338a2cce41c841f0910650ad955d3807', 0, 1, 'PEPL', 'PEPL', '0', 'f2c9ed3d4f2d46fcb59f268d4d84f2de', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d954bfcfc02c4d01adeb41be60c6a5c5', 0, 1, 'PEPL', 'PEPL', '0', 'f2c9ed3d4f2d46fcb59f268d4d84f2de', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f0e2e372fee0441bb8b54d25ec43649e', 0, 1, 'PEPL', 'PEPL', '0', 'f2c9ed3d4f2d46fcb59f268d4d84f2de', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3f74f6df6f7c4691a9d0a5817fc86364', 0, 1, 'PEPL', 'PEPL', '0', 'f2c9ed3d4f2d46fcb59f268d4d84f2de', 'TARGET_FIELD', 'custHc');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('159b2b1bb09b4738a41123d1f31087e1', 0, 1, 'PEPL', 'PEPL', '0', 'f2c9ed3d4f2d46fcb59f268d4d84f2de', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a2243f6ee13f41ddbaa13390e228e9ed', 0, 1, 'PEPL', 'PEPL', '0', 'a790e018f0ce43cf8d55924b211f0685', 'ItemVendorFact', 'vendor', 'entity.itemVendorFact.vendor', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7db9892c446f4e37bd5b9ee2e7178209', 0, 1, 'PEPL', 'PEPL', '0', 'a2243f6ee13f41ddbaa13390e228e9ed', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9d1aa7e703924f9ab9e64369122f4d8f', 0, 1, 'PEPL', 'PEPL', '0', 'a2243f6ee13f41ddbaa13390e228e9ed', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fdc08ddef95a40e3bd466d1848463781', 0, 1, 'PEPL', 'PEPL', '0', 'a2243f6ee13f41ddbaa13390e228e9ed', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b9a4ced1ddf843c9845d7e7c5d9d3c18', 0, 1, 'PEPL', 'PEPL', '0', 'a2243f6ee13f41ddbaa13390e228e9ed', 'TARGET_FIELD', 'hcs');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5f4783a11147481880ce38035f374dc5', 0, 1, 'PEPL', 'PEPL', '0', 'a2243f6ee13f41ddbaa13390e228e9ed', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('38e19d89992e4e6a8df46bbefa09c952', 0, 1, 'PEPL', 'PEPL', '0', 'a790e018f0ce43cf8d55924b211f0685', 'ItemVendorFact', 'fact', 'entity.itemVendorFact.fact', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('61ddef7164d54ead8ca94bd5b4980404', 0, 1, 'PEPL', 'PEPL', '0', '38e19d89992e4e6a8df46bbefa09c952', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('061121f01c764476a734cab4f9102158', 0, 1, 'PEPL', 'PEPL', '0', '38e19d89992e4e6a8df46bbefa09c952', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('825cfc92759b4fcdad730bd0fe5c7091', 0, 1, 'PEPL', 'PEPL', '0', '38e19d89992e4e6a8df46bbefa09c952', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0b1eb1e6ce6d4b89875e686768a19541', 0, 1, 'PEPL', 'PEPL', '0', '38e19d89992e4e6a8df46bbefa09c952', 'TARGET_FIELD', 'factHc');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('89a59c532cc54c01b2d89b50836f6901', 0, 1, 'PEPL', 'PEPL', '0', '38e19d89992e4e6a8df46bbefa09c952', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('59e77c3c34fe406a89d4d812581dee1c', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 10, 'CheckDigitValidator', 'com.core.cbx.validation.validator.CheckDigitValidator', 'CheckDigitValidator', '0', 10);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8c6621cec039466f850db81fe26e00ed', 0, 1, 'PEPL', 'PEPL', '0', '59e77c3c34fe406a89d4d812581dee1c', 'Item', 'masterUpc', 'entity.masterUpc', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('920fb58d300d4be6b4f36a0dd27893c9', 0, 1, 'PEPL', 'PEPL', '0', '8c6621cec039466f850db81fe26e00ed', 'validateCodelistType', 'UPC_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('205bcc1120244937b1497099e883b97c', 0, 1, 'PEPL', 'PEPL', '0', '8c6621cec039466f850db81fe26e00ed', 'validateBaseField', 'upcType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1d01fc529cf740a89419eaa09cac822f', 0, 1, 'PEPL', 'PEPL', '0', '59e77c3c34fe406a89d4d812581dee1c', 'Item', 'masterEan', 'entity.masterEan', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cda7b35518124cb1828db78678e7655f', 0, 1, 'PEPL', 'PEPL', '0', '1d01fc529cf740a89419eaa09cac822f', 'validateCodelistType', 'EAN_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e4f640b94b5c4ff4b0dd6ad4ace58602', 0, 1, 'PEPL', 'PEPL', '0', '1d01fc529cf740a89419eaa09cac822f', 'validateBaseField', 'eanType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d5a54ca2dcd249c39c1385ebc04f8237', 0, 1, 'PEPL', 'PEPL', '0', '59e77c3c34fe406a89d4d812581dee1c', 'ItemSku', 'upc', 'entity.itemSku.upc', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5aed4db90f1d45449cf3cbfb59855af0', 0, 1, 'PEPL', 'PEPL', '0', 'd5a54ca2dcd249c39c1385ebc04f8237', 'validateCodelistType', 'UPC_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9e4022a120714fd198a2baef56130ca3', 0, 1, 'PEPL', 'PEPL', '0', 'd5a54ca2dcd249c39c1385ebc04f8237', 'validateBaseField', 'upcType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('40df43c3b6104f638c7338111164f2ee', 0, 1, 'PEPL', 'PEPL', '0', '59e77c3c34fe406a89d4d812581dee1c', 'ItemSku', 'ean', 'entity.itemSku.ean', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f9494aa98c8f494f9d059c1175209f48', 0, 1, 'PEPL', 'PEPL', '0', '40df43c3b6104f638c7338111164f2ee', 'validateCodelistType', 'EAN_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2ce99f0665f1409a90f267ae9117a231', 0, 1, 'PEPL', 'PEPL', '0', '40df43c3b6104f638c7338111164f2ee', 'validateBaseField', 'eanType');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('3264078e50984c4488549560c41cd185', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 11, 'NumericRangeValidator', 'com.core.cbx.validation.validator.NumericRangeValidator', 'NumericRangeValidator', '0', 11);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ac2352eda093428d9d8f574bb2ad9fe9', 0, 1, 'PEPL', 'PEPL', '0', '3264078e50984c4488549560c41cd185', 'Item', 'landedCost', 'entity.landedCost', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f0e254436ed741798b06b11cd2c8b16b', 0, 1, 'PEPL', 'PEPL', '0', 'ac2352eda093428d9d8f574bb2ad9fe9', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b8257182ca734c60aa4eb97ec8fccf77', 0, 1, 'PEPL', 'PEPL', '0', 'ac2352eda093428d9d8f574bb2ad9fe9', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ca25f09ee5024d31a13c3a595dcf923a', 0, 1, 'PEPL', 'PEPL', '0', '3264078e50984c4488549560c41cd185', 'Item', 'offerPrice', 'entity.offerPrice', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9337090cd2ba4850b30bc44f983b21be', 0, 1, 'PEPL', 'PEPL', '0', 'ca25f09ee5024d31a13c3a595dcf923a', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9916248aba47443b8456004e086d8911', 0, 1, 'PEPL', 'PEPL', '0', 'ca25f09ee5024d31a13c3a595dcf923a', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1bbfc8539940430b8e33f7a392d1548c', 0, 1, 'PEPL', 'PEPL', '0', '3264078e50984c4488549560c41cd185', 'Item', 'retailPrice', 'entity.retailPrice', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3e9e43e286964de88d43c48305827f7b', 0, 1, 'PEPL', 'PEPL', '0', '1bbfc8539940430b8e33f7a392d1548c', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('289308aee8ce4b878537fa42def21ff6', 0, 1, 'PEPL', 'PEPL', '0', '1bbfc8539940430b8e33f7a392d1548c', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b444e57af78045ff99ecb41b542c118a', 0, 1, 'PEPL', 'PEPL', '0', '3264078e50984c4488549560c41cd185', 'Item', 'initialOrderQty', 'entity.initialOrderQty', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('65c7c3ad857d4ed19cdf5b87882b9412', 0, 1, 'PEPL', 'PEPL', '0', 'b444e57af78045ff99ecb41b542c118a', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d9211b8b9e6e4216960034117ac498c7', 0, 1, 'PEPL', 'PEPL', '0', 'b444e57af78045ff99ecb41b542c118a', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('27ed96d22cad4199b10f2a6c4581c481', 0, 1, 'PEPL', 'PEPL', '0', '3264078e50984c4488549560c41cd185', 'Item', 'totalQty', 'entity.totalQty', 5, '1', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d51791f5532d48838f08db13e6d77048', 0, 1, 'PEPL', 'PEPL', '0', '27ed96d22cad4199b10f2a6c4581c481', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('edc0044918fb4abeb66a3c10ddbd7ebe', 0, 1, 'PEPL', 'PEPL', '0', '27ed96d22cad4199b10f2a6c4581c481', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('e9e54d894fb74f71958c875391fbe0e2', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 12, 'ManualRefreshValidator', 'com.core.cbx.item.validator.ManualRefreshValidator', 'ManualRefreshValidator', '0', 12);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('75f23f8dd09341abaeee1b51dff42336', 0, 1, 'PEPL', 'PEPL', '0', 'e9e54d894fb74f71958c875391fbe0e2', 'Item', 'specMeasurement', 'entity.specMeasurement', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cc6459ff85cc4f11ad65356b94c5310c', 0, 1, 'PEPL', 'PEPL', '0', '75f23f8dd09341abaeee1b51dff42336', 'GRID_ID', 'specMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9a7d844bc8d5460a818d92555dc95e24', 0, 1, 'PEPL', 'PEPL', '0', '75f23f8dd09341abaeee1b51dff42336', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9e9449ee92c545baa655b8d40fee71b0', 0, 1, 'PEPL', 'PEPL', '0', '75f23f8dd09341abaeee1b51dff42336', 'SYNC_GRID', 'specGradingRule');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c5175c18ab0947dbbfa2494cb81bc5c2', 0, 1, 'PEPL', 'PEPL', '0', '75f23f8dd09341abaeee1b51dff42336', 'SYNC_FIELD_ID', 'syncIdentifier');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f1a961d4d3484077abfed01b11bbf719', 0, 1, 'PEPL', 'PEPL', '0', '75f23f8dd09341abaeee1b51dff42336', 'ERROR_ID', 'REF057');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f14ab0e21e5649d4836fa9c627c475fc', 0, 1, 'PEPL', 'PEPL', '0', '75f23f8dd09341abaeee1b51dff42336', 'ACTION_IDS', 'ItemSaveDoc,ItemSaveAndConfirm');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('2bc10cc41c9c461b86a7c38fe89c1e0c', 0, 1, 'PEPL', 'PEPL', '0', 'bf2f986f98924a79916c8a0b2faf46e5', 13, 'DimensionUniqueLabelValidator', 'com.core.cbx.validation.validator.DimensionUniqueLabelValidator', 'DimensionUniqueLabelValidator', '1', 13);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('bc006cabb1ce4d8f89b58814e16e9e89', 0, 1, 'PEPL', 'PEPL', '0', '2bc10cc41c9c461b86a7c38fe89c1e0c', 'Item', 'itemSize', 'entity.itemSize', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5a88f8e16277451ea7e10c375cd2ddfe', 0, 1, 'PEPL', 'PEPL', '0', 'bc006cabb1ce4d8f89b58814e16e9e89', 'DIMENSION_FIELD_ID', 'dimension');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7216693b00414452bdf393bbe124e632', 0, 1, 'PEPL', 'PEPL', '0', 'bc006cabb1ce4d8f89b58814e16e9e89', 'DISPLAY_NAME_FIELD_ID', 'sizeDisplayName');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ab5188f85bd34491abf572d817e95f71', 0, 1, 'PEPL', 'PEPL', '0', 'bc006cabb1ce4d8f89b58814e16e9e89', 'HIDE_LABEL', 'TRUE');

INSERT INTO CNT_VALIDATION_PROFILE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VERSION, STATUS, DOC_STATUS, EDITING_STATUS, UPDATE_USER, UPDATED_ON, IS_LATEST, MAX_ERROR, CONSOLIDATE_ERROR, PRIORITY, IGNORE_CUSTOM_FIELD, PROFILE_NAME, INHERIT_FROM, REF_NO, REF_ENTITY_NAME, REF_ENTITY_VERSION, ACTION, ENABLED, CREATE_USER, CREATED_ON)
VALUES ('25ba8d6cc75948afb92b15c3816395f5', 0, 1, 'PEPL', 'PEPL', '0', NULL, NULL, NULL, NULL, NULL, TO_TIMESTAMP('2025-08-28 14:58:38', 'YYYY-MM-DD HH24:MI:SS'), '1', 0, '0', 0, '0', 'New Data Validation Profile Item 01[ver:1]', NULL, 'New Data Validation Profile Item 01[ver:1]', 'Item', 1, 'ItemSaveDoc,ItemSaveAndConfirm', '1', 'system', TO_TIMESTAMP('2025-08-28 14:58:38', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('97d65dd13aeb481d8f25cc02e942052a', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 1, 'MandatoryValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryValidator', '1', 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('48d6e57c4c4a48ab85536b61e389dca4', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'itemDesc', 'entity.itemDesc', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('17d38ff9b99649d2b868e8acc9a889d8', 0, 1, 'PEPL', 'PEPL', '0', '48d6e57c4c4a48ab85536b61e389dca4', 'LABEL_FIELD_ID', 'itemDesc');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6cc3618f3f9e4ba0b1ecd2367401ef08', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'hierarchy', 'entity.hierarchy', 2, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1d78450cb3094ad780ba552bbf10493a', 0, 1, 'PEPL', 'PEPL', '0', '6cc3618f3f9e4ba0b1ecd2367401ef08', 'LABEL_FIELD_ID', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('e03bad826a01466ab3d140793e0a948c', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'productCategory', 'entity.productCategory', 3, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8a6057d5332641679c01e615268b1082', 0, 1, 'PEPL', 'PEPL', '0', 'e03bad826a01466ab3d140793e0a948c', 'LABEL_FIELD_ID', 'productCategory');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d9714ba35d1542f2ba1fced43c9b4f72', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'itemNo', 'entity.itemNo', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5a86851eee574a579232ea1ea3685830', 0, 1, 'PEPL', 'PEPL', '0', 'd9714ba35d1542f2ba1fced43c9b4f72', 'LABEL_FIELD_ID', 'itemNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6c91c2df025641a8a9facfd840d02f9c', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'season', 'entity.season', 5, '1', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('28f7788eb0704dd18362c7e336dfd5eb', 0, 1, 'PEPL', 'PEPL', '0', '6c91c2df025641a8a9facfd840d02f9c', 'LABEL_FIELD_ID', 'season');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d8407352505b4064981c24217700e971', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'buyerItemNo', 'entity.buyerItemNo', 6, '1', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('92f432cb4ecc4ce7bcdcef7af038a0d1', 0, 1, 'PEPL', 'PEPL', '0', 'd8407352505b4064981c24217700e971', 'LABEL_FIELD_ID', 'buyerItemNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('62820445c9f44bc0988beafbe6ea28a5', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'custHcl1', 'entity.custHcl1', 7, '0', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isNotNewDoc' AND IS_LATEST = '1'), NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c94139589986434aa7f5a461472cf88e', 0, 1, 'PEPL', 'PEPL', '0', '62820445c9f44bc0988beafbe6ea28a5', 'LABEL_FIELD_ID', 'custHcl1');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1b605211e1cc4d25b00eadcfa1ea1d05', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemCustFinalDest', 'countryOfDestination', 'entity.itemCustFinalDest.countryOfDestination', 8, '0', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c430bb90bcc2402db7bb3d1086680c65', 0, 1, 'PEPL', 'PEPL', '0', '1b605211e1cc4d25b00eadcfa1ea1d05', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0dca7c724a4a446fac34550ff4998f54', 0, 1, 'PEPL', 'PEPL', '0', '1b605211e1cc4d25b00eadcfa1ea1d05', 'LABEL_FIELD_ID', 'countryOfDestination');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('7bcd60c638fc45a280f07de89a58fbef', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemCustFinalDest', 'portOfDischarge', 'entity.itemCustFinalDest.portOfDischarge', 9, '0', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('dbfc367b7f824c8b9c44a668c67c7f67', 0, 1, 'PEPL', 'PEPL', '0', '7bcd60c638fc45a280f07de89a58fbef', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('80631a02360d403a91e409b68bb43050', 0, 1, 'PEPL', 'PEPL', '0', '7bcd60c638fc45a280f07de89a58fbef', 'LABEL_FIELD_ID', 'portOfDischarge');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('dbaf3564955c4576bd183e90e292476a', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemCustFinalDest', 'finalDestination', 'entity.itemCustFinalDest.finalDestination', 10, '0', NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1d427e3a7ac345278cd4ede0ef9c7737', 0, 1, 'PEPL', 'PEPL', '0', 'dbaf3564955c4576bd183e90e292476a', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1e10bba12d6846c6b3d66a5ac63c1332', 0, 1, 'PEPL', 'PEPL', '0', 'dbaf3564955c4576bd183e90e292476a', 'LABEL_FIELD_ID', 'finalDestination');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('0654281e792d4ad281610d0894a466a1', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemImage', 'fileId', 'entity.itemImage.fileId', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7efae34246054a6ea84da96e2b93854b', 0, 1, 'PEPL', 'PEPL', '0', '0654281e792d4ad281610d0894a466a1', 'GRID_ID', 'itemImage');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4b48fc4342af478292380dba43ee1ac0', 0, 1, 'PEPL', 'PEPL', '0', '0654281e792d4ad281610d0894a466a1', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b88b50e45033420292512ef53c410afd', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemAttachment', 'fileId', 'entity.itemAttachment.fileId', 12, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isAttachTypeIdCER' AND IS_LATEST = '1'), NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d63c52dfc2544fad83e0cc32ab0df664', 0, 1, 'PEPL', 'PEPL', '0', 'b88b50e45033420292512ef53c410afd', 'GRID_ID', 'itemAttachment');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('06b818118e7d461e9d769eab773f9954', 0, 1, 'PEPL', 'PEPL', '0', 'b88b50e45033420292512ef53c410afd', 'LABEL_FIELD_ID', 'fileId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f4fb6d2d76b844d497e581f102ce6295', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecRequirement', 'seqNo', 'entity.specRequirement.seqNo', 13, '0', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('aa7f1f3e4bf447eebaaf07cd238fed62', 0, 1, 'PEPL', 'PEPL', '0', 'f4fb6d2d76b844d497e581f102ce6295', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2ea2b517e8204a3caf53bb5e4cf534d1', 0, 1, 'PEPL', 'PEPL', '0', 'f4fb6d2d76b844d497e581f102ce6295', 'LABEL_FIELD_ID', 'seqNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('3c81e3267e904994a9fc6b9da505c8f8', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecColorBom', 'specMaterial', 'entity.specColorBom.specMaterial', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6b702d6a95b0472e8543e4804c82bd83', 0, 1, 'PEPL', 'PEPL', '0', '3c81e3267e904994a9fc6b9da505c8f8', 'GRID_ID', 'specColorBom');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('db1cc750b8644f28b85fbfef1f68d388', 0, 1, 'PEPL', 'PEPL', '0', '3c81e3267e904994a9fc6b9da505c8f8', 'LABEL_FIELD_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('aedd9f7c04874a858975b1d980df44d8', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecArtworkBom', 'artworkName', 'entity.specArtworkBom.artworkName', 15, '1', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('658c6cca28e54113bc4b8a4304ee4840', 0, 1, 'PEPL', 'PEPL', '0', 'aedd9f7c04874a858975b1d980df44d8', 'GRID_ID', 'specArtworkBom');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2409da19bd5d46578c207b22b671fced', 0, 1, 'PEPL', 'PEPL', '0', 'aedd9f7c04874a858975b1d980df44d8', 'LABEL_FIELD_ID', 'artworkName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('bc1a1814195e423cbc030115e4a8f25b', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemColor', 'shortName', 'entity.itemColor.shortName', 16, '1', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b049261509d64a758b4edf980553e891', 0, 1, 'PEPL', 'PEPL', '0', 'bc1a1814195e423cbc030115e4a8f25b', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('95f430dbe6fd42a099c03c95641e6f96', 0, 1, 'PEPL', 'PEPL', '0', 'bc1a1814195e423cbc030115e4a8f25b', 'LABEL_FIELD_ID', 'shortName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6fa68ab2739a46d19d52cdb122fcf59a', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemSize', 'dimension', 'entity.itemSize.dimension', 17, '1', NULL, 17);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('de16aac6494c47f9b70758af94d9cfd8', 0, 1, 'PEPL', 'PEPL', '0', '6fa68ab2739a46d19d52cdb122fcf59a', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5191cd9c081b450c8fac1163d3b4d049', 0, 1, 'PEPL', 'PEPL', '0', '6fa68ab2739a46d19d52cdb122fcf59a', 'LABEL_FIELD_ID', 'dimension');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('50f3f641090440228e576f9c1d4ba168', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'ItemSize', 'sizeDisplayName', 'entity.itemSize.sizeDisplayName', 18, '1', NULL, 18);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('85b2bb36f24a4ce0993254eba0860469', 0, 1, 'PEPL', 'PEPL', '0', '50f3f641090440228e576f9c1d4ba168', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a385bcf7c9a44eb086ac62e1fb5d71bf', 0, 1, 'PEPL', 'PEPL', '0', '50f3f641090440228e576f9c1d4ba168', 'LABEL_FIELD_ID', 'sizeDisplayName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5084a36facd74aaf8e95baff953d2d61', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'itemSize', 'entity.itemSize', 19, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'validateClothingDivision' AND IS_LATEST = '1'), NULL, 19);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2c6624e1bf3b450a9c51c21ea093c85f', 0, 1, 'PEPL', 'PEPL', '0', '5084a36facd74aaf8e95baff953d2d61', 'LABEL_FIELD_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('17486374494e4178afe11af00570a627', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecRequirement', 'details', 'entity.specRequirement.details', 20, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isSpecRequirementMandatory' AND IS_LATEST = '1'), 1, 20);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7ab6fc479f4b4fa7bb98fb9c4805e411', 0, 1, 'PEPL', 'PEPL', '0', '17486374494e4178afe11af00570a627', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('162f2f165c25471488606ffec395791a', 0, 1, 'PEPL', 'PEPL', '0', '17486374494e4178afe11af00570a627', 'LABEL_FIELD_ID', 'details');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7d21c4d7dbc34bc3801e8516cec997ea', 0, 1, 'PEPL', 'PEPL', '0', '17486374494e4178afe11af00570a627', 'POSITION_LABEL_FIELD_ID', 'seqNo');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('831c965d5bb847519371c75ca8627b2d', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecDesign', 'seq', 'entity.specDesign.seq', 21, '0', NULL, 21);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bb20a9b26f7546a3b49d2b9ece33b380', 0, 1, 'PEPL', 'PEPL', '0', '831c965d5bb847519371c75ca8627b2d', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('74794ed5465c4cad81f78941d9ad7905', 0, 1, 'PEPL', 'PEPL', '0', '831c965d5bb847519371c75ca8627b2d', 'LABEL_FIELD_ID', 'seq');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('fa1705ee913b4701a5b52bfa9a350e7f', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecDesign', 'imageType', 'entity.specDesign.imageType', 22, '0', NULL, 22);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('22bebbfb6ff24153826212ddcb9747ba', 0, 1, 'PEPL', 'PEPL', '0', 'fa1705ee913b4701a5b52bfa9a350e7f', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b075552d946c4650b5e5cf52485b0f6c', 0, 1, 'PEPL', 'PEPL', '0', 'fa1705ee913b4701a5b52bfa9a350e7f', 'LABEL_FIELD_ID', 'imageType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('53140eb618ec4ec8a0549d245a43200a', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecDesign', 'imageId', 'entity.specDesign.imageId', 23, '0', NULL, 23);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bb8533672c584891bd2387ba4d038045', 0, 1, 'PEPL', 'PEPL', '0', '53140eb618ec4ec8a0549d245a43200a', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0a63fe927b044537802cd51ec54111a6', 0, 1, 'PEPL', 'PEPL', '0', '53140eb618ec4ec8a0549d245a43200a', 'LABEL_FIELD_ID', 'imageId');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9a99cb6378dc40129c014ff6c9879480', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecMaterial', 'materialName', 'entity.specMaterial.materialName', 24, '1', NULL, 24);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('95f5fecfad6f4b9f8240900edaafd78b', 0, 1, 'PEPL', 'PEPL', '0', '9a99cb6378dc40129c014ff6c9879480', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bb567cfce6e746328c87e448b8038aee', 0, 1, 'PEPL', 'PEPL', '0', '9a99cb6378dc40129c014ff6c9879480', 'LABEL_FIELD_ID', 'materialName');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('5d24c16baad74d7596a19a833a1cb03c', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecMaterial', 'materialType', 'entity.specMaterial.materialType', 25, '1', NULL, 25);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c0bb84379d3f4cd3832467214d479a42', 0, 1, 'PEPL', 'PEPL', '0', '5d24c16baad74d7596a19a833a1cb03c', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0bb5896782fe4e3ba362497a0e5b4e40', 0, 1, 'PEPL', 'PEPL', '0', '5d24c16baad74d7596a19a833a1cb03c', 'LABEL_FIELD_ID', 'materialType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a904c5ab6c1d4805a5436e238f5fa48c', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecMaterial', 'composition', 'entity.specMaterial.composition', 26, '1', NULL, 26);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('747a7c34ec3e4278bf882e4f74a29f37', 0, 1, 'PEPL', 'PEPL', '0', 'a904c5ab6c1d4805a5436e238f5fa48c', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('885548e533184a0eb226c098b13a6eff', 0, 1, 'PEPL', 'PEPL', '0', 'a904c5ab6c1d4805a5436e238f5fa48c', 'LABEL_FIELD_ID', 'composition');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('11a38110b8464b98b591e992f0ceab24', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecConstruction', 'constructionElement', 'entity.specConstruction.constructionElement', 27, '1', NULL, 27);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('125e9b1e700644f0b39ff0c1707eadb6', 0, 1, 'PEPL', 'PEPL', '0', '11a38110b8464b98b591e992f0ceab24', 'GRID_ID', 'specConstruction');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('07fe1a36dd0f4818aefc65fa00c41723', 0, 1, 'PEPL', 'PEPL', '0', '11a38110b8464b98b591e992f0ceab24', 'LABEL_FIELD_ID', 'constructionElement');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1dcaea17b3394d3ea11a1e0a3a86f481', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecConstruction', 'type', 'entity.specConstruction.type', 28, '1', NULL, 28);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d02cc041abfe4338b9e3da658dff37b0', 0, 1, 'PEPL', 'PEPL', '0', '1dcaea17b3394d3ea11a1e0a3a86f481', 'GRID_ID', 'specConstruction');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1d295cd9fbe746e8a93efbe03ab6cbae', 0, 1, 'PEPL', 'PEPL', '0', '1dcaea17b3394d3ea11a1e0a3a86f481', 'LABEL_FIELD_ID', 'type');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a099ad3fec184dfcb9f7e3e4b519c5f5', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecGradingRule', 'seq', 'entity.specGradingRule.seq', 29, '1', NULL, 29);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e3102e1fd20a4f7eb1dc2543af952bca', 0, 1, 'PEPL', 'PEPL', '0', 'a099ad3fec184dfcb9f7e3e4b519c5f5', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ce0c342f59c646dcb7d2687e418e5f0a', 0, 1, 'PEPL', 'PEPL', '0', 'a099ad3fec184dfcb9f7e3e4b519c5f5', 'LABEL_FIELD_ID', 'seq');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('34878adcbbd147a5843271999fc0443b', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecGradingRule', 'code', 'entity.specGradingRule.code', 30, '1', NULL, 30);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cded9cff677e46cd85a364238eb90666', 0, 1, 'PEPL', 'PEPL', '0', '34878adcbbd147a5843271999fc0443b', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4ec46ca5cc604c72a998ad4b41919c07', 0, 1, 'PEPL', 'PEPL', '0', '34878adcbbd147a5843271999fc0443b', 'LABEL_FIELD_ID', 'code');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('f591143e1ef347d699f968172d3a7cef', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecGradingRule', 'description', 'entity.specGradingRule.description', 31, '1', NULL, 31);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('db73f108e3fa4f31a6959037bac82d0d', 0, 1, 'PEPL', 'PEPL', '0', 'f591143e1ef347d699f968172d3a7cef', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1aa679dad5b8456b9572300e7f21d6a1', 0, 1, 'PEPL', 'PEPL', '0', 'f591143e1ef347d699f968172d3a7cef', 'LABEL_FIELD_ID', 'description');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ca61871f187f478ca8777fa6ba4c211b', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecAccessoriesMeasurement', 'seq', 'entity.specAccessoriesMeasurement.seq', 32, '0', NULL, 32);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('87f93f9fe774404ea7260ff9009b1486', 0, 1, 'PEPL', 'PEPL', '0', 'ca61871f187f478ca8777fa6ba4c211b', 'GRID_ID', 'specAccessoriesMeasurements');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('72457a02b46c40288aca360b736b26f7', 0, 1, 'PEPL', 'PEPL', '0', 'ca61871f187f478ca8777fa6ba4c211b', 'LABEL_FIELD_ID', 'seq');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('42e106c436c7496b88a3a994753f1ab1', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecAccessoriesMeasurement', 'description', 'entity.specAccessoriesMeasurement.description', 33, '1', NULL, 33);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8ccca8c4a5e34ec8991810fadf1b01fa', 0, 1, 'PEPL', 'PEPL', '0', '42e106c436c7496b88a3a994753f1ab1', 'GRID_ID', 'specAccessoriesMeasurements');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('04c582358abf424792e460c6a1cb3e8d', 0, 1, 'PEPL', 'PEPL', '0', '42e106c436c7496b88a3a994753f1ab1', 'LABEL_FIELD_ID', 'description');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('516fb80df8274f029ea6f0e009b533cb', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'SpecInstruction', 'type', 'entity.specInstruction.type', 34, '0', NULL, 34);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5e90556afe1d4e17a3ad86318589de3c', 0, 1, 'PEPL', 'PEPL', '0', '516fb80df8274f029ea6f0e009b533cb', 'GRID_ID', 'specInstruction');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e5d442c2932c4759b0e82d8c45a575d4', 0, 1, 'PEPL', 'PEPL', '0', '516fb80df8274f029ea6f0e009b533cb', 'LABEL_FIELD_ID', 'type');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d56262694a664bb184aec771e264baa5', 0, 1, 'PEPL', 'PEPL', '0', '97d65dd13aeb481d8f25cc02e942052a', 'Item', 'custCodelist19', 'entity.custCodelist19', 35, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'NEWUI_ItemReportCategoryIs_1.2' AND IS_LATEST = '1'), NULL, 35);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2201db941274481c9b809520877293c7', 0, 1, 'PEPL', 'PEPL', '0', 'd56262694a664bb184aec771e264baa5', 'LABEL_FIELD_ID', 'custCodelist19');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('6eaa9e8501bb47108cb17b85b958f502', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 2, 'UniqueInModuleValidator', 'com.core.cbx.validation.validator.UniqueInModuleValidator', 'UniqueInModuleValidator', '1', 2);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('35ebfe469eb84758b298ae1d6a3f6a54', 0, 1, 'PEPL', 'PEPL', '0', '6eaa9e8501bb47108cb17b85b958f502', 'Item', 'itemNo', 'entity.itemNo', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a16343c85c7d4564a68b7e22cc971046', 0, 1, 'PEPL', 'PEPL', '0', '35ebfe469eb84758b298ae1d6a3f6a54', 'CHECK_LATEST_VERSION', 'Y');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('002ea50bdcd44bff8c94df5d37b1d2a7', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 3, 'UniqueInSectionValidator', 'com.core.cbx.validation.validator.UniqueInSectionValidator', 'UniqueInSectionValidator', '1', 3);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('082e53ae61d64e52812ef55ee1aac3d7', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemCust', 'cust', 'entity.itemCust.cust', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b6ec43a6e40c4e888d1b01995f074350', 0, 1, 'PEPL', 'PEPL', '0', '082e53ae61d64e52812ef55ee1aac3d7', 'ERROR_ID', '08010088');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('aef12740e653404ca2decdd51ac0eb25', 0, 1, 'PEPL', 'PEPL', '0', '082e53ae61d64e52812ef55ee1aac3d7', 'FIELD_GROUP', 'cust,market,channel');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('32c467b441b84f23b2e2d46097a52f7e', 0, 1, 'PEPL', 'PEPL', '0', '082e53ae61d64e52812ef55ee1aac3d7', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b6dc93820b2b492086155b392a147f5c', 0, 1, 'PEPL', 'PEPL', '0', '082e53ae61d64e52812ef55ee1aac3d7', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('7f6ca1d8d9db4bc8bcb3b27b42858e22', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemCustFinalDest', 'cust', 'entity.itemCustFinalDest.cust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c754736220f24623a13dca12e40e82de', 0, 1, 'PEPL', 'PEPL', '0', '7f6ca1d8d9db4bc8bcb3b27b42858e22', 'ERROR_ID', '08010090');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9275cb6e14274546b75a3d73ee8b4c18', 0, 1, 'PEPL', 'PEPL', '0', '7f6ca1d8d9db4bc8bcb3b27b42858e22', 'FIELD_GROUP', 'cust,portOfDischarge,finalDestination');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bc570d847e1545e2aca38d28b428026d', 0, 1, 'PEPL', 'PEPL', '0', '7f6ca1d8d9db4bc8bcb3b27b42858e22', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5cf20b37afb848bc8a85fa7cee2c62c4', 0, 1, 'PEPL', 'PEPL', '0', '7f6ca1d8d9db4bc8bcb3b27b42858e22', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('08e4b195a7d74fafa023881bba80e259', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecAccessoriesMeasurement', 'refKey', 'entity.specAccessoriesMeasurement.refKey', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8d57ba4d05e84ecaa62dd9704852eb42', 0, 1, 'PEPL', 'PEPL', '0', '08e4b195a7d74fafa023881bba80e259', 'GRID_ID', 'specAccessoriesMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ec8770c6467842788b46a858b307a347', 0, 1, 'PEPL', 'PEPL', '0', '08e4b195a7d74fafa023881bba80e259', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('29bb1f7344c444e3a841bf75e2288e65', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecAccessoriesMeasurement', 'seq', 'entity.specAccessoriesMeasurement.seq', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8b0b030ed34c4bd79b79e69c131be87d', 0, 1, 'PEPL', 'PEPL', '0', '29bb1f7344c444e3a841bf75e2288e65', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('65f287d2a7dd48d9849c337e1dd6fed1', 0, 1, 'PEPL', 'PEPL', '0', '29bb1f7344c444e3a841bf75e2288e65', 'GRID_ID', 'specAccessoriesMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('95fe8faf5df840df8feaff01caae331f', 0, 1, 'PEPL', 'PEPL', '0', '29bb1f7344c444e3a841bf75e2288e65', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('33c88f5dfd724e98bcef3acd1e5512d9', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecGradingRule', 'code', 'entity.specGradingRule.code', 5, '1', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('68b558348e1e4527af41e738d8c9c957', 0, 1, 'PEPL', 'PEPL', '0', '33c88f5dfd724e98bcef3acd1e5512d9', 'ERROR_ID', 'pepl_08010003');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1d523e1c7830434ca5d038d3c66d7b07', 0, 1, 'PEPL', 'PEPL', '0', '33c88f5dfd724e98bcef3acd1e5512d9', 'GRID_ID', 'specGradingRules');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7900c11cc4bb4781a374b9206ddd7a9e', 0, 1, 'PEPL', 'PEPL', '0', '33c88f5dfd724e98bcef3acd1e5512d9', 'LABEL_FIELD_ID', 'code');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('67d9188ac0e047bbac2953ee518164ff', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecGradingRule', 'refKey', 'entity.specGradingRule.refKey', 6, '1', NULL, 6);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('489857b9f12c4f498f6b6c6626809a79', 0, 1, 'PEPL', 'PEPL', '0', '67d9188ac0e047bbac2953ee518164ff', 'GRID_ID', 'specGradingRule');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('19df937b4d8e4e8b84fd3743a54b7b75', 0, 1, 'PEPL', 'PEPL', '0', '67d9188ac0e047bbac2953ee518164ff', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a7cc0cfef2af46c999e01ef1e4cba3b5', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecGradingRule', 'seq', 'entity.specGradingRule.seq', 7, '1', NULL, 7);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('101ffcab0da5475bb60f236ddedc4ce7', 0, 1, 'PEPL', 'PEPL', '0', 'a7cc0cfef2af46c999e01ef1e4cba3b5', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d0aa56aec616449ea884dc5684872b93', 0, 1, 'PEPL', 'PEPL', '0', 'a7cc0cfef2af46c999e01ef1e4cba3b5', 'GRID_ID', 'specGradingRule');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('12a16ec715344d519822873fd184136a', 0, 1, 'PEPL', 'PEPL', '0', 'a7cc0cfef2af46c999e01ef1e4cba3b5', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9d3bc1544daa4a78b2c943a5b7c78eb8', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecMeasurement', 'refKey', 'entity.specMeasurement.refKey', 8, '1', NULL, 8);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cc6dd02e0c5a4c9999d94bec42ef0915', 0, 1, 'PEPL', 'PEPL', '0', '9d3bc1544daa4a78b2c943a5b7c78eb8', 'GRID_ID', 'specMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5f3100eabca340129f4fcc0db3f1247b', 0, 1, 'PEPL', 'PEPL', '0', '9d3bc1544daa4a78b2c943a5b7c78eb8', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6353de6f61e34101b1334e0d4b93193d', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecOther', 'seq', 'entity.specOther.seq', 9, '1', NULL, 9);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('765c0a055a68423e8dab04adcc8f0e25', 0, 1, 'PEPL', 'PEPL', '0', '6353de6f61e34101b1334e0d4b93193d', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f0c70cb217654a56a2afab1c8e8e3b93', 0, 1, 'PEPL', 'PEPL', '0', '6353de6f61e34101b1334e0d4b93193d', 'GRID_ID', 'specOther');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8f52d239a646476aa8786b87f0ece02a', 0, 1, 'PEPL', 'PEPL', '0', '6353de6f61e34101b1334e0d4b93193d', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1123b1405d8b40cabfdfd5a423a954b4', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecTreatment', 'seq', 'entity.specTreatment.seq', 10, '1', NULL, 10);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('76eb53e106554fd88f9d3a6275aa0b8e', 0, 1, 'PEPL', 'PEPL', '0', '1123b1405d8b40cabfdfd5a423a954b4', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a5396df749954f0092702ca8d8dda282', 0, 1, 'PEPL', 'PEPL', '0', '1123b1405d8b40cabfdfd5a423a954b4', 'GRID_ID', 'specTreatment');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('030ca041dcbc4530b15b3d68f5fd4a9f', 0, 1, 'PEPL', 'PEPL', '0', '1123b1405d8b40cabfdfd5a423a954b4', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('729db5e0a7544a6d8fd331b5d46d843d', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecDesign', 'seq', 'entity.specDesign.seq', 11, '1', NULL, 11);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b3f2814413c74b20842a0f545e2837f5', 0, 1, 'PEPL', 'PEPL', '0', '729db5e0a7544a6d8fd331b5d46d843d', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('358f89714a2943ca8eb416d2f1708576', 0, 1, 'PEPL', 'PEPL', '0', '729db5e0a7544a6d8fd331b5d46d843d', 'GRID_ID', 'specDesign');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c046674d21a84824a39e133f54d7c05f', 0, 1, 'PEPL', 'PEPL', '0', '729db5e0a7544a6d8fd331b5d46d843d', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b9cd67e174c1490990372cda70417abd', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecColorBom', 'seq', 'entity.specColorBom.seq', 12, '1', NULL, 12);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('17cd763e33cc419ea95464625059a58f', 0, 1, 'PEPL', 'PEPL', '0', 'b9cd67e174c1490990372cda70417abd', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fc59746571274293930ff2bee1487883', 0, 1, 'PEPL', 'PEPL', '0', 'b9cd67e174c1490990372cda70417abd', 'GRID_ID', 'specColorBom');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('737acdc40a744e7497e51b7da384f170', 0, 1, 'PEPL', 'PEPL', '0', 'b9cd67e174c1490990372cda70417abd', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9ee228d31c924c7382a33e39bebdb6f0', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecMaterial', 'materialName', 'entity.specMaterial.materialName', 13, '1', NULL, 13);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('15e20f0e074c4bcf9b389a3879203476', 0, 1, 'PEPL', 'PEPL', '0', '9ee228d31c924c7382a33e39bebdb6f0', 'ERROR_ID', '16052504');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9959a95a26a84cafa6611a473363c1b9', 0, 1, 'PEPL', 'PEPL', '0', '9ee228d31c924c7382a33e39bebdb6f0', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0c28570f52c94396b614b9add9b93365', 0, 1, 'PEPL', 'PEPL', '0', '9ee228d31c924c7382a33e39bebdb6f0', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8ec94d930d3448bdaf21a62318c5468f', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecMaterial', 'seq', 'entity.specMaterial.seq', 14, '1', NULL, 14);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('898f7dd4e76e4c34a0ce9fe82710a784', 0, 1, 'PEPL', 'PEPL', '0', '8ec94d930d3448bdaf21a62318c5468f', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cf89f82f847545f69619473c2869a1b0', 0, 1, 'PEPL', 'PEPL', '0', '8ec94d930d3448bdaf21a62318c5468f', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f76702f1a3af45969474e4b2b4c9b437', 0, 1, 'PEPL', 'PEPL', '0', '8ec94d930d3448bdaf21a62318c5468f', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d87b36e9e36e4d439e7576c59efc3561', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecRequirement', 'category', 'entity.specRequirement.category', 15, '0', NULL, 15);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9a3e9b537a774e059ce319441de3cb2d', 0, 1, 'PEPL', 'PEPL', '0', 'd87b36e9e36e4d439e7576c59efc3561', 'ERROR_ID', '08010100');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('26a3ed4ed2414c28a922f9f678c3286b', 0, 1, 'PEPL', 'PEPL', '0', 'd87b36e9e36e4d439e7576c59efc3561', 'FIELD_GROUP', 'category,type,description');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e3845a38c12748e1ab7753a30ac1fd39', 0, 1, 'PEPL', 'PEPL', '0', 'd87b36e9e36e4d439e7576c59efc3561', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('62b05df538214309bc6672eec9710657', 0, 1, 'PEPL', 'PEPL', '0', 'd87b36e9e36e4d439e7576c59efc3561', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8f4fbf55214f46d0b16deab6a5deb0b4', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'SpecRequirement', 'seqNo', 'entity.specRequirement.seqNo', 16, '1', NULL, 16);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f9fb942dbaae40e68220feeff59a77fb', 0, 1, 'PEPL', 'PEPL', '0', '8f4fbf55214f46d0b16deab6a5deb0b4', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1f0f7b8db71c4f9db6fd5015f4206e46', 0, 1, 'PEPL', 'PEPL', '0', '8f4fbf55214f46d0b16deab6a5deb0b4', 'GRID_ID', 'specRequirement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9145af99b7c44e59bf03c2689f89b376', 0, 1, 'PEPL', 'PEPL', '0', '8f4fbf55214f46d0b16deab6a5deb0b4', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8cf0a99d6f6c4de3ae8231fb084f9a20', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemColor', 'colorSeq', 'entity.itemColor.colorSeq', 17, '1', NULL, 17);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7ee3969da34a43de997df61d73177cef', 0, 1, 'PEPL', 'PEPL', '0', '8cf0a99d6f6c4de3ae8231fb084f9a20', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('47bc97a13f2643448122262625469a7e', 0, 1, 'PEPL', 'PEPL', '0', '8cf0a99d6f6c4de3ae8231fb084f9a20', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d1395e76d2b34d12882aefec0e32f134', 0, 1, 'PEPL', 'PEPL', '0', '8cf0a99d6f6c4de3ae8231fb084f9a20', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('c783100a0afe49cabd47b010457b25f2', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemSize', 'sizeSeq', 'entity.itemSize.sizeSeq', 18, '1', NULL, 18);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('42ee5153d6724030a7c4710e1d99534b', 0, 1, 'PEPL', 'PEPL', '0', 'c783100a0afe49cabd47b010457b25f2', 'ERROR_ID', '16052503');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('467ad05343964bbea09d263da08d5764', 0, 1, 'PEPL', 'PEPL', '0', 'c783100a0afe49cabd47b010457b25f2', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a20da1e2184345f5a9e8bcb170b85a59', 0, 1, 'PEPL', 'PEPL', '0', 'c783100a0afe49cabd47b010457b25f2', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('238b3fe65ba541df9f91e7d47016028d', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemVendorFact', 'vendor', 'entity.itemVendorFact.vendor', 19, '1', NULL, 19);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b8eb61d7510c4ac5966c7c52d7025335', 0, 1, 'PEPL', 'PEPL', '0', '238b3fe65ba541df9f91e7d47016028d', 'FIELD_GROUP', 'vendor,fact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('02cb0fbee39e409888367170992c2fcc', 0, 1, 'PEPL', 'PEPL', '0', '238b3fe65ba541df9f91e7d47016028d', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4ec14747af8a404db7d9df2b461453bc', 0, 1, 'PEPL', 'PEPL', '0', '238b3fe65ba541df9f91e7d47016028d', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('724197481d1f426d855bc6511fe5fb9c', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemRelated', 'relatedItem', 'entity.itemRelated.relatedItem', 20, '1', NULL, 20);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('924f8bd527cb44aa8e2fee802ce85907', 0, 1, 'PEPL', 'PEPL', '0', '724197481d1f426d855bc6511fe5fb9c', 'ERROR_ID', '08010098');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fd12bb571c3344d0a2e9c6578cc5df12', 0, 1, 'PEPL', 'PEPL', '0', '724197481d1f426d855bc6511fe5fb9c', 'FIELD_GROUP', 'relatedItem');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7d540e37421f4cc7a6b9b9f608f974e6', 0, 1, 'PEPL', 'PEPL', '0', '724197481d1f426d855bc6511fe5fb9c', 'GRID_ID', 'itemRelated');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fae7685e38df40ac9175af1acfdc6a50', 0, 1, 'PEPL', 'PEPL', '0', '724197481d1f426d855bc6511fe5fb9c', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('bb4e673700fa4f1cbe3020473a74cb97', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemSize', 'sizeCode', 'entity.itemSize.sizeCode', 21, '1', NULL, 21);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c5c1c7fe8b40477a8e1c8f2f4c00b745', 0, 1, 'PEPL', 'PEPL', '0', 'bb4e673700fa4f1cbe3020473a74cb97', 'GRID_ID', 'itemSize');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('251880a1a30340e3a44ce27f88c9083d', 0, 1, 'PEPL', 'PEPL', '0', 'bb4e673700fa4f1cbe3020473a74cb97', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d75d17a80a0242b7ae5c0ac03cf420cc', 0, 1, 'PEPL', 'PEPL', '0', '002ea50bdcd44bff8c94df5d37b1d2a7', 'ItemColor', 'shortName', 'entity.itemColor.shortName', 22, '1', NULL, 22);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('97670c36014145329db1af8623caa47b', 0, 1, 'PEPL', 'PEPL', '0', 'd75d17a80a0242b7ae5c0ac03cf420cc', 'ERROR_ID', 'REF083');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('64567c458163443693e961379454a61e', 0, 1, 'PEPL', 'PEPL', '0', 'd75d17a80a0242b7ae5c0ac03cf420cc', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3024ddcc99f84ab38180730941398f74', 0, 1, 'PEPL', 'PEPL', '0', 'd75d17a80a0242b7ae5c0ac03cf420cc', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('1e8b80973e344ac69a5abd3b19c8c989', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 4, 'ItemMarkAsDefaultValidator', 'com.core.cbx.item.action.ItemMarkAsDefaultValidator', 'ItemMarkAsDefaultValidator', '1', 4);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4a4217ae6d8d43e7af7d328a5a682c92', 0, 1, 'PEPL', 'PEPL', '0', '1e8b80973e344ac69a5abd3b19c8c989', 'Item', 'itemSourAgent', 'entity.itemSourAgent', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('ea9e2de4c0804574ba759f3ea6018445', 0, 1, 'PEPL', 'PEPL', '0', '1e8b80973e344ac69a5abd3b19c8c989', 'Item', 'itemCust', 'entity.itemCust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('264c8d41eecd47a59cc25e981775feec', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 5, 'ItemDependenceValidator', 'com.core.cbx.validation.validator.ItemDependenceValidator', 'ItemDependenceValidator', '1', 5);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('71830b6f8c7a4a84a7eae403b3b82866', 0, 1, 'PEPL', 'PEPL', '0', '264c8d41eecd47a59cc25e981775feec', 'ItemCustFinalDest', 'businessName', 'entity.itemCustFinalDest.businessName', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('116d0f6d8585468bbd1cc72cc1eaaa56', 0, 1, 'PEPL', 'PEPL', '0', '71830b6f8c7a4a84a7eae403b3b82866', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('fe36bad09c134cf49b59fedd1f22510b', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 6, 'ExpressionItemCountValidator', 'com.core.cbx.validation.validator.ExpressionItemCountValidator', 'ExpressionItemCountValidator', '1', 6);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('cbebe6915944430fa58ecd7b90abde94', 0, 1, 'PEPL', 'PEPL', '0', 'fe36bad09c134cf49b59fedd1f22510b', 'Item', 'itemColor', 'entity.itemColor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('010c6147b9154aeda95caa0499de4fad', 0, 1, 'PEPL', 'PEPL', '0', 'cbebe6915944430fa58ecd7b90abde94', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('798ddbf3815f41b182f27fa694f7c52f', 0, 1, 'PEPL', 'PEPL', '0', 'cbebe6915944430fa58ecd7b90abde94', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3da084fb0b9b4dbaaacb2b8036a84532', 0, 1, 'PEPL', 'PEPL', '0', 'cbebe6915944430fa58ecd7b90abde94', 'EXPRESSION', 'entity.isPrimary=true');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e46c93e3fe024abdb1498f5b76f5afc1', 0, 1, 'PEPL', 'PEPL', '0', 'cbebe6915944430fa58ecd7b90abde94', 'MIN_COUNT', '1');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c48f291b4d004a2a95f3fd3c0a85026b', 0, 1, 'PEPL', 'PEPL', '0', 'cbebe6915944430fa58ecd7b90abde94', 'ERROR_ID', '08020009');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('94121323462549cc805dc382ad5f9fb1', 0, 1, 'PEPL', 'PEPL', '0', 'fe36bad09c134cf49b59fedd1f22510b', 'Item', 'itemCust', 'entity.itemCust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8b2e450d0ff24c0f987805c1c08e7dff', 0, 1, 'PEPL', 'PEPL', '0', '94121323462549cc805dc382ad5f9fb1', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9aa70ab6a2014c00b7f81091cb00977c', 0, 1, 'PEPL', 'PEPL', '0', '94121323462549cc805dc382ad5f9fb1', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5af85d10a8084168b02ec6f53eda370a', 0, 1, 'PEPL', 'PEPL', '0', '94121323462549cc805dc382ad5f9fb1', 'EXPRESSION', 'entity.isDefault=true');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('cad79bec4ecc496caad16f2217bc6725', 0, 1, 'PEPL', 'PEPL', '0', '94121323462549cc805dc382ad5f9fb1', 'MIN_COUNT', '1');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e4ba5ccee5584355af4ff005d74a8062', 0, 1, 'PEPL', 'PEPL', '0', '94121323462549cc805dc382ad5f9fb1', 'ERROR_ID', '8010091');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d75fe934ad35418d9769d9000fe25be4', 0, 1, 'PEPL', 'PEPL', '0', 'fe36bad09c134cf49b59fedd1f22510b', 'Item', 'itemSourAgent', 'entity.itemSourAgent', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3772cb3603ba4413afb9c511e2966c95', 0, 1, 'PEPL', 'PEPL', '0', 'd75fe934ad35418d9769d9000fe25be4', 'GRID_ID', 'itemSourAgent');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c9b9e71660514610842bedb17d90a076', 0, 1, 'PEPL', 'PEPL', '0', 'd75fe934ad35418d9769d9000fe25be4', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b7b2cd1b04f64b1c944569a08de51f6e', 0, 1, 'PEPL', 'PEPL', '0', 'd75fe934ad35418d9769d9000fe25be4', 'EXPRESSION', 'entity.isDefault=true');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1afe21e10f7c477689b73f2b675d0364', 0, 1, 'PEPL', 'PEPL', '0', 'd75fe934ad35418d9769d9000fe25be4', 'MIN_COUNT', '1');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('76716e1a70504763a12cb233200b5b25', 0, 1, 'PEPL', 'PEPL', '0', 'd75fe934ad35418d9769d9000fe25be4', 'ERROR_ID', '8010091');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('5a202203a6f2479aa93193849ef171e9', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 7, 'ClassificationValidator', 'com.core.cbx.validation.validator.ClassificationValidator', 'ClassificationValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 7);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('331c4e1251b7455faed57eba684115a1', 0, 1, 'PEPL', 'PEPL', '0', '5a202203a6f2479aa93193849ef171e9', 'Item', 'itemCust', 'entity.itemCust', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9b766af727904ffbb7284701cf5e765e', 0, 1, 'PEPL', 'PEPL', '0', '331c4e1251b7455faed57eba684115a1', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8c98d0ac709a4fe6ac440af154ab4575', 0, 1, 'PEPL', 'PEPL', '0', '331c4e1251b7455faed57eba684115a1', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ed28c21373c44e58a975b3b6b43480dc', 0, 1, 'PEPL', 'PEPL', '0', '331c4e1251b7455faed57eba684115a1', 'TARGET_FIELD', 'cust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('802ef30f1d934615ae9e692ab2b3cb04', 0, 1, 'PEPL', 'PEPL', '0', '331c4e1251b7455faed57eba684115a1', 'DOCUMENT_NO', 'custCode');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('85cac0d44cfd4161a34f1c8e6c4c27eb', 0, 1, 'PEPL', 'PEPL', '0', '5a202203a6f2479aa93193849ef171e9', 'Item', 'itemVendorFact', 'entity.itemVendorFact', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('8431a8f9cd1942399e62b4f3c0b5eb34', 0, 1, 'PEPL', 'PEPL', '0', '85cac0d44cfd4161a34f1c8e6c4c27eb', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d153123d7ce3467293e70ad23844dc95', 0, 1, 'PEPL', 'PEPL', '0', '85cac0d44cfd4161a34f1c8e6c4c27eb', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0223fe3ab9d342f8be497d3167ce4d6c', 0, 1, 'PEPL', 'PEPL', '0', '85cac0d44cfd4161a34f1c8e6c4c27eb', 'TARGET_FIELD', 'vendor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7d1cb5ddee874b6bbcc83b6e0a9064f6', 0, 1, 'PEPL', 'PEPL', '0', '85cac0d44cfd4161a34f1c8e6c4c27eb', 'DOCUMENT_NO', 'vendorCode');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('77e6c126ccc240c48137a89a7cd923b3', 0, 1, 'PEPL', 'PEPL', '0', '5a202203a6f2479aa93193849ef171e9', 'Item', 'itemVendorFact', 'entity.itemVendorFact', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('1a61ddec0f27407db8b0ad36620bed5b', 0, 1, 'PEPL', 'PEPL', '0', '77e6c126ccc240c48137a89a7cd923b3', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('077bffdf544245c2ac8db96768c263b3', 0, 1, 'PEPL', 'PEPL', '0', '77e6c126ccc240c48137a89a7cd923b3', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('754eb8da943b448bb795e7344137c537', 0, 1, 'PEPL', 'PEPL', '0', '77e6c126ccc240c48137a89a7cd923b3', 'TARGET_FIELD', 'fact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3f956d8bd389410e9ea7b11ef4024c64', 0, 1, 'PEPL', 'PEPL', '0', '77e6c126ccc240c48137a89a7cd923b3', 'DOCUMENT_NO', 'factCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('7c8a9ba2f7d14df58558258449b1e64f', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 8, 'ItemValidatorForColor', 'com.core.cbx.item.validator.ItemValidatorForColorProductCategory', 'ItemValidatorForColorValidator', '0', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isClassificationSecurityMode' AND IS_LATEST = '1'), 8);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('9291e4acc7724d6883f0ae231beb4742', 0, 1, 'PEPL', 'PEPL', '0', '7c8a9ba2f7d14df58558258449b1e64f', 'Item', 'itemColor', 'entity.itemColor', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6787a6a02882440ab0ebf149fafc33da', 0, 1, 'PEPL', 'PEPL', '0', '9291e4acc7724d6883f0ae231beb4742', 'GRID_ID', 'itemColor');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e1dc0611937747769e17c1cdef3abce3', 0, 1, 'PEPL', 'PEPL', '0', '9291e4acc7724d6883f0ae231beb4742', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('924c307c04644ea98ee024e19a811a09', 0, 1, 'PEPL', 'PEPL', '0', '9291e4acc7724d6883f0ae231beb4742', 'TARGET_FIELD', 'color');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a7f51e41a8e44bb19c0aa5de3ad03d0d', 0, 1, 'PEPL', 'PEPL', '0', '9291e4acc7724d6883f0ae231beb4742', 'DOCUMENT_NO', 'colorCode');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, CONDITION_ID, INTERNAL_SEQ_NO)
VALUES ('44e8f70458244175bbaaf3214c2e551e', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 9, 'HCLValidator', 'com.core.cbx.validation.validator.HCLValidator', 'HCLValidator', '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isHclSecurityMode' AND IS_LATEST = '1'), 9);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('6db0a72ac1f243419af201d93f9e285b', 0, 1, 'PEPL', 'PEPL', '0', '44e8f70458244175bbaaf3214c2e551e', 'ItemCust', 'cust', 'entity.itemCust.cust', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e1c8ee599dce43c981985684339803c6', 0, 1, 'PEPL', 'PEPL', '0', '6db0a72ac1f243419af201d93f9e285b', 'GRID_ID', 'itemCust');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9626cb59f5644edfa25dc11457a06ca6', 0, 1, 'PEPL', 'PEPL', '0', '6db0a72ac1f243419af201d93f9e285b', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a46fabb4ebfe4edeb47198b43b82cd22', 0, 1, 'PEPL', 'PEPL', '0', '6db0a72ac1f243419af201d93f9e285b', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('fcc2e714c0f54e159d5eed47a30f638e', 0, 1, 'PEPL', 'PEPL', '0', '6db0a72ac1f243419af201d93f9e285b', 'TARGET_FIELD', 'custHc');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bb122b4f3d264f80adda73fdb68dc556', 0, 1, 'PEPL', 'PEPL', '0', '6db0a72ac1f243419af201d93f9e285b', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('7e2e9253fbb442fd8e672ffa94afa0b3', 0, 1, 'PEPL', 'PEPL', '0', '44e8f70458244175bbaaf3214c2e551e', 'ItemCustFinalDest', 'cust', 'entity.itemCustFinalDest.cust', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3e47917d759947099354ee1c1fffe80d', 0, 1, 'PEPL', 'PEPL', '0', '7e2e9253fbb442fd8e672ffa94afa0b3', 'GRID_ID', 'itemCustFinalDest');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3365a20f1b634639aefda65ab6a2c3ef', 0, 1, 'PEPL', 'PEPL', '0', '7e2e9253fbb442fd8e672ffa94afa0b3', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f31f2311815d41fa88f94a9c583b91fa', 0, 1, 'PEPL', 'PEPL', '0', '7e2e9253fbb442fd8e672ffa94afa0b3', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d9752664c0424942b656389bb3b5f79c', 0, 1, 'PEPL', 'PEPL', '0', '7e2e9253fbb442fd8e672ffa94afa0b3', 'TARGET_FIELD', 'custHc');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('bde26357f21b4e42b9a5b5949d75dd7f', 0, 1, 'PEPL', 'PEPL', '0', '7e2e9253fbb442fd8e672ffa94afa0b3', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('900538c75e23438f8ef283235862b3ec', 0, 1, 'PEPL', 'PEPL', '0', '44e8f70458244175bbaaf3214c2e551e', 'ItemVendorFact', 'vendor', 'entity.itemVendorFact.vendor', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c15bf43176bc4bfdbcdc8e806a85859d', 0, 1, 'PEPL', 'PEPL', '0', '900538c75e23438f8ef283235862b3ec', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5a1d672ed82f43679e80670275a0f3ec', 0, 1, 'PEPL', 'PEPL', '0', '900538c75e23438f8ef283235862b3ec', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('59320bfd3ed34719986ede7ddb775b82', 0, 1, 'PEPL', 'PEPL', '0', '900538c75e23438f8ef283235862b3ec', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('238d0bdbc9084288aa2cb341de1a8943', 0, 1, 'PEPL', 'PEPL', '0', '900538c75e23438f8ef283235862b3ec', 'TARGET_FIELD', 'hcs');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('08983075913f43c795f81d782c9aa756', 0, 1, 'PEPL', 'PEPL', '0', '900538c75e23438f8ef283235862b3ec', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('1689af69876c4f54a9c85c3f4459c5be', 0, 1, 'PEPL', 'PEPL', '0', '44e8f70458244175bbaaf3214c2e551e', 'ItemVendorFact', 'fact', 'entity.itemVendorFact.fact', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c2ad4a2f2fe34148a486e1ad67c0b9bd', 0, 1, 'PEPL', 'PEPL', '0', '1689af69876c4f54a9c85c3f4459c5be', 'GRID_ID', 'itemVendorFact');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ed289c44e9ff4d5ba3b03ef291c5cb02', 0, 1, 'PEPL', 'PEPL', '0', '1689af69876c4f54a9c85c3f4459c5be', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a560b6204c6441b1adf8e09732609bbc', 0, 1, 'PEPL', 'PEPL', '0', '1689af69876c4f54a9c85c3f4459c5be', 'HEADER_HCL_FIELD', 'hierarchy');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('203ca24489b747df8347aefaf23c6589', 0, 1, 'PEPL', 'PEPL', '0', '1689af69876c4f54a9c85c3f4459c5be', 'TARGET_FIELD', 'factHc');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('dedc2e8718c94a2482440603fba7f4ed', 0, 1, 'PEPL', 'PEPL', '0', '1689af69876c4f54a9c85c3f4459c5be', 'TYPE', 'gridSelectMaster');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('a0a06ef4612d453f9005efa2dca37c08', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 10, 'CheckDigitValidator', 'com.core.cbx.validation.validator.CheckDigitValidator', 'CheckDigitValidator', '0', 10);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('0caac102d4ea477b89eb9a9fa641cb46', 0, 1, 'PEPL', 'PEPL', '0', 'a0a06ef4612d453f9005efa2dca37c08', 'Item', 'masterUpc', 'entity.masterUpc', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5fbc5444b8ba491e9fdbe6907a4ea40a', 0, 1, 'PEPL', 'PEPL', '0', '0caac102d4ea477b89eb9a9fa641cb46', 'validateCodelistType', 'UPC_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('4362d31328c04bc8841b88a20468dd04', 0, 1, 'PEPL', 'PEPL', '0', '0caac102d4ea477b89eb9a9fa641cb46', 'validateBaseField', 'upcType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('92fef4d8991641cfb460bbba919d4c98', 0, 1, 'PEPL', 'PEPL', '0', 'a0a06ef4612d453f9005efa2dca37c08', 'Item', 'masterEan', 'entity.masterEan', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('6a88ca0e5d274a97b36746041d81448b', 0, 1, 'PEPL', 'PEPL', '0', '92fef4d8991641cfb460bbba919d4c98', 'validateCodelistType', 'EAN_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('900f551958c54441a9262e80280d7c4d', 0, 1, 'PEPL', 'PEPL', '0', '92fef4d8991641cfb460bbba919d4c98', 'validateBaseField', 'eanType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('899d72640c1e4e3a8688f9c3aaa7eea3', 0, 1, 'PEPL', 'PEPL', '0', 'a0a06ef4612d453f9005efa2dca37c08', 'ItemSku', 'upc', 'entity.itemSku.upc', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('26a39d7ff73d431fb34bde307696f50f', 0, 1, 'PEPL', 'PEPL', '0', '899d72640c1e4e3a8688f9c3aaa7eea3', 'validateCodelistType', 'UPC_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d4c0bd8f52b14973b109793214be5618', 0, 1, 'PEPL', 'PEPL', '0', '899d72640c1e4e3a8688f9c3aaa7eea3', 'validateBaseField', 'upcType');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('59db57d47e754417b18d1c846ff7e825', 0, 1, 'PEPL', 'PEPL', '0', 'a0a06ef4612d453f9005efa2dca37c08', 'ItemSku', 'ean', 'entity.itemSku.ean', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0950b316464e4ea384e45441b96e06d1', 0, 1, 'PEPL', 'PEPL', '0', '59db57d47e754417b18d1c846ff7e825', 'validateCodelistType', 'EAN_TYPE');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('feeaaa0380f34156b5b5cb386ad7090d', 0, 1, 'PEPL', 'PEPL', '0', '59db57d47e754417b18d1c846ff7e825', 'validateBaseField', 'eanType');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('af9687d01fe94105b268737990f050c2', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 11, 'NumericRangeValidator', 'com.core.cbx.validation.validator.NumericRangeValidator', 'NumericRangeValidator', '0', 11);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('d1ac58cf67504138a207a63118a54110', 0, 1, 'PEPL', 'PEPL', '0', 'af9687d01fe94105b268737990f050c2', 'Item', 'landedCost', 'entity.landedCost', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b297083a131444009f0defea5b27c042', 0, 1, 'PEPL', 'PEPL', '0', 'd1ac58cf67504138a207a63118a54110', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('f2d3be17ce74401dbc881864b673b17a', 0, 1, 'PEPL', 'PEPL', '0', 'd1ac58cf67504138a207a63118a54110', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('33476226312041cbb4240f21e737dd3e', 0, 1, 'PEPL', 'PEPL', '0', 'af9687d01fe94105b268737990f050c2', 'Item', 'offerPrice', 'entity.offerPrice', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5255d77c6e8541279ae1d9ff4030352b', 0, 1, 'PEPL', 'PEPL', '0', '33476226312041cbb4240f21e737dd3e', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('87d409a83deb4d63b695339fde3c3de8', 0, 1, 'PEPL', 'PEPL', '0', '33476226312041cbb4240f21e737dd3e', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4c640401d3874f3da83939d927096389', 0, 1, 'PEPL', 'PEPL', '0', 'af9687d01fe94105b268737990f050c2', 'Item', 'retailPrice', 'entity.retailPrice', 3, '1', NULL, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2d140079e4544c648c508a62701df6f0', 0, 1, 'PEPL', 'PEPL', '0', '4c640401d3874f3da83939d927096389', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('9623c42690444f5e96840e5a7be7fc56', 0, 1, 'PEPL', 'PEPL', '0', '4c640401d3874f3da83939d927096389', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('7434bd37294048dab7fd9ca1856f4d76', 0, 1, 'PEPL', 'PEPL', '0', 'af9687d01fe94105b268737990f050c2', 'Item', 'initialOrderQty', 'entity.initialOrderQty', 4, '1', NULL, 4);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('b649a54830ac4db685c64e407118dece', 0, 1, 'PEPL', 'PEPL', '0', '7434bd37294048dab7fd9ca1856f4d76', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ac97ee3219824c5e868b517db6b25b11', 0, 1, 'PEPL', 'PEPL', '0', '7434bd37294048dab7fd9ca1856f4d76', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('b514dc7b1b814cee86c3ad4d93190381', 0, 1, 'PEPL', 'PEPL', '0', 'af9687d01fe94105b268737990f050c2', 'Item', 'totalQty', 'entity.totalQty', 5, '1', NULL, 5);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('0ec12822398045e7b836411d2d56e37e', 0, 1, 'PEPL', 'PEPL', '0', 'b514dc7b1b814cee86c3ad4d93190381', 'GREATE_THAN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c69a016177f34522ba09d51946459065', 0, 1, 'PEPL', 'PEPL', '0', 'b514dc7b1b814cee86c3ad4d93190381', 'ERROR_ID', '15000022');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('165e904c58bf4316b564a1e28e8421f4', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 12, 'ManualRefreshValidator', 'com.core.cbx.item.validator.ManualRefreshValidator', 'ManualRefreshValidator', '0', 12);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('a9390b13806f45f890ca7d1e2573a3f2', 0, 1, 'PEPL', 'PEPL', '0', '165e904c58bf4316b564a1e28e8421f4', 'Item', 'specMeasurement', 'entity.specMeasurement', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('98726c48ed6f44eb98a745d5f1f66942', 0, 1, 'PEPL', 'PEPL', '0', 'a9390b13806f45f890ca7d1e2573a3f2', 'GRID_ID', 'specMeasurement');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('033bfc3fdc064b859e0d631111f99447', 0, 1, 'PEPL', 'PEPL', '0', 'a9390b13806f45f890ca7d1e2573a3f2', 'LABEL_FIELD_ID', '$GRID_LABEL');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ac1c0c2d25854ab0820335b6427fa905', 0, 1, 'PEPL', 'PEPL', '0', 'a9390b13806f45f890ca7d1e2573a3f2', 'SYNC_GRID', 'specGradingRule');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('7160f25f670543bc968691205586eead', 0, 1, 'PEPL', 'PEPL', '0', 'a9390b13806f45f890ca7d1e2573a3f2', 'SYNC_FIELD_ID', 'syncIdentifier');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('c2d926bb3dad48bc8e7935fab2470b30', 0, 1, 'PEPL', 'PEPL', '0', 'a9390b13806f45f890ca7d1e2573a3f2', 'ERROR_ID', 'REF057');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e5d158e663dd48a9affb5788f6ea8f81', 0, 1, 'PEPL', 'PEPL', '0', 'a9390b13806f45f890ca7d1e2573a3f2', 'ACTION_IDS', 'ItemSaveDoc,ItemSaveAndConfirm');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('50313e198a0643d092192c4d251f4655', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 13, 'DimensionUniqueLabelValidator', 'com.core.cbx.validation.validator.DimensionUniqueLabelValidator', 'DimensionUniqueLabelValidator', '1', 13);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('45ada81b2cc842f7afe356d775e76bee', 0, 1, 'PEPL', 'PEPL', '0', '50313e198a0643d092192c4d251f4655', 'Item', 'itemSize', 'entity.itemSize', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('45ecd39fdee345578e39e1a1afb1fb61', 0, 1, 'PEPL', 'PEPL', '0', '45ada81b2cc842f7afe356d775e76bee', 'DIMENSION_FIELD_ID', 'dimension');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('df8c7bc8db9e4bea9034b243fc2341c5', 0, 1, 'PEPL', 'PEPL', '0', '45ada81b2cc842f7afe356d775e76bee', 'DISPLAY_NAME_FIELD_ID', 'sizeDisplayName');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('39ff2fc1d0bc4d65b954c336cc17dc3f', 0, 1, 'PEPL', 'PEPL', '0', '45ada81b2cc842f7afe356d775e76bee', 'HIDE_LABEL', 'TRUE');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('45540d69ea26491eb7baad31480e4b85', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 14, 'MandatoryCustomFieldValidator', 'com.core.cbx.validation.validator.MandatoryValidator', 'MandatoryCustomFieldValidator', '1', 14);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('4fe46d43ed72433da85002c8464f069f', 0, 1, 'PEPL', 'PEPL', '0', '45540d69ea26491eb7baad31480e4b85', 'Item', 'custCodelist19', 'entity.custCodelist19', 1, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'NEWUI_ItemReportCategoryIs_1.2' AND IS_LATEST = '1'), NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('32aab1af24874fb9b034777893c7e922', 0, 1, 'PEPL', 'PEPL', '0', '4fe46d43ed72433da85002c8464f069f', 'LABEL_FIELD_ID', 'custCodelist19');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('14e56d84e5eb4dd08670f3dbb9bb4075', 0, 1, 'PEPL', 'PEPL', '0', '45540d69ea26491eb7baad31480e4b85', 'Item', 'custCodelist23', 'entity.custCodelist23', 2, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isRoyaltyPaidByPEPCO' AND IS_LATEST = '1'), 1, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('5dc396358d1f4856bd9c9d93764745ea', 0, 1, 'PEPL', 'PEPL', '0', '14e56d84e5eb4dd08670f3dbb9bb4075', 'LABEL_FIELD_ID', 'custCodelist23');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_ID, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('8fc7a2c69af0496299b534668365bde1', 0, 1, 'PEPL', 'PEPL', '0', '45540d69ea26491eb7baad31480e4b85', 'Item', 'custCodelist24', 'entity.custCodelist24', 3, '1', (SELECT ID FROM CNT_CONDITION WHERE DOMAIN_ID = 'PEPL' AND NAME = 'isRoyaltyPaidByPEPCO' AND IS_LATEST = '1'), 1, 3);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('e58d1c5f7f86424486a07fcaaf9a8da7', 0, 1, 'PEPL', 'PEPL', '0', '8fc7a2c69af0496299b534668365bde1', 'LABEL_FIELD_ID', 'custCodelist24');

INSERT INTO CNT_VALIDATION_RULE
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, PROFILE_ID, LINE_NO, RULE_TYPE, VALIDATOR_CLASS_NAME, RESTAPI_BEAN_NAME, ENABLED, INTERNAL_SEQ_NO)
VALUES ('1be039ec2c8b43f8946895454a2bdbad', 0, 1, 'PEPL', 'PEPL', '0', '25ba8d6cc75948afb92b15c3816395f5', 15, 'NumRangeCustomFieldValidator', 'com.core.cbx.validation.validator.NumericRangeValidator', 'NumRangeCustomFieldValidator', '1', 15);

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('69be655903e4424c8bff72150c5f29fc', 0, 1, 'PEPL', 'PEPL', '0', '1be039ec2c8b43f8946895454a2bdbad', 'SpecMaterial', 'custDecimal2', 'entity.specMaterial.custDecimal2', 1, '1', NULL, 1);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('75b8a257701e432fbb2e8bd6f62179aa', 0, 1, 'PEPL', 'PEPL', '0', '69be655903e4424c8bff72150c5f29fc', 'MIN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('505bfccc9b42470ea7321421add9945b', 0, 1, 'PEPL', 'PEPL', '0', '69be655903e4424c8bff72150c5f29fc', 'MAX_VALUE', '100');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('ec133e2045f1477dab8086de8dbf57af', 0, 1, 'PEPL', 'PEPL', '0', '69be655903e4424c8bff72150c5f29fc', 'GRID_ID', 'specMaterial');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('2ef57ca213d44afb89da005d35ba4057', 0, 1, 'PEPL', 'PEPL', '0', '69be655903e4424c8bff72150c5f29fc', 'LABEL_FIELD_ID', 'custDecimal2');

INSERT INTO CNT_VALIDATION_FIELD
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_RULE_ID, REF_ENTITY_NAME, FIELD_ID, FIELD_PATH, LINE_NO, ENABLED, CONDITION_TYPE, INTERNAL_SEQ_NO)
VALUES ('92ab008178fd48539b722fc9c6e1fa32', 0, 1, 'PEPL', 'PEPL', '0', '1be039ec2c8b43f8946895454a2bdbad', 'SpecPack', 'custDecimal2', 'entity.specPack.custDecimal2', 2, '1', NULL, 2);

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('87df5c7aa9df4721be58ddb8db1f67e1', 0, 1, 'PEPL', 'PEPL', '0', '92ab008178fd48539b722fc9c6e1fa32', 'MIN_VALUE', '0');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('a1ce6f1dae33405381bafc85da1b5381', 0, 1, 'PEPL', 'PEPL', '0', '92ab008178fd48539b722fc9c6e1fa32', 'MAX_VALUE', '100');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('d99acf0766b04dfebd1e6a2d1ae72cfa', 0, 1, 'PEPL', 'PEPL', '0', '92ab008178fd48539b722fc9c6e1fa32', 'GRID_ID', 'specPack');

INSERT INTO CNT_VALIDATION_FIELD_PARAM
 (ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE, VALIDATION_FIELD_ID, PARAM_KEY, PARAM_VALUE)
VALUES ('3bdf114adf4d4d9cba64c81df46f4900', 0, 1, 'PEPL', 'PEPL', '0', '92ab008178fd48539b722fc9c6e1fa32', 'LABEL_FIELD_ID', 'custDecimal2');

