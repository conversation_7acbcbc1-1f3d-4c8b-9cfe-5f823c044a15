--// FILE: DML_LABEL.sql

INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoActiveView.column.ccyName.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoActiveView.column.ccyName.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoChainOfCustodyView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoChainOfCustodyView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoDraftView.column.ccyName.block', 'en_US', 'PEPL', '', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoDraftView.column.ccyName.block' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoOfficialView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoOfficialView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoOsQtyByItemView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoOsQtyByItemView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoOsQtyByShipView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoOsQtyByShipView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoQtyItemView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoQtyItemView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoQtyShipForDashboardView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoQtyShipForDashboardView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoQtyShipView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoQtyShipView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoReleasedToVendorView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoReleasedToVendorView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoShipView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoShipView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoVendorChangeProposedView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoVendorChangeProposedView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoVendorConfirmedView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoVendorConfirmedView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoVendorRejectedView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoVendorRejectedView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoView.column.ccyName', 'en_US', 'PEPL', 'Confirmed Currency', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoView.column.ccyName' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoOsQtyByItemView.column.price', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoOsQtyByItemView.column.price' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoOsQtyByShipView.column.price', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoOsQtyByShipView.column.price' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoQtyItemView.column.price', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoQtyItemView.column.price' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoQtyShipForDashboardView.column.price', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoQtyShipForDashboardView.column.price' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoQtyShipView.column.price', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoQtyShipView.column.price' AND LOCALE='en_US');
INSERT INTO CNT_LABEL (ID, TYPE, MODULE_CODE, LABEL_ID, LOCALE, DOMAIN_ID, LABEL, VERSION, EDITING_STATUS, DOC_STATUS)
SELECT LOWER(SYS_GUID()), 'label', 'view', 'lbl.view.vpoShipmentItemColorView.column.sellPrice', 'en_US', 'PEPL', 'Confirmed Price', 1, 'confirmed', 'active' WHERE NOT EXISTS (SELECT 1 FROM CNT_LABEL WHERE DOMAIN_ID='PEPL' AND LABEL_ID='lbl.view.vpoShipmentItemColorView.column.sellPrice' AND LOCALE='en_US');


update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoActiveView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoChainOfCustodyView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoDraftView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoOfficialView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoOsQtyByItemView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoOsQtyByShipView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoQtyItemView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoQtyShipForDashboardView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoQtyShipView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoReleasedToVendorView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoShipView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoVendorChangeProposedView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoVendorConfirmedView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoVendorRejectedView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Currency' where label_id = 'lbl.view.vpoView.column.ccyName' and domain_id = 'PEPL' and locale = 'en_US';

update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.view.vpoOsQtyByItemView.column.price' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.view.vpoOsQtyByShipView.column.price' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.view.vpoQtyItemView.column.price' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.view.vpoQtyShipForDashboardView.column.price' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.view.vpoQtyShipView.column.price' and domain_id = 'PEPL' and locale = 'en_US';
update cnt_label set "label"  = 'Confirmed Price' where label_id = 'lbl.view.vpoShipmentItemColorView.column.sellPrice' and domain_id = 'PEPL' and locale = 'en_US';
