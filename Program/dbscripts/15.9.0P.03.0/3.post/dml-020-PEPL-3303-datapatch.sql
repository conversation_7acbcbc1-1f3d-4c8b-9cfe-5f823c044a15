CREATE table if not EXISTS bak_cnt_eval_measurement_fit_PEPL3303_20250820 AS 
SELECT cse.ref_no as se_ref, cse."version" as se_ver, cse.id as se_id, cse.domain_id as cse_domain, 
ci.ref_no as item_ref, ci."version" as item_ver,
cemf.id as cemf_id, cemf.item_number_format as cemf_format, ci.number_format as item_format
FROM cnt_eval_measurement_fit cemf  
JOIN cnt_sample_evaluation cse ON cse.id = cemf.parent_id 
JOIN cnt_item ci ON ci.ref_no = cse.item_ref
WHERE cemf.item_number_format IS NULL 
  AND ci.number_format IS NOT NULL 
  and ci.is_latest 
  and ci.domain_id = cse.domain_id 
  AND cse.is_latest = true;
 
update cnt_eval_measurement_fit cemf set item_number_format = bt.item_format
from bak_cnt_eval_measurement_fit_PEPL3303_20250820 bt
where id = bt.cemf_id;

delete from cnt_serialized_entity cse where target_id in (select se_id from bak_cnt_eval_measurement_fit_PEPL3303_20250820);
