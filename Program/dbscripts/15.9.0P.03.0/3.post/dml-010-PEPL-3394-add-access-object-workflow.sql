
BEGIN;

DO $body$ 
DECLARE
DOMAINID VARCHAR(200) :=  '';
NEWOBJECTID VARCHAR(200) = 'workflow';
DOMAINS RECORD;
  REFDOMAINROLES CURSOR IS
    SELECT ID, NAME FROM CNT_ROLE
    WHERE DOMAIN_ID = '/'
    AND IS_LATEST = '1'
    AND NAME IN ('SuperAdministratorRole','GeneralAdministratorRole','ClientAdministratorRole','vendors','facts','custs','forwarders')
    ;
  REFDOMAINGROUPS CURSOR IS
    SELECT ID, NAME FROM CNT_GROUP
    WHERE DOMAIN_ID = '/'
    AND IS_LATEST = '1'
    AND NAME IN ('SuperAdministratorGroup','GeneralAdministratorGroup','ClientAdministratorGroup','vendors','facts','custs','forwarders','EDITOR','READER')
    ;
BEGIN

    FOR DOMAINS IN (SELECT ID FROM CNT_DOMAIN  WHERE ID NOT IN (
        SELECT CD.ID FROM CNT_DOMAIN CD
        INNER JOIN CNT_DOMAIN_GROUP_MEMBER CDGM ON CD.ID = CDGM.MEMBER_DOMAIN_ID
        INNER JOIN CNT_DOMAIN_GROUP CDG ON CDGM.DOMAIN_GROUP_ID = CDG.ID)
        AND ID NOT IN ('/','RD1','RD2','backend') and ref_domain_id = 'RD2' and is_latest=true)
    LOOP
    DOMAINID = DOMAINS.ID;


  --// Insert missed access object in DOMAINID
  INSERT INTO CNT_ACCESS_OBJECT
  (ID, REVISION, ENTITY_VERSION, VERSION, NAME, CREATE_USER, 
  CREATE_USER_NAME, CREATED_ON, OBJECT_ID, 
  REF_NO, OBJECT_TYPE, DOC_STATUS, EDITING_STATUS,
  OBJECT_VERSION, DOMAIN_ID, DESCN, IS_LATEST, HUB_DOMAIN_ID, IS_FOR_REFERENCE) 
  SELECT SYS_GUID(), AO.REVISION, AO.ENTITY_VERSION, AO.VERSION, AO.NAME, AO.CREATE_USER, 
  AO.CREATE_USER_NAME, AO.CREATED_ON, AO.OBJECT_ID,
  AO.REF_NO, AO.OBJECT_TYPE, AO.DOC_STATUS, AO.EDITING_STATUS,
  AO.OBJECT_VERSION, DOMAINID, AO.DESCN, AO.IS_LATEST, DOMAINID, AO.IS_FOR_REFERENCE 
  FROM CNT_ACCESS_OBJECT AO 
  WHERE AO.DOMAIN_ID = '/'
  AND AO.IS_LATEST = '1'
  AND AO.OBJECT_ID =NEWOBJECTID
  AND NOT EXISTS (
    SELECT 1 FROM CNT_ACCESS_OBJECT DAO
    WHERE DAO.OBJECT_ID =NEWOBJECTID AND DAO.OBJECT_TYPE=AO.OBJECT_TYPE AND DOMAIN_ID=DOMAINID
  )
  ;

  --// For loop each role
  FOR REFDOMAINROLE IN REFDOMAINROLES LOOP
    --// Remove Action Rules
    DELETE FROM CNT_RULE_ACTION WHERE ROLE_ID = (
      SELECT RA.ID FROM CNT_ROLE RA
      INNER JOIN CNT_ROLE RB ON RB.NAME = RA.NAME AND RB.ID = REFDOMAINROLE.ID
      WHERE RA.IS_LATEST = '1'
      AND RA.DOMAIN_ID = DOMAINID)
    and ACCESS_OBJECT_ID in (
        select id from CNT_ACCESS_OBJECT AO2 where AO2.DOMAIN_ID = DOMAINID 
        AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = NEWOBJECTID)
;
    --// Copy Action Rules of referenced domain to DOMAINID
    INSERT INTO CNT_RULE_ACTION (
      ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
      ACCESS_OBJECT_ID,
      ROLE_ID,
      ACTION_ID,
      CONDITION_ID,
      ACCESS_RIGHT
    )
    SELECT SYS_GUID(), A.REVISION, A.ENTITY_VERSION, AO2.DOMAIN_ID, AO2.HUB_DOMAIN_ID, A.IS_FOR_REFERENCE,
      AO2.ID AS ACCESS_OBJECT_ID,
      R2.ID AS ROLE_ID,
      A.ACTION_ID,
      C2.ID AS CONDITION_ID,
      A.ACCESS_RIGHT
    FROM CNT_RULE_ACTION A
    INNER JOIN CNT_CONDITION C ON A.CONDITION_ID = C.ID
    INNER JOIN CNT_CONDITION C2 ON C2.DOMAIN_ID = DOMAINID AND C2.IS_LATEST = '1' AND C2.NAME = C.NAME
    INNER JOIN CNT_ACCESS_OBJECT AO ON AO.ID = A.ACCESS_OBJECT_ID AND AO.OBJECT_ID =NEWOBJECTID
    INNER JOIN CNT_ACCESS_OBJECT AO2 ON AO2.DOMAIN_ID = DOMAINID AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = AO.OBJECT_ID AND AO2.OBJECT_TYPE = AO.OBJECT_TYPE
    INNER JOIN CNT_ROLE R ON A.ROLE_ID = R.ID
    INNER JOIN CNT_ROLE R2 ON R2.DOMAIN_ID = DOMAINID AND R2.IS_LATEST = '1' AND R2.NAME = R.NAME
    WHERE R.NAME = REFDOMAINROLE.NAME
    AND R.DOMAIN_ID = '/'
    AND NOT EXISTS (
      SELECT 1
      FROM CNT_RULE_ACTION A2
      WHERE A2.ACCESS_OBJECT_ID = AO2.ID
      AND A2.ROLE_ID = R2.ID
      AND A2.ACTION_ID = A.ACTION_ID
      AND A2.CONDITION_ID = C2.id
    );
     --// Remove UI Rules
    DELETE FROM CNT_RULE_UI WHERE ROLE_ID = (
      SELECT RA.ID FROM CNT_ROLE RA
      INNER JOIN CNT_ROLE RB ON RB.NAME = RA.NAME AND RB.ID = REFDOMAINROLE.ID
      WHERE RA.IS_LATEST = '1'
      AND RA.DOMAIN_ID = DOMAINID)
    and ACCESS_OBJECT_ID in (
        select id from CNT_ACCESS_OBJECT AO2 where AO2.DOMAIN_ID = DOMAINID 
        AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = NEWOBJECTID);
    --// Copy UI Rules of referenced domain to DOMAINID
    INSERT INTO CNT_RULE_UI (
      ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
      ACCESS_OBJECT_ID,
      ROLE_ID,
      UI_ID,
      CONDITION_ID,
      ACCESS_RIGHT
    )
    SELECT SYS_GUID(), A.REVISION, A.ENTITY_VERSION, AO2.DOMAIN_ID, AO2.HUB_DOMAIN_ID, A.IS_FOR_REFERENCE,
      AO2.ID AS ACCESS_OBJECT_ID,
      R2.ID AS ROLE_ID,
      A.UI_ID,
      C2.ID AS CONDITION_ID,
      A.ACCESS_RIGHT
    FROM CNT_RULE_UI A
    INNER JOIN CNT_CONDITION C ON A.CONDITION_ID = C.ID
    INNER JOIN CNT_CONDITION C2 ON C2.DOMAIN_ID = DOMAINID AND C2.IS_LATEST = '1' AND C2.NAME = C.NAME
    INNER JOIN CNT_ACCESS_OBJECT AO ON AO.ID = A.ACCESS_OBJECT_ID AND AO.OBJECT_ID =NEWOBJECTID
    INNER JOIN CNT_ACCESS_OBJECT AO2 ON AO2.DOMAIN_ID = DOMAINID AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = AO.OBJECT_ID AND AO2.OBJECT_TYPE = AO.OBJECT_TYPE
    INNER JOIN CNT_ROLE R ON A.ROLE_ID = R.ID
    INNER JOIN CNT_ROLE R2 ON R2.DOMAIN_ID = DOMAINID AND R2.IS_LATEST = '1' AND R2.NAME = R.NAME
    WHERE R.NAME = REFDOMAINROLE.NAME
    AND R.DOMAIN_ID = '/'
    AND NOT EXISTS (
      SELECT 1
      FROM CNT_RULE_UI A2
      WHERE A2.ACCESS_OBJECT_ID = AO2.ID
      AND A2.ROLE_ID = R2.ID
      AND A2.UI_ID = A.UI_ID
      AND A2.CONDITION_ID = C2.id
    );
    --// Remove field deny Rules
    DELETE FROM CNT_RULE_FIELD_DENY WHERE ROLE_ID = (
      SELECT RA.ID FROM CNT_ROLE RA
      INNER JOIN CNT_ROLE RB ON RB.NAME = RA.NAME AND RB.ID = REFDOMAINROLE.ID
      WHERE RA.IS_LATEST = '1'
      AND RA.DOMAIN_ID = DOMAINID)
        and ACCESS_OBJECT_ID in (
        select id from CNT_ACCESS_OBJECT AO2 where AO2.DOMAIN_ID = DOMAINID 
        AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = NEWOBJECTID);
    --// Copy field deny Rules of referenced domain to DOMAINID
    INSERT INTO CNT_RULE_FIELD_DENY (
      ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
      ACCESS_OBJECT_ID,
      ROLE_ID,
      FIELD_ID
    )
    SELECT SYS_GUID(), A.REVISION, A.ENTITY_VERSION, AO2.DOMAIN_ID, AO2.HUB_DOMAIN_ID, A.IS_FOR_REFERENCE,
      AO2.ID AS ACCESS_OBJECT_ID,
      R2.ID AS ROLE_ID,
      A.FIELD_ID
    FROM CNT_RULE_FIELD_DENY A
    INNER JOIN CNT_ACCESS_OBJECT AO ON AO.ID = A.ACCESS_OBJECT_ID  AND AO.OBJECT_ID =NEWOBJECTID
    INNER JOIN CNT_ACCESS_OBJECT AO2 ON AO2.DOMAIN_ID = DOMAINID AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = AO.OBJECT_ID AND AO2.OBJECT_TYPE = AO.OBJECT_TYPE
    INNER JOIN CNT_ROLE R ON A.ROLE_ID = R.ID
    INNER JOIN CNT_ROLE R2 ON R2.DOMAIN_ID = DOMAINID AND R2.IS_LATEST = '1' AND R2.NAME = R.NAME
    WHERE R.NAME = REFDOMAINROLE.NAME
    AND R.DOMAIN_ID = '/'
    AND NOT EXISTS (
      SELECT 1
      FROM CNT_RULE_FIELD_DENY A2
      WHERE A2.ACCESS_OBJECT_ID = AO2.ID
      AND A2.ROLE_ID = R2.ID
      AND A2.FIELD_ID = A.FIELD_ID
    );
  END LOOP;
  --// For loop each group
  FOR REFDOMAINGROUP IN REFDOMAINGROUPS LOOP
    --// Remove data Rules
    DELETE FROM CNT_MEMBER_RULE_DATA WHERE MEMBER_ID = (
      SELECT GA.ID FROM CNT_GROUP GA
      INNER JOIN CNT_GROUP GB ON GB.NAME = GA.NAME AND GB.ID = REFDOMAINGROUP.ID
      WHERE GA.IS_LATEST = '1'
      AND GA.DOMAIN_ID = DOMAINID)
         and ACCESS_OBJECT_ID in (
        select id from CNT_ACCESS_OBJECT AO2 where AO2.DOMAIN_ID = DOMAINID 
        AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = NEWOBJECTID);
    --// Copy data Rules of referenced domain to DOMAINID
    INSERT INTO CNT_MEMBER_RULE_DATA (
      ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
      MEMBER_ID, MEMBER_TYPE,
      ACCESS_OBJECT_ID,
      CONDITION_ID,
      ACCESS_RIGHT,
      EXPIRE_DATE_TIME
    )
    SELECT SYS_GUID(), A.REVISION, A.ENTITY_VERSION, AO2.DOMAIN_ID, AO2.HUB_DOMAIN_ID, A.IS_FOR_REFERENCE,
      G2.ID AS MEMBER_ID, A.MEMBER_TYPE,
      AO2.ID AS ACCESS_OBJECT_ID,
      C2.ID AS CONDITION_ID,
      A.ACCESS_RIGHT,
      A.EXPIRE_DATE_TIME
    FROM CNT_MEMBER_RULE_DATA A
    INNER JOIN CNT_CONDITION C ON A.CONDITION_ID = C.ID
    INNER JOIN CNT_CONDITION C2 ON C2.DOMAIN_ID = DOMAINID AND C2.IS_LATEST = '1' AND C2.NAME = C.NAME
    INNER JOIN CNT_ACCESS_OBJECT AO ON AO.ID = A.ACCESS_OBJECT_ID  AND AO.OBJECT_ID =NEWOBJECTID
    INNER JOIN CNT_ACCESS_OBJECT AO2 ON AO2.DOMAIN_ID = DOMAINID AND AO2.IS_LATEST = '1' AND AO2.OBJECT_ID = AO.OBJECT_ID AND AO2.OBJECT_TYPE = AO.OBJECT_TYPE
    INNER JOIN CNT_GROUP G ON A.MEMBER_ID = G.ID
    INNER JOIN CNT_GROUP G2 ON G2.DOMAIN_ID = DOMAINID AND G2.IS_LATEST = '1' AND G2.NAME = G.NAME
    WHERE G.NAME = REFDOMAINGROUP.NAME
    AND G.DOMAIN_ID = '/'
    AND NOT EXISTS (
      SELECT 1
      FROM CNT_MEMBER_RULE_DATA A2
      WHERE A2.ACCESS_OBJECT_ID = AO2.ID
      AND A2.MEMBER_ID = G2.ID
      AND A2.CONDITION_ID = C2.id
    );
  END LOOP;

    --************* 01 RULE_ACTION ***************
    /*
        Consequence
        -----------
        1 <USER> <GROUP> entry contains:
            + 1 role
            + 1 accessObject
            + multiple condition (selection field)
            + multiple actions with same role, accessObject, condition (multiple selection fields)
     */
    DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleActionAdmin' AND DOMAIN_ID = DOMAINID
    and PARENT_ID in (
        select id from CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID = DOMAINID
        and OBJ_ID=NEWOBJECTID
    );
    DELETE FROM CNT_RULE_ACTION_ADMIN WHERE DOMAIN_ID = DOMAINID
     and OBJ_ID=NEWOBJECTID
    ;

    INSERT INTO CNT_RULE_ACTION_ADMIN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, IS_DISALLOW, CONDITION_ID_VALUE
    )
    SELECT LOWER(SYS_GUID()), 1, 1, rule.DOMAIN_ID, rule.DOMAIN_ID, '0',
        rule.ROLE_ID, ao.OBJECT_ID, ao.OBJECT_TYPE, ao.NAME, (case when rule.access_right = 2 then true else false end) as IS_DISALLOW,condition.name
    FROM CNT_RULE_ACTION rule
    INNER JOIN CNT_ROLE role ON role.ID = rule.ROLE_ID AND role.IS_LATEST = '1'
    INNER JOIN CNT_CONDITION condition ON condition.ID = rule.CONDITION_ID AND condition.IS_LATEST = '1'
    INNER JOIN CNT_ACCESS_OBJECT ao ON ao.ID = rule.ACCESS_OBJECT_ID AND ao.IS_LATEST = '1' and ao.OBJECT_ID=NEWOBJECTID
    WHERE rule.DOMAIN_ID = DOMAINID
    GROUP BY rule.ROLE_ID, ao.OBJECT_ID, ao.OBJECT_TYPE, ao.NAME, RULE.ACCESS_RIGHT, rule.DOMAIN_ID ,condition.name ;

    INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME)
    SELECT CRAA.ID, DOMAINID, C.ENTITY_NAME
    FROM CNT_RULE_ACTION_ADMIN CRAA
    INNER JOIN CNT_ENTITY_DEFINITION C ON C.PRODUCT_TABLE_NAME ='CNT_RULE_ACTION_ADMIN'
    where CRAA.OBJ_ID=NEWOBJECTID
    AND NOT EXISTS (
        SELECT 1
        FROM CTM_ROLE
        WHERE ID = CRAA.ID
    );

    INSERT INTO CNT_ROLE_SLN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON,
        PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE
    )
    SELECT LOWER(SYS_GUID()), 1, 1, ruleAdmin.DOMAIN_ID, ruleAdmin.DOMAIN_ID, '0',
        'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        ruleAdmin.ID, 'RuleActionAdmin', 'conditionId', 'Condition', C.ID, C.NAME
    FROM CNT_RULE_ACTION_ADMIN ruleAdmin
        INNER JOIN CNT_ACCESS_OBJECT ao
        ON ao.OBJECT_ID    = ruleAdmin.OBJ_ID
        AND ao.OBJECT_TYPE = ruleAdmin.OBJ_TYPE
        AND ao.DOMAIN_ID   = ruleAdmin.DOMAIN_ID
        AND ao.IS_LATEST   = '1'
        and ao.OBJECT_ID=NEWOBJECTID
        INNER JOIN CNT_RULE_ACTION rule
        ON rule.ROLE_ID           = ruleAdmin.ROLE_ID
        AND RULE.ACCESS_OBJECT_ID = AO.ID
        AND RULE.DOMAIN_ID        = RULEADMIN.DOMAIN_ID
        INNER JOIN CNT_CONDITION C
        ON C.ID                   = RULE.CONDITION_ID
        WHERE RULEADMIN.DOMAIN_ID = DOMAINID
        and rule.ACCESS_RIGHT = (case when RULEADMIN.IS_DISALLOW then 2 else 1 end)
        AND ruleAdmin.condition_id_value = C.NAME
  GROUP BY RULEADMIN.DOMAIN_ID, RULEADMIN.ID, C.ID, C.NAME, RULEADMIN.IS_DISALLOW;

    INSERT INTO CNT_ROLE_SLN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON,
        PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE
    )
    SELECT LOWER(SYS_GUID()), 1, 1, ruleAdmin.DOMAIN_ID, ruleAdmin.DOMAIN_ID, '0',
        'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        ruleAdmin.ID, 'RuleActionAdmin', 'actionId', null, null, aoa.ACTION_ID
    FROM CNT_RULE_ACTION_ADMIN ruleAdmin
    INNER JOIN CNT_ACCESS_OBJECT ao 
        ON ao.OBJECT_ID = ruleAdmin.OBJ_ID AND ao.OBJECT_TYPE = ruleAdmin.OBJ_TYPE
        AND ao.DOMAIN_ID = ruleAdmin.DOMAIN_ID AND ao.IS_LATEST = '1' and ao.OBJECT_ID=NEWOBJECTID
    INNER JOIN CNT_ACCESS_OBJECT aroot
    ON aroot.OBJECT_ID = ao.OBJECT_ID
    AND aroot.OBJECT_TYPE = ao.OBJECT_TYPE
    AND aroot.DOMAIN_ID = '/'
    AND aroot.IS_LATEST = '1'
    INNER JOIN CNT_RULE_ACTION rule
        ON rule.ROLE_ID = ruleAdmin.ROLE_ID
        AND rule.ACCESS_OBJECT_ID = ao.ID
        AND rule.DOMAIN_ID = ruleAdmin.DOMAIN_ID
    INNER JOIN CNT_ACCESS_OBJECT_ACTION aoa
        ON aroot.ID = aoa.ACCESS_OBJECT_ID
        AND rule.ACTION_ID = aoa.ACTION_ID
    INNER JOIN CNT_CONDITION C ON C.ID = rule.CONDITION_ID
    WHERE ruleAdmin.DOMAIN_ID = DOMAINID
   and rule.ACCESS_RIGHT = (case when RULEADMIN.IS_DISALLOW then 2 else 1 end) AND ruleAdmin.condition_id_value = C.NAME
    GROUP BY ruleAdmin.DOMAIN_ID, ruleAdmin.ID, aoa.ID, aoa.ACTION_ID, ruleAdmin.IS_DISALLOW;


    --************* 02 RULE_UI ***********************************************
    /*
        Consequence
        -----------
        1 <USER> <GROUP> entry contains:
            + 1 role
            + 1 access object
            + 1 access right
            + multiple condition (selection field)
            + multiple ui field Id with same object, access right, condition (multiple selection fields)
     */
  --// Note: objectId selection field is deprecated (there's no objectId selection field anymore)
    DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleUiAdmin' AND DOMAIN_ID = DOMAINID
        and PARENT_ID in (
        select id from CNT_RULE_UI_ADMIN WHERE DOMAIN_ID = DOMAINID
        and OBJ_ID=NEWOBJECTID
    );
    DELETE FROM CNT_RULE_UI_ADMIN WHERE DOMAIN_ID = DOMAINID and OBJ_ID=NEWOBJECTID;

    INSERT INTO CNT_RULE_UI_ADMIN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        ROLE_ID, OBJ_ID, OBJ_TYPE, condition_id_value, OBJ_NAME, ACCESS_RIGHT
    )
    SELECT LOWER(SYS_GUID()), 1, 1, rule.DOMAIN_ID, rule.DOMAIN_ID, '0',
        rule.ROLE_ID, ao.OBJECT_ID, ao.OBJECT_TYPE, condition.name,  ao.NAME, rule.ACCESS_RIGHT
    FROM CNT_RULE_UI rule
    INNER JOIN CNT_ROLE role ON role.ID = rule.ROLE_ID AND role.IS_LATEST = '1'
    INNER JOIN CNT_CONDITION condition ON condition.ID = rule.CONDITION_ID AND condition.IS_LATEST = '1'
    INNER JOIN CNT_ACCESS_OBJECT ao ON ao.ID = rule.ACCESS_OBJECT_ID AND ao.IS_LATEST = '1' and ao.OBJECT_ID=NEWOBJECTID
    WHERE rule.DOMAIN_ID = DOMAINID
    GROUP BY rule.ROLE_ID, ao.OBJECT_ID, ao.OBJECT_TYPE, condition.name, ao.NAME, rule.ACCESS_RIGHT, rule.DOMAIN_ID;

    INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME)
    SELECT CRUA.ID, DOMAINID, C.ENTITY_NAME
    FROM CNT_RULE_UI_ADMIN CRUA
    INNER JOIN CNT_ENTITY_DEFINITION C ON C.PRODUCT_TABLE_NAME ='CNT_RULE_UI_ADMIN'
    where  CRUA.OBJ_ID=NEWOBJECTID
    AND NOT EXISTS (
        SELECT 1
        FROM CTM_ROLE
        WHERE ID = CRUA.ID
    );

    INSERT INTO CNT_ROLE_SLN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON,
        PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE
    )
    SELECT LOWER(SYS_GUID()), 1, 1, ruleAdmin.DOMAIN_ID, ruleAdmin.DOMAIN_ID, '0',
        'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        ruleAdmin.ID, 'RuleUiAdmin', 'conditionId', 'Condition', C.ID, C.NAME
    FROM CNT_RULE_UI_ADMIN ruleAdmin
    INNER JOIN CNT_ACCESS_OBJECT ao
    ON ao.OBJECT_ID    = ruleAdmin.OBJ_ID
    AND ao.OBJECT_TYPE = ruleAdmin.OBJ_TYPE
    AND ao.DOMAIN_ID   = ruleAdmin.DOMAIN_ID
    AND ao.IS_LATEST   = '1' and ao.OBJECT_ID=NEWOBJECTID
    INNER JOIN CNT_RULE_UI rule
    ON rule.ROLE_ID           = ruleAdmin.ROLE_ID
    AND RULE.ACCESS_OBJECT_ID = AO.ID
    AND rule.ACCESS_RIGHT = ruleAdmin.ACCESS_RIGHT::numeric
    AND RULE.DOMAIN_ID        = RULEADMIN.DOMAIN_ID
    INNER JOIN CNT_CONDITION C
    ON C.ID                   = RULE.CONDITION_ID
    WHERE RULEADMIN.DOMAIN_ID = DOMAINID  AND ruleAdmin.condition_id_value = C.NAME
    GROUP BY RULEADMIN.DOMAIN_ID, RULEADMIN.ID, C.ID, C.NAME, RULEADMIN.ACCESS_RIGHT;

    INSERT INTO CNT_ROLE_SLN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        CREATE_USER, UPDATE_USER, CREATED_ON, UPDATED_ON,
        PARENT_ID, PARENT_ENTITY, FIELD_ID, REF_ENTITY, REF_ID, DISPLAY_VALUE
    )
    SELECT LOWER(SYS_GUID()), 1, 1, ruleAdmin.DOMAIN_ID, ruleAdmin.DOMAIN_ID, '0',
        'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        ruleAdmin.ID, 'RuleUiAdmin', 'uiFieldId', null, null, rule.UI_ID
    FROM CNT_RULE_UI_ADMIN ruleAdmin
    INNER JOIN CNT_ACCESS_OBJECT ao
        ON ao.OBJECT_ID = ruleAdmin.OBJ_ID AND ao.OBJECT_TYPE = ruleAdmin.OBJ_TYPE
        AND ao.DOMAIN_ID = ruleAdmin.DOMAIN_ID AND ao.IS_LATEST = '1' and ao.OBJECT_ID=NEWOBJECTID
    INNER JOIN CNT_RULE_UI rule
        ON rule.ROLE_ID = ruleAdmin.ROLE_ID
        AND rule.ACCESS_OBJECT_ID = ao.ID
        AND rule.ACCESS_RIGHT = to_number(ruleAdmin.ACCESS_RIGHT,'999')
        AND rule.DOMAIN_ID = ruleAdmin.DOMAIN_ID
    INNER JOIN CNT_CONDITION C
    ON C.ID = rule.CONDITION_ID
    WHERE ruleAdmin.DOMAIN_ID = DOMAINID AND ruleAdmin.condition_id_value = C.NAME
    GROUP BY ruleAdmin.DOMAIN_ID, ruleAdmin.ID, rule.UI_ID;

    --****************** 03 RULE_FIELD_DENY ***********************************************
    /*
        Consequence
        -----------
        1 <USER> <GROUP> entry contains:
            + 1 role
            + 1 access object
            + 1 fieldId
     */

    --// objectId selection field is depercated
    DELETE FROM CNT_ROLE_SLN WHERE PARENT_ENTITY='RuleFieldDenyAdmin' AND DOMAIN_ID = DOMAINID
            and PARENT_ID in (
        select id from CNT_RULE_FIELD_DENY_ADMIN WHERE DOMAIN_ID = DOMAINID
        and OBJ_ID=NEWOBJECTID
    );
    DELETE FROM CNT_RULE_FIELD_DENY_ADMIN WHERE DOMAIN_ID = DOMAINID and OBJ_ID=NEWOBJECTID;

    INSERT INTO CNT_RULE_FIELD_DENY_ADMIN (
        ID, REVISION, ENTITY_VERSION, DOMAIN_ID, HUB_DOMAIN_ID, IS_FOR_REFERENCE,
        ROLE_ID, OBJ_ID, OBJ_TYPE, OBJ_NAME, FIELD_ID, FIELD_PATH
    )
    SELECT LOWER(SYS_GUID()), 1, 1, rule.DOMAIN_ID, rule.DOMAIN_ID, '0',
        rule.ROLE_ID, ao.OBJECT_ID, ao.OBJECT_TYPE, ao.NAME, rule.FIELD_ID, rule.FIELD_ID
    FROM CNT_RULE_FIELD_DENY rule
    INNER JOIN CNT_ROLE role ON role.ID = rule.ROLE_ID AND role.IS_LATEST = '1'
    INNER JOIN CNT_ACCESS_OBJECT ao ON ao.ID = rule.ACCESS_OBJECT_ID AND ao.IS_LATEST = '1' and ao.OBJECT_ID=NEWOBJECTID
    WHERE rule.DOMAIN_ID = DOMAINID
    GROUP BY rule.ROLE_ID, ao.OBJECT_ID, ao.OBJECT_TYPE, ao.NAME, rule.FIELD_ID, rule.DOMAIN_ID;

    INSERT INTO CTM_ROLE(ID, DOMAIN_ID, REF_ENTITY_NAME)
    SELECT CRFDA.ID, DOMAINID, C.ENTITY_NAME
    FROM CNT_RULE_FIELD_DENY_ADMIN CRFDA
    INNER JOIN CNT_ENTITY_DEFINITION C ON C.PRODUCT_TABLE_NAME ='CNT_RULE_FIELD_DENY_ADMIN'
    where CRFDA.OBJ_ID=NEWOBJECTID
    AND NOT EXISTS (
        SELECT 1
        FROM CTM_ROLE
        WHERE ID = CRFDA.ID
    );
END LOOP;

END;
$body$ LANGUAGE PLPGSQL;



--// clear the user acl cache #1 (in serialized entity)
DELETE FROM CNT_SERIALIZED_ENTITY WHERE TARGET_ENTITY IN ('UserCacheData', 'ChecksumCacheData', 'Role');

--// clear the user acl cache #2 (in the corrspodning table)
DELETE FROM CNT_DOMAIN_GROUP_CACHED_RULES;

DELETE FROM CNT_CHECKSUM_CACHE_RULE;
DELETE FROM CNT_CHECKSUM_CACHE_HCL_NODE;
DELETE FROM CNT_CKSUM_CACHE_CLASSIFICATION;

DELETE FROM CNT_CHECKSUM_CACHE_DATA;

DELETE FROM CNT_USER_CACHE_RULE;
DELETE FROM CNT_USER_CACHE_HCL_NODE;
DELETE FROM CNT_USER_CACHE_CLASSIFICATION;

DELETE FROM CNT_USER_CACHE_DATA;


--// FILE: DDL_Access_Object_M.sql

INSERT INTO cnt_access_object_m (ID,revision,hub_domain_id,"version",status,doc_status,editing_status,create_user,create_user_name,update_user,update_user_name,created_on,updated_on,integration_source,integration_status,integration_note,is_cpm_initialized,is_latest,ref_no,party_template_ref,party_template_ver,party_template_name,party_name1,party_name2,party_name3,party_name4,party_name5,domain_id ) SELECT(SELECT ID FROM cnt_access_object cao WHERE object_id = 'workflow' AND object_type = 'view' AND domain_id = '/' AND is_latest ),0,'/',0,NULL,'active',NULL,'system','system',NULL,NULL,now( ),NULL,NULL,NULL,NULL,NULL,TRUE,'view:workflow',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'/' WHERE NOT EXISTS ( SELECT 1 FROM cnt_access_object_m WHERE domain_id = '/' AND ref_no = 'view:workflow' );

INSERT INTO cnt_access_object_m (ID,revision,hub_domain_id,"version",status,doc_status,editing_status,create_user,create_user_name,update_user,update_user_name,created_on,updated_on,integration_source,integration_status,integration_note,is_cpm_initialized,is_latest,ref_no,party_template_ref,party_template_ver,party_template_name,party_name1,party_name2,party_name3,party_name4,party_name5,domain_id ) SELECT(SELECT ID FROM cnt_access_object cao WHERE object_id = 'workflow' AND object_type = 'form' AND domain_id = '/' AND is_latest ),0,'/',0,NULL,'active',NULL,'system','system',NULL,NULL,now( ),NULL,NULL,NULL,NULL,NULL,TRUE,'form:workflow',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'/' WHERE NOT EXISTS ( SELECT 1 FROM cnt_access_object_m WHERE domain_id = '/' AND ref_no = 'form:workflow' );

INSERT INTO cnt_access_object_m (ID,revision,hub_domain_id,"version",status,doc_status,editing_status,create_user,create_user_name,update_user,update_user_name,created_on,updated_on,integration_source,integration_status,integration_note,is_cpm_initialized,is_latest,ref_no,party_template_ref,party_template_ver,party_template_name,party_name1,party_name2,party_name3,party_name4,party_name5,domain_id ) SELECT(SELECT ID FROM cnt_access_object cao WHERE object_id = 'workflow' AND object_type = 'entity' AND domain_id = '/' AND is_latest ),0,'/',0,NULL,'active',NULL,'system','system',NULL,NULL,now( ),NULL,NULL,NULL,NULL,NULL,TRUE,'entity:workflow',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'/' WHERE NOT EXISTS ( SELECT 1 FROM cnt_access_object_m WHERE domain_id = '/' AND ref_no = 'entity:workflow' );

INSERT INTO cnt_access_object_m (ID,revision,hub_domain_id,"version",status,doc_status,editing_status,create_user,create_user_name,update_user,update_user_name,created_on,updated_on,integration_source,integration_status,integration_note,is_cpm_initialized,is_latest,ref_no,party_template_ref,party_template_ver,party_template_name,party_name1,party_name2,party_name3,party_name4,party_name5,domain_id ) SELECT(SELECT ID FROM cnt_access_object cao WHERE object_id = 'workflow' AND object_type = 'view' AND domain_id = 'PEPL' AND is_latest ),0,'PEPL',0,NULL,'active',NULL,'system','system',NULL,NULL,now( ),NULL,NULL,NULL,NULL,NULL,TRUE,'view:workflow',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'PEPL' WHERE NOT EXISTS ( SELECT 1 FROM cnt_access_object_m WHERE domain_id = 'PEPL' AND ref_no = 'view:workflow' );

INSERT INTO cnt_access_object_m (ID,revision,hub_domain_id,"version",status,doc_status,editing_status,create_user,create_user_name,update_user,update_user_name,created_on,updated_on,integration_source,integration_status,integration_note,is_cpm_initialized,is_latest,ref_no,party_template_ref,party_template_ver,party_template_name,party_name1,party_name2,party_name3,party_name4,party_name5,domain_id ) SELECT(SELECT ID FROM cnt_access_object cao WHERE object_id = 'workflow' AND object_type = 'form' AND domain_id = 'PEPL' AND is_latest ),0,'PEPL',0,NULL,'active',NULL,'system','system',NULL,NULL,now( ),NULL,NULL,NULL,NULL,NULL,TRUE,'form:workflow',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'PEPL' WHERE NOT EXISTS ( SELECT 1 FROM cnt_access_object_m WHERE domain_id = 'PEPL' AND ref_no = 'form:workflow' );

INSERT INTO cnt_access_object_m (ID,revision,hub_domain_id,"version",status,doc_status,editing_status,create_user,create_user_name,update_user,update_user_name,created_on,updated_on,integration_source,integration_status,integration_note,is_cpm_initialized,is_latest,ref_no,party_template_ref,party_template_ver,party_template_name,party_name1,party_name2,party_name3,party_name4,party_name5,domain_id ) SELECT(SELECT ID FROM cnt_access_object cao WHERE object_id = 'workflow' AND object_type = 'entity' AND domain_id = 'PEPL' AND is_latest ),0,'PEPL',0,NULL,'active',NULL,'system','system',NULL,NULL,now( ),NULL,NULL,NULL,NULL,NULL,TRUE,'entity:workflow',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'PEPL' WHERE NOT EXISTS ( SELECT 1 FROM cnt_access_object_m WHERE domain_id = 'PEPL' AND ref_no = 'entity:workflow' );

COMMIT;