#    ***************************************************************
#    * Licensed to the Apache Software Foundation (ASF) under one
#    * or more contributor license agreements.  See the NOTICE file
#    * distributed with this work for additional information
#    * regarding copyright ownership.  The ASF licenses this file
#    * to you under the Apache License, Version 2.0 (the
#    * "License"); you may not use this file except in compliance
#    * with the License.  You may obtain a copy of the License at
#    *
#    *   http://www.apache.org/licenses/LICENSE-2.0
#    *
#    * Unless required by applicable law or agreed to in writing,
#    * software distributed under the License is distributed on an
#    * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
#    * KIND, either express or implied.  See the License for the
#    * specific language governing permissions and limitations
#    * under the License.
#    ***************************************************************
#  ****************************************************
#  * Work Path Setting
#  ****************************************************


# Project settings
domainId=PEPL
project.name=PEPL
project.full.name=PEPL
depend.product.version=15.9.0
project.version=15.9.0P.05.0
# tag.previous.version : used for delta find if there are any changes in the others/CBX_* folders
tag.previous.version=PEPL.15.9.0P.04.0


war.remove.jar=
# war.delta.includes.[project.version]
# if need to pack the file in the war file, set the value to the includes value
# eg: war.delta.includes.8.3.2P.01.0=xxx


task.remove.jar=
# The delta files that includes in the cbx-task-update-twl-[project.version] zip
# task.delta.includes.8.2.0P.2R.99.4=scheduler/*.*
task.delta.includes.12.22.0P.08.6=command_listener_start.bat,command_listener_start.sh


# Java-source files settings
dir.src=src/main/java
dir.res=src/main/resources


# Webapp settings
# war.check.local - whether to check local path for the existence of the war file. Don't copy if local file found
dir.webapp=src/main/webapp
war.dir=war
war.local.path=${war.dir}/cbx-biz-${depend.product.version}.war
war.remote.full.path=${dir.product.release}/${depend.product.version}/cbx-full
war.remote.path=${war.remote.full.path}/bin/cbx-biz-${depend.product.version}.war
war.check.local=true


# build settings
dir.lib=${dir.webapp}/WEB-INF/lib
dir.build=build
dir.build.classes=${dir.build}/classes

#  ****************************************************
#  * Release Setting
#  ****************************************************
dir.product.release=//*************/data/release

# Project customized product directories
#
dir.cbx_common=others/CBX_Common
dir.cbx_core=others/CBX_Core
dir.cbx_ui=others/CBX_UI
dir.cbx_general=others/CBX_General
dir.cbx_biz=others/CBX_Business
dir.cbx_war=others/CBX_War
dir.task=others/task

# release.product.upgrade.[project.version]
# 		The CBX versions in comma separated list to be upgraded in the specified project version.
# 		Note: There should not be any spaces in-between the versions
# release.product.upgrade.run.data_migration  specify if we will handle and run data migration scripts
# release.product.upgrade.run.hide_new_feature  specify if we will handle and run hide new feature scripts
#
#release.product.upgrade.9.22.0P.01.0=
#release.product.upgrade.10.15.0P.01.0=
#release.product.upgrade.automation.domain_id.replacement=true
#release.product.upgrade.run.product.data_migration=true
#release.product.upgrade.run.product.hide_new_feature=true

# hornetq.delta.includes.[project.version]
# if need to pack the file in others/HornetQ, set the value to the includes value
# e.g. hornetq.delta.includes.8.3.2P.01.0=hornetq-*.xml, *.txt
#hornetq.delta.includes.9.22.0P.01.0=

dir.product.temp=/temp/cbx-core
dir.temp.target=${dir.product.temp}/target
dir.target=target
dir.release=release
dir.release.cbx-biz=cbx-biz
dir.release.cbx-general=cbx-general
dir.release.cbx-ui=cbx-ui
dir.release.cbx-core=cbx-core
dir.release.cbx-common=cbx-common
dir.release.cbx-project=cbx-pepl
dir.release.cbx-pepl=cbx-pepl


#  ****************************************************
#  * DB Setting
#  ****************************************************
#DB Type --- oracle,mysql,db2,sqlserver,pgsql
gen.db.type=pgsql

#
# Following section are variables used in running the DB scriptss.
#
# dir.release.full: The release folder of pts-pack-full
# dir.apply_script.base: The base folder of the DB script to be applied
# apply_script.path: The relative path of the script file to be applied (based on dir.apply_script.base)
#                    e.g. 3.pre/ddl-010-pts-script.sql
# apply_script.user: The user apply the script. Jenkins can have the build user env. variable: https://wiki.jenkins.io/display/JENKINS/Build+User+Vars+Plugin
#
dir.release.pts-full=cbx-full
dir.apply_script.base=dbscripts/${project.version}
apply_script.path=<input_parameter>
apply_script.user=nobody

#
# Common settings
# release.db.host: Host of the DB
# release.db.port: Port of the DB
#
#release.db.host=psg-pgsql10.zh.coresolutions.com
release.db.host=localhost
release.db.port=5432

#
# release.db.name: DB Name
# release.db.user: DB User
# release.db.pwd: DB Password
# release.db.dump.folder: Folder store the previous release DB dump (used by the release-all in build-release.xml)
# release.db.dump.path: Path of the previous release DB dump
#
release.db.name=pts_build9
release.db.user=pts_build9
release.db.pwd=p
release.db.dump.folder=/opt/pgsql/cbx/dbdump
release.db.dump.file=pts.10.15.0P.01.0.zip

#dir.build.dump.imp=${dir.target}/imp
# release.db.postscript: script to be run after running all the upgrade scripts
#                        should be found under the folder: ant-lib folder
release.db.postscript=none

# release.db.configurationscript: script to be run before backup-db-dump
#                                 should be found under the folder: ant-lib folder
release.db.configurationscript=project-update-configuration.sql
