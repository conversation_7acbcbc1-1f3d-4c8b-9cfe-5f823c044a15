<?xml version="1.0" encoding="UTF-8" ?>
<project name="cbx-pepl" default="compile" basedir=".">

    <taskdef resource="net/sf/antcontrib/antlib.xml" classpath="ant-lib/ant-contrib-1.0b3.jar"/>
    <import file="ant-lib/macro-project.xml"/>

    <!-- =================================
            Load Property Setting
     ================================= -->
    <property file="build.properties"/>

    <property name="jar.name" value="cbx-ext-${domainId}.${project.version}.jar" />

    <!-- Jetty settings -->
    <path id="jetty.plugin.classpath">
        <fileset dir="jetty-lib" includes="*.jar"/>
    </path>

    <taskdef classpathref="jetty.plugin.classpath" resource="tasks.properties" loaderref="jetty.loader" />

    <typedef name="selectChannelConnector" classname="org.mortbay.jetty.nio.SelectChannelConnector"
            classpathref="jetty.plugin.classpath" loaderref="jetty.loader" />

    <!-- =================================
               compile classpath
     ================================= -->
    <path id="compile.lib.path">
        <fileset dir="${dir.lib}">
            <include name="**/*.jar"/>
        </fileset>
    </path>

    <!-- =================================
      target: clean
     ================================= -->
    <target name="clean" description="Clean the target folders">
        <delete dir="${dir.build}" includeemptydirs="true" />
        <delete dir="${dir.release}" includeemptydirs="true" />
        <delete dir="${basedir}/jetty-temp" includeemptydirs="true" />
        <delete dir="${basedir}/logs" includeemptydirs="true" />
        <delete dir="${basedir}/temp" includeemptydirs="true" />
    </target>

   <!-- =================================
      target: compile
     ================================= -->
    <target name="compile" description="Compile the project">
        <mkdir dir="${dir.build.classes}" />
        <copy todir="${dir.build.classes}">
          <fileset dir="${dir.src}">
               <include name="**/*.xml"/>
               <include name="**/*.properties"/>
               <exclude name="**/*log4j.properties" />
               <exclude name="**/*log4j.xml" />
          </fileset>
          <fileset dir="${dir.res}">
               <include name="**/*.xml"/>
               <include name="**/*.json"/>
               <include name="**/*.properties"/>
               <include name="**/*.xsd"/>
               <include name="**/*.jasper"/>
               <include name="**/*.jpg"/>
               <exclude name="**/*log4j.properties" />
               <exclude name="**/*log4j.xml" />
          </fileset>
        </copy>
        <javac srcdir="${dir.src}" destdir="${dir.build.classes}" classpathref="compile.lib.path"
               target="1.8" source="1.8" includeantruntime="false" debug="true"/>
    </target>

    <!-- =================================
      target: jar
     ================================= -->
    <target name="jar" depends="clean,compile" description="Make a jar file for this project">
        <jar destfile="${dir.release}/${jar.name}">
            <fileset dir="${dir.build.classes}" />
        </jar>
    </target>

    <!-- ====================================
      target: copy dlgl jar to lib directory
     ====================================== -->
    <target name="copy.jar.to.lib" depends="clean,compile,jar" description="Make a jar file for this project">
         <delete dir="${dir.lib}" includes="cbx-ext-*.jar" />
         <if>
            <available file="${dir.release}/${jar.name}" type="file"/>
            <then>
               <copy todir="${dir.lib}" file="${dir.release}/${jar.name}" preservelastmodified="true"
                        overwrite="true"/>
            </then>
         </if>
    </target>

     <target name="-genActionMappingSql" depends="clean,compile">
		     <path id="test.path.id">
					<pathelement path="${java.home}/../lib/tools.jar" />       <!-- Needed for JavaDoc -->
					<path location="${dir.build}/classes" />
					<fileset dir="${dir.lib}">
						<include name="**/*.jar" />
					</fileset>
				</path>
         <taskdef name="genActionMappingSqlTask" classname="com.core.cbx.resource.build.ActionMappingGenerator">
             <classpath refid ="test.path.id"/>
         </taskdef>
          <genActionMappingSqlTask
             srcdir="${basedir}/src/main/java"
             targetFile="${dbfileActionMap}"
             userId="cbx-pepl"
             dbType="pgsql"  >
		</genActionMappingSqlTask>
     </target>

    <!-- =================================
        Target: genActionMappingSql
        Description: generate the Action Mapping SQL
     ================================= -->
     <target name="genActionMappingSql" description="Generater the Action Mapping SQL">
        <antcall target="-genActionMappingSql">
          <param name="dbfileActionMap" value="${dir.build}/action-map.sql"/>
        </antcall>
        	<echo file="${dir.build}/action-map.sql" append="true"/>
        	<var name="dir.db_common" value="./dbscripts/_common"/>
        	<concat destfile="${dir.build}/action-map.sql" append="yes" fixlastline="yes">
               <filelist dir="${dir.db_common}" files="004-update-action-map.sql"/>
            </concat>
      </target>

    <!-- =================================
        Target: gen.checksum
        Description: generate the checksum file
     ================================= -->
    <target name="gen.checksum" description="Generate check sum file">
        <genChecksumWithPathInfo dir="${final.release}" prefix="cbx-ext-${domainId}.${project.version}-"/>
    </target>

     <!-- =================================
        Target: clean.env
        Description: clean the environment
     ===================================== -->
     <target name="clean.env" description="clean the webapp directory">
        <delete dir="${dir.webapp}" includeemptydirs="true" />
     </target>

    <!-- =========================================
        Target: init.env
        Description: initialize the environment
     ========================================== -->
     <target name="init.env" depends="clean.env" description="Init the development environment">
         <if>
            <not>
               <available file="${dir.webapp}" type="dir"/>
            </not>
            <then>
               <mkdir dir="${dir.webapp}"/>
            </then>
         </if>
         <if>
            <not>
               <available file="${war.dir}" type="dir"/>
            </not>
            <then>
               <mkdir dir="${war.dir}"/>
            </then>
         </if>
         <if>
            <not>
               <available file="${war.local.path}" type="file"/>
            </not>
            <then>
                <copy todir="${war.dir}" file="${war.remote.path}" />
            </then>
         </if>
         <unzip src="${war.local.path}" dest="${dir.webapp}"/>
     </target>

    <!-- =================================
      Jetty: run
      Ref URL: http://docs.codehaus.org/display/JETTY/Ant+Jetty+Plugin
     ================================= -->
    <target name="jetty.run" depends="copy.jar.to.lib" description="Run the Jetty Server">
        <copy todir="${dir.build.classes}">
          <fileset dir="${dir.src}">
               <include name="**/*log4j.properties" />
               <include name="**/*log4j.xml" />
          </fileset>
          <fileset dir="${dir.res}">
               <include name="**/*log4j.properties" />
               <include name="**/*log4j.xml" />
          </fileset>
        </copy>
        <jetty tempDirectory="jetty-temp" >
            <systemProperties>
                <systemProperty name="jetty.status" value="jetty-status-001"/>
                <systemProperty name="system.disableDistributedCache" value="true"/>
                <systemProperty name="system.disableEntityCache" value="true"/>
            </systemProperties>
            <connectors>
                <selectChannelConnector port="8083" />
            </connectors>
            <webApp name="main" warfile="${dir.webapp}" contextpath="/main"  jettyEnvXml="settings/jettyenv.xml">
                <lib dir="lib" includes="*.jar" />
                <classes dir="${dir.build.classes}"/>
            </webApp>
        </jetty>
    </target>

    <!-- =================================
      Target: gen.sys.msg
      Description: generate the system meesage for SystemMessage.txt
     ================================= -->
    <target name="gen.sys.msg">
        <echo>Generate the SystemMessage SQL</echo>
        <genSystemMessage messageTxtPath="${dir.res}/SystemMessage.txt"
                          targetSQLPath="${dir.build}/system/DML_SYS_MSG.sql"
                          domainId="${domainId}"/>
    </target>

    <!-- =================================
      Target: eclipse
      Description: generate the eclipse .project and .classpath files
     ================================= -->
    <taskdef name="eclipse" classname="prantl.ant.eclipse.EclipseTask" classpath="ant-lib/ant-eclipse-1.0-core.jar" />

    <target name="eclipse" description="synchronize Eclipse Project Files">
        <if>
           <not>
              <available file="${dir.lib}" type="dir"/>
           </not>
           <then>
              <mkdir dir="${dir.lib}"/>
           </then>
        </if>
        <echo>Eclipse project name: ${project.name}</echo>
        <eclipse mode="java" updatealways="true">
            <project name="${project.name}">
                <!--buildCommand name="net.sf.eclipsecs.core.CheckstyleBuilder" nature="net.sf.eclipsecs.core.CheckstyleNature"/>
                <buildCommand name="edu.umd.cs.findbugs.plugin.eclipse.findbugsBuilder" nature="edu.umd.cs.findbugs.plugin.eclipse.findbugsNature"/-->
            </project>
            <classpath>
                <source path="${dir.src}" output="${dir.build.classes}"/>
                <source path="${dir.res}" output="${dir.build.classes}"/>
                <library pathref="compile.lib.path"/>
                <output path="${dir.build.classes}" />
            </classpath>
        </eclipse>
        <delete dir="${dir.webapp}" includeemptydirs="true" />
    </target>

</project>
