<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.core.cbx</groupId>
    <artifactId>cbx-PEPL</artifactId>
    <packaging>jar</packaging>
    <version>12.22.0P.09.6</version>
    <name>cbx-PEPL</name>

    <properties>
        <cbx-biz.version>12.22.0</cbx-biz.version>
        <cbx-vendor-migration.version>9.5.0</cbx-vendor-migration.version>

        <!-- other properties -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <checkstyle.config.location>${project.basedir}/settings/core-cnt-checkstyle-5.0.xml</checkstyle.config.location>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <distribute.release.url>http://192.168.2.55:8088/nexus/content/repositories/cbx-releases</distribute.release.url>
        <distribute.snapshots.url>http://192.168.2.55:8088/nexus/content/repositories/cbx-snapshots</distribute.snapshots.url>
        <distribute.release.artifactory.url>http://artifactory.cbxsoftware.com/artifactory/libs-release-local</distribute.release.artifactory.url>
        <distribute.snapshots.artifactory.url>http://artifactory.cbxsoftware.com/artifactory/libs-snapshot-local</distribute.snapshots.artifactory.url>
    </properties>

    <build>
        <resources>
            <resource>
                <directory>${basedir}</directory>
                <includes>
                    <include>releasenotes.txt</include>
                    <include>source-buildinstructions.txt</include>
                    <include>task-instructions.txt</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xsd</include>
                    <include>**/*.jasper</include>
                    <include>**/*.jpg</include>
                </includes>
                <excludes>
                    <exclude>**/*log4j2.xml</exclude>
                    <exclude>**/log4j-xinclude-properties.xml</exclude>
                    <exclude>releasenotes.txt</exclude>
                    <exclude>source-buildinstructions.txt</exclude>
                </excludes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.2.20.v20161216</version>
                <configuration>
                    <webApp>
                        <contextPath>/main</contextPath>
                        <jettyEnvXml>settings/jetty9env.xml</jettyEnvXml>
                        <webInfIncludeJarPattern>.*/cbx-[^/]*\.jar$|.*/classes/.*</webInfIncludeJarPattern>
                    </webApp>
                    <httpConnector>
                        <port>8083</port>
                    </httpConnector>
                    <systemProperties>
                        <systemProperty>
                            <name>jetty.status</name>
                            <value>jetty-status-001</value>
                        </systemProperty>
                        <systemProperty>
                            <name>system.disableDistributedCache</name>
                            <value>true</value>
                        </systemProperty>
                        <systemProperty>
                            <name>system.disableEntityCache</name>
                            <value>true</value>
                        </systemProperty>
                    </systemProperties>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.core.oracle</groupId>
                        <artifactId>ojdbc</artifactId>
                        <version>12.1.0.2.0</version>
                    </dependency>
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>9.4.1211.jre7</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <excludes>
                        <exclude>releasenotes.txt</exclude>
                        <exclude>source-buildinstructions.txt</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>package-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                            <goal>test-jar-no-fork</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.17</version>
                <dependencies>
                    <dependency>
                        <groupId>com.core.cbx</groupId>
                        <artifactId>core-checkstyle-checks</artifactId>
                        <version>0.0.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>6.19</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>2.5</version>
            <scope>provided</scope>
        </dependency>
        <!-- runtime -->
        <dependency>
            <groupId>com.core.cbx</groupId>
            <artifactId>cbx-biz</artifactId>
            <version>${cbx-biz.version}</version>
        </dependency>
                        <dependency>
                    <groupId>com.core.cbx</groupId>
                    <artifactId>cbx-vendor-migration</artifactId>
                    <version>${cbx-vendor-migration.version}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>*</artifactId>
                            <groupId>*</groupId>
                        </exclusion>
                    </exclusions>
                    <scope>compile</scope>
                </dependency>
    </dependencies>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.8.cbx</version>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>jettyLog</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-resources-plugin</artifactId>
                        <version>2.6</version>
                        <executions>
                            <execution>
                                <id>copy-log4j</id>
                                <phase>test-compile</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/classes</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>src/main/resources</directory>
                                            <includes>
                                                <include>log4j2.xml</include>
                                                <include>log4j-xinclude-properties.xml</include>
                                            </includes>
                                            <filtering>true</filtering>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>asciidoctor</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.5.0</version>
                        <executions>
                            <execution>
                                <id>apiDocumentGenerator</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>java</goal>
                                </goals>
                                <configuration>
                                    <mainClass>com.core.cbx.api.generator.ApiDocumentGenerator</mainClass>
                                </configuration>
                            </execution>
                            <execution>
                                <id>apiReleaseListGenerator</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>java</goal>
                                </goals>
                                <configuration>
                                        <mainClass>com.core.cbx.api.generator.ApiReleaseListGenerator</mainClass>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.asciidoctor</groupId>
                        <artifactId>asciidoctor-maven-plugin</artifactId>
                        <version>1.5.2</version>
                        <dependencies>
                            <dependency>
                                <groupId>org.asciidoctor</groupId>
                                <artifactId>asciidoctorj-pdf</artifactId>
                                <version>1.5.0-alpha.11</version>
                            </dependency>
                            <!-- Comment this section to use the default
                                jruby artifact provided by the plugin -->
                            <dependency>
                                <groupId>org.jruby</groupId>
                                <artifactId>jruby-complete</artifactId>
                                <version>9.1.7.0</version>
                            </dependency>
                            <!-- Comment this section to use the default
                                AsciidoctorJ artifact provided by the plugin -->
                            <dependency>
                                <groupId>org.asciidoctor</groupId>
                                <artifactId>asciidoctorj</artifactId>
                                <version>1.5.4.1</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>output-html</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>process-asciidoc</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>src/docs/html</outputDirectory>
                                    <sourceHighlighter>coderay</sourceHighlighter>
                                    <backend>html</backend>
                                </configuration>
                            </execution>
                            <execution>
                                <id>output-docbook</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>process-asciidoc</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>src/docs/xml</outputDirectory>
                                    <backend>docbook</backend>
                                    <doctype>book</doctype>
                                </configuration>
                            </execution>
                            <execution>
                                <id>generate-pdf-doc</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>process-asciidoc</goal>
                                </goals>
                                <configuration>
                                    <backend>pdf</backend>
                                    <!-- Since 1.5.0-alpha.9 PDF back-end
                                        can use 'rouge' as well as 'coderay' source highlighting -->
                                    <sourceHighlighter>rouge</sourceHighlighter>
                                    <outputDirectory>src/docs/pdf</outputDirectory>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <sourceDirectory>src/docs/asciidoc</sourceDirectory>
                            <headerFooter>false</headerFooter>
                            <preserveDirectories>false</preserveDirectories>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
             <id>hk-artifactory</id>
             <distributionManagement>
                <repository>
                    <id>cbx_server</id>
                    <name>CBX Repository</name>
                    <url>${distribute.release.artifactory.url}</url>
                </repository>
                <snapshotRepository>
                    <id>cbx_server</id>
                    <name>CBX Snapshots Repository</name>
                    <url>${distribute.snapshots.artifactory.url}</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>cbx_server</id>
            <name>CBX Repository</name>
            <url>${distribute.release.url}</url>
        </repository>
        <snapshotRepository>
            <id>cbx_server</id>
            <name>CBX Snapshots Repository</name>
            <url>${distribute.snapshots.url}</url>
        </snapshotRepository>
    </distributionManagement>

</project>
