<?xml version="1.0" encoding="UTF-8" ?>
<project basedir="." default="release-full" name="project-release" xmlns:cbx="antlib:com.core.cbx.script">

    <property name="cbx.script.version" value="1.0"/>

    <taskdef resource="com/core/cbx/script/antlib.xml" classpath="ant-lib/cbx-script-${cbx.script.version}.jar" uri="antlib:com.core.cbx.script"/>

    <taskdef classpath="ant-lib/ant-contrib-1.0b3.jar" resource="net/sf/antcontrib/antlib.xml" />
    <import file="${basedir}/ant-lib/macro-project.xml" />

    <!-- =================================
            Property Setting
       ================================= -->
    <property file="build.properties" />
    <property name="dir.temp" value="${basedir}/temp" />
    <property name="project.jar.name" value="cbx-ext-${domainId}.${project.version}.jar" />

    <!-- =================================
             compile classpath
       ================================= -->
    <path id="compile.lib.path">
        <fileset dir="${dir.lib}">
            <include name="**/*.jar" />
        </fileset>
        <fileset dir="${basedir}/lib">
            <include name="*.jar" />
        </fileset>
    </path>

    <!-- =================================
        Target: clean
       ================================= -->
    <target description="Clean the target folders" name="clean">
        <cbx:cbx-clean dir="${dir.build},${dir.target},${dir.temp},${dir.webapp}" includeemptydirs="true" quiet="true" />
    </target>

    <!-- =========================================
        Target: unzip-war
       ========================================== -->
    <target description="copy and unzip product war" name="unzip-war">
        <cbx:unzip-war dir.webapp="${dir.webapp}" war.dir="${war.dir}" war.local.path="${war.local.path}" war.remote.path="${war.remote.path}" />
    </target>

    <!-- =========================================
        Target: copy-customized-jars
       ========================================== -->
    <target description="Copy project customized jars" name="copy-customized-jars">
        <cbx:copy-customized-jars dir.release.cbx-common="${dir.release.cbx-common}" dir.release.cbx-core="${dir.release.cbx-core}" dir.release.cbx-ui="${dir.release.cbx-ui}" dir.release.cbx-general="${dir.release.cbx-general}" dir.release.cbx-biz="${dir.release.cbx-biz}" dir.lib="${dir.lib}" dir.temp="${dir.temp}"/>
    </target>

    <!-- =================================
        Target: compile
      ================================= -->
    <target description="Compile the project" name="compile">
        <cbx:cbx-compile dir.build.classes="${dir.build.classes}" dir.src="${dir.src}" dir.res="${dir.res}" project.name="${project.name}"/>
        <copy todir="${dir.build.classes}">
          <fileset dir="${dir.res}">
               <include name="**/*.json"/>
          </fileset>
        </copy>
    </target>

    <!-- =================================
      target: jar
     ================================= -->
    <target name="jar">
        <cbx:cbx-jar dir.build.classes="${dir.build.classes}" project.jar.name="${project.jar.name}" dir.target="${dir.target}" />
    </target>

    <!-- =================================
        Target: release-jar
     ================================= -->
    <target depends="clean, unzip-war, copy-customized-jars, compile, jar" name="release-jar" />

    <!-- =================================
        Target: gen-action-map-sql
     ================================= -->
    <target name="gen-action-map-sql">
        <cbx:gen-action-map-sql dir.build.classes="${dir.build.classes}" project.name="${project.name}" dir.target="${dir.target}" />
    </target>

    <!-- =================================
        Target: pack-war
      ================================= -->
    <target depends="clean, unzip-war, copy-customized-jars" name="pack-war">
        <cbx:packWar project.version="${project.version}" project.name="${project.name}" dir.target="${dir.target}" dir.lib="${dir.lib}" dir.cbx.war="${dir.cbx_war}" dir.webapp="${dir.webapp}" dir.release.cbx-project.name="${dir.release.cbx-project}" depend.product.version="${depend.product.version}" war.remove.jar="${war.remove.jar}" dir.temp="${dir.temp}"/>
    </target>

    <!-- =================================
        Target: pack-task
       ================================= -->
    <target depends="clean" name="pack-task">
        <cbx:packTask project.version="${project.version}" project.name="${project.name}" depend.product.version="${depend.product.version}" dir.target="${dir.target}" dir.task="${dir.task}" dir.build="${dir.build}" dir.temp="${dir.temp}"  dir.release.cbx-core="${dir.release.cbx-core}" dir.release.cbx-common="${dir.release.cbx-common}"  dir.release.cbx-ui="${dir.release.cbx-ui}" dir.release.cbx-general="${dir.release.cbx-general}" dir.release.cbx-biz="${dir.release.cbx-biz}" dir.release.cbx-project.name="${dir.release.cbx-project}" dir.product.release="${dir.product.release}" task.remove.jar="${task.remove.jar}" />
    </target>

    <!-- =================================
        Target: pack-hornetq
    ================================== -->
    <target depends="clean" name="pack-hornetq">
        <cbx:packHornetq project.version="${project.version}" dir.target="${dir.target}" />
    </target>

    <!-- =================================
        Target: pack-guide
    ================================= -->
    <target depends="clean" name="pack-guide">
        <cbx:packGuide project.name="${project.name}" project.version="${project.version}" project.full.name="${project.full.name}" dir.target="${dir.target}" />
    </target>

    <!-- =================================
           Target: pack-others
    ================================== -->
    <target depends="clean" name="pack-others">
        <cbx:packOthers project.version="${project.version}" dir.target="${dir.target}"/>
    </target>

	<!-- =================================
    	Target: pack-config-xml
    ================================= -->
	<target depends="clean" name="pack-config-xml">
		<cbx:packConfigXml project.version="${project.version}" dir.target="${dir.target}" />
	</target>

    <!-- =================================
        Target: release-full
    ================================== -->
    <target name="release-full" depends="clean, pack-hornetq, pack-config-xml, pack-others, pack-guide">
        <cbx:releaseFull dir.release.db="${dir.release.db}" dir.target="${dir.target}" dir.release.war="${dir.release.war}" dir.release.task="${dir.release.task}" />
    </target>

    <!-- =================================
        Target: customize-biz-jar
       ================================= -->
    <target depends="clean" name="customize-biz-jar">
        <cbx:customize-biz-jar dir.cbx.biz="${dir.cbx_biz}" dir.cbx.core="${dir.cbx_core}" dir.cbx.common="${dir.cbx_common}" dir.cbx.ui="${dir.cbx_ui}" dir.cbx.general="${dir.cbx_general}" dir.temp="${dir.temp}" dir.product.release="${dir.product.release}" depend.product.version="${depend.product.version}" project.name="${project.name}" />
    </target>
    
    <!-- =================================
        Target: customize-general-jar
       ================================= -->
    <target depends="clean" name="customize-general-jar">
        <cbx:customize-general-jar dir.cbx.core="${dir.cbx_core}" dir.cbx.common="${dir.cbx_common}" dir.cbx.ui="${dir.cbx_ui}" dir.cbx.general="${dir.cbx_general}" dir.temp="${dir.temp}" dir.product.release="${dir.product.release}" depend.product.version="${depend.product.version}" project.name="${project.name}" />
    </target>

    <!-- =================================
        Target: customize-ui-jar
       ================================= -->
    <target depends="clean" name="customize-ui-jar">
        <cbx:customize-ui-jar dir.cbx.core="${dir.cbx_core}" dir.cbx.common="${dir.cbx_common}" dir.cbx.ui="${dir.cbx_ui}" dir.temp="${dir.temp}" dir.product.release="${dir.product.release}" depend.product.version="${depend.product.version}" project.name="${project.name}" />
    </target>

    <!-- =================================
        Target: customize-core-jar
       ================================= -->
    <target depends="clean" name="customize-core-jar">
        <cbx:customize-core-jar dir.cbx.core="${dir.cbx_core}" dir.cbx.common="${dir.cbx_common}" dir.temp="${dir.temp}" dir.product.release="${dir.product.release}" depend.product.version="${depend.product.version}" project.name="${project.name}"/>
    </target>

    <!-- =================================
        Target: customize-common-jar
       ================================= -->
    <target depends="clean" name="customize-common-jar">
        <cbx:customize-common-jar dir.cbx.common="${dir.cbx_common}" dir.temp="${dir.temp}" dir.product.release="${dir.product.release}" depend.product.version="${depend.product.version}" project.name="${project.name}"/>
    </target>
    
    <!-- ====================================================================================
        Target: release-all
              - dir.release.full : archive folder of the project-pack-full
              - dir.release.pack-db-dump : the archive folder of the project-pack-db-dump
             - release.db.dump.folder: the folder to store the releaesd db dump
              - this assume that there *MUST* have cbx-task-update-*.zip in the project-pack-full
    ===================================================================================== -->
    <target depends="clean" name="release-all">  
        <if>
            <and>
            <istrue value="${overwrite}"/>
            </and>
            <then>
                <delete dir="${final.release}" includeemptydirs="true" />
            </then>
        </if>
        <if>
            <and>
            <available file="${dir.release.cbx.restapi}" type="dir"/>
            <istrue value="${include.cbx.restapi.jar}"/>
            </and>
            <then>
                <mkdir dir="${dir.target}/restapi"/>
                <copy todir="${dir.target}/restapi" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.cbx.restapi}">
                        <include name="cbx-restapi-*.jar"/>
                    </fileset>
                </copy>
            </then>
        </if>
        <if>
            <and>
            <available file="${dir.release.project.restapi}" type="dir"/>
            <istrue value="${include.project.restapi.jar}"/>
            </and>
            <then>
                <mkdir dir="${dir.target}/restapi/ext_lib"/>
                <copy todir="${dir.target}/restapi/ext_lib" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.project.restapi}">
                        <include name="*.jar"/>
                    </fileset>
                </copy>
            </then>
        </if>
        <if>
            <and>
            <available file="${dir.release.cbx.cbxapp}" type="dir"/>
            <istrue value="${include.cbx.cbxapp}"/>
            </and>
            <then>
                <mkdir dir="${dir.target}/new_ui"/>
                <copy todir="${dir.target}/new_ui" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.cbx.cbxapp}">
                        <include name="cbx-app.zip"/>
                    </fileset>
                </copy>
            </then>
        </if>      
        <if>
            <and>
            <available file="${dir.release.cbx.esdbsync}" type="dir"/>
            <istrue value="${include.cbx.esdbsync}"/>
            </and>
            <then>
                <mkdir dir="${dir.target}/esdb_sync"/>
                <copy todir="${dir.target}/esdb_sync" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.cbx.esdbsync}"/>
                </copy>
            </then>
        </if>
        <if>
            <and>
            <available file="${dir.release.cbx.tbyint}" type="dir"/>
            <istrue value="${include.cbx.tbyint}"/>
            </and>
            <then>
                <mkdir dir="${dir.target}/tbyint"/>
                <copy todir="${dir.target}/tbyint" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.cbx.tbyint}"/>
                </copy>
            </then>
        </if>
        <if>
            <and>
            <available file="${dir.release.cbx.cas}" type="dir"/>
            <istrue value="${include.cbx.cas}"/>
            </and>
            <then>
                <mkdir dir="${dir.target}/cas"/>
                <copy todir="${dir.target}/cas" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.full}/others/cas">
                        <include name="cas-*.war"/>
                    </fileset>
                    <mapper from="cas-(.*).war" to="cas.war" type="regexp" />
                </copy>
            </then>
        </if>
        <if>
            <available file="${dir.release.full}/others/template" type="dir"/>
            <then>
                <mkdir dir="${dir.target}"/>
                <mkdir dir="${dir.target}/template"/>
                <copy todir="${dir.target}/template" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.full}/others/template">
                        <include name="cbx-template*.zip"/>
                    </fileset>
                </copy>
            </then>
        </if>
        <if>
            <available file="${dir.release.full}/others/ext_bin_lib" type="dir"/>
            <then>
                <mkdir dir="${dir.target}"/>
                <mkdir dir="${dir.target}/bin"/>
                <copy todir="${dir.target}/bin" overwrite="true" preservelastmodified="true">
                    <fileset dir="${dir.release.full}/others/ext_bin_lib">
                        <include name="*.jar"/>
                    </fileset>
                </copy>
            </then>
        </if>        
        <cbx:releaseAll dir.release.full="${dir.release.full}" project.name="${project.name}" project.version="${project.version}" dir.target="${dir.target}" dir.build="${dir.build}" tag.previous.version="${tag.previous.version}" depend.product.version="${depend.product.version}" domainId="${domainId}" final.release="${final.release}" dir.release.pack-db-dump="${dir.release.pack-db-dump}" release.db.dump.folder="${release.db.dump.folder}" commonPattern=".*[\\/]Program[\\/]others[\\/]CBX_Common[\\/].*" corePattern=".*[\\/]Program[\\/]others[\\/]CBX_Core[\\/].*" uiPattern=".*[\\/]Program[\\/]others[\\/]CBX_UI[\\/].*" generalPattern=".*[\\/]Program[\\/]others[\\/]CBX_General[\\/].*" bizPattern=".*[\\/]Program[\\/]others[\\/]CBX_Business[\\/].*" extPattern=".*[\\/]Program[\\/]src[\\/].*" dir.release.compliance="${dir.release.compliance}"/>
    </target>

<target name="gitChange">
    <cbx:checkSubModulesGitChanges
        since="${tag.previous.version}" core="changes.core" common="changes.common" 
        ui="changes.ui" general="changes.general" biz="changes.biz" ext="changes.ext"
commonPattern=".*[\\/]Program[\\/]others[\\/]CBX_Common[\\/].*" corePattern=".*[\\/]Program[\\/]others[\\/]CBX_Core[\\/].*" uiPattern=".*[\\/]Program[\\/]others[\\/]CBX_UI[\\/].*" generalPattern=".*[\\/]Program[\\/]others[\\/]CBX_General[\\/].*" bizPattern=".*[\\/]Program[\\/]others[\\/]CBX_Business[\\/].*" extPattern=".*[\\/]Program[\\/]src[\\/].*"
        />
</target>



    <!-- =================================
        Target: customize-compliance-jar  : SON customized "dir.product.release" value change from "dir.product.release" to "project.compliance.customize.path", customized "depend.product.version" value change from "depend.product.version" to "cbx.compliance.version"
       ================================= -->
    <target depends="clean" name="customize-compliance-jar">
        <cbx:customize-compliance-jar dir.cbx.compliance="${dir.cbx_compliance}" dir.temp="${dir.temp}" dir.product.release="${project.compliance.customize.path}" depend.product.version="${cbx.compliance.version}" project.name="${project.name}"/>
    </target>



    <!-- =================================
        Target: copy-jar-to-target
       ================================= -->
    <target name="copy-jar-to-target">
        <cbx:copy-jar-to-target dir.target="${dir.target}" dir.temp.target="${dir.temp.target}" project.name="${project.name}" project.version="${project.version}" />
    </target>

    <!-- =================================
        Target: copy-compliance-jar-to-target
       ================================= -->
    <target name="copy-compliance-jar-to-target">
        <mkdir dir="${dir.target}" />
        <cbx:if>
            <available file="${dir.temp.target}" type="dir" />
            <then>
                <copy todir="${dir.target}">
                    <fileset dir="${dir.temp.target}">
                        <include name="compliance-*.jar"/>
                        <exclude name="*sources*"/>
                        <exclude name="*jasper*"/>
                    </fileset>
                    <mapper from="(compliance-)?(.*).jar" to="cbx-\1\2-${project.name}-${project.version}.jar" type="regexp" />
                </copy>
            </then>
            <else>
                <concat destfile="${dir.target}/NO_CHANGE">NO_CHANGE</concat>
                <copy todir="${dir.target}">
                    <fileset dir="${project.compliance.customize.path}/${cbx.compliance.version}/cbx-compliance">
                        <include name="compliance-*.jar"/>
                        <exclude name="*sources*"/>
                        <exclude name="*jasper*"/>
                    </fileset>
                    <mapper from="(compliance-)?(.*).jar" to="cbx-\1\2-${project.name}-${project.version}.jar" type="regexp" />
                </copy>
            </else>
        </cbx:if>
    </target>


    <!-- =================================
        Target: get-compliance-zip : SON customized compliance.remote.dir value change from "war.remote.full.path" to "project.compliance.customize.path"
       ================================= -->
    <target name="get-compliance-zip" depends="clean">
        <property name="compliance.remote.dir" value="${project.compliance.customize.path}/bin"/>
        <property name="compliance.remote.path" value="${compliance.remote.dir}/cbx-compliance-${depend.product.version}.zip"/>
        <cbx:if>
          <available file="${compliance.remote.dir}" type="dir" />
          <then>
             <copy file="${compliance.remote.path}" todir="${dir.target}" />
          </then>
          <else>
             <echo>Can not found the folder: ${compliance.remote.dir}</echo>
          </else>
        </cbx:if>
    </target>


    <!-- =================================
        Target: customize-restapi-jar  : SON customized "dir.product.release" value change from "dir.product.release" to "project.compliance.restapi.path", customized "depend.product.version" value change from "depend.product.version" to "cbx.restapi.version"
       ================================= -->
    <target depends="clean" name="customize-restapi-jar">
        <echo>${cbx.restapi.version}</echo>
        <customize-restapi-jar dir.cbx.restapi="${dir.restapi}" dir.temp="${dir.temp}" dir.product.release="${son.restapi.customize.path}" depend.product.version="${cbx.restapi.version}" project.name="${project.name}"/>
    </target>

       <!-- =================================
            Target: copy-restapi-jar-to-target
           ================================= -->
        <target name="copy-restapi-jar-to-target">
            <mkdir dir="${dir.target}" />
            <cbx:if>
                <available file="${dir.temp.target}" type="dir" />
            <then>
                <copy todir="${dir.target}">
                    <fileset dir="${dir.temp.target}">
                        <include name="cbx-restapi-*.jar"/>
                        <exclude name="*sources*"/>
                    </fileset>
                    <mapper from="(cbx-restapi-)?(.*).jar" to="\1\2-${project.name}-${project.version}.jar" type="regexp" />
                </copy>
            </then>
            <else>
                <concat destfile="${dir.target}/NO_CHANGE">NO_CHANGE</concat>
                <echo>${son.restapi.customize.path}/${cbx.restapi.version}/</echo>
                <copy todir="${dir.target}">
                    <fileset dir="${son.restapi.customize.path}/${cbx.restapi.version}/">
                        <include name="cbx-restapi-*.jar"/>
                        <exclude name="*sources*"/>
                    </fileset>
                    <mapper from="(cbx-restapi-)?(.*).jar" to="\1\2-${project.name}-${project.version}.jar" type="regexp" />
                </copy>
            </else>
            </cbx:if>
        </target>
        <!-- =================================
        Target: get-restapi-zip : SON customized restapi.remote.dir value change from "war.remote.full.path" to "son.restapi.customize.path"
       ================================= -->
    <target name="get-restapi-zip" depends="clean">
        <property name="restapi.remote.dir" value="${son.restapi.customize.path}/bin"/>
        <property name="restapi.remote.path" value="${restapi.remote.dir}/cbx-restapi-${cbx.restapi.version}.zip"/>
        <cbx:if>
          <available file="${restapi.remote.dir}" type="dir" />
          <then>
             <copy file="${restapi.remote.path}" todir="${dir.target}" />
          </then>
          <else>
             <echo>Can not found the folder: ${restapi.remote.dir}</echo>
          </else>
        </cbx:if>
    </target>


    <!-- =================================
        Target: customize-cbx_tby_int-jar  : SON customized "dir.product.release" value change from "dir.product.release" to "project.cbx_tby_int.path", customized "depend.product.version" value change from "depend.product.version" to "cbx.cbx_tby_int.version"
       ================================= -->
    <target depends="clean" name="customize-cbx_tby_int-jar">
        <echo>${cbx_tby_int.version}</echo>
        <customize-cbx_tby_int-jar dir.cbx.tby.int="${dir.cbx_tby_int}" dir.temp="${dir.temp}" dir.product.release="${project.cbx_tby_int.customize.path}" depend.product.version="${cbx_tby_int.version}" project.name="${project.name}"/>
    </target>

       <!-- =================================
            Target: copy-cbx_tby_int-jar-to-target
           ================================= -->
        <target name="copy-cbx_tby_int-jar-to-target">
            <mkdir dir="${dir.target}" />
            <cbx:if>
                <available file="${dir.temp.target}" type="dir" />
            <then>
                <copy todir="${dir.target}">
                    <fileset dir="${dir.temp.target}">
                        <include name="cbx-tby-int-*.jar"/>
                        <exclude name="*sources*"/>
                    </fileset>
                    <mapper from="(cbx-tby-int-)?(.*).jar" to="\1\2-${project.name}-${project.version}.jar" type="regexp" />
                </copy>
            </then>
            <else>
                <concat destfile="${dir.target}/NO_CHANGE">NO_CHANGE</concat>
                <echo>${project.cbx_tby_int.customize.path}/${cbx.cbx_tby_int.version}/</echo>
                <copy todir="${dir.target}">
                    <fileset dir="${project.cbx_tby_int.customize.path}/${cbx.cbx_tby_int.version}/">
                        <include name="cbx-tby-int-*.jar"/>
                        <exclude name="*sources*"/>
                    </fileset>
                    <mapper from="(cbx-tby-int-)?(.*).jar" to="\1\2-${project.name}-${project.version}.jar" type="regexp" />
                </copy>
            </else>
            </cbx:if>
        </target>
        <!-- =================================
        Target: get-cbx_tby_int-zip : SON customized restapi.remote.dir value change from "war.remote.full.path" to "project.cbx_tby_int.customize.path"
       ================================= -->
    <target name="get-cbx_tby_int-zip" depends="clean">
        <property name="cbx_tby_int.remote.dir" value="${project.cbx_tby_int.customize.path}/bin"/>
        <property name="cbx_tby_int.remote.path" value="${cbx_tby_int.remote.dir}/cbx-cbx_tby_int-${cbx.cbx_tby_int.version}.zip"/>
        <cbx:if>
          <available file="${cbx_tby_int.remote.dir}" type="dir" />
          <then>
             <copy file="${cbx_tby_int.remote.path}" todir="${dir.target}" />
          </then>
          <else>
             <echo>Can not found the folder: ${cbx_tby_int.remote.dir}</echo>
          </else>
        </cbx:if>
    </target>


        <!-- =================================
        Target: cas-pack-war
      ================================= -->
    <target depends="clean" name="cas-pack-war">
        <cbx:unzip-war dir.webapp="${dir.temp}/cas" war.dir="${dir.temp}/zips" war.local.path="${dir.temp}/zips/cas.war" war.remote.path="${project.cas.customize.path}/cas.war" />
        <packCasWar project.version="${project.version}" project.name="${project.name}" dir.target="${dir.target}"
            dir.cas.war="${dir.cas}" dir.cas="${dir.temp}/cas" dir.release.cbx-project.name="${dir.release.cbx-project}" depend.product.version="${depend.product.version}" dir.temp="${dir.temp}"/>
    </target>
</project>
