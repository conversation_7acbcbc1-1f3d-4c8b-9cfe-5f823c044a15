#The field id whose value is type of DynamicEntity Entity (Trigger in item_gen_mpo)
csParentCollectionId=custInvItem
colorCollectionId=invItemColor
sizeCollectionId=invItemSize
csCollectionId=invItemCs
colorKey=specColorId
sizeKey=specSizeId
csColorKey=specColorId
csSizeKey=specSizeId
csColorEntity=invItemColor
csSizeEntity=invItemSize
colorMapFields=specId
csColorMapFields=specId
sizeMapFields=
csSizeMapFields=
csEntityName=InvItemCs