#The field id whose value is type of DynamicEntity Entity (Trigger in vpo_select_mpo_item)
csParentCollectionId=vpoItem
colorCollectionId=vpoItemColor
sizeCollectionId=vpoItemSize
csCollectionId=vpoItemCs
colorKey=specColorId
sizeKey=specSizeId
csColorKey=specColorId
csSizeKey=specSizeId
csColorEntity=vpoItemColorId
csSizeEntity=vpoItemSizeId
colorMapFields=specId
csColorMapFields=specId
sizeMapFields=
csSizeMapFields=
csEntityName=VpoItemCs
