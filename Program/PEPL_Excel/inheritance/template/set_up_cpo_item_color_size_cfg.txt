#The field id whose value is type of DynamicEntity Entity (Trigger in os_gen_cpo or cpo_select_os_item)
csParentCollectionId=cpoItem
colorCollectionId=cpoItemColor
sizeCollectionId=cpoItemSize
csCollectionId=cpoItemCs
colorKey=itemColorId
sizeKey=itemSizeId
csColorKey=itemColorId
csSizeKey=itemSizeId
csColorEntity=cpoItemColorId
csSizeEntity=cpoItemSizeId
colorMapFields=itemId
csColorMapFields=itemId
sizeMapFields=
csSizeMapFields=
csEntityName=CpoItemCs
