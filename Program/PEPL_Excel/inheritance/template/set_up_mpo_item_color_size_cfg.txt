#The field id whose value is type of DynamicEntity Entity (Trigger in item_gen_mpo)
csParentCollectionId=mpoItems
colorCollectionId=mpoItemColors
sizeCollectionId=mpoItemSizes
csCollectionId=mpoItemCs
colorKey=specColorId
sizeKey=specSizeId
csColorKey=specColorId
csSizeKey=specSizeId
csColorEntity=mpoItemColor
csSizeEntity=mpoItemSize
colorMapFields=specId
csColorMapFields=specId
sizeMapFields=
csSizeMapFields=
csEntityName=MpoItemCs