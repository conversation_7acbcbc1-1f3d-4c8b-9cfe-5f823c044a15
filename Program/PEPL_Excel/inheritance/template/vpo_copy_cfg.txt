#configuration to set up field value in vpo
sourceEntity=vpoItem
sourceLookupKey=itemId.id,lotNo
destinationEntity=vpoShipDtl,vpoCharge,vpoShipDc,vpoDcDtl,
destinationLookupKey=vpoItemId.itemId.id,vpoItemId.lotNo
destinationValueTo=vpoItemId.id
destinationValueFrom=id
csParentCollectionId=vpoItem/vpoShipDtl
colorCollectionId=vpoItemColor/vpoShipDtlColor
sizeCollectionId=vpoItemSize/vpoShipDtlSize
csCollectionId=vpoItemCs/vpoShipDtlCs
colorKey=specColorId/specColorId
sizeKey=specSizeId/specSizeId
csColorKey=specColorId/specColorId
csSizeKey=specSizeId/specSizeId
csColorEntity=vpoItemColorId/vpoShipDtlColorId
csSizeEntity=vpoItemSizeId/vpoShipDtlSizeId
colorMapFields=specId/specId
csColorMapFields=specId/specId
sizeMapFields=specId/specId
csSizeMapFields=specId/specId
csEntityName=VpoItemCs/VpoShipDtlCs
csFieldValueFormColor=cpoItemColorId/
csFieldValueFormSize=cpoItemSizeId/
