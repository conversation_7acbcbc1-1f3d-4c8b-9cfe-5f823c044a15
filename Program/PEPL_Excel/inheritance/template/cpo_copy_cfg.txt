#configuration to set up field value in cpo
sourceEntity=cpoItem
sourceLookupKey=itemId.id,lotNo
destinationEntity=cpoShipDtl,cpoCharge,cpoShipDc,cpoDcDtl,
destinationLookupKey=cpoItemId.itemId.id,cpoItemId.lotNo
destinationValueTo=cpoItemId.id
destinationValueFrom=id
csParentCollectionId=cpoItem/cpoShipDtl
colorCollectionId=cpoItemColor/cpoShipDtlColor
sizeCollectionId=cpoItemSize/cpoShipDtlSize
csCollectionId=cpoItemCs/cpoShipDtlCs
colorKey=specColorId/specColorId
sizeKey=specSizeId/specSizeId
csColorKey=specColorId/specColorId
csSizeKey=specSizeId/specSizeId
csColorEntity=cpoItemColorId/cpoShipDtlColorId
csSizeEntity=cpoItemSizeId/cpoShipDtlSizeId
colorMapFields=specId/specId
csColorMapFields=specId/specId
sizeMapFields=specId/specId
csSizeMapFields=specId/specId
csEntityName=CpoItemCs/CpoShipDtlCs
csFieldValueFormColor=cpoItemColorId/
csFieldValueFormSize=cpoItemSizeId/
