#The field id whose value is type of DynamicEntity Entity (Trigger in vpo_Proppose_Change_VpoAck)
csParentCollectionId=vpoAckItems/vpoAckShipDtls
colorCollectionId=vpoAckItemColors/vpoAckShipDtlColors
sizeCollectionId=vpoAckItemSizes/vpoAckShipDtlSizes
csCollectionId=vpoAckItemCss/vpoAckShipDtlCss
colorKey=specColorId/specColorId
sizeKey=specSizeId/specSizeId
csColorKey=specColorId/specColorId
csSizeKey=specSizeId/specSizeId
csColorEntity=vpoAckItemColor/vpoAckShipDtlColor
csSizeEntity=vpoAckItemSize/vpoAckShipDtlSize
colorMapFields=specId/specId
csColorMapFields=specId/specId
sizeMapFields=specId/specId
csSizeMapFields=specId/specId
csEntityName=VpoAckItemCs/VpoAckShipDtlCs
