#The field id whose value is type of DynamicEntity Entity
csParentCollectionId=osItem
colorCollectionId=osItemColor
sizeCollectionId=osItemSize
csCollectionId=osItemCs
colorKey=specColorId
sizeKey=specSizeId
csColorKey=specColorId
csSizeKey=specSizeId
csColorEntity=osItemColorId
csSizeEntity=osItemSizeId
colorMapFields=specId
csColorMapFields=specId
sizeMapFields=
csSizeMapFields=
csEntityName=OsItemCs
