#configuration to set up field value in vpo
sourceEntity=vpoItem
sourceLookupKey=itemId.id,lotNo
destinationEntity=vpoShipDtl,vpoCharge,vpoShipDc,VpoDcDtl,
destinationLookupKey=vpoItemId.itemId.id,vpoItemId.lotNo
destinationValueTo=vpoItemId.id
destinationValueFrom=id
csParentCollectionId=vpoItem/vpoShipDtl
colorCollectionId=vpoItemColor/vpoShipDtlColor
sizeCollectionId=vpoItemSize/vpoShipDtlSize
csCollectionId=vpoItemCs/vpoShipDtlCs
colorKey=itemColorId/itemColorId
sizeKey=itemSizeId/itemSizeId
csColorKey=itemColorId/itemColorId
csSizeKey=itemSizeId/itemSizeId
csColorEntity=vpoItemColorId/vpoShipDtlColorId
csSizeEntity=vpoItemSizeId/vpoShipDtlSizeId
colorMapFields=itemId/itemId
csColorMapFields=itemId/itemId
sizeMapFields=itemId/itemId
csSizeMapFields=itemId/itemId
csEntityName=VpoItemCs/VpoShipDtlCs
csFieldValueFormColor=cpoItemColorId/
csFieldValueFormSize=cpoItemSizeId/
colorSeq=seq/seq
sizeSeq=seq/seq
csColorSeq=colorSeq/colorSeq
csSizeSeq=sizeSeq/sizeSeq
