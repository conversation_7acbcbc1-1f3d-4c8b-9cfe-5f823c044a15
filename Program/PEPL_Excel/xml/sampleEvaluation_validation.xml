<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="sampleEvaluation" position="sampleEvaluation_validation.xlsx">
  <sheet id="ValidationProfile" position="sampleEvaluation_validation.xlsx,ValidationProfile">
    <ValidationProfile position="sampleEvaluation_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,ValidationProfile,4">
          <id>sampleEvaluationValidatorId</id>
          <profileName>Default Data Validation Profile SampleEvaluation[ver:1]</profileName>
          <entityName>SampleEvaluation</entityName>
          <entityVer>1</entityVer>
          <action>SaveAndConfirm,SaveDoc,Mark<PERSON>Sub<PERSON>,MarkAsInProgress,Mark<PERSON><PERSON><PERSON><PERSON>,MarkAsRejected,MarkAsWaive,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,<PERSON><PERSON><PERSON>ustom<PERSON>tatus04Doc,Mark<PERSON><PERSON>ustomStatus05Doc,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus06Doc,MarkAs<PERSON>ustomStatus07Doc,Mark<PERSON><PERSON>ustom<PERSON>tatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>14-Sep-2015</updatedOn>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationProfile,5">
          <id>sampleEvaluationValidatorId2</id>
          <profileName>Default Data Validation Profile SampleEvaluation[ver:1]</profileName>
          <entityName>SampleEvaluation</entityName>
          <entityVer>1</entityVer>
          <action>SampleEvaluationSendToVendor</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>14-Sep-2015</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="sampleEvaluation_validation.xlsx,ValidationRule">
    <ValidationRule position="sampleEvaluation_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,5">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,6">
          <type>CompareToDayValidator</type>
          <className>com.core.pepl.validation.validator.CompareToDayValidator</className>
          <condition/>
          <enabled>N</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="sampleEvaluation_validation.xlsx,MandatoryValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,MandatoryValidator,1" profileId="sampleEvaluationValidatorId" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,8">
          <entityName>EvaluationDtl</entityName>
          <fieldId>evaluation</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>evaluation</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,9">
          <entityName>EvaluationDtl</entityName>
          <fieldId>section</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>section</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,10">
          <entityName>SampleEvaluationImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationImages</GRID_ID>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,11">
          <entityName>SampleEvaluationImage</entityName>
          <fieldId>imageTypeId</fieldId>
          <condition>isEvaluateForQualitySampleAndIsShipmentSample</condition>
          <conditionType>0</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationImages</GRID_ID>
          <LABEL_FIELD_ID>imageTypeId</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,12">
          <entityName>SampleEvaluationAttach</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationAttachs</GRID_ID>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,13">
          <entityName>SampleEvaluation</entityName>
          <fieldId>currentFitSampleSize</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currentFitSampleSize</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,14">
          <entityName>SampleEvaluation</entityName>
          <fieldId>currentFitUser</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currentFitUser</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,15">
          <entityName>SampleEvaluation</entityName>
          <fieldId>currentFitUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currentFitUOM</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleEvaluation_validation.xlsx,MandatoryValidator,18" profileId="sampleEvaluationValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,25">
          <entityName>EvaluationDtl</entityName>
          <fieldId>evaluation</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>evaluation</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,26">
          <entityName>EvaluationDtl</entityName>
          <fieldId>section</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>section</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,27">
          <entityName>SampleEvaluationImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationImages</GRID_ID>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,28">
          <entityName>SampleEvaluationImage</entityName>
          <fieldId>imageTypeId</fieldId>
          <condition>isEvaluateForQualitySampleAndIsShipmentSample</condition>
          <conditionType>0</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationImages</GRID_ID>
          <LABEL_FIELD_ID>imageTypeId</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,29">
          <entityName>SampleEvaluationAttach</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationAttachs</GRID_ID>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="sampleEvaluation_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,ExternalActiveValidator,1" profileId="sampleEvaluationValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,ExternalActiveValidator,8">
          <entityName>SampleEvaluation</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="CompareToDayValidator" position="sampleEvaluation_validation.xlsx,CompareToDayValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,CompareToDayValidator,1" profileId="sampleEvaluationValidatorId" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,CompareToDayValidator,8">
          <entityName>SampleEvaluation</entityName>
          <fieldId>custDate5</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID/>
          <LABEL_FIELD_ID>custDate5</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0005</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleEvaluation_validation.xlsx,CompareToDayValidator,11" profileId="sampleEvaluationValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,CompareToDayValidator,18">
          <entityName>SampleEvaluation</entityName>
          <fieldId>custDate5</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID/>
          <LABEL_FIELD_ID>custDate5</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0005</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
