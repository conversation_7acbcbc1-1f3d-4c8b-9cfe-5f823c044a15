<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="qq" position="qq_validation.xlsx">
  <sheet id="ValidationProfile" position="qq_validation.xlsx,ValidationProfile">
    <ValidationProfile position="qq_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="qq_validation.xlsx,ValidationProfile,4">
          <id>VendorValidator</id>
          <profileName>VendorValidator</profileName>
          <entityName>QqItem</entityName>
          <entityVer>1</entityVer>
          <action>PopupCommonOkAction</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.100</updatedOn>
        </element>
        <element position="qq_validation.xlsx,ValidationProfile,5">
          <id>VendorImagesValidator</id>
          <profileName>VendorImagesValidator</profileName>
          <entityName>QqItem</entityName>
          <entityVer>1</entityVer>
          <action>PopupVendorImagesOkAction</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.100</updatedOn>
        </element>
        <element position="qq_validation.xlsx,ValidationProfile,6">
          <id>D31C3F35D2594A7DA97FDCF5E42A9476</id>
          <profileName>Default Data Validation Profile Qq[ver:1]</profileName>
          <entityName>Qq</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,SendToBuyer,SaveAndConfirm,MarkAsQuoted,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,MarkAsCustomStatus06Doc,MarkAsCustomStatus07Doc,MarkAsCustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.100</updatedOn>
        </element>
        <element position="qq_validation.xlsx,ValidationProfile,7">
          <id>FormPopUpMandatoryValidator</id>
          <profileName>FormPopUpMandatoryValidator</profileName>
          <entityName>QqItem</entityName>
          <entityVer>1</entityVer>
          <action>PopupMoreImagesDefaultOkAction</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.100</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="qq_validation.xlsx,ValidationRule">
    <ValidationRule position="qq_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="qq_validation.xlsx,ValidationRule,4">
          <type>FormPopUpMandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="qq_validation.xlsx,ValidationRule,5">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="qq_validation.xlsx,ValidationRule,6">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="qq_validation.xlsx,ValidationRule,7">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="qq_validation.xlsx,ValidationRule,8">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <restapiBeanName>HCLValidator</restapiBeanName>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="qq_validation.xlsx,MandatoryValidator">
    <ValidationField position="qq_validation.xlsx,MandatoryValidator,1" profileId="D31C3F35D2594A7DA97FDCF5E42A9476" profileName="">
      <elements id="default">
        <element position="qq_validation.xlsx,MandatoryValidator,8">
          <entityName>Qq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>expiryDate</LABEL_FIELD_ID>
        </element>
        <element position="qq_validation.xlsx,MandatoryValidator,9">
          <entityName>Qq</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="qq_validation.xlsx,MandatoryValidator,10">
          <entityName>QqItem</entityName>
          <fieldId>itemDesc</fieldId>
          <condition>isVendorDeclinedNotChecked</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>qqItems</GRID_ID>
          <LABEL_FIELD_ID>itemDesc</LABEL_FIELD_ID>
        </element>
        <element position="qq_validation.xlsx,MandatoryValidator,11">
          <entityName>QqItem</entityName>
          <fieldId>itemName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>qqItems</GRID_ID>
          <LABEL_FIELD_ID>itemName</LABEL_FIELD_ID>
        </element>
        <element position="qq_validation.xlsx,MandatoryValidator,12">
          <entityName>QqImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>qqImages</GRID_ID>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
        </element>
        <element position="qq_validation.xlsx,MandatoryValidator,13">
          <entityName>QqAttachment</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>qqAttachments</GRID_ID>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="qq_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="qq_validation.xlsx,UniqueInModuleValidator,1" profileId="D31C3F35D2594A7DA97FDCF5E42A9476" profileName="">
      <elements id="default">
        <element position="qq_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Qq</entityName>
          <fieldId>qqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="qq_validation.xlsx,ClassificationValidator">
    <ValidationField position="qq_validation.xlsx,ClassificationValidator,1" profileId="D31C3F35D2594A7DA97FDCF5E42A9476" profileName="">
      <elements id="default">
        <element position="qq_validation.xlsx,ClassificationValidator,8">
          <entityName>Qq</entityName>
          <fieldId>qqItems</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>qqItems</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>item</TARGET_FIELD>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="qq_validation.xlsx,HCLValidator">
    <ValidationField position="qq_validation.xlsx,HCLValidator,1" profileId="D31C3F35D2594A7DA97FDCF5E42A9476" profileName="">
      <elements id="default">
        <element position="qq_validation.xlsx,HCLValidator,8">
          <entityName>Qq</entityName>
          <fieldId>qqItems</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>qqItems</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hierarchy</TARGET_FIELD>
          <TYPE>gridSelectBiz</TYPE>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FormPopUpMandatoryValidator" position="qq_validation.xlsx,FormPopUpMandatoryValidator">
    <ValidationField position="qq_validation.xlsx,FormPopUpMandatoryValidator,1" profileId="FormPopUpMandatoryValidator" profileName="FormPopUpMandatoryValidator">
      <elements id="default">
        <element position="qq_validation.xlsx,FormPopUpMandatoryValidator,8">
          <entityName>QqItemBuyerImage</entityName>
          <fieldId>file</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>buyerImages</GRID_ID>
          <LABEL_FIELD_ID>file</LABEL_FIELD_ID>
          <LABEL>lbl.popupMoreImagesDefault</LABEL>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="qq_validation.xlsx,FormPopUpMandatoryValidator,15" profileId="VendorValidator" profileName="VendorValidator">
      <elements id="default">
        <element position="qq_validation.xlsx,FormPopUpMandatoryValidator,22">
          <entityName>QqItemVendorAttachment</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorAttachments</GRID_ID>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <LABEL>lbl.popupQqItemVendorAttachments</LABEL>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="qq_validation.xlsx,FormPopUpMandatoryValidator,26" profileId="VendorImagesValidator" profileName="VendorImagesValidator">
      <elements id="default">
        <element position="qq_validation.xlsx,FormPopUpMandatoryValidator,33">
          <entityName>QqItemVendorImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorImages</GRID_ID>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
          <LABEL>lbl.popupQqItemVendorImagesDefault</LABEL>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
