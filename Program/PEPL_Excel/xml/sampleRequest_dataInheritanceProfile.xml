<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataInheritanceProfile module="sampleRequest" position="sampleRequest_dataInheritanceProfile.xlsx">
  <sheet id="DIP_test_dip_profile" position="sampleRequest_dataInheritanceProfile.xlsx,DIP_test_dip_profile">
    <DataInheritanceProfile position="sampleRequest_dataInheritanceProfile.xlsx,DIP_test_dip_profile,1">
      <elements id="dataInheritanceProfile">
        <element position="sampleRequest_dataInheritanceProfile.xlsx,DIP_test_dip_profile,4">
          <id>itemToSampleRequest</id>
          <actionId>itemToSampleRequest</actionId>
          <dmrName>itemToSampleRequest</dmrName>
          <dmrVersion>1</dmrVersion>
          <srcEntityName>Item</srcEntityName>
          <dstEntityName>SampleRequest</dstEntityName>
          <domain>PEPL</domain>
          <dstCondition/>
          <updatedDate>2014-11-21</updatedDate>
          <sequence>4.0</sequence>
        </element>
        <element position="sampleRequest_dataInheritanceProfile.xlsx,DIP_test_dip_profile,5">
          <id>sampleRequestSelectVendor</id>
          <actionId>sampleRequestSelectVendor</actionId>
          <dmrName>sampleRequestSelectVendor</dmrName>
          <dmrVersion>1</dmrVersion>
          <srcEntityName>Vendor</srcEntityName>
          <dstEntityName>SampleRequest</dstEntityName>
          <domain>PEPL</domain>
          <dstCondition/>
          <updatedDate>2014-10-23</updatedDate>
          <sequence>3.0</sequence>
        </element>
        <element position="sampleRequest_dataInheritanceProfile.xlsx,DIP_test_dip_profile,6">
          <id>sampleRequestAddItem</id>
          <actionId>sampleRequestAddItem</actionId>
          <dmrName>sampleRequestAddItem</dmrName>
          <dmrVersion>1</dmrVersion>
          <srcEntityName>Item</srcEntityName>
          <dstEntityName>SampleRequest</dstEntityName>
          <domain>PEPL</domain>
          <dstCondition/>
          <updatedDate>2014-11-21</updatedDate>
          <sequence>4.0</sequence>
        </element>
      </elements>
    </DataInheritanceProfile>
  </sheet>
</dataInheritanceProfile>
