<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="vq" position="vq_validation.xlsx">
  <sheet id="ValidationProfile" position="vq_validation.xlsx,ValidationProfile">
    <ValidationProfile position="vq_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="vq_validation.xlsx,ValidationProfile,4">
          <id>8d29af2e378d419c894a0c15f8313cf3</id>
          <profileName>Default Data Validation Profile Vq[ver:1]</profileName>
          <entityName>Vq</entityName>
          <entityVer>1</entityVer>
          <action>Quoted,MarkShortListed,RejectToBuy,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,Mark<PERSON><PERSON>ustom<PERSON>tatus06Doc,Mark<PERSON><PERSON>ustomStatus07Doc,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus08D<PERSON>,MarkAs<PERSON>ustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.597</updatedOn>
        </element>
        <element position="vq_validation.xlsx,ValidationProfile,5">
          <id>POPUPCOSTBREAKDOWN_VALIDATION_PROFILE_ID</id>
          <profileName>PopupCostBreakdown validation</profileName>
          <entityName>Vq</entityName>
          <entityVer>1</entityVer>
          <action>QuotationCostBreakdownRecalculate</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.597</updatedOn>
        </element>
        <element position="vq_validation.xlsx,ValidationProfile,6">
          <id>8d29af2e378d419c894a0c15f8313aaa</id>
          <profileName>Default Data Validation Profile Vq[ver:1] SendToVendor</profileName>
          <entityName>Vq</entityName>
          <entityVer>1</entityVer>
          <action>SendToVendor</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.597</updatedOn>
        </element>
        <element position="vq_validation.xlsx,ValidationProfile,7">
          <id>VQ_PRODUCT_WEIGHT_VALIDATION</id>
          <profileName>Default Data Validation Profile Vq[ver:1] SaveDoc,SaveAndConfirm,SubmitVq,ConfirmToBuy</profileName>
          <entityName>Vq</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,SaveAndConfirm,SubmitVq,ConfirmToBuy</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.597</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="vq_validation.xlsx,ValidationRule">
    <ValidationRule position="vq_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="vq_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vq_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vq_validation.xlsx,ValidationRule,6">
          <type>NumericRangeValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <restapiBeanName>NumericRangeValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vq_validation.xlsx,ValidationRule,7">
          <type>EmailValidator</type>
          <className>com.core.cbx.validation.validator.EmailValidator</className>
          <restapiBeanName>EmailValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vq_validation.xlsx,ValidationRule,8">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="vq_validation.xlsx,ValidationRule,9">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <restapiBeanName>HCLValidator</restapiBeanName>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="vq_validation.xlsx,ValidationRule,10">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <restapiBeanName>ExternalActiveValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="vq_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="vq_validation.xlsx,UniqueInModuleValidator,1" profileId="8d29af2e378d419c894a0c15f8313cf3" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Vq</entityName>
          <fieldId>vqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,UniqueInModuleValidator,11" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>Vq</entityName>
          <fieldId>vqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,UniqueInModuleValidator,21" profileId="VQ_PRODUCT_WEIGHT_VALIDATION" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,UniqueInModuleValidator,28">
          <entityName>Vq</entityName>
          <fieldId>vqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="MandatoryValidator" position="vq_validation.xlsx,MandatoryValidator">
    <ValidationField position="vq_validation.xlsx,MandatoryValidator,1" profileId="8d29af2e378d419c894a0c15f8313cf3" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,MandatoryValidator,8">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,9">
          <entityName>Vq</entityName>
          <fieldId>vqType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vqType</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,10">
          <entityName>Vq</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,11">
          <entityName>Vq</entityName>
          <fieldId>unitCost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>unitCost</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,12">
          <entityName>Vq</entityName>
          <fieldId>incoterm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>incoterm</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,13">
          <entityName>Vq</entityName>
          <fieldId>shipmentMethod</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>shipmentMethod</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,14">
          <entityName>Vq</entityName>
          <fieldId>countryOfOrigin</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>countryOfOrigin</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,15">
          <entityName>Vq</entityName>
          <fieldId>countryOfShipment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>countryOfShipment</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,16">
          <entityName>Vq</entityName>
          <fieldId>portOfLoading</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>portOfLoading</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,17">
          <entityName>Vq</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,18">
          <entityName>VqCarton</entityName>
          <fieldId>cartonType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqCarton</GRID_ID>
          <LABEL_FIELD_ID>cartonType</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,19">
          <entityName>VqImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqImage</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,20">
          <entityName>VqContact</entityName>
          <fieldId>contactTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>contactTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,21">
          <entityName>VqContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,22">
          <entityName>VqContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,23">
          <entityName>VqAddress</entityName>
          <fieldId>addressTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>addressTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,24">
          <entityName>VqAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,25">
          <entityName>VqAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,26">
          <entityName>Vq</entityName>
          <fieldId>vqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vqNo</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,MandatoryValidator,29" profileId="VQ_PRODUCT_WEIGHT_VALIDATION" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,MandatoryValidator,36">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,37">
          <entityName>Vq</entityName>
          <fieldId>vqType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vqType</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,38">
          <entityName>Vq</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,39">
          <entityName>Vq</entityName>
          <fieldId>unitCost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>unitCost</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,40">
          <entityName>Vq</entityName>
          <fieldId>incoterm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>incoterm</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,41">
          <entityName>Vq</entityName>
          <fieldId>shipmentMethod</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>shipmentMethod</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,42">
          <entityName>Vq</entityName>
          <fieldId>countryOfOrigin</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>countryOfOrigin</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,43">
          <entityName>Vq</entityName>
          <fieldId>countryOfShipment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>countryOfShipment</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,44">
          <entityName>Vq</entityName>
          <fieldId>portOfLoading</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>portOfLoading</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,45">
          <entityName>Vq</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,46">
          <entityName>VqCarton</entityName>
          <fieldId>cartonType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqCarton</GRID_ID>
          <LABEL_FIELD_ID>cartonType</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,47">
          <entityName>VqImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqImage</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,48">
          <entityName>VqContact</entityName>
          <fieldId>contactTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>contactTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,49">
          <entityName>VqContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,50">
          <entityName>VqContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,51">
          <entityName>VqAddress</entityName>
          <fieldId>addressTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>addressTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,52">
          <entityName>VqAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,53">
          <entityName>VqAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,54">
          <entityName>Vq</entityName>
          <fieldId>vqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vqNo</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,55">
          <entityName>Vq</entityName>
          <fieldId>prodWeight</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>prodWeight</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,MandatoryValidator,58" profileId="POPUPCOSTBREAKDOWN_VALIDATION_PROFILE_ID" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,MandatoryValidator,65">
          <entityName>VqOtherCharge</entityName>
          <fieldId>type</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqOtherCharges</GRID_ID>
          <LABEL_FIELD_ID>type</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,66">
          <entityName>VqOtherCharge</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqOtherCharges</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,67">
          <entityName>VqOtherCharge</entityName>
          <fieldId>basis</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqOtherCharges</GRID_ID>
          <LABEL_FIELD_ID>basis</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,68">
          <entityName>VqOtherCharge</entityName>
          <fieldId>rate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqOtherCharges</GRID_ID>
          <LABEL_FIELD_ID>rate</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,69">
          <entityName>VqOtherCharge</entityName>
          <fieldId>currency</fieldId>
          <condition>basisMatchToFixed</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>vqOtherCharges</GRID_ID>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,70">
          <entityName>VqComponentCost</entityName>
          <fieldId>consumption</fieldId>
          <condition>rfqRequiresOpenCosting</condition>
          <conditionType>0</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>vqComponentCosts</GRID_ID>
          <LABEL_FIELD_ID>consumption</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,71">
          <entityName>VqComponentCost</entityName>
          <fieldId>unitCost</fieldId>
          <condition>rfqRequiresOpenCosting</condition>
          <conditionType>0</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>vqComponentCosts</GRID_ID>
          <LABEL_FIELD_ID>unitCost</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,72">
          <entityName>VqComponentCost</entityName>
          <fieldId>currency</fieldId>
          <condition>rfqRequiresOpenCosting</condition>
          <conditionType>0</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>vqComponentCosts</GRID_ID>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,73">
          <entityName>VqAdditionalCost</entityName>
          <fieldId>consumption</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAdditionalCosts</GRID_ID>
          <LABEL_FIELD_ID>consumption</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,74">
          <entityName>VqAdditionalCost</entityName>
          <fieldId>unitCost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAdditionalCosts</GRID_ID>
          <LABEL_FIELD_ID>unitCost</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,75">
          <entityName>VqAdditionalCost</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAdditionalCosts</GRID_ID>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,MandatoryValidator,78" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,MandatoryValidator,85">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,86">
          <entityName>Vq</entityName>
          <fieldId>vqType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vqType</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,87">
          <entityName>Vq</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,88">
          <entityName>Vq</entityName>
          <fieldId>unitCost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>unitCost</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,89">
          <entityName>Vq</entityName>
          <fieldId>incoterm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>incoterm</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,90">
          <entityName>Vq</entityName>
          <fieldId>shipmentMethod</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>shipmentMethod</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,91">
          <entityName>Vq</entityName>
          <fieldId>countryOfOrigin</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>countryOfOrigin</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,92">
          <entityName>Vq</entityName>
          <fieldId>countryOfShipment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>countryOfShipment</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,93">
          <entityName>Vq</entityName>
          <fieldId>portOfLoading</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>portOfLoading</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,94">
          <entityName>Vq</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,95">
          <entityName>VqCarton</entityName>
          <fieldId>cartonType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqCarton</GRID_ID>
          <LABEL_FIELD_ID>cartonType</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,96">
          <entityName>VqImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqImage</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,97">
          <entityName>VqContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,98">
          <entityName>VqContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,99">
          <entityName>VqAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,MandatoryValidator,100">
          <entityName>VqAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqAddress</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumericRangeValidator" position="vq_validation.xlsx,NumericRangeValidator">
    <ValidationField position="vq_validation.xlsx,NumericRangeValidator,1" profileId="8d29af2e378d419c894a0c15f8313cf3" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,NumericRangeValidator,8">
          <entityName>VqCarton</entityName>
          <fieldId>cartonQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE/>
          <MIN_VALUE>0</MIN_VALUE>
          <GRID_ID>vqCarton</GRID_ID>
          <LABEL_FIELD_ID>cartonQty</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,NumericRangeValidator,11" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,NumericRangeValidator,18">
          <entityName>Vq</entityName>
          <fieldId>prodWeight</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <MIN_VALUE/>
          <GRID_ID/>
          <LABEL_FIELD_ID>prodWeight</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,NumericRangeValidator,19">
          <entityName>VqCarton</entityName>
          <fieldId>cartonQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE/>
          <MIN_VALUE>0</MIN_VALUE>
          <GRID_ID>vqCarton</GRID_ID>
          <LABEL_FIELD_ID>cartonQty</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,NumericRangeValidator,22" profileId="VQ_PRODUCT_WEIGHT_VALIDATION" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,NumericRangeValidator,29">
          <entityName>Vq</entityName>
          <fieldId>prodWeight</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <MIN_VALUE/>
          <GRID_ID/>
          <LABEL_FIELD_ID>prodWeight</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,NumericRangeValidator,30">
          <entityName>VqCarton</entityName>
          <fieldId>cartonQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE/>
          <MIN_VALUE>0</MIN_VALUE>
          <GRID_ID>vqCarton</GRID_ID>
          <LABEL_FIELD_ID>cartonQty</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="EmailValidator" position="vq_validation.xlsx,EmailValidator">
    <ValidationField position="vq_validation.xlsx,EmailValidator,1" profileId="8d29af2e378d419c894a0c15f8313cf3" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,EmailValidator,8">
          <entityName>Vq</entityName>
          <fieldId>contactEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>contactEmail</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,EmailValidator,9">
          <entityName>VqContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,EmailValidator,12" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,EmailValidator,19">
          <entityName>Vq</entityName>
          <fieldId>contactEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>contactEmail</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,EmailValidator,20">
          <entityName>VqContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,EmailValidator,23" profileId="VQ_PRODUCT_WEIGHT_VALIDATION" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,EmailValidator,30">
          <entityName>Vq</entityName>
          <fieldId>contactEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>contactEmail</LABEL_FIELD_ID>
        </element>
        <element position="vq_validation.xlsx,EmailValidator,31">
          <entityName>VqContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vqContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="vq_validation.xlsx,ClassificationValidator">
    <ValidationField position="vq_validation.xlsx,ClassificationValidator,1" profileId="8d29af2e378d419c894a0c15f8313cf3" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,ClassificationValidator,8">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,ClassificationValidator,11" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,ClassificationValidator,18">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,ClassificationValidator,21" profileId="VQ_PRODUCT_WEIGHT_VALIDATION" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,ClassificationValidator,28">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="vq_validation.xlsx,HCLValidator">
    <ValidationField position="vq_validation.xlsx,HCLValidator,1" profileId="8d29af2e378d419c894a0c15f8313cf3" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,HCLValidator,8">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,HCLValidator,11" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,HCLValidator,18">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vq_validation.xlsx,HCLValidator,21" profileId="VQ_PRODUCT_WEIGHT_VALIDATION" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,HCLValidator,28">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="vq_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="vq_validation.xlsx,ExternalActiveValidator,1" profileId="8d29af2e378d419c894a0c15f8313aaa" profileName="">
      <elements id="default">
        <element position="vq_validation.xlsx,ExternalActiveValidator,8">
          <entityName>Vq</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
