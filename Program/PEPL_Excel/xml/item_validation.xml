<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="item" position="item_validation.xlsx">
  <sheet id="ValidationProfile" position="item_validation.xlsx,ValidationProfile">
    <ValidationProfile position="item_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="item_validation.xlsx,ValidationProfile,4">
          <id>63a6e04a77f74e4aad4194a9c726590a</id>
          <profileName>Default Data Validation Profile Item[ver:1]</profileName>
          <entityName>Item</entityName>
          <entityVer>1</entityVer>
          <action>MarkAsConcept,MarkAsCosting,MarkAsA<PERSON>pted,MarkAsFinalized,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,Mark<PERSON><PERSON>ustom<PERSON>tatus03Doc,MarkAs<PERSON>ustomStatus04D<PERSON>,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus05<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>07<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>08D<PERSON>,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus09Doc,<PERSON><PERSON><PERSON><PERSON><PERSON>Status10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.889</updatedOn>
        </element>
        <element position="item_validation.xlsx,ValidationProfile,5">
          <id>afab133445724901a5c0c2da0d63f35b</id>
          <profileName>New Data Validation Profile Item 01[ver:1]</profileName>
          <entityName>Item</entityName>
          <entityVer>1</entityVer>
          <action>ItemSaveDoc,ItemSaveAndConfirm</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.889</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="item_validation.xlsx,ValidationRule">
    <ValidationRule position="item_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="item_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <restapiBeanName>UniqueInSectionValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,7">
          <type>ItemMarkAsDefaultValidator</type>
          <className>com.core.cbx.item.action.ItemMarkAsDefaultValidator</className>
          <restapiBeanName>ItemMarkAsDefaultValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,8">
          <type>ItemDependenceValidator</type>
          <className>com.core.cbx.validation.validator.ItemDependenceValidator</className>
          <restapiBeanName>ItemDependenceValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,9">
          <type>ExpressionItemCountValidator</type>
          <className>com.core.cbx.validation.validator.ExpressionItemCountValidator</className>
          <restapiBeanName>ExpressionItemCountValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,10">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,11">
          <type>ItemValidatorForColor</type>
          <className>com.core.cbx.item.validator.ItemValidatorForColorProductCategory</className>
          <restapiBeanName>ItemValidatorForColorValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>N</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,12">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <restapiBeanName>HCLValidator</restapiBeanName>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,13">
          <type>CheckDigitValidator</type>
          <className>com.core.cbx.validation.validator.CheckDigitValidator</className>
          <restapiBeanName>CheckDigitValidator</restapiBeanName>
          <condition/>
          <enabled>N</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,14">
          <type>NumericRangeValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <restapiBeanName>NumericRangeValidator</restapiBeanName>
          <condition/>
          <enabled>N</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,15">
          <type>ManualRefreshValidator</type>
          <className>com.core.cbx.item.validator.ManualRefreshValidator</className>
          <restapiBeanName>ManualRefreshValidator</restapiBeanName>
          <condition/>
          <enabled>N</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,16">
          <type>DimensionUniqueLabelValidator</type>
          <className>com.core.cbx.validation.validator.DimensionUniqueLabelValidator</className>
          <restapiBeanName>DimensionUniqueLabelValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,17">
          <type>MandatoryCustomFieldValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryCustomFieldValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ValidationRule,18">
          <type>NumRangeCustomFieldValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <restapiBeanName>NumRangeCustomFieldValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="item_validation.xlsx,MandatoryValidator">
    <ValidationField position="item_validation.xlsx,MandatoryValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,MandatoryValidator,8">
          <entityName>Item</entityName>
          <fieldId>itemDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>itemDesc</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,9">
          <entityName>Item</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,10">
          <entityName>Item</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,11">
          <entityName>Item</entityName>
          <fieldId>itemNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,12">
          <entityName>Item</entityName>
          <fieldId>season</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>season</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,13">
          <entityName>Item</entityName>
          <fieldId>buyerItemNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>buyerItemNo</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,14">
          <entityName>Item</entityName>
          <fieldId>custHcl1</fieldId>
          <condition>isNotNewDoc</condition>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custHcl1</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,15">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>countryOfDestination</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>countryOfDestination</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,16">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>portOfDischarge</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>portOfDischarge</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,17">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>finalDestination</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>finalDestination</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,18">
          <entityName>ItemImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemImage</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,19">
          <entityName>ItemAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition>isAttachTypeIdCER</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemAttachment</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,20">
          <entityName>SpecRequirement</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,21">
          <entityName>SpecColorBom</entityName>
          <fieldId>specMaterial</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specColorBom</GRID_ID>
          <LABEL_FIELD_ID>specMaterial</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,22">
          <entityName>SpecArtworkBom</entityName>
          <fieldId>artworkName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specArtworkBom</GRID_ID>
          <LABEL_FIELD_ID>artworkName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,23">
          <entityName>ItemColor</entityName>
          <fieldId>shortName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>shortName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,24">
          <entityName>ItemSize</entityName>
          <fieldId>dimension</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>dimension</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,25">
          <entityName>ItemSize</entityName>
          <fieldId>sizeDisplayName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>sizeDisplayName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,26">
          <entityName>Item</entityName>
          <fieldId>itemSize</fieldId>
          <condition>validateClothingDivision</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>itemSize</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,27">
          <entityName>SpecRequirement</entityName>
          <fieldId>details</fieldId>
          <condition>isSpecRequirementMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>details</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID>seqNo</POSITION_LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,28">
          <entityName>SpecDesign</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>seq</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,29">
          <entityName>SpecDesign</entityName>
          <fieldId>imageType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>imageType</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,30">
          <entityName>SpecDesign</entityName>
          <fieldId>imageId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>imageId</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,31">
          <entityName>SpecMaterial</entityName>
          <fieldId>materialName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>materialName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,32">
          <entityName>SpecMaterial</entityName>
          <fieldId>materialType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>materialType</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,33">
          <entityName>SpecConstruction</entityName>
          <fieldId>constructionElement</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specConstruction</GRID_ID>
          <LABEL_FIELD_ID>constructionElement</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,34">
          <entityName>SpecConstruction</entityName>
          <fieldId>type</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specConstruction</GRID_ID>
          <LABEL_FIELD_ID>type</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,35">
          <entityName>SpecGradingRule</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>seq</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,36">
          <entityName>SpecGradingRule</entityName>
          <fieldId>code</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>code</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,37">
          <entityName>SpecGradingRule</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,38">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specAccessoriesMeasurements</GRID_ID>
          <LABEL_FIELD_ID>seq</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,39">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specAccessoriesMeasurements</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,40">
          <entityName>SpecInstruction</entityName>
          <fieldId>type</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specInstruction</GRID_ID>
          <LABEL_FIELD_ID>type</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,MandatoryValidator,43" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,MandatoryValidator,50">
          <entityName>Item</entityName>
          <fieldId>itemDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>itemDesc</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,51">
          <entityName>Item</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,52">
          <entityName>Item</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,53">
          <entityName>Item</entityName>
          <fieldId>itemNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,54">
          <entityName>Item</entityName>
          <fieldId>season</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>season</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,55">
          <entityName>Item</entityName>
          <fieldId>buyerItemNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>buyerItemNo</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,56">
          <entityName>Item</entityName>
          <fieldId>custHcl1</fieldId>
          <condition>isNotNewDoc</condition>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custHcl1</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,57">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>countryOfDestination</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>countryOfDestination</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,58">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>portOfDischarge</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>portOfDischarge</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,59">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>finalDestination</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>finalDestination</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,60">
          <entityName>ItemImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemImage</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,61">
          <entityName>ItemAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition>isAttachTypeIdCER</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemAttachment</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,62">
          <entityName>SpecRequirement</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,63">
          <entityName>SpecColorBom</entityName>
          <fieldId>specMaterial</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specColorBom</GRID_ID>
          <LABEL_FIELD_ID>specMaterial</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,64">
          <entityName>SpecArtworkBom</entityName>
          <fieldId>artworkName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specArtworkBom</GRID_ID>
          <LABEL_FIELD_ID>artworkName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,65">
          <entityName>ItemColor</entityName>
          <fieldId>shortName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>shortName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,66">
          <entityName>ItemSize</entityName>
          <fieldId>dimension</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>dimension</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,67">
          <entityName>ItemSize</entityName>
          <fieldId>sizeDisplayName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>sizeDisplayName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,68">
          <entityName>Item</entityName>
          <fieldId>itemSize</fieldId>
          <condition>validateClothingDivision</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>itemSize</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,69">
          <entityName>SpecRequirement</entityName>
          <fieldId>details</fieldId>
          <condition>isSpecRequirementMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>details</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID>seqNo</POSITION_LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,70">
          <entityName>SpecDesign</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>seq</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,71">
          <entityName>SpecDesign</entityName>
          <fieldId>imageType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>imageType</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,72">
          <entityName>SpecDesign</entityName>
          <fieldId>imageId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>imageId</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,73">
          <entityName>SpecMaterial</entityName>
          <fieldId>materialName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>materialName</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,74">
          <entityName>SpecMaterial</entityName>
          <fieldId>materialType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>materialType</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,75">
          <entityName>SpecMaterial</entityName>
          <fieldId>composition</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>composition</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,76">
          <entityName>SpecConstruction</entityName>
          <fieldId>constructionElement</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specConstruction</GRID_ID>
          <LABEL_FIELD_ID>constructionElement</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,77">
          <entityName>SpecConstruction</entityName>
          <fieldId>type</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specConstruction</GRID_ID>
          <LABEL_FIELD_ID>type</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,78">
          <entityName>SpecGradingRule</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>seq</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,79">
          <entityName>SpecGradingRule</entityName>
          <fieldId>code</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>code</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,80">
          <entityName>SpecGradingRule</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,81">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specAccessoriesMeasurements</GRID_ID>
          <LABEL_FIELD_ID>seq</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,82">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specAccessoriesMeasurements</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,83">
          <entityName>SpecInstruction</entityName>
          <fieldId>type</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>specInstruction</GRID_ID>
          <LABEL_FIELD_ID>type</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
        <element position="item_validation.xlsx,MandatoryValidator,84">
          <entityName>Item</entityName>
          <fieldId>custCodelist19</fieldId>
          <condition>NEWUI_ItemReportCategoryIs_1.2</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custCodelist19</LABEL_FIELD_ID>
          <POSITION_LABEL_FIELD_ID/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="MandatoryCustomFieldValidator" position="item_validation.xlsx,MandatoryCustomFieldValidator">
    <ValidationField position="item_validation.xlsx,MandatoryCustomFieldValidator,1" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,MandatoryCustomFieldValidator,8">
          <entityName>Item</entityName>
          <fieldId>custCodelist19</fieldId>
          <condition>NEWUI_ItemReportCategoryIs_1.2</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custCodelist19</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,MandatoryCustomFieldValidator,9">
          <entityName>Item</entityName>
          <fieldId>custCodelist23</fieldId>
          <condition>isRoyaltyPaidByPEPCO</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custCodelist23</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,MandatoryCustomFieldValidator,10">
          <entityName>Item</entityName>
          <fieldId>custCodelist24</fieldId>
          <condition>isRoyaltyPaidByPEPCO</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custCodelist24</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="item_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="item_validation.xlsx,UniqueInModuleValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Item</entityName>
          <fieldId>itemNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <CHECK_LATEST_VERSION>Y</CHECK_LATEST_VERSION>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,UniqueInModuleValidator,11" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>Item</entityName>
          <fieldId>itemNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <CHECK_LATEST_VERSION>Y</CHECK_LATEST_VERSION>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="item_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="item_validation.xlsx,UniqueInSectionValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>ItemCust</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010088</ERROR_ID>
          <FIELD_GROUP>cust,market,channel</FIELD_GROUP>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,9">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010090</ERROR_ID>
          <FIELD_GROUP>cust,portOfDischarge,finalDestination</FIELD_GROUP>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,10">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>refKey</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>specAccessoriesMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,11">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specAccessoriesMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,12">
          <entityName>SpecGradingRule</entityName>
          <fieldId>code</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>pepl_08010003</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>code</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,13">
          <entityName>SpecGradingRule</entityName>
          <fieldId>refKey</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>specGradingRule</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,14">
          <entityName>SpecGradingRule</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specGradingRule</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,15">
          <entityName>SpecMeasurement</entityName>
          <fieldId>refKey</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>specMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,16">
          <entityName>SpecOther</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specOther</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,17">
          <entityName>SpecTreatment</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specTreatment</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,18">
          <entityName>SpecDesign</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,19">
          <entityName>SpecColorBom</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specColorBom</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,20">
          <entityName>SpecMaterial</entityName>
          <fieldId>materialName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052504</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,21">
          <entityName>SpecMaterial</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,22">
          <entityName>SpecRequirement</entityName>
          <fieldId>category</fieldId>
          <condition/>
          <conditionType/>
          <enabled>n</enabled>
          <ERROR_ID>08010100</ERROR_ID>
          <FIELD_GROUP>category,type,description</FIELD_GROUP>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,23">
          <entityName>SpecRequirement</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,24">
          <entityName>ItemColor</entityName>
          <fieldId>colorSeq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,25">
          <entityName>ItemSize</entityName>
          <fieldId>sizeSeq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,26">
          <entityName>ItemVendorFact</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP>vendor,fact</FIELD_GROUP>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,27">
          <entityName>ItemRelated</entityName>
          <fieldId>relatedItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010098</ERROR_ID>
          <FIELD_GROUP>relatedItem</FIELD_GROUP>
          <GRID_ID>itemRelated</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,28">
          <entityName>ItemSize</entityName>
          <fieldId>sizeCode</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,UniqueInSectionValidator,31" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,UniqueInSectionValidator,38">
          <entityName>ItemCust</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010088</ERROR_ID>
          <FIELD_GROUP>cust,market,channel</FIELD_GROUP>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,39">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010090</ERROR_ID>
          <FIELD_GROUP>cust,portOfDischarge,finalDestination</FIELD_GROUP>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,40">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>refKey</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>specAccessoriesMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,41">
          <entityName>SpecAccessoriesMeasurement</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specAccessoriesMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,42">
          <entityName>SpecGradingRule</entityName>
          <fieldId>code</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>pepl_08010003</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specGradingRules</GRID_ID>
          <LABEL_FIELD_ID>code</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,43">
          <entityName>SpecGradingRule</entityName>
          <fieldId>refKey</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>specGradingRule</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,44">
          <entityName>SpecGradingRule</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specGradingRule</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,45">
          <entityName>SpecMeasurement</entityName>
          <fieldId>refKey</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>specMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,46">
          <entityName>SpecOther</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specOther</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,47">
          <entityName>SpecTreatment</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specTreatment</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,48">
          <entityName>SpecDesign</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specDesign</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,49">
          <entityName>SpecColorBom</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specColorBom</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,50">
          <entityName>SpecMaterial</entityName>
          <fieldId>materialName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052504</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,51">
          <entityName>SpecMaterial</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,52">
          <entityName>SpecRequirement</entityName>
          <fieldId>category</fieldId>
          <condition/>
          <conditionType/>
          <enabled>n</enabled>
          <ERROR_ID>08010100</ERROR_ID>
          <FIELD_GROUP>category,type,description</FIELD_GROUP>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,53">
          <entityName>SpecRequirement</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>specRequirement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,54">
          <entityName>ItemColor</entityName>
          <fieldId>colorSeq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,55">
          <entityName>ItemSize</entityName>
          <fieldId>sizeSeq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>16052503</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,56">
          <entityName>ItemVendorFact</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP>vendor,fact</FIELD_GROUP>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,57">
          <entityName>ItemRelated</entityName>
          <fieldId>relatedItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010098</ERROR_ID>
          <FIELD_GROUP>relatedItem</FIELD_GROUP>
          <GRID_ID>itemRelated</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,58">
          <entityName>ItemSize</entityName>
          <fieldId>sizeCode</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>itemSize</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,UniqueInSectionValidator,59">
          <entityName>ItemColor</entityName>
          <fieldId>shortName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>REF083</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ItemMarkAsDefaultValidator" position="item_validation.xlsx,ItemMarkAsDefaultValidator">
    <ValidationField position="item_validation.xlsx,ItemMarkAsDefaultValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ItemMarkAsDefaultValidator,8">
          <entityName>Item</entityName>
          <fieldId>itemSourAgent</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ItemMarkAsDefaultValidator,9">
          <entityName>Item</entityName>
          <fieldId>itemCust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,ItemMarkAsDefaultValidator,12" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ItemMarkAsDefaultValidator,19">
          <entityName>Item</entityName>
          <fieldId>itemSourAgent</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
        <element position="item_validation.xlsx,ItemMarkAsDefaultValidator,20">
          <entityName>Item</entityName>
          <fieldId>itemCust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ItemDependenceValidator" position="item_validation.xlsx,ItemDependenceValidator">
    <ValidationField position="item_validation.xlsx,ItemDependenceValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ItemDependenceValidator,8">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>businessName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <GRID_ID>itemCustFinalDest</GRID_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,ItemDependenceValidator,11" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ItemDependenceValidator,18">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>businessName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <GRID_ID>itemCustFinalDest</GRID_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExpressionItemCountValidator" position="item_validation.xlsx,ExpressionItemCountValidator">
    <ValidationField position="item_validation.xlsx,ExpressionItemCountValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ExpressionItemCountValidator,8">
          <entityName>Item</entityName>
          <fieldId>itemColor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <EXPRESSION>entity.isPrimary=true</EXPRESSION>
          <MIN_COUNT>1</MIN_COUNT>
          <ERROR_ID>08020009</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,ExpressionItemCountValidator,9">
          <entityName>Item</entityName>
          <fieldId>itemCust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <EXPRESSION>entity.isDefault=true</EXPRESSION>
          <MIN_COUNT>1</MIN_COUNT>
          <ERROR_ID>8010091</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,ExpressionItemCountValidator,10">
          <entityName>Item</entityName>
          <fieldId>itemSourAgent</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemSourAgent</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <EXPRESSION>entity.isDefault=true</EXPRESSION>
          <MIN_COUNT>1</MIN_COUNT>
          <ERROR_ID>8010091</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,ExpressionItemCountValidator,13" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ExpressionItemCountValidator,20">
          <entityName>Item</entityName>
          <fieldId>itemColor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <EXPRESSION>entity.isPrimary=true</EXPRESSION>
          <MIN_COUNT>1</MIN_COUNT>
          <ERROR_ID>08020009</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,ExpressionItemCountValidator,21">
          <entityName>Item</entityName>
          <fieldId>itemCust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <EXPRESSION>entity.isDefault=true</EXPRESSION>
          <MIN_COUNT>1</MIN_COUNT>
          <ERROR_ID>8010091</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,ExpressionItemCountValidator,22">
          <entityName>Item</entityName>
          <fieldId>itemSourAgent</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemSourAgent</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <EXPRESSION>entity.isDefault=true</EXPRESSION>
          <MIN_COUNT>1</MIN_COUNT>
          <ERROR_ID>8010091</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="item_validation.xlsx,ClassificationValidator">
    <ValidationField position="item_validation.xlsx,ClassificationValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ClassificationValidator,8">
          <entityName>Item</entityName>
          <fieldId>itemCust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>cust</TARGET_FIELD>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="item_validation.xlsx,ClassificationValidator,9">
          <entityName>Item</entityName>
          <fieldId>itemVendorFact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>vendor</TARGET_FIELD>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="item_validation.xlsx,ClassificationValidator,10">
          <entityName>Item</entityName>
          <fieldId>itemVendorFact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>fact</TARGET_FIELD>
          <DOCUMENT_NO>factCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,ClassificationValidator,13" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ClassificationValidator,20">
          <entityName>Item</entityName>
          <fieldId>itemCust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>cust</TARGET_FIELD>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="item_validation.xlsx,ClassificationValidator,21">
          <entityName>Item</entityName>
          <fieldId>itemVendorFact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>vendor</TARGET_FIELD>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="item_validation.xlsx,ClassificationValidator,22">
          <entityName>Item</entityName>
          <fieldId>itemVendorFact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>fact</TARGET_FIELD>
          <DOCUMENT_NO>factCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="item_validation.xlsx,HCLValidator">
    <ValidationField position="item_validation.xlsx,HCLValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,HCLValidator,8">
          <entityName>ItemCust</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="item_validation.xlsx,HCLValidator,9">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="item_validation.xlsx,HCLValidator,10">
          <entityName>ItemVendorFact</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="item_validation.xlsx,HCLValidator,11">
          <entityName>ItemVendorFact</entityName>
          <fieldId>fact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>factHc</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,HCLValidator,14" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,HCLValidator,21">
          <entityName>ItemCust</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCust</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="item_validation.xlsx,HCLValidator,22">
          <entityName>ItemCustFinalDest</entityName>
          <fieldId>cust</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemCustFinalDest</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="item_validation.xlsx,HCLValidator,23">
          <entityName>ItemVendorFact</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="item_validation.xlsx,HCLValidator,24">
          <entityName>ItemVendorFact</entityName>
          <fieldId>fact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemVendorFact</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>factHc</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ItemValidatorForColor" position="item_validation.xlsx,ItemValidatorForColor">
    <ValidationField position="item_validation.xlsx,ItemValidatorForColor,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ItemValidatorForColor,8">
          <entityName>Item</entityName>
          <fieldId>itemColor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>color</TARGET_FIELD>
          <DOCUMENT_NO>colorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,ItemValidatorForColor,11" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ItemValidatorForColor,18">
          <entityName>Item</entityName>
          <fieldId>itemColor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>itemColor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>color</TARGET_FIELD>
          <DOCUMENT_NO>colorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="CheckDigitValidator" position="item_validation.xlsx,CheckDigitValidator">
    <ValidationField position="item_validation.xlsx,CheckDigitValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,CheckDigitValidator,8">
          <entityName>Item</entityName>
          <fieldId>masterUpc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>UPC_TYPE</validateCodelistType>
          <validateBaseField>upcType</validateBaseField>
        </element>
        <element position="item_validation.xlsx,CheckDigitValidator,9">
          <entityName>Item</entityName>
          <fieldId>masterEan</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>EAN_TYPE</validateCodelistType>
          <validateBaseField>eanType</validateBaseField>
        </element>
        <element position="item_validation.xlsx,CheckDigitValidator,10">
          <entityName>ItemSku</entityName>
          <fieldId>upc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>UPC_TYPE</validateCodelistType>
          <validateBaseField>upcType</validateBaseField>
        </element>
        <element position="item_validation.xlsx,CheckDigitValidator,11">
          <entityName>ItemSku</entityName>
          <fieldId>ean</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>EAN_TYPE</validateCodelistType>
          <validateBaseField>eanType</validateBaseField>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,CheckDigitValidator,14" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,CheckDigitValidator,21">
          <entityName>Item</entityName>
          <fieldId>masterUpc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>UPC_TYPE</validateCodelistType>
          <validateBaseField>upcType</validateBaseField>
        </element>
        <element position="item_validation.xlsx,CheckDigitValidator,22">
          <entityName>Item</entityName>
          <fieldId>masterEan</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>EAN_TYPE</validateCodelistType>
          <validateBaseField>eanType</validateBaseField>
        </element>
        <element position="item_validation.xlsx,CheckDigitValidator,23">
          <entityName>ItemSku</entityName>
          <fieldId>upc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>UPC_TYPE</validateCodelistType>
          <validateBaseField>upcType</validateBaseField>
        </element>
        <element position="item_validation.xlsx,CheckDigitValidator,24">
          <entityName>ItemSku</entityName>
          <fieldId>ean</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <validateCodelistType>EAN_TYPE</validateCodelistType>
          <validateBaseField>eanType</validateBaseField>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumericRangeValidator" position="item_validation.xlsx,NumericRangeValidator">
    <ValidationField position="item_validation.xlsx,NumericRangeValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,NumericRangeValidator,8">
          <entityName>Item</entityName>
          <fieldId>landedCost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,9">
          <entityName>Item</entityName>
          <fieldId>offerPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,10">
          <entityName>Item</entityName>
          <fieldId>retailPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,11">
          <entityName>Item</entityName>
          <fieldId>initialOrderQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,12">
          <entityName>Item</entityName>
          <fieldId>totalQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,NumericRangeValidator,15" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,NumericRangeValidator,22">
          <entityName>Item</entityName>
          <fieldId>landedCost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,23">
          <entityName>Item</entityName>
          <fieldId>offerPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,24">
          <entityName>Item</entityName>
          <fieldId>retailPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,25">
          <entityName>Item</entityName>
          <fieldId>initialOrderQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
        <element position="item_validation.xlsx,NumericRangeValidator,26">
          <entityName>Item</entityName>
          <fieldId>totalQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ManualRefreshValidator" position="item_validation.xlsx,ManualRefreshValidator">
    <ValidationField position="item_validation.xlsx,ManualRefreshValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ManualRefreshValidator,8">
          <entityName>Item</entityName>
          <fieldId>specMeasurement</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <SYNC_GRID>specGradingRule</SYNC_GRID>
          <SYNC_FIELD_ID>syncIdentifier</SYNC_FIELD_ID>
          <ERROR_ID>REF057</ERROR_ID>
          <ACTION_IDS>ItemSaveDoc,ItemSaveAndConfirm</ACTION_IDS>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,ManualRefreshValidator,11" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,ManualRefreshValidator,18">
          <entityName>Item</entityName>
          <fieldId>specMeasurement</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>specMeasurement</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <SYNC_GRID>specGradingRule</SYNC_GRID>
          <SYNC_FIELD_ID>syncIdentifier</SYNC_FIELD_ID>
          <ERROR_ID>REF057</ERROR_ID>
          <ACTION_IDS>ItemSaveDoc,ItemSaveAndConfirm</ACTION_IDS>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="DimensionUniqueLabelValidator" position="item_validation.xlsx,DimensionUniqueLabelValidator">
    <ValidationField position="item_validation.xlsx,DimensionUniqueLabelValidator,1" profileId="63a6e04a77f74e4aad4194a9c726590a" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,DimensionUniqueLabelValidator,8">
          <entityName>Item</entityName>
          <fieldId>itemSize</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <DIMENSION_FIELD_ID>dimension</DIMENSION_FIELD_ID>
          <DISPLAY_NAME_FIELD_ID>sizeDisplayName</DISPLAY_NAME_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="item_validation.xlsx,DimensionUniqueLabelValidator,11" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,DimensionUniqueLabelValidator,18">
          <entityName>Item</entityName>
          <fieldId>itemSize</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <DIMENSION_FIELD_ID>dimension</DIMENSION_FIELD_ID>
          <DISPLAY_NAME_FIELD_ID>sizeDisplayName</DISPLAY_NAME_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="CompareToDayValidator" position="item_validation.xlsx,CompareToDayValidator">
    <ValidationField position="item_validation.xlsx,CompareToDayValidator,1" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,CompareToDayValidator,8">
          <entityName>SpecConstruction</entityName>
          <fieldId>custDate3</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>specConstruction</GRID_ID>
          <LABEL_FIELD_ID>custDate3</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0004</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumRangeCustomFieldValidator" position="item_validation.xlsx,NumRangeCustomFieldValidator">
    <ValidationField position="item_validation.xlsx,NumRangeCustomFieldValidator,1" profileId="afab133445724901a5c0c2da0d63f35b" profileName="">
      <elements id="default">
        <element position="item_validation.xlsx,NumRangeCustomFieldValidator,8">
          <entityName>SpecMaterial</entityName>
          <fieldId>custDecimal2</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE/>
          <ERROR_ID/>
          <MIN_VALUE>0</MIN_VALUE>
          <MAX_VALUE>100</MAX_VALUE>
          <GRID_ID>specMaterial</GRID_ID>
          <LABEL_FIELD_ID>custDecimal2</LABEL_FIELD_ID>
        </element>
        <element position="item_validation.xlsx,NumRangeCustomFieldValidator,9">
          <entityName>SpecPack</entityName>
          <fieldId>custDecimal2</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GREATE_THAN_VALUE/>
          <ERROR_ID/>
          <MIN_VALUE>0</MIN_VALUE>
          <MAX_VALUE>100</MAX_VALUE>
          <GRID_ID>specPack</GRID_ID>
          <LABEL_FIELD_ID>custDecimal2</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
