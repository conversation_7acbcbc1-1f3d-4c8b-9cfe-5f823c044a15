<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="sampleTracker" position="sampleTracker_validation.xlsx">
  <sheet id="ValidationProfile" position="sampleTracker_validation.xlsx,ValidationProfile">
    <ValidationProfile position="sampleTracker_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ValidationProfile,4">
          <id>sampleTrackerValidatorId</id>
          <profileName>Default Data Validation Profile SampleTracker[ver:1]</profileName>
          <entityName>SampleTracker</entityName>
          <entityVer>1</entityVer>
          <action>SampleTrackerSendToBuyer,SaveDoc,SubmitSampleTracker,NotifyInternal,NotifyExternal,MarkAsSubmit,MarkAsR<PERSON>ei<PERSON>,<PERSON><PERSON><PERSON>n<PERSON>rog<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,MarkAsCustomStatus01Doc,MarkAs<PERSON>ustomStatus02D<PERSON>,Mark<PERSON><PERSON>ustomStatus03D<PERSON>,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus04Doc,MarkAsCustomStatus05Doc,MarkAsCustomStatus06Doc,MarkAsCustomStatus07Doc,MarkAsCustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc,SampleDetailsSubmitAction</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.270</updatedOn>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationProfile,5">
          <id>sampleTrackerValidatorId2</id>
          <profileName>Default Data Validation Profile SampleTracker[ver:1]</profileName>
          <entityName>SampleTracker</entityName>
          <entityVer>1</entityVer>
          <action>SampleTrackerSendToVendor</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.270</updatedOn>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationProfile,6">
          <id>sampleTrackerValidatorId3</id>
          <profileName>Default Data Validation Profile SampleTracker[ver:1]</profileName>
          <entityName>SampleTracker</entityName>
          <entityVer>1</entityVer>
          <action>SampleRequestST</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.270</updatedOn>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationProfile,7">
          <id>sampleTrackerValidatorId4</id>
          <profileName>Default Data Validation Profile SampleTracker[ver:1]</profileName>
          <entityName>SampleTracker</entityName>
          <entityVer>1</entityVer>
          <action>SaveAndConfirm</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:06.270</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="sampleTracker_validation.xlsx,ValidationRule">
    <ValidationRule position="sampleTracker_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationRule,6">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationRule,7">
          <type>AnyChildrenHasRecordValidator</type>
          <className>com.core.cbx.validation.validator.AnyChildrenHasRecordValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationRule,8">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationRule,9">
          <type>CompareToDayValidator</type>
          <className>com.core.pepl.validation.validator.CompareToDayValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleTracker_validation.xlsx,ValidationRule,10">
          <type>MaterialInstructionsValidator</type>
          <className>com.core.pepl.validation.validator.MaterialInstructionsValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="sampleTracker_validation.xlsx,MandatoryValidator">
    <ValidationField position="sampleTracker_validation.xlsx,MandatoryValidator,1" profileId="sampleTrackerValidatorId" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,8">
          <entityName>SampleTracker</entityName>
          <fieldId>trackerNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>trackerNo</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,9">
          <entityName>SampleTracker</entityName>
          <fieldId>vendorEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>vendorEmail</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,10">
          <entityName>SampleTracker</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,11">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,12">
          <entityName>SampleTracker</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isNotVendorDomain</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,13">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,14">
          <entityName>SampleTrackerImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerImages</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,15">
          <entityName>SampleTrackerAttachment</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerAttachments</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,16">
          <entityName>SampleDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,17">
          <entityName>MaterialDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,18">
          <entityName>SampleDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>qualitySampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,19">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>visualSampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,20">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>isLabDipAndAttachNotNull</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,MandatoryValidator,23" profileId="sampleTrackerValidatorId4" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,30">
          <entityName>SampleTracker</entityName>
          <fieldId>trackerNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>trackerNo</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,31">
          <entityName>SampleTracker</entityName>
          <fieldId>vendorEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>vendorEmail</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,32">
          <entityName>SampleTracker</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,33">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,34">
          <entityName>SampleTracker</entityName>
          <fieldId>productCategory</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,35">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,36">
          <entityName>SampleTrackerImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerImages</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,37">
          <entityName>SampleTrackerAttachment</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerAttachments</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,38">
          <entityName>SampleDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,39">
          <entityName>MaterialDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,40">
          <entityName>SampleDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>qualitySampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,41">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>visualSampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,42">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>isLabDipAndAttachNotNull</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,MandatoryValidator,45" profileId="sampleTrackerValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,52">
          <entityName>SampleTrackerImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerImages</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,53">
          <entityName>SampleTrackerAttachment</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerAttachments</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,54">
          <entityName>SampleDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,55">
          <entityName>MaterialDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,56">
          <entityName>SampleDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>qualitySampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,57">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>visualSampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,58">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>isLabDipAndAttachNotNull</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,MandatoryValidator,61" profileId="sampleTrackerValidatorId3" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,68">
          <entityName>SampleTracker</entityName>
          <fieldId>trackerNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>trackerNo</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,69">
          <entityName>SampleTracker</entityName>
          <fieldId>vendorEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>vendorEmail</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,70">
          <entityName>SampleTracker</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,71">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,72">
          <entityName>SampleTracker</entityName>
          <fieldId>productCategory</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,73">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <GRID_ID/>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,74">
          <entityName>SampleTrackerImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerImages</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,75">
          <entityName>SampleTrackerAttachment</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <GRID_ID>sampleTrackerAttachments</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,76">
          <entityName>SampleDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,77">
          <entityName>MaterialDetail</entityName>
          <fieldId>sampleType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>sampleType</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,78">
          <entityName>SampleDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>qualitySampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>sampleDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,79">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>visualSampleColorPatternMandatory</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,MandatoryValidator,80">
          <entityName>MaterialDetail</entityName>
          <fieldId>colorAndPattern</fieldId>
          <condition>isLabDipAndAttachNotNull</condition>
          <conditionType>1</conditionType>
          <enabled>N</enabled>
          <LABEL_FIELD_ID>colorAndPattern</LABEL_FIELD_ID>
          <GRID_ID>materialDetail</GRID_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="sampleTracker_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="sampleTracker_validation.xlsx,UniqueInModuleValidator,1" profileId="sampleTrackerValidatorId" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>SampleTracker</entityName>
          <fieldId>trackerNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,UniqueInModuleValidator,11" profileId="sampleTrackerValidatorId4" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>SampleTracker</entityName>
          <fieldId>trackerNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,UniqueInModuleValidator,21" profileId="sampleTrackerValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,UniqueInModuleValidator,28">
          <entityName>SampleTracker</entityName>
          <fieldId>trackerNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="sampleTracker_validation.xlsx,ClassificationValidator">
    <ValidationField position="sampleTracker_validation.xlsx,ClassificationValidator,1" profileId="sampleTrackerValidatorId" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,8">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,9">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,ClassificationValidator,12" profileId="sampleTrackerValidatorId4" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,19">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,20">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,ClassificationValidator,23" profileId="sampleTrackerValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,30">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,31">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,ClassificationValidator,34" profileId="sampleTrackerValidatorId3" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,41">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="sampleTracker_validation.xlsx,ClassificationValidator,42">
          <entityName>SampleTracker</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>item</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="AnyChildrenHasRecordValidator" position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator">
    <ValidationField position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,1" profileId="sampleTrackerValidatorId" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,8">
          <entityName>SampleTracker</entityName>
          <fieldId>id</fieldId>
          <condition>isSampleEvaluationDisabled</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>lbl.sampleTracker.detailTables</LABEL_FIELD_ID>
          <ERROR_ID>08010034</ERROR_ID>
          <COLLECTION_FIELD_IDS>sampleDetail,materialDetail,documentDetail</COLLECTION_FIELD_IDS>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,11" profileId="sampleTrackerValidatorId4" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,18">
          <entityName>SampleTracker</entityName>
          <fieldId>id</fieldId>
          <condition>isSampleEvaluationDisabled</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>lbl.sampleTracker.detailTables</LABEL_FIELD_ID>
          <ERROR_ID>08010034</ERROR_ID>
          <COLLECTION_FIELD_IDS>sampleDetail,materialDetail,documentDetail</COLLECTION_FIELD_IDS>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,21" profileId="sampleTrackerValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,28">
          <entityName>SampleTracker</entityName>
          <fieldId>id</fieldId>
          <condition>isSampleEvaluationDisabled</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>lbl.sampleTracker.detailTables</LABEL_FIELD_ID>
          <ERROR_ID>08010034</ERROR_ID>
          <COLLECTION_FIELD_IDS>sampleDetail,materialDetail,documentDetail</COLLECTION_FIELD_IDS>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,31" profileId="sampleTrackerValidatorId3" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,AnyChildrenHasRecordValidator,38">
          <entityName>SampleTracker</entityName>
          <fieldId>id</fieldId>
          <condition>isSampleEvaluationDisabled</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>lbl.sampleTracker.detailTables</LABEL_FIELD_ID>
          <ERROR_ID>08010034</ERROR_ID>
          <COLLECTION_FIELD_IDS>sampleDetail,materialDetail,documentDetail</COLLECTION_FIELD_IDS>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="sampleTracker_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="sampleTracker_validation.xlsx,ExternalActiveValidator,1" profileId="sampleTrackerValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,ExternalActiveValidator,8">
          <entityName>SampleTracker</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="CompareToDayValidator" position="sampleTracker_validation.xlsx,CompareToDayValidator">
    <ValidationField position="sampleTracker_validation.xlsx,CompareToDayValidator,1" profileId="sampleTrackerValidatorId" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,CompareToDayValidator,8">
          <entityName>SampleDetail</entityName>
          <fieldId>custDate1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>sampleDetail</GRID_ID>
          <LABEL_FIELD_ID>custDate1</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0005</ERROR_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,CompareToDayValidator,9">
          <entityName>MaterialDetail</entityName>
          <fieldId>custDate2</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>materialDetail</GRID_ID>
          <LABEL_FIELD_ID>custDate2</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0006</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,CompareToDayValidator,12" profileId="sampleTrackerValidatorId4" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,CompareToDayValidator,19">
          <entityName>SampleDetail</entityName>
          <fieldId>custDate1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>sampleDetail</GRID_ID>
          <LABEL_FIELD_ID>custDate1</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0005</ERROR_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,CompareToDayValidator,20">
          <entityName>MaterialDetail</entityName>
          <fieldId>custDate2</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>materialDetail</GRID_ID>
          <LABEL_FIELD_ID>custDate2</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0006</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleTracker_validation.xlsx,CompareToDayValidator,23" profileId="sampleTrackerValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,CompareToDayValidator,30">
          <entityName>SampleDetail</entityName>
          <fieldId>custDate1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>sampleDetail</GRID_ID>
          <LABEL_FIELD_ID>custDate1</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0005</ERROR_ID>
        </element>
        <element position="sampleTracker_validation.xlsx,CompareToDayValidator,31">
          <entityName>MaterialDetail</entityName>
          <fieldId>custDate2</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <BEFORE_DAY_VALUE>3</BEFORE_DAY_VALUE>
          <AFTER_DAY_VALUE>3</AFTER_DAY_VALUE>
          <GRID_ID>materialDetail</GRID_ID>
          <LABEL_FIELD_ID>custDate2</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0006</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="MaterialInstructionsValidator" position="sampleTracker_validation.xlsx,MaterialInstructionsValidator">
    <ValidationField position="sampleTracker_validation.xlsx,MaterialInstructionsValidator,1" profileId="sampleTrackerValidatorId4" profileName="">
      <elements id="default">
        <element position="sampleTracker_validation.xlsx,MaterialInstructionsValidator,8">
          <entityName>MaterialDetail</entityName>
          <fieldId>instructions</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>instructions</GRID_ID>
          <LABEL_FIELD_ID>materialDetail</LABEL_FIELD_ID>
          <ERROR_ID>PEPL0007</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
