<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="sampleRequest" position="sampleRequest_dataMappingRule.xlsx">
  <sheet id="itemToSampleRequest" position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest">
    <DataMappingRule description="Mapping from Item to SampleRequest" domain="PEPL" dstEntityName="SampleRequest" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemToSampleRequest" position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,9">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord() &amp;&amp; entity.isNotAvailable='0' &amp;&amp; entity.vendor.docStatus='active' &amp;&amp; !(['draft', 'pending'].contains(entity.vendor.editingStatus))</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.factName[0].ref</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord() &amp;&amp; entity.factName.size=1 &amp;&amp; entity.factName[0].ref.docStatus='active'</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,11">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact.vendor</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.email</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>(entity.resultEmails=null; foreach (item : entity.contacts) { if(item.contactTypeId.size&gt;0) {foreach (type : item.contactTypeId) {if(type.refRef=='DEFAULT_FOR_SAMPLE') {entity.resultEmails = (entity.resultEmails==null ? '' : entity.resultEmails + ',') + item.email}}}} return entity.resultEmails)</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorCode</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.vendorCode</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorName</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.businessName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,15">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,17">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Field</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,18">
          <mappingType>Field</mappingType>
          <srcFieldId>setNo</srcFieldId>
          <dstFieldId>setNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Field</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,19">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Field</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,20">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>itemShortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Field</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,21">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Field</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,22">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>sampleRequestItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,23">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>sampleRequestItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,24">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,25">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord</srcFieldId>
          <dstFieldId>sampleRequestItem.sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,26">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>sampleRequestItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,27">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>sampleRequestItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,28">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.sourcingRecordVendorFact</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>SourcingRecord</mappedValue>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord() &amp;&amp; entity.isNotAvailable='0' &amp;&amp; entity.vendor.docStatus='active' &amp;&amp; !(['draft', 'pending'].contains(entity.vendor.editingStatus))</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,29">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.factName[0].ref</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord() &amp;&amp; entity.factName.size=1 &amp;&amp; entity.factName[0].ref.docStatus='active'</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,30">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.sourcingRecordVendorFact.vendor</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,31">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.email</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>(entity.resultEmails=null; foreach (item : entity.contacts) { if(item.contactTypeId.size&gt;0) {foreach (type : item.contactTypeId) {if(type.refRef=='DEFAULT_FOR_SAMPLE') {entity.resultEmails = (entity.resultEmails==null ? '' : entity.resultEmails + ',') + item.email}}}} return entity.resultEmails)</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,32">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorCode</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.vendorCode</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,33">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorName</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.businessName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleRequest_dataMappingRule.xlsx,itemToSampleRequest,37">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.SampleRequestSelectItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleRequestSelectVendor" position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor">
    <DataMappingRule description="Mapping from Vendor to SampleRequest" domain="PEPL" dstEntityName="SampleRequest" dstEntityVersion="1" effectiveDate="2012-02-20" id="sampleRequestSelectVendor" position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,11">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestSelectVendor,16">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.SampleRequestSelectVendorProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleRequestAddItem" position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem">
    <DataMappingRule description="Mapping from Item to SampleRequest" domain="PEPL" dstEntityName="SampleRequest" dstEntityVersion="1" effectiveDate="2012-02-20" id="sampleRequestAddItem" position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,11">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>sampleRequestItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,12">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>sampleRequestItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,13">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord() &amp;&amp; entity.isNotAvailable='0' &amp;&amp; entity.vendor.docStatus='active' &amp;&amp; !(['draft', 'pending'].contains(entity.vendor.editingStatus))</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.factName[0].ref</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord() &amp;&amp; entity.factName.size=1 &amp;&amp; entity.factName[0].ref.docStatus='active'</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact.vendor</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.email</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>(entity.resultEmails=null; foreach (item : entity.contacts) { if(item.contactTypeId.size&gt;0) {foreach (type : item.contactTypeId) {if(type.refRef=='DEFAULT_FOR_SAMPLE') {entity.resultEmails = (entity.resultEmails==null ? '' : entity.resultEmails + ',') + item.email}}}} return entity.resultEmails)</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorCode</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.vendorCode</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorName</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.businessName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.sampleRequestAddItem</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>SAMPLE_REQUEST_ADD_ITEM</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,20">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord</srcFieldId>
          <dstFieldId>sampleRequestItem.sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,21">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>sampleRequestItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>sampleRequestItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.sourcingRecordVendorFact</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>SourcingRecord</mappedValue>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord() &amp;&amp; entity.isNotAvailable='0' &amp;&amp; entity.vendor.docStatus='active' &amp;&amp; !(['draft', 'pending'].contains(entity.vendor.editingStatus))</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.factName[0].ref</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord() &amp;&amp; entity.factName.size=1 &amp;&amp; entity.factName[0].ref.docStatus='active'</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,25">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.sourcingRecordVendorFact.vendor</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.email</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>(entity.resultEmails=null; foreach (item : entity.contacts) { if(item.contactTypeId.size&gt;0) {foreach (type : item.contactTypeId) {if(type.refRef=='DEFAULT_FOR_SAMPLE') {entity.resultEmails = (entity.resultEmails==null ? '' : entity.resultEmails + ',') + item.email}}}} return entity.resultEmails)</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,27">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorCode</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.vendorCode</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,28">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.vendorName</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>entity.businessName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,29">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleRequestVendor.sampleRequestAddItem</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue>SAMPLE_REQUEST_ADD_ITEM</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleRequest_dataMappingRule.xlsx,sampleRequestAddItem,33">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.SampleRequestSelectItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
