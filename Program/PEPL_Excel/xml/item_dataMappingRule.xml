<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="item" position="item_dataMappingRule.xlsx">
  <sheet id="itemAttachmentsCopy" position="item_dataMappingRule.xlsx,itemAttachmentsCopy">
    <DataMappingRule description="Mapping for Copy Item Attachments" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemAttachmentsCopy" position="item_dataMappingRule.xlsx,itemAttachmentsCopy,1" srcEntityName="ItemAttachment" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemAttachmentsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAttachmentsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemImagesCopy" position="item_dataMappingRule.xlsx,itemImagesCopy">
    <DataMappingRule description="Mapping for Copy Item Images" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemImagesCopy" position="item_dataMappingRule.xlsx,itemImagesCopy,1" srcEntityName="ItemImage" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemImagesCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemImagesCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemImagesCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemCustCopy" position="item_dataMappingRule.xlsx,itemCustCopy">
    <DataMappingRule description="Mapping from item cust copy" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemCustCopy" position="item_dataMappingRule.xlsx,itemCustCopy,1" srcEntityName="ItemCust" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemCustCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCustCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemCust</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCustCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemOtherCopy" position="item_dataMappingRule.xlsx,itemOtherCopy">
    <DataMappingRule description="Mapping from item other copy" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemOtherCopy" position="item_dataMappingRule.xlsx,itemOtherCopy,1" srcEntityName="ItemOther" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemOtherCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemOtherCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemOther</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemCustSelect" position="item_dataMappingRule.xlsx,itemCustSelect">
    <DataMappingRule description="Mapping from cust to item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemCustSelect" position="item_dataMappingRule.xlsx,itemCustSelect,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemCustSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCustSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemCust</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCustSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCustSelect,11">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemCust.cust</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemCopyDoc" position="item_dataMappingRule.xlsx,itemCopyDoc">
    <DataMappingRule description="Mapping from item copy doc" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemCopyDoc" position="item_dataMappingRule.xlsx,itemCopyDoc,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>buyerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,15">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>refItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemCopyDoc,19">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.ItemCopyDocProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemCopyDoc2" position="item_dataMappingRule.xlsx,itemCopyDoc2">
    <DataMappingRule description="Mapping from item copy doc" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemCopyDoc2" position="item_dataMappingRule.xlsx,itemCopyDoc2,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>buyerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,15">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>refItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemCopyDoc2,19">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.ItemCopyDoc2Processor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemAgentSelect" position="item_dataMappingRule.xlsx,itemAgentSelect">
    <DataMappingRule description="Mapping from agent to item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemAgentSelect" position="item_dataMappingRule.xlsx,itemAgentSelect,1" srcEntityName="Codelist" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemAgentSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAgentSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemSourAgent</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAgentSelect,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemSourAgent.agent</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewOs" position="item_dataMappingRule.xlsx,itemNewOs">
    <DataMappingRule description="Mapping from Item to Offersheet" domain="PEPL" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewOs" position="item_dataMappingRule.xlsx,itemNewOs,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewOs,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,9">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,10">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,11">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,12">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,13">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,15">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,16">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,18">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>osItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,19">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,20">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>osItem.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,21">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>osItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,22">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>osItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,23">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>osItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,24">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specSize</srcFieldId>
          <dstFieldId>osItem.osItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,25">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,26">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,27">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,28">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,29">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,30">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specColor</srcFieldId>
          <dstFieldId>osItem.osItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,31">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,32">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,33">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,34">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,35">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewOs,39">
          <type>PreProcessor</type>
          <templateName>Offersheet Item select config</templateName>
          <templateFile>os_item_select_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemExtraChildEntityProcessor</implementationClass>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewOs,40">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewOffersheetDataModelProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewSourcingRecord" position="item_dataMappingRule.xlsx,itemNewSourcingRecord">
    <DataMappingRule description="Mapping from item to sourcing record" domain="PEPL" dstEntityName="SourcingRecord" dstEntityVersion="1" effectiveDate="2016-09-21" id="itemNewSourcingRecord" position="item_dataMappingRule.xlsx,itemNewSourcingRecord,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2016-09-21">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,12">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,13">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,15">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,16">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact</srcFieldId>
          <dstFieldId>sourcingRecordVendorFact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isNotAvailable</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vqVendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewSourcingRecord,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vqFactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewCustPo" position="item_dataMappingRule.xlsx,itemNewCustPo">
    <DataMappingRule description="Mapping from item to cust" domain="PEPL" dstEntityName="Cpo" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemNewCustPo" position="item_dataMappingRule.xlsx,itemNewCustPo,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>cpoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,11">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,12">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,14">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,16">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,17">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>cpoItem.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>cpoItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,21">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>cpoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>cpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,23">
          <mappingType>Field</mappingType>
          <srcFieldId>specId.version</srcFieldId>
          <dstFieldId>cpoItem.specVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,24">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>cpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,25">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specSize</srcFieldId>
          <dstFieldId>cpoItem.cpoItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>altLabel</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,32">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specColor</srcFieldId>
          <dstFieldId>cpoItem.cpoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,34">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,37">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,38">
          <mappingType>Field</mappingType>
          <srcFieldId>altColor</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,42">
          <type>PreProcessor</type>
          <templateName>CPO Item select config</templateName>
          <templateFile>cpo_item_select_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemExtraChildEntityProcessor</implementationClass>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPo,43">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewCpoSetupfieldVauleProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewCustPoCust" position="item_dataMappingRule.xlsx,itemNewCustPoCust">
    <DataMappingRule description="Mapping from item to CPO Customer" domain="PEPL" dstEntityName="Cpo" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemNewCustPoCust" position="item_dataMappingRule.xlsx,itemNewCustPoCust,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewCustPoCust,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPoCust,9">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDefault='1'</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPoCust,10">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPoCust,11">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustPoCust,12">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewRfq" position="item_dataMappingRule.xlsx,itemNewRfq">
    <DataMappingRule description="Mapping from item to rfq" domain="PEPL" dstEntityName="Rfq" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemNewRfq" position="item_dataMappingRule.xlsx,itemNewRfq,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2015-12-1">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewRfq,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,9">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,10">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,11">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact</srcFieldId>
          <dstFieldId>rfqVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,12">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact.vendor</srcFieldId>
          <dstFieldId>rfqVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfqItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,14">
          <mappingType>Field</mappingType>
          <srcFieldId>initialOrderQty</srcFieldId>
          <dstFieldId>plannedQuantity</dstFieldId>
          <dstFieldType>Number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>plannedQuantity</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.defaultSourcingRecord.initialOrderQty</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,16">
          <mappingType>Field</mappingType>
          <srcFieldId>custCheckbox3</srcFieldId>
          <dstFieldId>rfqItem.custCheckbox1</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfqItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,18">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>rfqItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,19">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>rfqItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,20">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord</srcFieldId>
          <dstFieldId>rfqItem.sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,21">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>rfqItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfq,22">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>rfqItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewRfq,26">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.ItemGenRfqProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewVpo" position="item_dataMappingRule.xlsx,itemNewVpo">
    <DataMappingRule description="Mapping from item to vpo" domain="PEPL" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemNewVpo" position="item_dataMappingRule.xlsx,itemNewVpo,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewVpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,11">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,12">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,13">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>responsibleParty1</srcFieldId>
          <dstFieldId>responsibleParty1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isFromDefaultEntity(entity)</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>responsibleParty2</srcFieldId>
          <dstFieldId>responsibleParty2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isFromDefaultEntity(entity)</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>responsibleParty3</srcFieldId>
          <dstFieldId>responsibleParty3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isFromDefaultEntity(entity)</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>responsibleParty4</srcFieldId>
          <dstFieldId>responsibleParty4</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isFromDefaultEntity(entity)</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,21">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,22">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>vpoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.vendorItemNo</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,24">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,25">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>vpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,26">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>vpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,27">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>vpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,28">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>vpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,29">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>vpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,30">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>vpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,31">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSize</srcFieldId>
          <dstFieldId>vpoItem.vpoItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemSize</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,34">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,37">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,38">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,39">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>vpoItem.vpoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,40">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,41">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,42">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,43">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,44">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,45">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpo,46">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewVpo,50">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewVpoSetupfieldVauleProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewVpoCust" position="item_dataMappingRule.xlsx,itemNewVpoCust">
    <DataMappingRule description="Mapping from item to vpo Customer" domain="PEPL" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemNewVpoCust" position="item_dataMappingRule.xlsx,itemNewVpoCust,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,9">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDefault='1'</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,10">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemCust.cust.paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,12">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,13">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,14">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust.paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,16">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,17">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemCust</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemCust</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,18">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDefault='1'</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVpoCust,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemVendorFact.vendor</srcFieldId>
          <dstFieldId>vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="pricingRecordsNewVq" position="item_dataMappingRule.xlsx,pricingRecordsNewVq">
    <DataMappingRule description="Mapping from item to vq" domain="PEPL" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-15" id="pricingRecordsNewVq" position="item_dataMappingRule.xlsx,pricingRecordsNewVq,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>confirmedToBuy</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>vendorItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,13">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,14">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,15">
          <mappingType>Section</mappingType>
          <srcFieldId>briefId</srcFieldId>
          <dstFieldId>briefId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,17">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,18">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>requestedSpecId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,19">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,20">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,pricingRecordsNewVq,24">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.PricingRecordsNewVqPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemCustDestCopy" position="item_dataMappingRule.xlsx,itemCustDestCopy">
    <DataMappingRule description="Mapping from item cust destination copy" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-09-19" id="itemCustDestCopy" position="item_dataMappingRule.xlsx,itemCustDestCopy,1" srcEntityName="ItemCustFinalDest" srcEntityVersion="1" status="1" updatedDate="2012-09-19">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemCustDestCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemCustDestCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemCustFinalDest</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custMarketFirInheritance" position="item_dataMappingRule.xlsx,custMarketFirInheritance">
    <DataMappingRule description="Mapping from cust market to item" domain="PEPL" dstEntityName="ItemCust" dstEntityVersion="1" effectiveDate="2012-03-15" id="custMarketFirInheritance" position="item_dataMappingRule.xlsx,custMarketFirInheritance,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,custMarketFirInheritance,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custMarketFirInheritance,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cust</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custMarketSecInheritance" position="item_dataMappingRule.xlsx,custMarketSecInheritance">
    <DataMappingRule description="Mapping from cust market to item" domain="PEPL" dstEntityName="ItemCust" dstEntityVersion="1" effectiveDate="2012-03-15" id="custMarketSecInheritance" position="item_dataMappingRule.xlsx,custMarketSecInheritance,1" srcEntityName="CustMarket" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,custMarketSecInheritance,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custMarketSecInheritance,9">
          <mappingType>Section</mappingType>
          <srcFieldId>market</srcFieldId>
          <dstFieldId>market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custMarketSecInheritance,10">
          <mappingType>Section</mappingType>
          <srcFieldId>channel</srcFieldId>
          <dstFieldId>channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custFinalDestFirInheritance" position="item_dataMappingRule.xlsx,custFinalDestFirInheritance">
    <DataMappingRule description="Mapping From Customer Final Destination to Item" domain="PEPL" dstEntityName="ItemCustFinalDest" dstEntityVersion="1" effectiveDate="2012-09-18" id="custFinalDestFirInheritance" position="item_dataMappingRule.xlsx,custFinalDestFirInheritance,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2012-09-18">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,custFinalDestFirInheritance,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestFirInheritance,9">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>cust</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestFirInheritance,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>businessName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custFinalDestSecInheritance" position="item_dataMappingRule.xlsx,custFinalDestSecInheritance">
    <DataMappingRule description="Mapping From Customer Final Destination to Item" domain="PEPL" dstEntityName="ItemCustFinalDest" dstEntityVersion="1" effectiveDate="2012-09-18" id="custFinalDestSecInheritance" position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,1" srcEntityName="CustDestination" srcEntityVersion="1" status="1" updatedDate="2012-09-18">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,9">
          <mappingType>Field</mappingType>
          <srcFieldId>calCostQuoted</srcFieldId>
          <dstFieldId>calCostOnQuoted</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,10">
          <mappingType>Field</mappingType>
          <srcFieldId>calCostConfirmed</srcFieldId>
          <dstFieldId>calCostOnConfirmedToBuy</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,11">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfDestination</srcFieldId>
          <dstFieldId>countryOfDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,12">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,13">
          <mappingType>Section</mappingType>
          <srcFieldId>finalDestination</srcFieldId>
          <dstFieldId>finalDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,custFinalDestSecInheritance,14">
          <mappingType>Section</mappingType>
          <srcFieldId>distributionMethod</srcFieldId>
          <dstFieldId>distributionMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewProject" position="item_dataMappingRule.xlsx,itemNewProject">
    <DataMappingRule description="Mapping from item to project" domain="PEPL" dstEntityName="Project" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewProject" position="item_dataMappingRule.xlsx,itemNewProject,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewProject,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,9">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,10">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,11">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,12">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,13">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,14">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSourAgent</srcFieldId>
          <dstFieldId>projectSourcingAgents</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,16">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,17">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSourAgent.agent</srcFieldId>
          <dstFieldId>projectSourcingAgents.agent</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,18">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust</srcFieldId>
          <dstFieldId>projectCustomers</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,20">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>notes</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,21">
          <mappingType>Field</mappingType>
          <srcFieldId>custItemNo</srcFieldId>
          <dstFieldId>customerItemNo</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,22">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,23">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.market</srcFieldId>
          <dstFieldId>projectCustomers.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,24">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.channel</srcFieldId>
          <dstFieldId>projectCustomers.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,25">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.brand</srcFieldId>
          <dstFieldId>projectCustomers.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,26">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust</srcFieldId>
          <dstFieldId>projectCustomers.customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,27">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.upcType</srcFieldId>
          <dstFieldId>projectCustomers.upcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,28">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.eanType</srcFieldId>
          <dstFieldId>projectCustomers.eanType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,29">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>projectItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,30">
          <mappingType>Field</mappingType>
          <srcFieldId>landedCost</srcFieldId>
          <dstFieldId>landedCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,31">
          <mappingType>Field</mappingType>
          <srcFieldId>retailPrice</srcFieldId>
          <dstFieldId>retailPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,32">
          <mappingType>Field</mappingType>
          <srcFieldId>margin</srcFieldId>
          <dstFieldId>margin</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,33">
          <mappingType>Field</mappingType>
          <srcFieldId>ffDueDate</srcFieldId>
          <dstFieldId>ffDueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,34">
          <mappingType>Field</mappingType>
          <srcFieldId>dcDueDate</srcFieldId>
          <dstFieldId>dcDueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,35">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>landedCost</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.defaultSourcingRecord.landedCost</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,36">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>retailPrice</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.defaultSourcingRecord.retailPrice</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,37">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>margin</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.defaultSourcingRecord.margin</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,38">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>ffDueDate</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.defaultSourcingRecord.ffDueDate</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,39">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>dcDueDate</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.defaultSourcingRecord.dcDueDate</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,40">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>projectItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,41">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>projectItems.currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,42">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord</srcFieldId>
          <dstFieldId>projectItems.sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,43">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.currency</srcFieldId>
          <dstFieldId>projectItems.currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewProject,44">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>projectItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewProject,48">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewProjectPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewCatalog" position="item_dataMappingRule.xlsx,itemNewCatalog">
    <DataMappingRule description="Mapping from item to catalog" domain="PEPL" dstEntityName="Catalog" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewCatalog" position="item_dataMappingRule.xlsx,itemNewCatalog,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,9">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,10">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,11">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,12">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSourAgent</srcFieldId>
          <dstFieldId>catalogSourcingAgents</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,13">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,14">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSourAgent.agent</srcFieldId>
          <dstFieldId>catalogSourcingAgents.agent</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust</srcFieldId>
          <dstFieldId>catalogCustomers</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,16">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,17">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>notes</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,18">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.market</srcFieldId>
          <dstFieldId>catalogCustomers.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.channel</srcFieldId>
          <dstFieldId>catalogCustomers.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,20">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCust.cust</srcFieldId>
          <dstFieldId>catalogCustomers.customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,21">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>catalogItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,22">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>catalogItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,23">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>catalogItems.spec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,24">
          <mappingType>Section</mappingType>
          <srcFieldId>briefId</srcFieldId>
          <dstFieldId>catalogItems.brief</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,25">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>catalogItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewCatalog,29">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewCatalogPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewMpo" position="item_dataMappingRule.xlsx,itemNewMpo">
    <DataMappingRule description="Mapping from item to mpo" domain="PEPL" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewMpo" position="item_dataMappingRule.xlsx,itemNewMpo,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewMpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,9">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,11">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>mpoItems.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>mpoItems.isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>mpoItems.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>mpoItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>mpoItems.spec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>mpoItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specSize</srcFieldId>
          <dstFieldId>mpoItems.mpoItemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,20">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,21">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,22">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId>altLabel</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,26">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specColor</srcFieldId>
          <dstFieldId>mpoItems.mpoItemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>altColor</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewMpo,36">
          <type>PreProcessor</type>
          <templateName>MPO Item select config 2</templateName>
          <templateFile>mpo_item_select_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemExtraChildEntityProcessor</implementationClass>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewMpo,37">
          <type>PostProcessor</type>
          <templateName>itemNewMpoTemplate</templateName>
          <templateFile>set_up_mpo_item_color_size_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewMpoProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewCustInv" position="item_dataMappingRule.xlsx,itemNewCustInv">
    <DataMappingRule description="Mapping from item to customer invoice" domain="PEPL" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewCustInv" position="item_dataMappingRule.xlsx,itemNewCustInv,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,9">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,10">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,11">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>custInvItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>custInvItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,14">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>custInvItem.isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,15">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>custInvItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,16">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>custInvItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>custInvItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewCustInv,21">
          <type>PostProcessor</type>
          <templateName>itemNewCustomerInvoiceTemplate</templateName>
          <templateFile>set_up_customerInvoice_item_color_size_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewCustInvPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewVendorInvoice" position="item_dataMappingRule.xlsx,itemNewVendorInvoice">
    <DataMappingRule description="Mapping from item to mpo" domain="PEPL" dstEntityName="VendorInvoice" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewVendorInvoice" position="item_dataMappingRule.xlsx,itemNewVendorInvoice,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,9">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,10">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,11">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,14">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,15">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,16">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,18">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,19">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>image</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,20">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specSize</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.viShipmentItemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,21">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,22">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,23">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,24">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,25">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,26">
          <mappingType>Section</mappingType>
          <srcFieldId>specId.specColor</srcFieldId>
          <dstFieldId>vendorInvoiceShipItems.viShipmentItemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,27">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>specColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,28">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,29">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,30">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,31">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,35">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemExtraChildEntityProcessor</implementationClass>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewVendorInvoice,36">
          <type>PostProcessor</type>
          <templateName>itemNewVendorInvoiceTemplate</templateName>
          <templateFile>set_up_vendorInvoice_item_color_size_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewVendorInvoiceProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectColor" position="item_dataMappingRule.xlsx,itemSelectColor">
    <DataMappingRule description="Mapping from color to item Color" domain="PEPL" dstEntityName="ItemColor" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectColor" position="item_dataMappingRule.xlsx,itemSelectColor,1" srcEntityName="Color" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectColor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColor,9">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColor,10">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColor,11">
          <mappingType>Field</mappingType>
          <srcFieldId>altName</srcFieldId>
          <dstFieldId>altName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColor,12">
          <mappingType>Field</mappingType>
          <srcFieldId>altColorName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColor,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnlyFlag</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColor,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>color</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemAddToColorLibrary" position="item_dataMappingRule.xlsx,itemAddToColorLibrary">
    <DataMappingRule description="Mapping from color to item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemAddToColorLibrary" position="item_dataMappingRule.xlsx,itemAddToColorLibrary,1" srcEntityName="Color" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,10">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>itemColor.colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,11">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>itemColor.colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,12">
          <mappingType>Field</mappingType>
          <srcFieldId>altName</srcFieldId>
          <dstFieldId>itemColor.altName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,13">
          <mappingType>Field</mappingType>
          <srcFieldId>altColorName</srcFieldId>
          <dstFieldId>itemColor.shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,14">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor.colorPalette</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemColor.isReadOnlyFlag</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemColor.color</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemAddToColorLibrary,20">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemAddToColorLibraryPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSizeCopy" position="item_dataMappingRule.xlsx,itemSizeCopy">
    <DataMappingRule description="Copy the item size in item module" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSizeCopy" position="item_dataMappingRule.xlsx,itemSizeCopy,1" srcEntityName="ItemSize" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>itemSize.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>itemSize.sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>itemSize.sizeDisplayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>isInactive</srcFieldId>
          <dstFieldId>itemSize.isInactive</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSizeCopy,14">
          <mappingType>Section</mappingType>
          <srcFieldId>dimension</srcFieldId>
          <dstFieldId>itemSize.dimension</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectColorSizes" position="item_dataMappingRule.xlsx,itemSelectColorSizes">
    <DataMappingRule description="Mapping from size template to item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectColorSizes" position="item_dataMappingRule.xlsx,itemSelectColorSizes,1" srcEntityName="SizeTemplateSize" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemSize.id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,11">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>itemSize.sizeSeq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,12">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>itemSize.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,13">
          <mappingType>Field</mappingType>
          <srcFieldId>label</srcFieldId>
          <dstFieldId>itemSize.sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,14">
          <mappingType>Field</mappingType>
          <srcFieldId>altLabel</srcFieldId>
          <dstFieldId>itemSize.sizeDisplayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemSelectColorSizes,18">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.AddSizeTemplateDimentionToItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectVendor" position="item_dataMappingRule.xlsx,itemSelectVendor">
    <DataMappingRule description="Mapping from vendor to item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectVendor" position="item_dataMappingRule.xlsx,itemSelectVendor,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectVendor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectVendor,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemVendorFact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectVendor,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemVendorFact.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemSelectVendor,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.pepl.inheritance.customizedprocessor.ItemSelectVendorProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectFactory" position="item_dataMappingRule.xlsx,itemSelectFactory">
    <DataMappingRule description="Mapping from factory to item" domain="PEPL" dstEntityName="ItemFactory" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectFactory" position="item_dataMappingRule.xlsx,itemSelectFactory,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectFactory,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFactory,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>fact</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectRelatedItem" position="item_dataMappingRule.xlsx,itemSelectRelatedItem">
    <DataMappingRule description="Mapping from item to item" domain="PEPL" dstEntityName="ItemRelated" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectRelatedItem" position="item_dataMappingRule.xlsx,itemSelectRelatedItem,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectRelatedItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectRelatedItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>relatedItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectRelatedItem,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>relatedItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectRelatedItem,11">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectColorColorPalette" position="item_dataMappingRule.xlsx,itemSelectColorColorPalette">
    <DataMappingRule description="Mapping from color color palette to item Color" domain="PEPL" dstEntityName="ItemColor" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectColorColorPalette" position="item_dataMappingRule.xlsx,itemSelectColorColorPalette,1" srcEntityName="ColorPalette" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectColorColorPalette,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectColorColorPalette,9">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>colorPalette</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectPatterns" position="item_dataMappingRule.xlsx,itemSelectPatterns">
    <DataMappingRule description="Mapping from Pattern to item Color" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectPatterns" position="item_dataMappingRule.xlsx,itemSelectPatterns,1" srcEntityName="Pattern" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,10">
          <mappingType>Field</mappingType>
          <srcFieldId>patternCode</srcFieldId>
          <dstFieldId>itemColor.colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,11">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>itemColor.colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,12">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>itemColor.shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,13">
          <mappingType>Field</mappingType>
          <srcFieldId>altName</srcFieldId>
          <dstFieldId>itemColor.altName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemColor.isReadOnlyFlag</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,15">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>itemColor.colorImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPatterns,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemColor.colorPattern</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewCorrectiveActionPlans" position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans">
    <DataMappingRule description="Mapping from Item to CorrectiveActionPlans" domain="PEPL" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewCorrectiveActionPlans" position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans,10">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans,11">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewCorrectiveActionPlans,15">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewCapProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewRequestForSpecifications" position="item_dataMappingRule.xlsx,itemNewRequestForSpecifications">
    <DataMappingRule description="Mapping from Item to Request For Specifications" domain="PEPL" dstEntityName="Rfs" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewRequestForSpecifications" position="item_dataMappingRule.xlsx,itemNewRequestForSpecifications,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewRequestForSpecifications,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRequestForSpecifications,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfsItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRequestForSpecifications,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfsItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRequestForSpecifications,11">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectSizeTemplate" position="item_dataMappingRule.xlsx,itemSelectSizeTemplate">
    <DataMappingRule description="Mapping from size template to item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectSizeTemplate" position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,1" srcEntityName="SizeTemplate" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,9">
          <mappingType>Section</mappingType>
          <srcFieldId>sizeTemplateSizes</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isAvailable='0'</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemSize.id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,11">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>itemSize.sizeSeq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,12">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>itemSize.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,13">
          <mappingType>Field</mappingType>
          <srcFieldId>label</srcFieldId>
          <dstFieldId>itemSize.sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,14">
          <mappingType>Field</mappingType>
          <srcFieldId>altLabel</srcFieldId>
          <dstFieldId>itemSize.sizeDisplayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>dimension</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>$Root.dimension</mappedValue>
          <mappedValueType>SourceParent</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custText2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>$Root.name</mappedValue>
          <mappedValueType>SourceParent</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemSelectSizeTemplate,20">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemSelectSizeTemplatePostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectMeasurementTemplate" position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate">
    <DataMappingRule description="Mapping measurementTemplatePoints and measurementTemplateSizes from MeasurementTemplate to Spec" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectMeasurementTemplate" position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,1" srcEntityName="MeasurementTemplate" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,9">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePoints</srcFieldId>
          <dstFieldId>specGradingRule</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,10">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>measurementTemplatePointId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,12">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,13">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,14">
          <mappingType>Field</mappingType>
          <srcFieldId>remark</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,15">
          <mappingType>Field</mappingType>
          <srcFieldId>requireInspection</srcFieldId>
          <dstFieldId>requireInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,16">
          <mappingType>Field</mappingType>
          <srcFieldId>pointOfMeasure</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,17">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleMeasurement</srcFieldId>
          <dstFieldId>sampleMeasurement</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,18">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleMeasurement</srcFieldId>
          <dstFieldId>revisedMeasurement</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,19">
          <mappingType>Field</mappingType>
          <srcFieldId>tolerancePositive</srcFieldId>
          <dstFieldId>tolerancePositive</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,20">
          <mappingType>Field</mappingType>
          <srcFieldId>toleranceNegative</srcFieldId>
          <dstFieldId>toleranceNegative</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,21">
          <mappingType>Section</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>specGradingRule.position</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,22">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>specGradingRule.imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,23">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePointSizes</srcFieldId>
          <dstFieldId>specGradingSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,24">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeValue</srcFieldId>
          <dstFieldId>measurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,25">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeUniqueKey</srcFieldId>
          <dstFieldId>sizeUniqueKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,26">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplateSizeId</srcFieldId>
          <dstFieldId>specGradingSize.itemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,27">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePointId</srcFieldId>
          <dstFieldId>specGradingSize.specGradingRule</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplate,31">
          <type>PostProcessor</type>
          <templateName>specSelectMeasurementTemplate</templateName>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ResetSpecGradingRulesProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectMeasurementTemplateSe" position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe">
    <DataMappingRule description="Mapping from MeasurementTemplate to Spec" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectMeasurementTemplateSelection" position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,1" srcEntityName="MeasurementTemplate" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,9">
          <mappingType>Field</mappingType>
          <srcFieldId>notesOrInstructions</srcFieldId>
          <dstFieldId>pomNotesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>templateDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,11">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>templateName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>measureTempl</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,13">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementUnit</srcFieldId>
          <dstFieldId>measurementUnit</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,14">
          <mappingType>Section</mappingType>
          <srcFieldId>numberFormat</srcFieldId>
          <dstFieldId>numberFormat</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,15">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementType</srcFieldId>
          <dstFieldId>measurementType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,16">
          <mappingType>Section</mappingType>
          <srcFieldId>type</srcFieldId>
          <dstFieldId>templateType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,17">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePoints</srcFieldId>
          <dstFieldId>specGradingRule</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,18">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>measurementTemplatePointId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,19">
          <mappingType>Field</mappingType>
          <srcFieldId>pointOfMeasure</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,20">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,21">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,22">
          <mappingType>Field</mappingType>
          <srcFieldId>remark</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,23">
          <mappingType>Field</mappingType>
          <srcFieldId>requireInspection</srcFieldId>
          <dstFieldId>requireInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,24">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleMeasurement</srcFieldId>
          <dstFieldId>sampleMeasurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,25">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleMeasurement</srcFieldId>
          <dstFieldId>revisedMeasurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,26">
          <mappingType>Field</mappingType>
          <srcFieldId>tolerancePositive</srcFieldId>
          <dstFieldId>tolerancePositive</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,27">
          <mappingType>Field</mappingType>
          <srcFieldId>toleranceNegative</srcFieldId>
          <dstFieldId>toleranceNegative</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,28">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,29">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePoints.image</srcFieldId>
          <dstFieldId>specGradingRule.imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,30">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePoints.position</srcFieldId>
          <dstFieldId>specGradingRule.position</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,31">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePointSizes</srcFieldId>
          <dstFieldId>specGradingSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,32">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeValue</srcFieldId>
          <dstFieldId>measurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,33">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeUniqueKey</srcFieldId>
          <dstFieldId>sizeUniqueKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,34">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplateSizeId</srcFieldId>
          <dstFieldId>specGradingSize.itemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,35">
          <mappingType>Section</mappingType>
          <srcFieldId>measurementTemplatePointId</srcFieldId>
          <dstFieldId>specGradingSize.specGradingRule</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemSelectMeasurementTemplateSe,39">
          <type>PostProcessor</type>
          <templateName>specSelectMeasurementTemplate</templateName>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ResetSpecGradingRulesProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="specGradingRuleToMeasurement" position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement">
    <DataMappingRule description="Mapping for Grading Rule To Measurement" domain="PEPL" dstEntityName="SpecMeasurement" dstEntityVersion="1" effectiveDate="2012-02-20" id="specGradingRuleToMeasurement" position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,1" srcEntityName="SpecGradingRule" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,9">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,11">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,12">
          <mappingType>Field</mappingType>
          <srcFieldId>requireInspection</srcFieldId>
          <dstFieldId>requireInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,13">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleMeasurement</srcFieldId>
          <dstFieldId>sampleMeasurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,14">
          <mappingType>Field</mappingType>
          <srcFieldId>revisedMeasurement</srcFieldId>
          <dstFieldId>revisedMeasurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,15">
          <mappingType>Field</mappingType>
          <srcFieldId>tolerancePositive</srcFieldId>
          <dstFieldId>tolerancePositive</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,16">
          <mappingType>Field</mappingType>
          <srcFieldId>toleranceNegative</srcFieldId>
          <dstFieldId>toleranceNegative</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,17">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,18">
          <mappingType>Field</mappingType>
          <srcFieldId>syncIdentifier</srcFieldId>
          <dstFieldId>syncIdentifier</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,19">
          <mappingType>Field</mappingType>
          <srcFieldId>refKey</srcFieldId>
          <dstFieldId>refKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,20">
          <mappingType>Section</mappingType>
          <srcFieldId>imageId</srcFieldId>
          <dstFieldId>imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRuleToMeasurement,21">
          <mappingType>Section</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specGradingSizeToMeasSize" position="item_dataMappingRule.xlsx,specGradingSizeToMeasSize">
    <DataMappingRule description="Mapping for carry from SpecGradingSize to SpecMeasurementSize" domain="PEPL" dstEntityName="SpecMeasurementSize" dstEntityVersion="1" effectiveDate="2012-02-20" id="specGradingSizeToMeasSize" position="item_dataMappingRule.xlsx,specGradingSizeToMeasSize,1" srcEntityName="SpecGradingSize" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specGradingSizeToMeasSize,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingSizeToMeasSize,9">
          <mappingType>Field</mappingType>
          <srcFieldId>measurement</srcFieldId>
          <dstFieldId>measurement</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specAccessoriesSelectMeasuremen" position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen">
    <DataMappingRule description="Mapping from Measurement Template Points to Spec Accessories Measurements" domain="PEPL" dstEntityName="SpecAccessoriesMeasurement" dstEntityVersion="1" effectiveDate="2012-03-14" id="specAccessoriesSelectMeasurementTmpl" position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,1" srcEntityName="MeasurementTemplatePoint" srcEntityVersion="1" status="1" updatedDate="2012-03-14">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,10">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,11">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,12">
          <mappingType>Field</mappingType>
          <srcFieldId>remark</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,13">
          <mappingType>Field</mappingType>
          <srcFieldId>pointOfMeasure</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,14">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleMeasurement</srcFieldId>
          <dstFieldId>sampleMeasurement</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,15">
          <mappingType>Field</mappingType>
          <srcFieldId>tolerancePositive</srcFieldId>
          <dstFieldId>tolerancePositive</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,16">
          <mappingType>Field</mappingType>
          <srcFieldId>toleranceNegative</srcFieldId>
          <dstFieldId>toleranceNegative</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specAccessoriesSelectMeasuremen,17">
          <mappingType>Section</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specGradingRulesCopy" position="item_dataMappingRule.xlsx,specGradingRulesCopy">
    <DataMappingRule description="Mapping for Copy Spec Grading Rules" domain="PEPL" dstEntityName="SpecGradingRule" dstEntityVersion="1" effectiveDate="2012-02-20" id="specGradingRulesCopy" position="item_dataMappingRule.xlsx,specGradingRulesCopy,1" srcEntityName="SpecGradingRule" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specGradingRulesCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRulesCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>com.core.pepl.defaulting.generator.SeqNoGenerator("specGradingRule","seq")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specGradingRulesCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>syncIdentifier</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.SpecGradingRuleSyncIdentifierGenerator</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specGradingSizeCopy" position="item_dataMappingRule.xlsx,specGradingSizeCopy">
    <DataMappingRule description="Mapping for Copy Spec Grading Size" domain="PEPL" dstEntityName="SpecGradingSize" dstEntityVersion="1" effectiveDate="2012-02-20" id="specGradingSizeCopy" position="item_dataMappingRule.xlsx,specGradingSizeCopy,1" srcEntityName="SpecGradingSize" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specGradingSizeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specPackageCopy" position="item_dataMappingRule.xlsx,specPackageCopy">
    <DataMappingRule description="Mapping for Copy Spec Package" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-20" id="specPackageCopy" position="item_dataMappingRule.xlsx,specPackageCopy,1" srcEntityName="SpecPack" srcEntityVersion="1" status="1" updatedDate="2013-12-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specPackageCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specPackageCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specPack</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specPackageCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specPackageArtworkCopy" position="item_dataMappingRule.xlsx,specPackageArtworkCopy">
    <DataMappingRule description="Mapping for Copy Spec Package" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-20" id="specPackageArtworkCopy" position="item_dataMappingRule.xlsx,specPackageArtworkCopy,1" srcEntityName="SpecPackArtwork" srcEntityVersion="1" status="1" updatedDate="2013-12-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specPackageArtworkCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specPackageArtworkCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specPackArtwork</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specPackageArtworkCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectReqTemplateItem" position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem">
    <DataMappingRule description="Mapping from ReqTemplItem to SpecRequirement" domain="PEPL" dstEntityName="SpecRequirement" dstEntityVersion="1" effectiveDate="2016-09-14" id="itemSelectReqTemplateItem" position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,1" srcEntityName="ReqTemplItem" srcEntityVersion="1" status="1" updatedDate="2016-09-14">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>dataString</srcFieldId>
          <dstFieldId>dataString</dstFieldId>
          <dstFieldType>string-m</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDecimal</srcFieldId>
          <dstFieldId>dataDecimal</dstFieldId>
          <dstFieldType>decimal-rate</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDate</srcFieldId>
          <dstFieldId>dataDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>mandatory</srcFieldId>
          <dstFieldId>mandatory</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>require</srcFieldId>
          <dstFieldId>require</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>notesOrInstructions</srcFieldId>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string-l</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>dataEntity</srcFieldId>
          <dstFieldId>dataEntity</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>unitOfRequirement</srcFieldId>
          <dstFieldId>unitOfRequirement</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,17">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectReqTemplateItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment</srcFieldId>
          <dstFieldId>attachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectPackagingTask1" position="item_dataMappingRule.xlsx,itemSelectPackagingTask1">
    <DataMappingRule description="Mapping from Packaging Detail to Item's Spec Packaging" domain="PEPL" dstEntityName="SpecPack" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectPackagingTask1" position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,1" srcEntityName="PackagingDetail" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>packagingDescription</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId>notesInstructions</srcFieldId>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,12">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask1,13">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectPackagingTask2" position="item_dataMappingRule.xlsx,itemSelectPackagingTask2">
    <DataMappingRule description="Mapping from Packaging to Item's Spec Packaging" domain="PEPL" dstEntityName="SpecPack" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectPackagingTask2" position="item_dataMappingRule.xlsx,itemSelectPackagingTask2,1" srcEntityName="Packaging" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask2,10">
          <mappingType>Section</mappingType>
          <srcFieldId>packagingType</srcFieldId>
          <dstFieldId>type</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingTask2,11">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>packaging</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectPackagingArtworkTask1" position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1">
    <DataMappingRule description="Mapping from Packaging Artwork Detail  to Spec Packaging Artwork" domain="PEPL" dstEntityName="SpecPackArtwork" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectPackagingArtworkTask1" position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,1" srcEntityName="PackagingArtworkDetail" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,12">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask1,13">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectPackagingArtworkTask2" position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask2">
    <DataMappingRule description="Mapping from Packaging Artwork to Spec Packaging Artwork" domain="PEPL" dstEntityName="SpecPackArtwork" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemSelectPackagingArtworkTask2" position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask2,1" srcEntityName="PackagingArtwork" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask2,10">
          <mappingType>Section</mappingType>
          <srcFieldId>packagingArtworkType</srcFieldId>
          <dstFieldId>type</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectPackagingArtworkTask2,11">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>packagingArtwork</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specSelectComponent" position="item_dataMappingRule.xlsx,specSelectComponent">
    <DataMappingRule description="Mapping from Componet to SpecMaterial" domain="PEPL" dstEntityName="SpecMaterial" dstEntityVersion="1" effectiveDate="2012-02-20" id="specSelectComponent" position="item_dataMappingRule.xlsx,specSelectComponent,1" srcEntityName="Component" srcEntityVersion="1" status="1" updatedDate="2014-01-03">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specSelectComponent,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,9">
          <mappingType>Field</mappingType>
          <srcFieldId>materialName</srcFieldId>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>materialDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,11">
          <mappingType>Field</mappingType>
          <srcFieldId>componentNo</srcFieldId>
          <dstFieldId>materialNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,12">
          <mappingType>Field</mappingType>
          <srcFieldId>composition</srcFieldId>
          <dstFieldId>composition</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,13">
          <mappingType>Field</mappingType>
          <srcFieldId>consumption</srcFieldId>
          <dstFieldId>consumption</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,14">
          <mappingType>Field</mappingType>
          <srcFieldId>wastagePercentage</srcFieldId>
          <dstFieldId>wastage</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,15">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,16">
          <mappingType>Field</mappingType>
          <srcFieldId>notes</srcFieldId>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,18">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment</srcFieldId>
          <dstFieldId>materialImageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,19">
          <mappingType>Section</mappingType>
          <srcFieldId>materialType</srcFieldId>
          <dstFieldId>materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,20">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>materialSupplier</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,21">
          <mappingType>Section</mappingType>
          <srcFieldId>millVendor</srcFieldId>
          <dstFieldId>mill</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,22">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,23">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectComponent,24">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>component</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specSelectMaterialPalette" position="item_dataMappingRule.xlsx,specSelectMaterialPalette">
    <DataMappingRule description="Mapping from MaterialPalette to SpecMaterial" domain="PEPL" dstEntityName="SpecMaterial" dstEntityVersion="1" effectiveDate="2014-11-05" id="specSelectMaterialPalette" position="item_dataMappingRule.xlsx,specSelectMaterialPalette,1" srcEntityName="MaterialPaletteMaterial" srcEntityVersion="1" status="1" updatedDate="2014-11-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,9">
          <mappingType>Field</mappingType>
          <srcFieldId>materialName</srcFieldId>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>materialDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,11">
          <mappingType>Field</mappingType>
          <srcFieldId>materialNo</srcFieldId>
          <dstFieldId>materialNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,12">
          <mappingType>Field</mappingType>
          <srcFieldId>composition</srcFieldId>
          <dstFieldId>composition</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>consumption</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.componentId.consumption</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>wastage</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.componentId.wastagePercentage</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.componentId.unitCost</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.componentId.notes</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialSupplier</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.componentId.vendor</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>mill</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.componentId.millVendor</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.componentId.uom</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,21">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.componentId.currency</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,22">
          <mappingType>Section</mappingType>
          <srcFieldId>materialImageId</srcFieldId>
          <dstFieldId>materialImageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,23">
          <mappingType>Section</mappingType>
          <srcFieldId>materialType</srcFieldId>
          <dstFieldId>materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specSelectMaterialPalette,24">
          <mappingType>Section</mappingType>
          <srcFieldId>componentId</srcFieldId>
          <dstFieldId>component</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specMaterialCopy" position="item_dataMappingRule.xlsx,specMaterialCopy">
    <DataMappingRule description="Mapping for Copy Spec material" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-16" id="specMaterialCopy" position="item_dataMappingRule.xlsx,specMaterialCopy,1" srcEntityName="SpecMaterial" srcEntityVersion="1" status="1" updatedDate="2013-12-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specMaterialCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specMaterial</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>com.core.pepl.defaulting.generator.SeqNoGenerator("specMaterial","seq")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specColorBomCopy" position="item_dataMappingRule.xlsx,specColorBomCopy">
    <DataMappingRule description="Mapping for Copy Spec Color BOM" domain="PEPL" dstEntityName="SpecColorBom" dstEntityVersion="1" effectiveDate="2016-09-21" id="specColorBomCopy" position="item_dataMappingRule.xlsx,specColorBomCopy,1" srcEntityName="SpecColorBom" srcEntityVersion="1" status="1" updatedDate="2016-09-21">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specColorBomCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specColorBomCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>com.core.pepl.defaulting.generator.SeqNoGenerator("specColorBom","seq")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specColorBomItemColorCopy" position="item_dataMappingRule.xlsx,specColorBomItemColorCopy">
    <DataMappingRule description="Mapping for Copy Spec Color BOM Item Color" domain="PEPL" dstEntityName="SpecColorBomItemColor" dstEntityVersion="1" effectiveDate="2016-09-21" id="specColorBomItemColorCopy" position="item_dataMappingRule.xlsx,specColorBomItemColorCopy,1" srcEntityName="SpecColorBomItemColor" srcEntityVersion="1" status="1" updatedDate="2016-09-21">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specColorBomItemColorCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specColorBomItemColorCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specColorBomItemColorCopy,10">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specColorBomItemColorCopy,11">
          <mappingType>Section</mappingType>
          <srcFieldId>materialColor</srcFieldId>
          <dstFieldId>materialColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemLabelSelect" position="item_dataMappingRule.xlsx,itemLabelSelect">
    <DataMappingRule description="Mapping from LabelProfile to Item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemLabelSelect" position="item_dataMappingRule.xlsx,itemLabelSelect,1" srcEntityName="LabelProfile" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specInstruction</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>remark</srcFieldId>
          <dstFieldId>name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,14">
          <mappingType>Section</mappingType>
          <srcFieldId>type</srcFieldId>
          <dstFieldId>specInstruction.type</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,15">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>specInstruction.imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemLabelSelect,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specInstruction.labelProfile</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specInstructionCopy" position="item_dataMappingRule.xlsx,specInstructionCopy">
    <DataMappingRule description="Mapping for Copy Spec Instruction" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-16" id="specInstructionCopy" position="item_dataMappingRule.xlsx,specInstructionCopy,1" srcEntityName="SpecInstruction" srcEntityVersion="1" status="1" updatedDate="2013-12-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specInstructionCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specInstructionCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specInstruction</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specInstructionCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specConstructionCopy" position="item_dataMappingRule.xlsx,specConstructionCopy">
    <DataMappingRule description="Mapping for Copy Spec Construction" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-16" id="specConstructionCopy" position="item_dataMappingRule.xlsx,specConstructionCopy,1" srcEntityName="SpecConstruction" srcEntityVersion="1" status="1" updatedDate="2013-12-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specConstructionCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specConstructionCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specConstruction</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectConstruction" position="item_dataMappingRule.xlsx,itemSelectConstruction">
    <DataMappingRule description="Mapping from Codelist to Item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-29" id="itemSelectConstruction" position="item_dataMappingRule.xlsx,itemSelectConstruction,1" srcEntityName="Codelist" srcEntityVersion="1" status="1" updatedDate="2013-01-03">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectConstruction,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectConstruction,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specConstruction</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectConstruction,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specConstruction.constructionElement</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specDesignCopy" position="item_dataMappingRule.xlsx,specDesignCopy">
    <DataMappingRule description="Mapping for Copy Spec Design" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-16" id="specDesignCopy" position="item_dataMappingRule.xlsx,specDesignCopy,1" srcEntityName="SpecDesign" srcEntityVersion="1" status="1" updatedDate="2013-12-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specDesignCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specDesignCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specDesign</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specDesignCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>com.core.pepl.defaulting.generator.SeqNoGenerator("specDesign","seq")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectArtwork" position="item_dataMappingRule.xlsx,itemSelectArtwork">
    <DataMappingRule description="Mapping from Artwork to Item" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2013-12-20" id="itemSelectArtwork" position="item_dataMappingRule.xlsx,itemSelectArtwork,1" srcEntityName="Artwork" srcEntityVersion="1" status="1" updatedDate="2013-12-20">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specArtworkBom</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isArtworkTypeReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,12">
          <mappingType>Field</mappingType>
          <srcFieldId>artworkName</srcFieldId>
          <dstFieldId>artworkName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,14">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>artworkCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,15">
          <mappingType>Section</mappingType>
          <srcFieldId>artworkType</srcFieldId>
          <dstFieldId>specArtworkBom.artworkType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,16">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>specArtworkBom.imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectArtwork,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specArtworkBom.artwork</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="specArtworkBomCopy" position="item_dataMappingRule.xlsx,specArtworkBomCopy">
    <DataMappingRule description="Mapping for Copy Spec Artwork" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2014-10-28" id="specArtworkBomCopy" position="item_dataMappingRule.xlsx,specArtworkBomCopy,1" srcEntityName="SpecArtworkBom" srcEntityVersion="1" status="1" updatedDate="2014-10-28">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specArtworkBomCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specArtworkBomCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specArtworkBom</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specArtworkBomCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectArtworkPalette" position="item_dataMappingRule.xlsx,itemSelectArtworkPalette">
    <DataMappingRule description="Mapping from Artwork Palette to Spec Artwork Bom" domain="PEPL" dstEntityName="SpecArtworkBom" dstEntityVersion="1" effectiveDate="2015-03-16" id="itemSelectArtworkPalette" position="item_dataMappingRule.xlsx,itemSelectArtworkPalette,1" srcEntityName="ArtworkPalette" srcEntityVersion="1" status="1" updatedDate="2015-03-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectArtworkPalette,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemSelectFromArtwork" position="item_dataMappingRule.xlsx,itemSelectFromArtwork">
    <DataMappingRule description="Mapping from Artwork Palette Combo to Spec Artwork Bom" domain="PEPL" dstEntityName="SpecArtworkBom" dstEntityVersion="1" effectiveDate="2015-03-16" id="itemSelectFromArtwork" position="item_dataMappingRule.xlsx,itemSelectFromArtwork,1" srcEntityName="Artwork" srcEntityVersion="1" status="1" updatedDate="2015-03-16">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isArtworkTypeReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,12">
          <mappingType>Field</mappingType>
          <srcFieldId>artworkName</srcFieldId>
          <dstFieldId>artworkName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,13">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>artworkCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,14">
          <mappingType>Section</mappingType>
          <srcFieldId>artworkType</srcFieldId>
          <dstFieldId>artworkType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,15">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemSelectFromArtwork,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>artwork</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewRfiHeader" position="item_dataMappingRule.xlsx,itemNewRfiHeader">
    <DataMappingRule description="Mapping from Item to RFI Header" domain="PEPL" dstEntityName="Rfi" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewRfiHeader" position="item_dataMappingRule.xlsx,itemNewRfiHeader,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewRfiHeader,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfiHeader,9">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfiHeader,10">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfiHeader,11">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewRfi" position="item_dataMappingRule.xlsx,itemNewRfi">
    <DataMappingRule description="Mapping from Item to RFI" domain="PEPL" dstEntityName="Rfi" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewRfi" position="item_dataMappingRule.xlsx,itemNewRfi,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewRfi,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfi,9">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfi,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfiItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfi,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>rfiItems.isReadOnly</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfi,12">
          <mappingType>Field</mappingType>
          <srcFieldId>styleNo</srcFieldId>
          <dstFieldId>rfiItems.designNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewRfi,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfiItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewRfi,17">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.RfiSelectItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewQuotation" position="item_dataMappingRule.xlsx,itemNewQuotation">
    <DataMappingRule description="Mapping from Item to VQ" domain="PEPL" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-02-20" id="itemNewQuotation" position="item_dataMappingRule.xlsx,itemNewQuotation,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,13">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,14">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,15">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,16">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,17">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord</srcFieldId>
          <dstFieldId>sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,18">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,19">
          <mappingType>Section</mappingType>
          <srcFieldId>custCodelist3</srcFieldId>
          <dstFieldId>custCodelist4</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,20">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,21">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,22">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial</srcFieldId>
          <dstFieldId>vqComponentCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,23">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vqComponentCosts.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>$Root</mappedValue>
          <mappedValueType>SourceParent</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,24">
          <mappingType>Field</mappingType>
          <srcFieldId>specMaterial.unitCost</srcFieldId>
          <dstFieldId>vqComponentCosts.unitCost</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,25">
          <mappingType>Field</mappingType>
          <srcFieldId>specMaterial.wastage</srcFieldId>
          <dstFieldId>vqComponentCosts.wastage</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,26">
          <mappingType>Field</mappingType>
          <srcFieldId>specMaterial.consumption</srcFieldId>
          <dstFieldId>vqComponentCosts.consumption</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,27">
          <mappingType>Field</mappingType>
          <srcFieldId>specMaterial.materialName</srcFieldId>
          <dstFieldId>vqComponentCosts.materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,28">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial.materialType</srcFieldId>
          <dstFieldId>vqComponentCosts.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,29">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial.currency</srcFieldId>
          <dstFieldId>vqComponentCosts.currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,30">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial.uom</srcFieldId>
          <dstFieldId>vqComponentCosts.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,31">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial.uom</srcFieldId>
          <dstFieldId>vqComponentCosts.consumptionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,itemNewQuotation,35">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemToQuotationProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="specMaterialToQuotation" position="item_dataMappingRule.xlsx,specMaterialToQuotation">
    <DataMappingRule description="Mapping from Item‘s specMaterial to Vq" domain="PEPL" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-15" id="specMaterialToQuotation" position="item_dataMappingRule.xlsx,specMaterialToQuotation,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2014-08-23">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,9">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial</srcFieldId>
          <dstFieldId>vqComponentCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,10">
          <mappingType>Field</mappingType>
          <srcFieldId>wastage</srcFieldId>
          <dstFieldId>wastage</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,11">
          <mappingType>Field</mappingType>
          <srcFieldId>materialName</srcFieldId>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,12">
          <mappingType>Field</mappingType>
          <srcFieldId>composition</srcFieldId>
          <dstFieldId>composition</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,13">
          <mappingType>Field</mappingType>
          <srcFieldId>consumption</srcFieldId>
          <dstFieldId>consumption</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,14">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>double</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,15">
          <mappingType>Section</mappingType>
          <srcFieldId>materialType</srcFieldId>
          <dstFieldId>vqComponentCosts.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,16">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>vqComponentCosts.currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,17">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial.uom</srcFieldId>
          <dstFieldId>vqComponentCosts.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,18">
          <mappingType>Section</mappingType>
          <srcFieldId>specMaterial.component</srcFieldId>
          <dstFieldId>vqComponentCosts.component</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="item_dataMappingRule.xlsx,specMaterialToQuotation,22">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SpecMaterialToQuotationProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="specRequirementCopy" position="item_dataMappingRule.xlsx,specRequirementCopy">
    <DataMappingRule description="Mapping from item requirement copy" domain="PEPL" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2017-02-23" id="specRequirementCopy" position="item_dataMappingRule.xlsx,specRequirementCopy,1" srcEntityName="SpecRequirement" srcEntityVersion="1" status="1" updatedDate="2017-03-01">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>specRequirement</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>specRequirement.seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>category</srcFieldId>
          <dstFieldId>specRequirement.category</dstFieldId>
          <dstFieldType>string-m</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>type</srcFieldId>
          <dstFieldId>specRequirement.type</dstFieldId>
          <dstFieldType>string-m</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>specRequirement.description</dstFieldId>
          <dstFieldType>string-m</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId>require</srcFieldId>
          <dstFieldId>specRequirement.require</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mandatory</srcFieldId>
          <dstFieldId>specRequirement.mandatory</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,16">
          <mappingType>Field</mappingType>
          <srcFieldId>notesOrInstructions</srcFieldId>
          <dstFieldId>specRequirement.notesOrInstructions</dstFieldId>
          <dstFieldType>string-l</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,17">
          <mappingType>Field</mappingType>
          <srcFieldId>details</srcFieldId>
          <dstFieldId>specRequirement.details</dstFieldId>
          <dstFieldType>string-l</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,18">
          <mappingType>Field</mappingType>
          <srcFieldId>unitOfReqBookName</srcFieldId>
          <dstFieldId>specRequirement.unitOfReqBookName</dstFieldId>
          <dstFieldType>string-s</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,19">
          <mappingType>Field</mappingType>
          <srcFieldId>dataType</srcFieldId>
          <dstFieldId>specRequirement.dataType</dstFieldId>
          <dstFieldType>string-s</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,20">
          <mappingType>Field</mappingType>
          <srcFieldId>dataString</srcFieldId>
          <dstFieldId>specRequirement.dataString</dstFieldId>
          <dstFieldType>string-m</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,21">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDecimal</srcFieldId>
          <dstFieldId>specRequirement.dataDecimal</dstFieldId>
          <dstFieldType>decimal-rate</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,22">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDate</srcFieldId>
          <dstFieldId>specRequirement.dataDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,23">
          <mappingType>Field</mappingType>
          <srcFieldId>dataEntityBookName</srcFieldId>
          <dstFieldId>specRequirement.dataEntityBookName</dstFieldId>
          <dstFieldType>string-s</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,24">
          <mappingType>Section</mappingType>
          <srcFieldId>dataEntity</srcFieldId>
          <dstFieldId>specRequirement.dataEntity</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,25">
          <mappingType>Section</mappingType>
          <srcFieldId>unitOfRequirement</srcFieldId>
          <dstFieldId>specRequirement.unitOfRequirement</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,26">
          <mappingType>Section</mappingType>
          <srcFieldId>imageId</srcFieldId>
          <dstFieldId>specRequirement.imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,specRequirementCopy,27">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment</srcFieldId>
          <dstFieldId>specRequirement.attachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="selectPatternByPalette" position="item_dataMappingRule.xlsx,selectPatternByPalette">
    <DataMappingRule description="Mapping from PatternByPalette to item Color" domain="PEPL" dstEntityName="ItemColor" dstEntityVersion="1" effectiveDate="2012-02-20" id="selectPatternByPalette" position="item_dataMappingRule.xlsx,selectPatternByPalette,1" srcEntityName="Pattern" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,9">
          <mappingType>Field</mappingType>
          <srcFieldId>patternCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,10">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,11">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,12">
          <mappingType>Field</mappingType>
          <srcFieldId>altName</srcFieldId>
          <dstFieldId>altName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnlyFlag</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,14">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>colorImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,selectPatternByPalette,15">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>colorPattern</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="Sheet1" position="item_dataMappingRule.xlsx,Sheet1">
    <DataMappingRule description="Mapping from PatternByPalette to item Color" domain="PEPL" dstEntityName="ItemColor" dstEntityVersion="1" effectiveDate="2012-02-20" id="selectPatternByPaletteTask02" position="item_dataMappingRule.xlsx,Sheet1,1" srcEntityName="PatternPalettePattern" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="item_dataMappingRule.xlsx,Sheet1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,Sheet1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,Sheet1,10">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,Sheet1,11">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,Sheet1,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isReadOnlyFlag</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,Sheet1,13">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>colorImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="item_dataMappingRule.xlsx,Sheet1,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>colorPattern</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
