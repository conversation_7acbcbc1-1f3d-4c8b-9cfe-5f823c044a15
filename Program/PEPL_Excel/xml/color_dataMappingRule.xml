<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="color" position="color_dataMappingRule.xlsx">
  <sheet id="colorSelectColorPalette" position="color_dataMappingRule.xlsx,colorSelectColorPalette">
    <DataMappingRule description="Mapping for Color select Color Palette" domain="PEPL" dstEntityName="Color" dstEntityVersion="1" effectiveDate="2014-08-14" id="colorSelectColorPalette" position="color_dataMappingRule.xlsx,colorSelectColorPalette,1" srcEntityName="ColorPalette" srcEntityVersion="1" status="1" updatedDate="2014-08-14">
      <elements id="mappingRule">
        <element position="color_dataMappingRule.xlsx,colorSelectColorPalette,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="color_dataMappingRule.xlsx,colorSelectColorPalette,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>colorColorPalettes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="color_dataMappingRule.xlsx,colorSelectColorPalette,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>colorColorPalettes.colorPaletteId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="color_dataMappingRule.xlsx,colorSelectColorPalette,11">
          <mappingType>Section</mappingType>
          <srcFieldId>custCodelist1</srcFieldId>
          <dstFieldId>colorColorPalettes.custCodelist1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
