<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="rfq" position="rfq_validation.xlsx">
  <sheet id="ValidationProfile" position="rfq_validation.xlsx,ValidationProfile">
    <ValidationProfile position="rfq_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="rfq_validation.xlsx,ValidationProfile,4">
          <id>ff973020d5c94ff09a73500f9dacd553</id>
          <profileName>Default Data Validation Profile Rfq[ver:1]</profileName>
          <entityName>Rfq</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,MarkAsCustomStatus06Doc,<PERSON><PERSON><PERSON>ustom<PERSON>tatus07Doc,MarkAs<PERSON>ustomStatus08Doc,Mark<PERSON><PERSON>ustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.100</updatedOn>
        </element>
        <element position="rfq_validation.xlsx,ValidationProfile,5">
          <id>722a4adc0a5f4e0ba9a314d8e96d719c</id>
          <profileName>Default Data Validation Profile Rfq[ver:1]</profileName>
          <entityName>Rfq</entityName>
          <entityVer>1</entityVer>
          <action>RfqSend</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:08.250</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="rfq_validation.xlsx,ValidationRule">
    <ValidationRule position="rfq_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="rfq_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <restapiBeanName>UniqueInSectionValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,7">
          <type>EmailValidator</type>
          <className>com.core.cbx.validation.validator.EmailValidator</className>
          <restapiBeanName>EmailValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,8">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,9">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <restapiBeanName>HCLValidator</restapiBeanName>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,10">
          <type>RFQSaveAndSendValidator</type>
          <className>com.core.pepl.validation.validator.RFQSaveAndSendValidator</className>
          <restapiBeanName>RFQSaveAndSendValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,11">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <restapiBeanName>ExternalActiveValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="rfq_validation.xlsx,MandatoryValidator">
    <ValidationField position="rfq_validation.xlsx,MandatoryValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,MandatoryValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>expiryDate</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,9">
          <entityName>Rfq</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,10">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,11">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqItem</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,12">
          <entityName>Rfq</entityName>
          <fieldId>rfqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqNo</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,MandatoryValidator,16" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,MandatoryValidator,23">
          <entityName>Rfq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>expiryDate</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,24">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqVendor</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,25">
          <entityName>Rfq</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,26">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,27">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqItem</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="rfq_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="rfq_validation.xlsx,UniqueInModuleValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>rfqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,UniqueInModuleValidator,11" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>Rfq</entityName>
          <fieldId>rfqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="rfq_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="rfq_validation.xlsx,UniqueInSectionValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqVendor</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,UniqueInSectionValidator,11" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInSectionValidator,18">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqVendor</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="EmailValidator" position="rfq_validation.xlsx,EmailValidator">
    <ValidationField position="rfq_validation.xlsx,EmailValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,EmailValidator,8">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
          <MULTIPLE_EMAILS>TRUE</MULTIPLE_EMAILS>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,EmailValidator,11" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,EmailValidator,18">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
          <MULTIPLE_EMAILS>TRUE</MULTIPLE_EMAILS>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="rfq_validation.xlsx,ClassificationValidator">
    <ValidationField position="rfq_validation.xlsx,ClassificationValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ClassificationValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>vendor</TARGET_FIELD>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
          <MATCH_TYPE>ALL</MATCH_TYPE>
        </element>
        <element position="rfq_validation.xlsx,ClassificationValidator,9">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>item</TARGET_FIELD>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
          <MATCH_TYPE/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,ClassificationValidator,12" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ClassificationValidator,19">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>vendor</TARGET_FIELD>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
          <MATCH_TYPE>ALL</MATCH_TYPE>
        </element>
        <element position="rfq_validation.xlsx,ClassificationValidator,20">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>item</TARGET_FIELD>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
          <MATCH_TYPE/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="rfq_validation.xlsx,HCLValidator">
    <ValidationField position="rfq_validation.xlsx,HCLValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,HCLValidator,8">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="rfq_validation.xlsx,HCLValidator,9">
          <entityName>RfqItem</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hierarchy</TARGET_FIELD>
          <TYPE>gridSelectBiz</TYPE>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,HCLValidator,12" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,HCLValidator,19">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="rfq_validation.xlsx,HCLValidator,20">
          <entityName>RfqItem</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hierarchy</TARGET_FIELD>
          <TYPE>gridSelectBiz</TYPE>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="rfq_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="rfq_validation.xlsx,ExternalActiveValidator,1" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ExternalActiveValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="RFQSaveAndSendValidator" position="rfq_validation.xlsx,RFQSaveAndSendValidator">
    <ValidationField position="rfq_validation.xlsx,RFQSaveAndSendValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,RFQSaveAndSendValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL>Error</LABEL>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,RFQSaveAndSendValidator,11" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,RFQSaveAndSendValidator,18">
          <entityName>Rfq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL>Error</LABEL>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
