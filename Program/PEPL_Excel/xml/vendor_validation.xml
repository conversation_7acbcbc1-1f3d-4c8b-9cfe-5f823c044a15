<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="vendor" position="vendor_validation.xlsx">
  <sheet id="ValidationProfile" position="vendor_validation.xlsx,ValidationProfile">
    <ValidationProfile position="vendor_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="vendor_validation.xlsx,ValidationProfile,4">
          <id>0f08f1ce84294b15838c3f08842db9f8</id>
          <profileName>Default Data Validation Profile Vendor[ver:1]</profileName>
          <entityName>Vendor</entityName>
          <entityVer>1</entityVer>
          <action>VendorMasterSendToBuyer,SaveDoc,SaveAndConfirm,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,Mark<PERSON><PERSON>ustomStatus06Doc,MarkAs<PERSON>ustomStatus07Doc,Mark<PERSON><PERSON>ustomStatus08Doc,MarkAsCustomStatus09Doc,Mark<PERSON><PERSON>ustomStatus10Doc,DraftStatus,OfficialStatus</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.864</updatedOn>
        </element>
        <element position="vendor_validation.xlsx,ValidationProfile,5">
          <id>0f08f1ce84294b15838c3f08842dbbbb</id>
          <profileName>Default Data Validation Profile Vendor[ver:1]</profileName>
          <entityName>Vendor</entityName>
          <entityVer>1</entityVer>
          <action>VendorMasterSendToVendor</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.864</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="vendor_validation.xlsx,ValidationRule">
    <ValidationRule position="vendor_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="vendor_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vendor_validation.xlsx,ValidationRule,5">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <restapiBeanName>UniqueInSectionValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vendor_validation.xlsx,ValidationRule,6">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vendor_validation.xlsx,ValidationRule,7">
          <type>EmailValidator</type>
          <className>com.core.cbx.validation.validator.EmailValidator</className>
          <restapiBeanName>EmailValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vendor_validation.xlsx,ValidationRule,8">
          <type>NumericRangeValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <restapiBeanName>NumericRangeValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vendor_validation.xlsx,ValidationRule,9">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="vendor_validation.xlsx,ValidationRule,10">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <restapiBeanName>ExternalActiveValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="vendor_validation.xlsx,MandatoryValidator">
    <ValidationField position="vendor_validation.xlsx,MandatoryValidator,1" profileId="0f08f1ce84294b15838c3f08842db9f8" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,MandatoryValidator,8">
          <entityName>Vendor</entityName>
          <fieldId>businessName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>businessName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,9">
          <entityName>Vendor</entityName>
          <fieldId>hcs</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchyGrid</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,10">
          <entityName>Vendor</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,11">
          <entityName>VendorHc</entityName>
          <fieldId>hclTypeName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>hcs</GRID_ID>
          <LABEL_FIELD_ID>hclTypeName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,12">
          <entityName>VendorCust</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>custName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,13">
          <entityName>VendorKeyCustomer</entityName>
          <fieldId>customerName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>keyCustomers</GRID_ID>
          <LABEL_FIELD_ID>customerName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,14">
          <entityName>VendorBusinessTurnoverByRegion</entityName>
          <fieldId>region</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>businessTurnovers</GRID_ID>
          <LABEL_FIELD_ID>region</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,15">
          <entityName>VendorHistorical</entityName>
          <fieldId>year</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>historicals</GRID_ID>
          <LABEL_FIELD_ID>year</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,16">
          <entityName>Vendor</entityName>
          <fieldId>addresses</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>addresses</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,17">
          <entityName>VendorAddress</entityName>
          <fieldId>businessName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>businessName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,18">
          <entityName>VendorAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,19">
          <entityName>VendorAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,20">
          <entityName>VendorAddress</entityName>
          <fieldId>city</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>city</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,21">
          <entityName>VendorAddress</entityName>
          <fieldId>state</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>state</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,22">
          <entityName>Vendor</entityName>
          <fieldId>contacts</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>contacts</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,23">
          <entityName>VendorImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>images</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,24">
          <entityName>VendorAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>attachments</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,25">
          <entityName>Vendor</entityName>
          <fieldId>vendorTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,26">
          <entityName>Vendor</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,27">
          <entityName>Vendor</entityName>
          <fieldId>shipmentMethod</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>shipmentMethod</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,28">
          <entityName>Vendor</entityName>
          <fieldId>incoterm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>incoterm</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,29">
          <entityName>VendorContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>contacts</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,30">
          <entityName>VendorContact</entityName>
          <fieldId>lastName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>contacts</GRID_ID>
          <LABEL_FIELD_ID>lastName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,31">
          <entityName>VendorContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>contacts</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,32">
          <entityName>VendorFact</entityName>
          <fieldId>isDefaultFact</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>facts</GRID_ID>
          <LABEL_FIELD_ID>Default</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vendor_validation.xlsx,MandatoryValidator,35" profileId="0f08f1ce84294b15838c3f08842dbbbb" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,MandatoryValidator,42">
          <entityName>Vendor</entityName>
          <fieldId>businessName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>businessName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,43">
          <entityName>VendorHc</entityName>
          <fieldId>hclTypeName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>hcs</GRID_ID>
          <LABEL_FIELD_ID>hclTypeName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,44">
          <entityName>VendorCust</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>custName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,45">
          <entityName>VendorKeyCustomer</entityName>
          <fieldId>customerName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>keyCustomers</GRID_ID>
          <LABEL_FIELD_ID>customerName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,46">
          <entityName>VendorBusinessTurnoverByRegion</entityName>
          <fieldId>region</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>businessTurnovers</GRID_ID>
          <LABEL_FIELD_ID>region</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,47">
          <entityName>VendorHistorical</entityName>
          <fieldId>year</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>historicals</GRID_ID>
          <LABEL_FIELD_ID>year</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,48">
          <entityName>Vendor</entityName>
          <fieldId>addresses</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>addresses</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,49">
          <entityName>VendorAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,50">
          <entityName>VendorAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>addresses</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,51">
          <entityName>Vendor</entityName>
          <fieldId>contacts</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>contacts</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,52">
          <entityName>VendorContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>contacts</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,53">
          <entityName>VendorContact</entityName>
          <fieldId>lastName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>contacts</GRID_ID>
          <LABEL_FIELD_ID>lastName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,54">
          <entityName>VendorContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>contacts</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,55">
          <entityName>VendorImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>images</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,MandatoryValidator,56">
          <entityName>VendorAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>attachments</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="vendor_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="vendor_validation.xlsx,UniqueInSectionValidator,1" profileId="0f08f1ce84294b15838c3f08842db9f8" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>VendorFact</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>facts</GRID_ID>
          <LABEL_FIELD_ID>factName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,9">
          <entityName>VendorCust</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>customerName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,10">
          <entityName>VendorKeyCustomer</entityName>
          <fieldId>customerName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>keyCustomers</GRID_ID>
          <LABEL_FIELD_ID>customerName</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,11">
          <entityName>VendorBusinessTurnoverByRegion</entityName>
          <fieldId>region</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>businessTurnovers</GRID_ID>
          <LABEL_FIELD_ID>region</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,12">
          <entityName>VendorHistorical</entityName>
          <fieldId>year</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>historicals</GRID_ID>
          <LABEL_FIELD_ID>year</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,13">
          <entityName>VendorOthers1</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorOthers1</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,14">
          <entityName>VendorOthers2</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorOthers2</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,15">
          <entityName>VendorOthers3</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorOthers3</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vendor_validation.xlsx,UniqueInSectionValidator,18" profileId="0f08f1ce84294b15838c3f08842dbbbb" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,25">
          <entityName>VendorFact</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>facts</GRID_ID>
          <LABEL_FIELD_ID>factName</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,26">
          <entityName>VendorCust</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>customerName</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,27">
          <entityName>VendorKeyCustomer</entityName>
          <fieldId>customerName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>keyCustomers</GRID_ID>
          <LABEL_FIELD_ID>customerName</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,28">
          <entityName>VendorBusinessTurnoverByRegion</entityName>
          <fieldId>region</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>businessTurnovers</GRID_ID>
          <LABEL_FIELD_ID>region</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,29">
          <entityName>VendorHistorical</entityName>
          <fieldId>year</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>historicals</GRID_ID>
          <LABEL_FIELD_ID>year</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,30">
          <entityName>VendorOthers1</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorOthers1</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <ERROR_ID>REF024</ERROR_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,31">
          <entityName>VendorOthers2</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorOthers2</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <ERROR_ID>REF024</ERROR_ID>
        </element>
        <element position="vendor_validation.xlsx,UniqueInSectionValidator,32">
          <entityName>VendorOthers3</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vendorOthers3</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <ERROR_ID>REF024</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="vendor_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="vendor_validation.xlsx,UniqueInModuleValidator,1" profileId="0f08f1ce84294b15838c3f08842db9f8" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Vendor</entityName>
          <fieldId>vendorCode</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <CHECK_LATEST_VERSION>Y</CHECK_LATEST_VERSION>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vendor_validation.xlsx,UniqueInModuleValidator,11" profileId="0f08f1ce84294b15838c3f08842dbbbb" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>Vendor</entityName>
          <fieldId>vendorCode</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <CHECK_LATEST_VERSION>Y</CHECK_LATEST_VERSION>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="EmailValidator" position="vendor_validation.xlsx,EmailValidator">
    <ValidationField position="vendor_validation.xlsx,EmailValidator,1" profileId="0f08f1ce84294b15838c3f08842db9f8" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,EmailValidator,8">
          <entityName>Vendor</entityName>
          <fieldId>companyEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>companyEmail</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,EmailValidator,9">
          <entityName>VendorContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vendor_validation.xlsx,EmailValidator,12" profileId="0f08f1ce84294b15838c3f08842dbbbb" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,EmailValidator,19">
          <entityName>Vendor</entityName>
          <fieldId>companyEmail</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>companyEmail</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,EmailValidator,20">
          <entityName>VendorContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumericRangeValidator" position="vendor_validation.xlsx,NumericRangeValidator">
    <ValidationField position="vendor_validation.xlsx,NumericRangeValidator,1" profileId="0f08f1ce84294b15838c3f08842db9f8" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,NumericRangeValidator,8">
          <entityName>VendorCust</entityName>
          <fieldId>relateSince</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <MAX_VALUE>9999</MAX_VALUE>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>relateSince</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,NumericRangeValidator,9">
          <entityName>VendorFact</entityName>
          <fieldId>relateSince</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <MAX_VALUE>9999</MAX_VALUE>
          <GRID_ID>facts</GRID_ID>
          <LABEL_FIELD_ID>relateSince</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vendor_validation.xlsx,NumericRangeValidator,12" profileId="0f08f1ce84294b15838c3f08842dbbbb" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,NumericRangeValidator,19">
          <entityName>VendorCust</entityName>
          <fieldId>relateSince</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <MAX_VALUE>9999</MAX_VALUE>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>relateSince</LABEL_FIELD_ID>
        </element>
        <element position="vendor_validation.xlsx,NumericRangeValidator,20">
          <entityName>VendorFact</entityName>
          <fieldId>relateSince</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <MAX_VALUE>9999</MAX_VALUE>
          <GRID_ID>facts</GRID_ID>
          <LABEL_FIELD_ID>relateSince</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="vendor_validation.xlsx,ClassificationValidator">
    <ValidationField position="vendor_validation.xlsx,ClassificationValidator,1" profileId="0f08f1ce84294b15838c3f08842db9f8" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,ClassificationValidator,8">
          <entityName>Vendor</entityName>
          <fieldId>custs</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>custs</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>custId</TARGET_FIELD>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="vendor_validation.xlsx,ClassificationValidator,9">
          <entityName>Vendor</entityName>
          <fieldId>facts</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>facts</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>factId</TARGET_FIELD>
          <DOCUMENT_NO>factCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="vendor_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="vendor_validation.xlsx,ExternalActiveValidator,1" profileId="0f08f1ce84294b15838c3f08842dbbbb" profileName="">
      <elements id="default">
        <element position="vendor_validation.xlsx,ExternalActiveValidator,8">
          <entityName>Vendor</entityName>
          <fieldId>docStatus</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendorCode</LABEL_FIELD_ID>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
