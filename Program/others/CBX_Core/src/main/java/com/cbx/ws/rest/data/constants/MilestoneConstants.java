// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.15.9.0P.04.0
// ============================================================================
// CHANGE LOG
// PEPL.15.9.0P.04.0 : 2025-09-01, Gwyn.gao, PEPL-3429
// CNT.6.8 : 2017-08-17, jim.huang, CNT-32540
// CNT.6.3.0 : 2017-03-21, denny.deng, CNT-29053
// CNT.6.1.0 : 2017-01-09, arthur.ou, CNT-27713
// CNT.6.0.c : 2016-11-12, jim.huang, CNT-26821 creation
// ============================================================================
package com.cbx.ws.rest.data.constants;

/**
 * <AUTHOR>
 * Used in MilestoneConverter.java to Converter DynamicEntity to Json.
 */
public interface MilestoneConstants {

    // String ENTITY_NAME = "milestone";
    String CPM_ID = "cpmId";
    String CODE = "code";
    String NAME = "name";
    String DOMAIN_ID = "domainId";
    String SEQUENCE = "sequence";
    String PLANNED_START_DATE = "plannedStartDate";
    String PLANNED_END_DATE = "plannedEndDate";
    String START_DATE = "startDate";
    String END_DATE = "endDate";
    String DURATION = "duration";
    String PLANNED_DURATION = "plannedDuration";
    String STATUS = "status";
    String PERCENTAGE = "percentage";
    String QUANTITY = "quantity";

    String DESCRIPTION = "description";
    String REASON = "reason";
    String REASON_DESCRIPTION = "reasonDescription";
    String COLOR_CODE = "colorCode";
    String PROPERTIES = "properties";
    String ASSIGNEES = "assignees";
    String DAILY_CHECK_CONDITIONS = "dailyCheckConditions";

    String CREATED_BY = "createdBy";
    String CREATED_ON = "createdOn";
    String UPDATED_BY = "updatedBy";
    String UPDATED_ON = "updatedOn";
    String CHANGES = "changes";
    String OPERATION = "operation";
    String PATH = "path";
    String OPERATION_CHANGE = "{lbl.changeHistory.operation.change}";
    String PATH_PREFIX = "lbl.popCpmTaskDetailWin.cpmTasks.";
    String SEARCH_FOR = "searchFor";

    
    String DUE_SOON_DAYS = "dueSoonDays";
    
    public  interface Assignee {
        String ENTITY_NAME = "entityName";
        String ID = "id";
    }

    public  interface DailyCheckCondition {
        String FIELD_ID = "fieldId";
        String WHEN = "when";
    }
}
