// Copyright (c) 1998-2016 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.17.0
// ============================================================================
// CHANGE LOG
// CNT.5.17 : 2016-07-29, alvin.choi, CNT-24214
// CNT.5.17.0 : 2016-08-04, alisa.yang, CNT-24560 check style
// CNT.5.15 : 2016-06-22, alvin.choi, CNT-22355
// CNT.5.9.GA : 2015-05-07, wilson.lun, creation CNT-17435
// ============================================================================
package com.cbx.ws.rest.jaxrs.exception;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.ResponseBuilder;
import javax.ws.rs.core.Response.Status;
import javax.ws.rs.ext.ExceptionMapper;

import com.cbx.ws.rest.exception.RESTExceptionConstants;
import com.cbx.ws.rest.exception.RESTExceptionHandler;
import com.core.cbx.common.type.DateTime;
import com.core.cbx.common.util.UUIDGenerator;
import com.core.cbx.resource.service.SystemMessageManager;

/**
 * <AUTHOR>
 * @param <E>
 *
 */
public abstract class AbstractExceptionMapper<E extends Throwable> implements ExceptionMapper<E> {

    private static final String DEFAULT_LOCALE = "en_US";
    @Context private HttpServletRequest request;

   @Override
    public Response toResponse(final E e) {
        final String errorRef = UUIDGenerator.getUUID();
        final String dateTime = DateTime.now().format(RESTExceptionHandler.DATETIME_FORMAT);

       final ResponseBuilder builder = Response.status(getStatus(e));
       final Object entity = getEntity(e);
       if (entity != null) {
           builder.entity(entity);
       }
       builder.header(HttpHeaders.DATE, dateTime);
       builder.header(RESTExceptionConstants.ExtendedHttpHeader.ERROR_REF, errorRef);
       builder.type(getContentType());

       final Response response = builder.build();

       //TODO: Also pass response attribute to log
       request.setAttribute(RESTExceptionConstants.Attribute.ERROR_DATETIME, dateTime);
       request.setAttribute(RESTExceptionConstants.Attribute.ERROR_REF, errorRef);

       RESTExceptionHandler.getInstance().log(e, request);

       return response;
    }

    /**
     * The error response status. By default, it is 500 Internal Server error.
     *
     * @param e the exception thrown
     * @return HTTP client/server error status code. It should be 4XX or 5XX.
     */
    protected int getStatus(final E e) {
        return Status.INTERNAL_SERVER_ERROR.getStatusCode();
    }

    /**
     * The error response entity.
     *
     * @param e the exception thrown
     * @return a response entity object
     */
    protected Object getEntity(final E e) {
        final String code = RESTExceptionConstants.ErrorResponseCode.INTERNAL_ERROR;
        final List<ErrorDetail> errors = new ArrayList<ErrorDetail>();
        final String message = SystemMessageManager.getInstance().getMessage("13010001", DEFAULT_LOCALE);
        errors.add(new ErrorDetail(message));
        return new ErrorResponseEntity(code, errors);
    }

    /**
     *
     * @return ContentType String of Request
     */
    private String getContentType(){

        //TODO : change to handle different request content types, such as: xml
        return MediaType.APPLICATION_JSON;

    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

}
