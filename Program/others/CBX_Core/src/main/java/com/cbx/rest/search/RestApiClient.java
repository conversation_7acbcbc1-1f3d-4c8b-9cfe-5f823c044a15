// Copyright (c) 1998-2024 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CBX 15.5 GA
// ============================================================================
// CHANGE LOG
// CBX 15.5 GA : 2025-02-25, Gwyn.gao, OI-2018
// CBX 14.18 GA : 2024-08-30, richard.tan, LIDL-18865
// CBX 14.15 GA : 2024-07-15, jay.sun, PCHI-1328
// CBX 14.14 GA : 2024-07-10, jay.sun, PCHI-1295
// CBX 14.8 GA : 2024-04-09, eason.li PCHI-1019
// CBX 14.6 GA : 2024-3-14, flinn.huang, PCHI-1004 merged system account
// CBX 13.18 GA : 2023-09-06, jeff.cheng, WUN-5372
// CBX 13.15 GA : 2023-07-17, lydia.xie, BLN-1542
// CBX 13.12 GA : 2023-06-06, luson.lu, PEPL-2801
// CBX 13.11 GA : 2023-05-23, Nick.Fu, ALPHA-1006
// CBX 13.10 GA : 2023-05-17, hugh.zhang, SPM-846
// CBX 13.10 GA : 2023-05-17, hugh.zhang, SPM-845
// CBX 13.8 : 2023-04-13, dwade.ye, TMN-482
// SPM 12.17.0P.19.12 : 2023-02-16, hugh.zhang, SPM-656
// SPM 12.17.0P.19.2 : 2023-01-12, hugh.zhang, SPM-554
// CBX 13.8 GA : 2027-03-28, mila.wu, CCC-484
// CBX 12.23 GA : 2022-12-06, glyn.yu, ALPHA-893
// CBX 11.20 GA : 2021-10-21, denny.deng, KME-6493
// CBX.11.13 : 2020-07-12, sam.liang ARE-1143
// CBX 11.12.0 GA : 2021-06-23, jammy.yuan, DFM-68
// CBX 11.10 : 2021-06-01, mila.wu, DFM-69
// CBX 11.7 : 2021-04-21, ben.wu, WUN-1412
// CBX 10.23 GA : 2020-11-19, denny.deng, WUN-1183
// CBX 10.16.0 GA : 2020-08-11, luson.lu, PGS-2130
// CBX 10.12.0 GA : 2020-06-19, leon.lin, CBX9-1372
// CBX 10.11 GA : 2020-06-01, encin.li, SON-26
// CBX 10.11 GA : 2020-05-29, denny.deng, KME-5091
// CBX 10.11 GA : 2020-05-26, lewis.liu, KME-5167
// CBX 10.10 GA : 2020-05-15, encin.li, SON-68
// CBX 10.8 GA : 2020-04-16, Vincent.tan, LDL-10546
// CBX 9.27 GA : 2020-01-08, john.huang, KME-5030
// CBX 9.26 GA : 2019-12-26, mason.mo, KME-5026
// CBX 9.14 GA: 2019-07-18, lewis.liu, KME-4762
// CBX 9.4.0 GA : 2019-02-26, john.huang, KME-4378
// CBX 9.3.0 GA : 2019-01-28, mason.mo, KME-4377
// CBX 9.2.0 GA : 2019-01-18, mason.mo, KME-3529
// ============================================================================
package com.cbx.rest.search;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.cbx.ws.rest.client.cbx.RestTemplateHelper;
import com.cbx.ws.rest.jaxrs.exception.RestAPIException;
import com.core.cbx.conf.service.SystemConfigManager;
import com.core.cbx.data.DynamicEntityModel;
import com.core.cbx.data.constants.ExceptionConstants;
import com.core.cbx.data.constants.User;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.EntityConstants;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.data.search.Criterion;
import com.core.cbx.data.search.Restriction;
import com.core.cbx.security.AuthenticationUtil;
import com.core.snp.ref.restful.action.RestApiCallingBaseWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 */
public final class RestApiClient {// NOPMD

    public static final String APPLICATION_JSON = "application/json; charset=UTF-8";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String AUTHORIZATION = "Authorization";
    public static final String HEADER_ACCEPT = "accept ";
    public static final String REST_URL_PREFIX;
    public static final String REST_API_PROTOCOL;
    public static final String REST_API_HOST;
    public static final String REST_API_PORT;
    public static final String REST_URL;
    public static final String LOGIN_URL;
    private static final String AUTH_BEARER = "bearer ";
    private static final String AMFORI_BACKEND_API_USER = "amfori.backend.api.user";
    public static String PRINT_FILE_NAME;
    private static final int SOCKET_TIME_OUT = 15 * 60 * 1000;

    private final static ObjectMapper mapper = new ObjectMapper();

    static {
        final String attributeProtocol = SystemConfigManager.getInstance().getConfigValue("cbx.restapi.server.protocol");
        REST_API_PROTOCOL = StringUtils.isNotBlank(attributeProtocol) ? attributeProtocol : "http";
        final String attributeHost = SystemConfigManager.getInstance().getConfigValue("cbx.restapi.server.host");
        REST_API_HOST = StringUtils.isNotBlank(attributeHost) ? attributeHost : "localhost";
        final String attributePort = SystemConfigManager.getInstance().getConfigValue("cbx.restapi.server.port");
        REST_API_PORT = StringUtils.isNotBlank(attributePort) ? attributePort : "8080";
        REST_URL_PREFIX = REST_API_PROTOCOL + "://" + REST_API_HOST + ":" + REST_API_PORT;
        REST_URL = REST_URL_PREFIX + "/api/";
        LOGIN_URL = REST_URL_PREFIX + "/oauth2/token";
        mapper.registerModule(new ParameterNamesModule());
        mapper.registerModule(new Jdk8Module());
        mapper.registerModule(new JavaTimeModule()); // new module, NOT JSR310Module
    }

    private static final Logger LOGGER = LogManager.getLogger(RestApiClient.class);

    private static class SingleTonHoler {
        private static final RestApiClient INSTANCE = new RestApiClient();// NOPMD
    }

    private RestApiClient() {
    }

    public static RestApiClient getInstance() {
        return SingleTonHoler.INSTANCE;
    }

    public boolean isServerReady() {
        final String workingDomainId = AuthenticationUtil.getUserWorkingDomainId();
        try {
            Oauth2TokenUtil.restTemplate("", workingDomainId, LOGIN_URL).getAccessToken();
        } catch (final ResourceAccessException e) {
            final String message = e.getMessage();
            if (StringUtils.contains(message, "Connection refused: connect")) {
                return false;
            }
        }
        return true;
    }

    private String getToken() {
        final String workingDomainId = AuthenticationUtil.getUserWorkingDomainId();
        String loginId = AuthenticationUtil.getUser().getUserEntity().getString(User.LOGIN_ID);
        if (StringUtils.equalsIgnoreCase(loginId, AuthenticationUtil.SYSTEM_TEMP_LOGIN)) {
            return getToken(workingDomainId);
        }
        return getToken(loginId, workingDomainId);
    }

    private String getToken(final String loginId, final String workingDomainId) {
        if (StringUtils.isBlank(loginId)) {
            return getToken(workingDomainId);
        }
        final OAuth2AccessToken oAuth2AccessToken = Oauth2TokenUtil.getToken(loginId, workingDomainId, LOGIN_URL);
        return oAuth2AccessToken.getValue();
    }

    public OAuth2AccessToken getOAuth2AccessToken(final String loginId, final String workingDomainId) {
        return Oauth2TokenUtil.getToken(loginId, workingDomainId, LOGIN_URL);
    }

    private String getToken(final String workingDomainId) {
        final OAuth2AccessToken oAuth2AccessToken = Oauth2TokenUtil.getToken(workingDomainId, LOGIN_URL);
        return oAuth2AccessToken.getValue();
    }

    public Map<String, String> getTokenMap(final String loginId, final String workingDomainId) {
        final Map<String, String> result = new HashMap<String, String>();
        final OAuth2AccessToken oAuth2AccessToken = Oauth2TokenUtil.getToken(loginId, workingDomainId, LOGIN_URL);
        result.put("token", oAuth2AccessToken.getValue());
        result.put("refreshToken", oAuth2AccessToken.getRefreshToken() != null ? oAuth2AccessToken.getRefreshToken().getValue() : StringUtils.EMPTY);
        return result;
    }

    public void indexMainEntity(final String docId, final String refNo, final String version,
                                final String workingDomainId, final String module) {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        String refNoUrl = refNo;
        if (StringUtils.isNotBlank(version)) {
            refNoUrl = refNoUrl + "/" + version;
        }
        // index mainEntity without refNo , e.g : notification
        if (StringUtils.isEmpty(refNo) && StringUtils.isNotEmpty(docId)) {
            refNoUrl = docId;
        }
        final String restModule = CbxSearchClientConstants.esModuleMap.get(module) == null ? module + 's' : CbxSearchClientConstants.esModuleMap.get(module);
        final String mainPojoUrl = REST_URL + restModule + "/index/" + refNoUrl;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.PUT, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("index document by restapi server used=" + (indexEnd - indexStart) + ", refNo:" + refNo
                + ", module:" + module + ", version" + version + ", by system account, workingDomainId:"
                + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index main entity result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to index main entity, exception:" + response.getBody());
        }
    }


    public void indexCpm(final String cpmId, final String workingDomainId) {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final String mainPojoUrl = REST_URL + "cpm/index/" + cpmId;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.PUT, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("index cpm by restapi server used=" + (indexEnd - indexStart) + ", cpmId:" + cpmId
                + ", by system account, workingDomainId:" + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index cpm result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to index cpm, exception:" + response.getBody());
        }
    }

    public void initCpm(final String moduleId, final String refNo, final String loginId, final String workingDomainId) {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final String mainPojoUrl = REST_URL + "cpm/init/" + moduleId + "/" + refNo;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.GET, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("init cpm by restapi server used=" + (indexEnd - indexStart) + ", module id:" + moduleId + ", "
                + ", loginId:" + loginId + ", workingDomainId:" + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index cpm result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to init cpm, exception:" + response.getBody());
        }
    }

    public void reinitCpm(final String moduleId, final String refNo, final String workingDomainId) {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final String mainPojoUrl = REST_URL + "cpm/reinitialize/" + moduleId + "/" + refNo;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.GET, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("re-init cpm by restapi server used=" + (indexEnd - indexStart) + ", module id:" + moduleId + ", "
                + ", by system account, workingDomainId:" + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index cpm result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to re-init cpm, exception:" + response.getBody());
        }
    }

    //Map.key=id:entityName, Map.value=pojo json
    public void indexLineItem(final String docId, final String refNo, final String version,
                              final String workingDomainId, final String module, final String deleteInfo) throws DataException {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        String inputDeleteInfo = deleteInfo;
        if (StringUtils.isBlank(inputDeleteInfo)) {
            final Map<String, String> map = new HashMap<>();
            try {
                inputDeleteInfo = mapper.writeValueAsString(map);
            } catch (final JsonProcessingException e) {
                throw new DataException(ExceptionConstants.DATA_EXCEPTION_000001,
                        "Fail to convert deleteInfo to json:" + inputDeleteInfo, e);
            }
        }
        final HttpEntity<String> request = new HttpEntity<String>(inputDeleteInfo, headers);
        String refNoUrl = refNo;
        if (StringUtils.isNotBlank(version)) {
            refNoUrl = refNoUrl + "/" + version;
        }
        // index mainEntity without refNo , e.g : notification
        if (StringUtils.isEmpty(refNo) && StringUtils.isNotEmpty(docId)) {
            refNoUrl = docId;
        }
        final String restModule = CbxSearchClientConstants.esModuleMap.get(module) == null ? module + 's' : CbxSearchClientConstants.esModuleMap.get(module);
        final String mainPojoUrl = REST_URL + restModule + "/index/lineitem/" + refNoUrl;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.PUT, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("index document by restapi server used=" + (indexEnd - indexStart) + ", refNo:" + refNo
                + ", module:" + module + ", version" + version + ", by system account, workingDomainId:"
                + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index line item entity result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to index line item entity, exception:" + response.getBody());
        }
    }

    public void reindexEntity(final String refNo, final String version,
                              final String workingDomainId, final String module, final String needReindexModules, final Boolean isLatest) {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        String refNoUrl = refNo;
        if (StringUtils.isNotBlank(version)) {
            refNoUrl = refNoUrl + "/" + version;
        }
        String isLatestUrl = "&isLatest=1";
        if (BooleanUtils.isFalse(isLatest)) {
            isLatestUrl = "&isLatest=0";
        }
        final String restModule = CbxSearchClientConstants.esModuleMap.get(module) == null ? module + 's' : CbxSearchClientConstants.esModuleMap.get(module);
        final String mainPojoUrl = REST_URL + restModule + "/reindex/" + refNoUrl
                + "?needReindexModules=" + needReindexModules + isLatestUrl;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.PUT, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("index document by restapi server used=" + (indexEnd - indexStart) + ", refNo:" + refNo
                + ", module:" + module + ", version" + version + ", by system account, workingDomainId:"
                + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index main entity result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to index main entity, exception:" + response.getBody());
        }
    }

    public String convertObjectToString(final Object object) throws DataException {
        try {
            return mapper.writeValueAsString(object);
        } catch (final JsonProcessingException e) {
            throw new DataException(ExceptionConstants.DATA_EXCEPTION_000001, "Fail to convert object to json" + object.toString(), e);
        }
    }

    public void deleteExpiredDocument(final String workingDomainId, final String indexName,
                                      final Integer expiredDays) {
        final long indexStart = System.currentTimeMillis();
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = createHttpRequestEntity(workingDomainId, "");
        final String url = REST_URL + "system/purge/" + indexName + "/" + expiredDays;
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info(
                "Delete expired document by restapi server used = {}, indexName: {}, expiredDays: {}, by system account, workingDomainId: {}.",
                indexEnd - indexStart, indexName, expiredDays, workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("Delete expired document result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to delete expired document, exception:" + response.getBody());
        }
    }

    public void deleteDocCache(final DynamicEntity doc, final String module) {
        final HttpEntity<String> request = createHttpRequestEntity(doc.getDomainId(), "");
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final String url = REST_URL + "cache/" + module + "/" + doc.getReference() + "/"
                + doc.getString(EntityConstants.VERSION);
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, request, String.class);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("delete doc (" + doc.getReference() + "/" + doc.getString(EntityConstants.VERSION)
                + ") cache result :" + response.getStatusCode());
        if (result) {
            LOGGER.warn("Fail to delete doc (" + doc.getReference() + "/" + doc.getString(EntityConstants.VERSION)
                    + ") cache.");
        }
    }

    public void deleteDocCache(final String reference,String domainId,String version, final String module) {
        final HttpEntity<String> request = createHttpRequestEntity(domainId, "");
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final String url = REST_URL + "cache/" + module + "/" + reference + "/"
                + version;
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, request, String.class);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("delete doc (" + reference + "/" + version
                + ") cache result :" + response.getStatusCode());
        if (result) {
            LOGGER.warn("Fail to delete doc (" + reference + "/" + version
                    + ") cache.");
        }
    }

    public void deleteRelateDocCache(final DynamicEntity doc,
                                     final Map<String, List<String>> relatedEntityMap) {
        final String accessToken = getToken(doc.getDomainId());
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);
        final HttpEntity<Map<String, List<String>>> request = new HttpEntity<>(relatedEntityMap, headers);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final String url = REST_URL + "cache/relateDoc";
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("delete related doc (" + doc.getReference() + "/" + doc.getString(EntityConstants.VERSION)
                + ") cache result :" + response.getStatusCode());
        if (result) {
            LOGGER.warn("Fail to delete related doc (" + doc.getReference() + "/"
                    + doc.getString(EntityConstants.VERSION) + ") cache.");
        }
    }

    public String[] getIndicesFromES(final String workingDomainId, final String indexName) {
        final long indexStart = System.currentTimeMillis();
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = createHttpRequestEntity(workingDomainId, "");
        final String url = REST_URL + "system/indices/" + indexName;
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info(
                "Get indices from ES server by restapi server used = {}, workingDomainId: {}, by system account.",
                indexEnd - indexStart, workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("Get indices from ES server result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to get indices from ES server, exception:" + response.getBody());
            return null;

        }
        final String body = response.getBody();
        return StringUtils.substringsBetween(body, "\"", "\"");
    }

    private HttpEntity<String> createHttpRequestEntity(final String domainId, final String loginId) {
        final String accessToken = getToken(loginId, domainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);
        return new HttpEntity<>(headers);
    }


    public void updateFactoryByAmfori(final String workingDomainId, final String amforiSiteId, final String jsonObj) {
        final String loginId = SystemConfigManager.getInstance().getConfigValue(AMFORI_BACKEND_API_USER);
        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, "application/json;charset=UTF-8");
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate(new HttpComponentsClientHttpRequestFactory());
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(jsonObj, headers);
        final String mainPojoUrl = REST_URL_PREFIX + "/api/factories/amfori/" + amforiSiteId;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.PATCH, request, String.class);
        final boolean result = HttpStatus.OK == response.getStatusCode();
        LOGGER.info("update amfori factory result :" + response.getStatusCode());
        if (!result) {
            LOGGER.error("Fail to update amfori factory, exception:" + response.getBody());
        }
    }

    public void calculateCpmDownstreamLatestDate(final String loginId, final String workingDomainId,
                                                 final String module, final String refNo, final String appliedLevel) {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final String url = REST_URL + "/cpm/calculateCpmDownstreamLatestDate/" + module + "/" + refNo + "?appliedLevel=" + appliedLevel;
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("Calculate Cpm downstream latest date by restapi server = " + (indexEnd - indexStart) + ", refNo:" + refNo
                + ", module:" + module + ", appliedLevel: " + appliedLevel + ", loginId:" + loginId + ", workingDomainId:"
                + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("Calculate Cpm downstream latest date result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to calculate Cpm downstream latest date, refNo:" + refNo
                    + ", module:" + module + ", appliedLevel: " + appliedLevel + ", loginId:" + loginId + ", workingDomainId:"
                    + workingDomainId + ", exception:" + response.getBody());
        }
    }

    public boolean deleteInspectionBookingScheduleLock(final String startDay, final String endDay, final String inspectionOffice, final String loginId) {
        final HttpEntity<String> request = createHttpRequestEntity(AuthenticationUtil.getUserWorkingDomainId(), loginId);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final String url = REST_URL + "inspect_bookings/deleteInspectionBookingScheduleLock/" + startDay + "/" + endDay + "/" + inspectionOffice;
        final ResponseEntity<Boolean> response = restTemplate.exchange(url, HttpMethod.DELETE, request, Boolean.class);
        return response.getBody();
    }

    public boolean deleteFactAuditScheduleLock(final String startDay, final String endDay, final String inspectionOffice, final String loginId) {
        final HttpEntity<String> request = createHttpRequestEntity(AuthenticationUtil.getUserWorkingDomainId(), loginId);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final String url = REST_URL + "factory_audits/deleteFactAuditScheduleLock/" + startDay + "/" + endDay + "/" + inspectionOffice;
        final ResponseEntity<Boolean> response = restTemplate.exchange(url, HttpMethod.DELETE, request, Boolean.class);
        return response.getBody();
    }

    public Map<String, Object> batchPrintTechPack(final String requestUrl, final String accessToken) throws Exception {

        final HttpHeaders headers = new HttpHeaders();
        headers.add(AUTHORIZATION, "Bearer " + accessToken);
        headers.add("Connection", "keep-alive");
        headers.add("accept", "application/json");
        final HttpEntity<String> request = new HttpEntity<>(headers);

        ResponseEntity<byte[]> response = null;
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final Map<String, Object> attachmentResult = new HashMap<>();
        try {
            response = restTemplate.exchange(REST_URL + requestUrl, HttpMethod.POST, request, byte[].class);
            // file name
            final String disposition = response.getHeaders().get("Content-Disposition").get(0);
            attachmentResult.put("PRINT_FILE_NAME", disposition.substring(disposition.substring(0, disposition.indexOf("=")).length() + 1, disposition.length()));
            attachmentResult.put("result", response.getBody());
            return attachmentResult;
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            LOGGER.error("getPrintInDoc : Error for the request: {}", REST_URL + requestUrl);
            throw new RestClientException(e.getResponseBodyAsString(), e);
        } catch (final RestClientException e) {
            throw new RestClientException("getPrintInDoc : Error for the request: " + REST_URL + requestUrl, e);
        }
    }

    public JSONArray loadDocFromCbx(final String requestUrl, final String loginId, final String workingDomainId) throws Exception {

        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();

        headers.add(AUTHORIZATION, "Bearer " + accessToken);
        headers.add("Connection", "keep-alive");
        headers.add("accept", "application/json");
        final HttpEntity<String> request = new HttpEntity<String>(headers);

        ResponseEntity<JSONArray> response = null;
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);

        try {
            response = restTemplate.exchange(REST_URL + requestUrl, HttpMethod.GET, request, JSONArray.class);

            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            LOGGER.error("loadDocFromCbx: {}", REST_URL + requestUrl);
            throw new RestClientException(e.getResponseBodyAsString(), e);
        } catch (final RestClientException e) {
            throw new RestClientException("Error for the request: " + REST_URL + requestUrl, e);
        }
    }

    public JSONObject saveDocIntoCbx(final JSONObject jsonObj, final String requestUrl, final String accessToken) throws Exception {

        final HttpHeaders headers = new HttpHeaders();

        headers.add(AUTHORIZATION, "Bearer " + accessToken);
        headers.add("Connection", "keep-alive");
        headers.add("Content-Type", "application/json");
        final HttpEntity<JSONObject> request = new HttpEntity<JSONObject>(jsonObj, headers);

        ResponseEntity<JSONObject> response = null;
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);

        try {
            response = restTemplate.exchange(REST_URL + requestUrl, HttpMethod.POST, request, JSONObject.class);
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            LOGGER.error("saveDocIntoCbx: {}", REST_URL + requestUrl);
            throw new RestClientException(e.getResponseBodyAsString(), e);
        } catch (final RestClientException e) {
            LOGGER.error("saveDocIntoCbx: {}", REST_URL + requestUrl);
            throw new RestClientException("ERROR.", e);
        }
    }

    public String post(final String url, final String body) throws RestAPIException {
        try {
            final String accessToken = getToken();
            final HttpHeaders headers = new HttpHeaders();
            headers.add(AUTHORIZATION, "Bearer " + accessToken);
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");
            final HttpEntity<String> request = body == null ? new HttpEntity<>(headers) : new HttpEntity<>(body, headers);
            ResponseEntity<String> response = null;
            final RestTemplate restTemplate = getRestTemplate();

            response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            return response.getBody();
        } catch (final Exception e) {
            LOGGER.error("post: {}", url);
            throw new RestAPIException(RestAPIException.ERROR_HTTP_CLIENT, "Failure call post request : " + url, e);
        }
    }

    public String put(final String url, final String body) throws RestAPIException {
        try {
            final String accessToken = getToken();
            final HttpHeaders headers = new HttpHeaders();
            headers.add(AUTHORIZATION, "Bearer " + accessToken);
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");
            final HttpEntity<String> request = body == null ? new HttpEntity<>(headers) : new HttpEntity<>(body, headers);
            ResponseEntity<String> response = null;
            final RestTemplate restTemplate = getRestTemplate();

            response = restTemplate.exchange(url, HttpMethod.PUT, request, String.class);
            return response.getBody();
        } catch (final Exception e) {
            LOGGER.error("put: {}", url);
            throw new RestAPIException(RestAPIException.ERROR_HTTP_CLIENT, "Failure call put request : " + url, e);
        }
    }


    public void delete(final String url) throws RestAPIException {
        try {
            final String accessToken = getToken();
            final HttpHeaders headers = new HttpHeaders();
            headers.add(AUTHORIZATION, "Bearer " + accessToken);
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");
            final HttpEntity<String> request = new HttpEntity<>(headers);
            final RestTemplate restTemplate = getRestTemplate();
            restTemplate.exchange(url, HttpMethod.DELETE, request, String.class);
        } catch (final Exception e) {
            LOGGER.error("delete: {}", url);
            throw new RestAPIException(RestAPIException.ERROR_HTTP_CLIENT, "Failure call delete request : " + url, e);
        }
    }

    public String get(final String url) throws RestAPIException {
        try {
            final String accessToken = getToken();
            final HttpHeaders headers = new HttpHeaders();
            headers.add(AUTHORIZATION, "Bearer " + accessToken);
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");
            final HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<String> response = null;
            final RestTemplate restTemplate = getRestTemplate();
            response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
            return response.getBody();
        } catch (final Exception e) {
            LOGGER.error("get: {}", url, e);
            throw new RestAPIException(RestAPIException.ERROR_HTTP_CLIENT, "Failure call get request : " + url, e);
        }
    }

    private RestTemplate getRestTemplate() {
        int socketTimeOut = SystemConfigManager.getInstance().getIntConfig(RestApiCallingBaseWrapper.SOCKET_TIMEOUT_CONFIG);
        if (socketTimeOut == -1) {
            socketTimeOut = SOCKET_TIME_OUT;
        }
        HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpComponentsClientHttpRequestFactory.setReadTimeout(socketTimeOut);
        final RestTemplate restTemplate = new RestTemplate(httpComponentsClientHttpRequestFactory);
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final StringHttpMessageConverter converter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
        restTemplate.getMessageConverters().add(0, converter);
        return restTemplate;
    }

    public JSONObject getInspectionReportByInspectionReportNo(final String inspectReportNo, final String loginId) throws DataException {
        final Criterion criterion = new Criterion("InspectReport");
        criterion.addRestriction(Restriction.eq("inspectReportNo", inspectReportNo));
        criterion.addRestriction(Restriction.eq("isLatest", true));

        final List<DynamicEntity> inspectionReport = DynamicEntityModel.findEntities(criterion, false);
        if (CollectionUtils.isEmpty(inspectionReport)) {
            LOGGER.info("cannot found inspection report by  inspect report no {}", inspectReportNo);
            return null;
        }
        final String refNo = inspectionReport.get(0).getReference();
        final ResponseEntity<JSONObject> response = generalApi(loginId, REST_URL + "inspect_reports/{refNo}", HttpMethod.GET, null, refNo);
        return response.getBody();
    }

    public JSONObject getInspectionBookingByRefNo(final String refNo, final String loginId) {
        final ResponseEntity<JSONObject> response = generalApi(loginId, REST_URL + "inspect_bookings/{refNo}", HttpMethod.GET, null, refNo);
        return response.getBody();
    }

    public void markAsInspectionBookingToScheduled(final String refNo, final String loginId) {
        generalApi(loginId, REST_URL + "inspect_bookings/{refNo}?status={status}", HttpMethod.PATCH, null, refNo, "scheduled");
    }

    public void markAsInspectionBooking(final String refNo, final String loginId, String status) {
        generalApi(loginId, REST_URL + "inspect_bookings/{refNo}?status={status}", HttpMethod.PATCH, null, refNo, status);
    }

    public JSONObject saveAndConfirmInspectionReport(final JSONObject ir, final String loginId) {
        final String accessToken = getToken(loginId, AuthenticationUtil.getUserWorkingDomainId());
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);
        final HttpEntity<JSONObject> body = new HttpEntity<>(ir, headers);
        final ResponseEntity<JSONObject> response = generalApi(null, REST_URL + "inspect_reports?action={action}", HttpMethod.POST, body, "saveAndConfirm");
        return response.getBody();
    }

    public void fetchBVInspectionReportEvent(final String loginId) {
        generalApi(loginId, REST_URL + "inspect_reports/event/create-report-from-bv", HttpMethod.GET, null);
    }

    public JSONObject doInheritance(final String actionId, final List<String> dmrNames, final List<String> sourceEntityIds, final String loginId) {
        final JSONObject requestBody = new JSONObject();
        requestBody.put("actionId", actionId);
        requestBody.put("dmrNames", dmrNames);
        requestBody.put("sourceEntityIds", sourceEntityIds);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + getToken(loginId, AuthenticationUtil.getUserWorkingDomainId()));
        final HttpEntity<String> request = new HttpEntity<>(requestBody.toString(), headers);
        final ResponseEntity<JSONObject> response = generalApi(null, REST_URL + "inheritance", HttpMethod.POST, request);
        return response.getBody();
    }

    public JSONObject uploadFile(final byte[] file, final String fileName, final String loginId) {
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + getToken(loginId, AuthenticationUtil.getUserWorkingDomainId()));

        final LinkedMultiValueMap<String, String> pdfHeaderMap = new LinkedMultiValueMap<>();
        pdfHeaderMap.add("Content-disposition", "form-data; name=file; filename=" + fileName);
        pdfHeaderMap.add("Content-type", "application/pdf");
        final HttpEntity<byte[]> doc = new HttpEntity<>(file, pdfHeaderMap);

        final LinkedMultiValueMap<String, Object> multipartReqMap = new LinkedMultiValueMap<>();
        multipartReqMap.add("file", doc);
        final HttpEntity<LinkedMultiValueMap<String, Object>> reqEntity = new HttpEntity<>(multipartReqMap, headers);
        final ResponseEntity<JSONObject> response = generalApi(null, REST_URL + "files/upload?type=attachment", HttpMethod.POST, reqEntity);
        return response.getBody();
    }

    public ResponseEntity<JSONObject> generalApi(final String loginId, final String url, final HttpMethod method, final HttpEntity requestTmp, final String... uriVariables) {
        HttpEntity request = requestTmp;
        final RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory());
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        if (request == null && loginId != null) {
            request = createHttpRequestEntity(AuthenticationUtil.getUserWorkingDomainId(), loginId);
        }
        return restTemplate.exchange(url, method, request, JSONObject.class, uriVariables);
    }

    public void batchUpdate(final String loginId, final String workingDomainId, final String requestUrl, final Map<String, String> params) {

        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();

        headers.add(AUTHORIZATION, "Bearer " + accessToken);
        headers.add("Connection", "keep-alive");
        headers.add("accept", "application/json");
        final HttpEntity<Map<String, String>> request = new HttpEntity<>(params, headers);

        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        try {
            restTemplate.exchange(REST_URL + requestUrl, HttpMethod.POST, request, Map.class);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            LOGGER.error("Error for the request: " + REST_URL + requestUrl);
            throw new RestClientException(e.getResponseBodyAsString(), e);
        } catch (final RestClientException e) {
            throw new RestClientException("Error for the request: " + REST_URL + requestUrl, e);
        }

    }

    public void indexSingleLineItem(final String refNo, final String module, final String loginId,
                                    final String workingDomainId, final String lineItemId) throws DataException {
        final long indexStart = System.currentTimeMillis();
        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final String mainPojoUrl = REST_URL + module + "/index/singlelineitem/" + refNo + "/" + lineItemId;
        final ResponseEntity<String> response = restTemplate.exchange(mainPojoUrl, HttpMethod.PUT, request, String.class);
        final long indexEnd = System.currentTimeMillis();
        LOGGER.info("index single line item by restapi server used=" + (indexEnd - indexStart) + ", refNo:" + refNo
                + ", module:" + module + ", lineItemId" + lineItemId + ", loginId:" + loginId + ", workingDomainId:" + workingDomainId);
        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("index single line item result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to index single line item, exception:" + response.getBody());
        }
    }


    public void partialCopyToItem(final String itemJson, final String url, final String loginId, final String domainId) {
        final String accessToken = getToken(loginId, domainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);
        final HttpEntity<String> request = new HttpEntity<>(itemJson, headers);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);

        try {
            final ResponseEntity<String> response = restTemplate.exchange(REST_URL + url, HttpMethod.POST, request, String.class);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            LOGGER.error("Error for the request: " + REST_URL + url);
            throw new RestClientException(e.getResponseBodyAsString(), e);
        } catch (final RestClientException e) {
            throw new RestClientException("Error for the request: " + REST_URL + url, e);
        }
    }

    public void recalculateCpmPlanDate(final String moduleId, final String refNo, final String selectedRefDocRefNos, final int calculationType, final String loginId, final String workingDomainId) {
        final String accessToken = getToken(loginId, workingDomainId);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, "bearer " + accessToken);
        final RestTemplate restTemplate = new RestTemplate();
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final String mainPojoUrl = REST_URL + "cpm/recalculateCpmPlanDate/" + moduleId + "/" + refNo + "?calculationType=" + calculationType + "&selectedRefDocRefNos=" + selectedRefDocRefNos;
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.GET, request, String.class);
        final long indexEnd = System.currentTimeMillis();

        final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
        LOGGER.info("recalculateCpmPlanDate cpm result :" + response.getStatusCode());
        if (result) {
            LOGGER.error("Fail to recalculateCpmPlanDate cpm, exception:" + response.getBody());
        }
    }

    public String putByUser(final String url, final String body, final DynamicEntity user) throws RestAPIException {
        try {
            final String accessToken = getToken(user.getString(User.LOGIN_ID), AuthenticationUtil.getUserWorkingDomainId());
            final HttpHeaders headers = new HttpHeaders();
            headers.add(AUTHORIZATION, "Bearer " + accessToken);
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");
            final HttpEntity<String> request = body == null ? new HttpEntity<>(headers) : new HttpEntity<>(body, headers);
            ResponseEntity<String> response = null;
            final RestTemplate restTemplate = getRestTemplate();

            response = restTemplate.exchange(url, HttpMethod.PUT, request, String.class);
            return response.getBody();
        } catch (final Exception e) {
            LOGGER.error("put: {}", url);
            throw new RestAPIException(RestAPIException.ERROR_HTTP_CLIENT, "Failure call put request : " + url, e);
        }
    }

    public String receiveVizionData(final String url, final String requestBody) {
        String loginId = AuthenticationUtil.getUser().getUserEntity().getString(User.LOGIN_ID);

        final String accessToken = getToken(loginId, AuthenticationUtil.getUserWorkingDomainId());
        final HttpHeaders headers = new HttpHeaders();
        headers.add(AUTHORIZATION, "Bearer " + accessToken);
        headers.add("Connection", "keep-alive");
        headers.add("Content-Type", "application/json");
        final HttpEntity<String> request = requestBody == null ? new HttpEntity<>(headers) : new HttpEntity<>(requestBody, headers);

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setReadTimeout(180000);
        requestFactory.setConnectTimeout(180000);
        final RestTemplate restTemplate = new RestTemplate(requestFactory);
        RestTemplateHelper.addPrintLogInterceptor(restTemplate);
        LOGGER.info("Command listener receiveVizionData url:" + url);
        final ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        return response.getBody();
    }

    public String postMilestones(final String url, final String body, final Map<String, String> params) throws RestAPIException {
        try {
            final String accessToken = getToken();
            final HttpHeaders headers = new HttpHeaders();
            headers.add(AUTHORIZATION, "Bearer " + accessToken);
            headers.add("Connection", "keep-alive");
            headers.add("Content-Type", "application/json;charset=UTF-8");
            final HttpEntity<String> request = body == null ? new HttpEntity<>(headers) : new HttpEntity<>(body, headers);
            ResponseEntity<String> response = null;
            final RestTemplate restTemplate = getRestTemplate();

            response = restTemplate.exchange(url, HttpMethod.POST, request, String.class, params);
            return response.getBody();
        } catch (final Exception e) {
            LOGGER.error("post: {}", url);
            throw new RestAPIException(RestAPIException.ERROR_HTTP_CLIENT, "Failure call post request : " + url, e);
        }
    }

    public void handleInvitationStatus(final String workingDomainId, final String pathUrl, 
    		final String email, final String loginId) {
    	 LOGGER.info("send updateInvitationStatus by restapi server used start email " + email);
         final long sendDocStart = System.currentTimeMillis();
         final String accessToken = getToken(loginId, workingDomainId);
         LOGGER.info("send updateInvitationStatus by restapi server used start accessToken" + accessToken);
         final HttpHeaders headers = new HttpHeaders();
         headers.add(RestApiClient.CONTENT_TYPE, "application/json;charset=UTF-8");
         headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);
         final HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
         httpRequestFactory.setConnectTimeout(100000);
         httpRequestFactory.setReadTimeout(300000);
         final RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
         final HttpEntity<String> request = new HttpEntity<String>(headers);
         final String mainPojoUrl = REST_URL + "invitations/" + pathUrl + "/" + email;
         LOGGER.info("updateInvitationStatus document by restapi server module: invitationRequest, loginId: " + loginId + ", workingDomainId:"
                 + workingDomainId +  ", pathUrl: " + pathUrl + ", email: " + email);
         final ResponseEntity<String> response =
                 restTemplate.exchange(mainPojoUrl, HttpMethod.POST, request, String.class);
         final long sendDocEnd = System.currentTimeMillis();
         final boolean result = HttpStatus.UNPROCESSABLE_ENTITY == response.getStatusCode();
         LOGGER.info("update Invitation Status by restapi server used=" + (sendDocEnd - sendDocStart));
         if (result) {
             LOGGER.error("Fail to update Invitation Status, exception:" + response.getBody());
         }
    }

    public boolean loadDocByRestapiWithAccessRight(final String moduleId, String loginId, final String workingDomainId,
                                                   final String refNo, final Long version) {
        LOGGER.info("send loadDocByRestapiWithAccessRight by restapi server used start: receiveStart: refNo" + refNo);
        final long sendDocStart = System.currentTimeMillis();
        final String accessToken = getToken(loginId, workingDomainId);
        LOGGER.info("send loadDocByRestapiWithAccessRight by restapi server used start: receiveStart: accessToken" + accessToken);
        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, "application/json;charset=UTF-8");
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);
        final HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectTimeout(100000);
        httpRequestFactory.setReadTimeout(300000);
        final RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
        final HttpEntity<String> request = new HttpEntity<String>(headers);
        final String restModule = CbxSearchClientConstants.esModuleMap.get(moduleId) == null ? moduleId + 's' : CbxSearchClientConstants.esModuleMap.get(moduleId);
        String refNoUrl = refNo;
        if (version != null) {
            refNoUrl = refNoUrl + "/" + version;
        }
        final String mainPojoUrl = REST_URL + restModule + "/" + refNoUrl;
        LOGGER.info("load document by restapi server module:" + moduleId + ", loginId:" + loginId + ", workingDomainId:"
                + workingDomainId + ", refNoUrl: " + refNoUrl);
        final ResponseEntity<String> response =
                restTemplate.exchange(mainPojoUrl, HttpMethod.GET, request, String.class);
        final long sendDocEnd = System.currentTimeMillis();
        final boolean result = HttpStatus.OK == response.getStatusCode();
        LOGGER.info("load document by restapi server used=" + (sendDocEnd - sendDocStart));
        if (!result) {
            LOGGER.error("Fail to load doc from restapi, exception:" + response.getBody());
        }
        return result;
    }

    public JSONObject getBatchMarkAsNotificationMsgFieldLabel(final List<String> msgKeyList, final String loginId, final String domainId, final String module, final String locale) {
        final String accessToken = getToken(loginId, AuthenticationUtil.getUserWorkingDomainId());

        final HttpHeaders headers = new HttpHeaders();
        headers.add(RestApiClient.CONTENT_TYPE, RestApiClient.APPLICATION_JSON);
        headers.add(RestApiClient.AUTHORIZATION, AUTH_BEARER + accessToken);

        final HttpEntity<List<String>> body = new HttpEntity<>(msgKeyList, headers);
        final ResponseEntity<JSONObject> response = generalApi(loginId, REST_URL + "batchMarkAs/fieldLabels?domainId={domainId}&module={module}&locale={locale}", HttpMethod.POST, body, domainId, module, locale);

        return response.getBody();
    }
}
