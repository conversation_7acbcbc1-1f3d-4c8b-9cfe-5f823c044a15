// Copyright (c) 1998-2024 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.15.9.0P.04.0
// ============================================================================
// CHANGE LOG
// PEPL.15.9.0P.04.0 : 2025-09-01, Gwyn.gao, PEPL-3429
// CBX 14.14 GA : 2024-07-10, jay.sun, PCHI-1295
// CBX 13.17 GA : 2023-08-15, luson.lu, PGS-2853
// CBX 9.2.0 GA : 2019-01-16, lewis.liu, KME-4330
// CNT.8.1.0 : 2018-01-10, stark, CNT-35364
// CNT.6.12.0 : 2017-12-15, winton.fang, CNT-34963
// CNT.6.11 : 2017-11-03, alvin.wan, CNT-34198
// CNT.6.9.0 : 2017-09-12, ives.li, CNT-33148
// CNT.6.8 : 2017-08-17, jim.huang, CNT-32540
// CNT.6.7   : 2017-07-12, francis.liang, CNT-31298
// CNT.6.5.0 : 2017-05-08, ives.li, CNT-30335
// CNT.6.4.0 : 2017-04-19, jim.huang, CNT-28125
// CNT.6.4.0 : 2017-03-31, jim.huang, CNT-29279
// CNT.6.3.0 : 2017-03-21, denny.deng, CNT-29053
// CNT.6.2.0 : 2017-02-16, jim.huang, CNT-28455
// CNT.6.1.0 : 2017-01-20, lance.lu, CNT-28007
// CNT.6.1 : 2017-01-19, jim.huang, CNT-27991
// CNT.6.1 : 2017-01-18, arthur.ou, CNT-27723
// CNT.6.1.0 : 2017-01-17, norman.yang, CNT-27943
// CNT.6.1.0 : 2017-01-16, denny.deng, CNT-27958
// CNT.6.1 : 2017-01-12, anthony.chen, CNT-27921
// CNT.6.1.0 : 2017-01-09, arthur.ou, CNT-27713
// CNT.6.1.0 : 2017-01-06, denny.deng, CNT-27820
// CNT.6.0.c : 2016-12-19, jim.huang, CNT-27360
// CNT.6.0.c : 2016-12-19, mark.lin, CNT-27529
// CNT.6.0.c : 2016-12-13, leon.lin, CNT-27309
// CNT.6.0.c : 2016-12-14, jim.huang, CNT-27127
// CNT.6.0c : 2016-12-12, norman.yang, CNT-27291
// CNT.6.0.c : 2016-12-03, jim.huang/norman.yang, CNT-27102
// CNT.6.0.c : 2016-11-12, jim.huang, CNT-26821 creation
// ============================================================================

package com.cbx.ws.rest.client.cpm;

import com.cbx.ws.rest.data.constants.MilestoneConstants;
import com.cbx.ws.rest.util.CpmDateUtil;
import com.core.cbx.common.type.DateTime;
import com.core.cbx.conf.service.SystemConfigManager;
import com.core.cbx.data.codelist.service.CodelistManager;
import com.core.cbx.data.constants.Codelist;
import com.core.cbx.data.constants.CpmTask;
import com.core.cbx.data.constants.ExceptionConstants;
import com.core.cbx.data.constants.System;
import com.core.cbx.data.def.EntityDefManager;
import com.core.cbx.data.def.EntityDefModel;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.DynamicEntityImp;
import com.core.cbx.data.entity.DynamicRefEntityProxy;
import com.core.cbx.data.entity.EntityConstants;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.security.AuthenticationUtil;
import com.core.cbx.security.user.CntUser;
import com.core.cbx.util.CpmUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public final class MilestoneConverter {

    public static final String ENTITY_SELECTION_FIELD = "SelectionField";
    public static final String DEFAULT_DUE_DAY_SOON = "3";
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final JsonNodeFactory factory = JsonNodeFactory.instance;
    private static final Logger LOGGER = LogManager.getLogger(MilestoneConverter.class);
    private static final int dateStringStart = 0;
    private static final int dateStringEnd = 10;
    private static final String PREFIX_CUST_DATE = "custDate";
    private static final String PREFIX_CUST_TEXT = "custText";
    private static final String PREFIX_CUST_MEMOTEXT = "custMemoText";
    private static final String PREFIX_CUST_DECIMAL = "custDecimal";
    private static final String PREFIX_CUXT_NUMBER = "custNumber";
    private static final String TIME_ZERO = " 00:00:00.000000000";
    private static final String POSTFIX_CODELIST_VERSION = "Ver";
    private static final String CPM_ASSIGNEES_LIST = "cpmAssigneesList";
    private static final TypeReference<List<JsonNode>> jsonlist = new TypeReference<List<JsonNode>>() {
    };

    /**
     * field :
     * 1.color code
     * 2.parent_id (cpmId)
     * 3.taskId / id (code)
     * 4.taskname (name)
     * 5.domainId
     * 6.sequence
     * 7.planStart (plannedStartDate)
     * 8.planEnd (plannedEndDate)
     * 9.actualStart (endDate)
     * 10.status
     * 11.percentage
     * 12.quantity
     * 13.description
     * 14.reasonId (reason)
     * 15.reasonDescription
     * 16.cpmTaskAssignees (assignees)
     * 17.dailyCheckConditions
     * 18.createdBy
     * 19.createdOn
     * 20.updatedBy
     * 21.updatedOn
     * 22.sttachment
     */

    private MilestoneConverter() {
    }


    public static String handleToJson(final DynamicEntity milestone, final Boolean isUpdate)
            throws ParseException, IOException {
        final JsonNode node = handleToJsonNode(milestone, isUpdate);
        return node == null ? null : node.toString();
    }

    /**
     * @param templTasks
     * @return
     * @throws ParseException
     * @throws IOException
     */
    @SuppressWarnings("unchecked")
    public static JsonNode handleToJsonNode(final DynamicEntity milestone, final Boolean isUpdate)
            throws ParseException, IOException {
        final ObjectNode milestoneNode = new ObjectNode(factory);

        final Map<String, Object> serialize = getMilestoneSerializedMap(milestone, isUpdate);

        if (serialize == null || serialize.isEmpty()) {
            return null;
        }
        final ObjectNode properties = new ObjectNode(factory);
        for (final Map.Entry<String, Object> entry : serialize.entrySet()) {
            // update should support null update, so if update case, pass it
            if (entry.getValue() == null && !isUpdate) {
                continue;
            }
            if (CpmTask.COLOR_CODE.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.COLOR_CODE, (String) entry.getValue());
                continue;
            }
            if (CpmTask.PARENT_ID.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.CPM_ID, (String) entry.getValue());
                continue;
            }
            if (CpmTask.TASK_ID.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.CODE, (String) entry.getValue());
                continue;
            }
            if (CpmTask.TASK_NAME.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.NAME, (String) entry.getValue());
                continue;
            }
            if (CpmTask.DOMAIN_ID.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.DOMAIN_ID, (String) entry.getValue());
                continue;
            }
            if (CpmTask.SEQUENCE.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.SEQUENCE,
                        (entry.getValue() == null) ? null : entry.getValue().toString());
                continue;
            }
            if (CpmTask.PLAN_START.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.PLANNED_START_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));
                continue;
            }
            if (CpmTask.PLAN_END.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.PLANNED_END_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));

                milestoneNode.put(MilestoneConstants.PLANNED_DURATION,
                        serialize.get(MilestoneConstants.PLANNED_DURATION) == null ? null : (Long) serialize.get(MilestoneConstants.PLANNED_DURATION));

                continue;
            }
            if (CpmTask.ACTUAL_START.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.START_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));
                continue;
            }
            if (CpmTask.ACTUAL_END.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.END_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));

                milestoneNode.put(MilestoneConstants.DURATION,
                        serialize.get(MilestoneConstants.DURATION) == null ? null : (Long) serialize.get(MilestoneConstants.DURATION));

                continue;
            }
            if (CpmTask.STATUS.equals(entry.getKey())) {
                codelistToJson(MilestoneConstants.STATUS, entry, milestoneNode, properties);
                continue;
            }
            if (CpmTask.PERCENTAGE.equals(entry.getKey())) {
                final BigDecimal percentage = (BigDecimal) entry.getValue();
                milestoneNode.put(MilestoneConstants.PERCENTAGE, percentage == null ? null : percentage.doubleValue());
                continue;
            }
            if (CpmTask.QUANTITY.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.QUANTITY, (Long) entry.getValue());
                continue;
            }
            if (CpmTask.DESCRIPTION.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.DESCRIPTION, (String) entry.getValue());
                continue;
            }
            if (CpmTask.REASON_ID.equals(entry.getKey())) {
                codelistToJson(MilestoneConstants.REASON, entry, milestoneNode, properties);
                continue;
            }
            if (CpmTask.REASON_DESCRIPTION.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.REASON_DESCRIPTION, (String) entry.getValue());
                continue;
            }
            if (CpmTask.CPM_TASK_ASSIGNEES.equals(entry.getKey())) {
                final List<DynamicEntity> docs = (List<DynamicEntity>) entry.getValue();
                final JsonNode assignees = docs == null ? new ObjectMapper().createArrayNode()
                        : AssigneeConverter.handleToListJson(docs, isUpdate);
                milestoneNode.set(MilestoneConstants.ASSIGNEES, assignees);
                continue;
            }
            if (MilestoneConstants.DAILY_CHECK_CONDITIONS.equals(entry.getKey())) {
                final List<Map<String, Object>> docs = (List<Map<String, Object>>) entry.getValue();
                final JsonNode dailyCheckConditions = docs == null ? null
                        : DailyCheckConditionConverter.handleToListJson(docs);
                milestoneNode.set(MilestoneConstants.DAILY_CHECK_CONDITIONS, dailyCheckConditions);
                continue;
            }
            if (CpmTask.CREATED_BY.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.CREATED_BY, (String) entry.getValue());
                continue;
            }
            if (CpmTask.CREATED_ON.equals(entry.getKey())) {
                if (entry.getValue() == null) {
                    milestoneNode.set(MilestoneConstants.CREATED_ON, null);
                } else {
                    final String value = entry.getValue().toString();
                    final String zoneTime = CpmDateUtil.toCpmStr(value);
                    milestoneNode.put(MilestoneConstants.CREATED_ON, zoneTime);
                }
                continue;
            }
            if (CpmTask.UPDATE_USER_NAME.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.UPDATED_BY, (String) entry.getValue());
                continue;
            }
            if (CpmTask.UPDATED_ON.equals(entry.getKey())) {
                if (entry.getValue() == null) {
                    milestoneNode.set(MilestoneConstants.UPDATED_ON, null);
                } else {
                    final String value = entry.getValue().toString();
                    final String zoneTime = CpmDateUtil.toCpmStr(value);
                    milestoneNode.put(MilestoneConstants.UPDATED_ON, zoneTime);
                }
                continue;
            }

            if (entry.getKey().equals(CpmTask.ATTACHMENT)) {
                final DynamicEntity attachment = (DynamicEntity) entry.getValue();
                final JsonNode attachmentNode = attachment == null ? new ObjectMapper().createArrayNode()
                        : AttachmentConverter.hangleToJson(attachment);
                properties.set(entry.getKey(), attachmentNode);
                continue;
            }
            if (entry.getKey().equals(CpmTask.FIELD_VALUE)) {
                properties.put(entry.getKey(), entry.getValue().toString());
                continue;
            }
            if (entry.getKey().equals(CpmTask.REF_ENTITY)) {
                properties.put(entry.getKey(), entry.getValue().toString());
                continue;
            }
            if (entry.getKey().equals(CpmTask.REF_FIELD_ID)) {
                properties.put(entry.getKey(), entry.getValue().toString());
                continue;
            }
            if (CpmTask.CPM_TASK_ADV_SECURITYS.equals(entry.getKey())) {
                final List<DynamicEntity> advancedSecuritysList = (List<DynamicEntity>) entry.getValue();
                final JsonNode advancedSecuritysNode = advancedSecuritysList == null ? null
                        : AdvancedSecurityConverter.handleToListJson(advancedSecuritysList, isUpdate);
                properties.set(entry.getKey(), advancedSecuritysNode);
                continue;
            }
            if (entry.getKey().equals(MilestoneConstants.PROPERTIES)) {
                final Map<String, String> propertiesStr = (Map<String, String>) entry.getValue();
                for (final Map.Entry<String, String> propertyEntry : propertiesStr.entrySet()) {
                    properties.put(propertyEntry.getKey(), propertyEntry.getValue());
                }
                continue;
            }
            if (EntityDefModel.isCustomFieldId(entry.getKey())) {
                final String custFieldId = entry.getKey();
                if (entry.getValue() == null) {
                    properties.set(custFieldId, null);
                } else if (custFieldId.startsWith(EntityConstants.PTY_POSTFIX_CUST_CODELIST)
                        || custFieldId.startsWith(EntityConstants.PTY_POSTFIX_CUST_HCL)) {
                    final DynamicEntity entity = (DynamicEntity) milestone.get(custFieldId);
                    properties.set(custFieldId, CustomFieldConverter.handleToJson(entity));
                } else {
                    properties.put(custFieldId, milestone.get(custFieldId).toString());
                }
            }
        }

        if (properties.size() != 0) {
            milestoneNode.set(MilestoneConstants.PROPERTIES, properties);
        }
        return milestoneNode;
    }

    /**
     * @param milestone
     * @return
     * @throws IOException
     * @throws JsonProcessingException
     * @throws ParseException
     */
    public static DynamicEntity handleToEntity(final String milestone) throws IOException, ParseException {
        final DynamicEntity doc = new DynamicEntityImp();
        doc.setEntityName(CpmTask.ENTITY_NAME_CPM_TASK);
        doc.setEntityVersion(1);
        handleToEntityWithDoc(milestone, doc);
        doc.setInitiated();
        return doc;
    }

    public static List<JsonNode> handleToListJson(final List<DynamicEntity> milestones, final Boolean isUpdate)
            throws ParseException, IOException {
        final List<JsonNode> result = new ArrayList<>();
        for (final DynamicEntity milestone : milestones) {
            if (milestone.isDeletedEntity()) {
                continue;
            }
            final JsonNode node = handleToJsonNode(milestone, isUpdate);
            if (node != null) {
                result.add(node);
            }
        }
        return result;
    }

    public static List<DynamicEntity> handleToListDynamicEntity(final String json) throws IOException, ParseException {
        final List<DynamicEntity> result = new ArrayList<>();
        final List<JsonNode> jsonList = mapper.readValue(json, jsonlist);
        for (final JsonNode node : jsonList) {
            final DynamicEntity doc = handleToEntity(node.toString());
            result.add(doc);
        }
        return result;
    }

    public static List<DynamicEntity> handleToListDynamicEntityWithDoc(final String json,
                                                                       final List<DynamicEntity> cpmTasks) throws IOException, ParseException {
        final List<JsonNode> jsonList = mapper.readValue(json, jsonlist);
        final Map<String, Integer> idMap = new HashMap<>();
        for (int i = 0; i < jsonList.size(); i++) {
            final String milestoneCode = jsonList.get(i).get(MilestoneConstants.CODE).asText();
            idMap.put(milestoneCode, i);
        }
        for (int i = 0; i < cpmTasks.size(); i++) {
            final DynamicEntity cpmTask = cpmTasks.get(i);
            final String taskId = cpmTask.getString(CpmTask.TASK_ID);
            final int position = idMap.get(taskId);
            final JsonNode node = jsonList.get(position);
            handleToEntityWithDoc(node.toString(), cpmTask);
        }
        return cpmTasks;
    }

    public static DynamicEntity handleToEntityWithDoc(final String json, final DynamicEntity doc)
            throws ParseException, IOException {
        final JsonNode milestoneNode = CpmConverter.mapper.readTree(json);
        final Iterator<String> keys = milestoneNode.fieldNames();
        while (keys.hasNext()) {
            final String key = keys.next();
            if (milestoneNode.get(key) == null
                    || StringUtils.equalsIgnoreCase(milestoneNode.get(key).toString(), "null")) {
                continue;
            }
            if (MilestoneConstants.CPM_ID.equals(key)) {
                doc.put(CpmTask.PARENT_ID, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.CODE.equals(key)) {
                doc.put(CpmTask.ID, milestoneNode.get(key).textValue());
                doc.put(CpmTask.TASK_ID, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.COLOR_CODE.equals(key)) {
                doc.put(CpmTask.COLOR_CODE, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.NAME.equals(key)) {
                doc.put(CpmTask.TASK_NAME, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.DOMAIN_ID.equals(key)) {
                doc.put(CpmTask.DOMAIN_ID, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.SEQUENCE.equals(key)) {
                doc.put(CpmTask.SEQUENCE, Long.valueOf(milestoneNode.get(key).textValue()));
                continue;
            }
            if (MilestoneConstants.PLANNED_START_DATE.equals(key)) {
                final DateTime dateTime = new DateTime(milestoneNode.get(key).textValue() + TIME_ZERO);
                doc.put(CpmTask.PLAN_START, dateTime);
                continue;
            }
            if (MilestoneConstants.PLANNED_END_DATE.equals(key)) {
                final DateTime dateTime = new DateTime(milestoneNode.get(key).textValue() + TIME_ZERO);
                doc.put(CpmTask.PLAN_END, dateTime);
                continue;
            }
            if (MilestoneConstants.START_DATE.equals(key)) {
                final DateTime dateTime = new DateTime(milestoneNode.get(key).textValue() + TIME_ZERO);
                doc.put(CpmTask.ACTUAL_START, dateTime);
                continue;
            }
            if (MilestoneConstants.END_DATE.equals(key)) {
                final DateTime dateTime = new DateTime(milestoneNode.get(key).textValue() + TIME_ZERO);
                doc.put(CpmTask.ACTUAL_END, dateTime);
                continue;
            }
            if (MilestoneConstants.STATUS.equals(key)) {
                jsonToCodelist(key, CpmTask.STATUS, milestoneNode, doc);
                continue;
            }
            if (MilestoneConstants.PERCENTAGE.equals(key)) {
                final Double value = milestoneNode.get(key).asDouble();
                final BigDecimal percentage = new BigDecimal(value);
                doc.put(CpmTask.PERCENTAGE, percentage);
                continue;
            }
            if (MilestoneConstants.QUANTITY.equals(key)) {
                doc.put(CpmTask.QUANTITY, milestoneNode.get(key).asLong());
                continue;
            }
            if (MilestoneConstants.DESCRIPTION.equals(key)) {
                doc.put(CpmTask.DESCRIPTION, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.REASON.equals(key)) {
                jsonToCodelist(key, CpmTask.REASON_ID, milestoneNode, doc);
                continue;
            }
            if (MilestoneConstants.REASON_DESCRIPTION.equals(key)) {
                doc.put(CpmTask.REASON_DESCRIPTION, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.PROPERTIES.equals(key)) {
                final JsonNode map = milestoneNode.get(key);
                final Iterator<String> items = map.fieldNames();
                while (items.hasNext()) {
                    final String item = items.next();
                    if (CpmTask.ATTACHMENT.equals(item)) {
                        final DynamicEntity attachment = AttachmentConverter.handleToEntity(map);
                        doc.put(CpmTask.ATTACHMENT, attachment);
                        continue;
                    }
                    if (CpmTask.CPM_TASK_ADV_SECURITYS.equals(item)) {
                        final ArrayNode advancedSecuritys = map.get(item).isNull() ? new ArrayNode(factory) : (ArrayNode) map.get(item);
                        final List<DynamicEntity> advancedSecuritysList =
                                AdvancedSecurityConverter.handleToListEntity(advancedSecuritys);
                        doc.put(CpmTask.CPM_TASK_ADV_SECURITYS, advancedSecuritysList);
                        continue;
                    }
                    if (item.startsWith(PREFIX_CUST_DATE)) {
                        final DateTime dateTime = new DateTime(map.get(item).textValue());
                        doc.put(item, dateTime);
                        continue;
                    }
                    if (item.startsWith(PREFIX_CUST_TEXT) || item.startsWith(PREFIX_CUST_MEMOTEXT)) {
                        doc.put(item, map.get(item).textValue());
                        continue;
                    }
                    if (item.startsWith(PREFIX_CUST_DECIMAL)) {
                        final String value = map.get(item).textValue();
                        final BigDecimal mathValue = new BigDecimal(value);
                        doc.put(item, mathValue);
                        continue;
                    }
                    if (item.startsWith(PREFIX_CUXT_NUMBER)) {
                        doc.put(item, map.get(item).asLong());
                        continue;
                    }
                    if (item.startsWith(EntityConstants.PTY_POSTFIX_CUST_CODELIST)
                            || item.startsWith(EntityConstants.PTY_POSTFIX_CUST_HCL)) {
                        doc.put(item, CustomFieldConverter.handleToEntity(map, item, doc.getString(CpmTask.TASK_ID)));
                        continue;
                    }
                    if (StringUtils.equals(item, CpmTask.FIELD_VALUE)) {
                        doc.put(item, map.get(item).asText());
                        continue;
                    }
                    if (StringUtils.equals(item, CpmTask.REF_ENTITY)) {
                        doc.put(item, map.get(item).asText());
                        continue;
                    }
                    if (StringUtils.equals(item, CpmTask.REF_FIELD_ID)) {
                        doc.put(item, map.get(item).asText());
                        continue;
                    }
                    doc.put(item, map.get(item).textValue());
                }
                continue;
            }
            if (MilestoneConstants.ASSIGNEES.equals(key)) {
                final ArrayNode assignees = (ArrayNode) milestoneNode.get(key);
                final List<DynamicEntity> cpmTaskAssignee = AssigneeConverter.handleToListEntity(assignees);
                doc.put(CpmTask.CPM_TASK_ASSIGNEES, cpmTaskAssignee);
                continue;
            }
            if (MilestoneConstants.CREATED_BY.equals(key)) {
                doc.put(CpmTask.CREATED_BY, milestoneNode.get(key).textValue());
                continue;
            }
            if (MilestoneConstants.CREATED_ON.equals(key)) {
                final String cpmZone = milestoneNode.get(key).textValue();
                final String value = CpmDateUtil.cpmZoneToCbxDateTimeStr(cpmZone);
                doc.put(CpmTask.CREATED_ON, new DateTime(value));
                continue;
            }
            if (MilestoneConstants.UPDATED_ON.equals(key)) {
                final String cpmZone = milestoneNode.get(key).textValue();
                final String value = CpmDateUtil.cpmZoneToCbxDateTimeStr(cpmZone);
                doc.put(CpmTask.UPDATED_ON, new DateTime(value));
                continue;

            }
            if (MilestoneConstants.UPDATED_BY.equals(key)) {
                doc.put(CpmTask.UPDATED_BY, milestoneNode.get(key).textValue());
                continue;
            }
        }
        doc.clearAllModifiedInfo();
        return doc;
    }

    public static DynamicEntity handleToEntityForCommandString(final String milestone) throws IOException, ParseException {
        final DynamicEntity doc = new DynamicEntityImp();
        doc.setEntityName(CpmTask.ENTITY_NAME_CPM_TASK);
        doc.setEntityVersion(1);
        handleToEntityWithDoc(milestone, doc);
        doc.setInitiated();
        return doc;
    }

    private static void codelistToJson(final String serverKey, final Entry<String, Object> entry,
                                       final ObjectNode node, final ObjectNode properties) {
        final DynamicEntity codelist = (DynamicEntity) entry.getValue();
        if (codelist == null) {
            node.set(serverKey, null);
            return;
        }

        /**
         * same with @link{EntitySaver#setCachedFieldValues}
         */
        DynamicEntity codelistBook = null;
        final String codelistBookId = codelist.getString(Codelist.PARENT_ID);
        if (codelistBookId != null) {
            try {
                codelistBook = CodelistManager.loadCodelistById(codelistBookId);
                if (codelistBook != null) {
                    final String bookName = codelistBook.getString(Codelist.NAME);
                    if (bookName != null) {
                        codelistBook = CodelistManager.loadCodelistByDate(bookName,
                                AuthenticationUtil.getUserWorkingDomainId(), DateTime.now());
                    }
                }
            } catch (final DataException e) {
                LOGGER.error("Message: {}; Description: {}", ExceptionConstants.DATA_EXCEPTION_000001, "", e);
            }

        }
        if (codelistBook == null) {
            LOGGER.info("can't find codelist's codelistBook, codelist = {}", codelist);
        }
        final long version = codelistBook == null ? 0L : codelistBook.getVersion();
        final String code = entry.getValue() == null ? null : codelist.getString(Codelist.CODE);
        node.put(serverKey, code);
        properties.put(serverKey + POSTFIX_CODELIST_VERSION, version);
    }

    private static void jsonToCodelist(final String serverKey, final String cbxKey,
                                       final JsonNode node, final DynamicEntity doc) {
        final String serevrVersion = serverKey + POSTFIX_CODELIST_VERSION;
        final JsonNode propertiesNode = node.get(MilestoneConstants.PROPERTIES);
        final String id = node.get(serverKey).textValue();
        DynamicEntity codelist = null;
        final JsonNode versionNode = (propertiesNode == null) ? null : propertiesNode.get(serevrVersion);
        if (versionNode == null) {
            try {
                final String bookname = EntityDefManager
                        .getFieldDefinition(CpmTask.ENTITY_NAME_CPM_TASK, cbxKey).getData1();
                final String workingDomain = AuthenticationUtil.getUserWorkingDomainId();
                codelist = CodelistManager.loadCodelistItem(bookname, workingDomain, id);
                if (codelist == null) {
                    final DynamicRefEntityProxy proxy = new DynamicRefEntityProxy(Codelist.ENTITY_NAME_CODELIST, id,
                            null, null, null);
                    codelist = proxy.getEntity();
                }
            } catch (final DataException e) {
                LOGGER.error("Message: {}; Description: {}", ExceptionConstants.DATA_EXCEPTION_000001, "fail to load codelist", e);
            }
        } else {
            final Long version = versionNode.asLong();
            try {
                final String bookname = EntityDefManager
                        .getFieldDefinition(CpmTask.ENTITY_NAME_CPM_TASK, cbxKey).getData1();
                final String workingDomain = AuthenticationUtil.getUserWorkingDomainId();
                codelist = CodelistManager.loadCodelistItem(bookname, workingDomain, version.intValue(),
                        id);
            } catch (final DataException e) {
                LOGGER.error("Message: {}; Description: {}", ExceptionConstants.DATA_EXCEPTION_000001, "fail to load codelist", e);
            }
        }
        doc.put(cbxKey, codelist);
    }


    /**
     * for milestone, return empty map when no data need to update
     *
     * @param doc
     * @param isUpdate
     * @return
     */
    private static Map<String, Object> getMilestoneSerializedMap(final DynamicEntity milestone, final Boolean isUpdate) {
        Map<String, Object> serializedMap = new HashMap<>();
        if (isUpdate) {
            serializedMap = CpmUtil.getMilestoneDeltaData(milestone);
            applyUpdateMilestoneInfo(serializedMap, milestone);
        } else {
            serializedMap = milestone;
            applyCreateMilestoneInfo(serializedMap, milestone);
        }
        return serializedMap;
    }

    /**
     * add some information that modify map don't contains
     *
     * @param serializedMap
     */
    private static void applyUpdateMilestoneInfo(final Map<String, Object> serializedMap,
                                                 final DynamicEntity milestone) {
        if (serializedMap == null || serializedMap.isEmpty()) {
            // no need to post this milestone to cpm server
            return;
        }
        final CntUser user = AuthenticationUtil.getUser();
        final String cpmId = (String) milestone.get(CpmTask.PARENT_ID);
        final String taskId = (String) milestone.get(CpmTask.TASK_ID);
        serializedMap.put(CpmTask.PARENT_ID, cpmId);
        serializedMap.put(CpmTask.TASK_ID, taskId);
        if (user != null) {
            serializedMap.put(EntityConstants.PTY_UPDATE_USER, user.getUsername());
            serializedMap.put(EntityConstants.UPDATE_USER_NAME, user.getDisplayUsername());
            serializedMap.put(EntityConstants.PTY_UPDATED_ON, DateTime.now());
            serializedMap.put(CpmTask.UPDATED_BY, user.getDisplayUsername());
        }
    }

    /**
     * add some information that original milestone don't contains, but is need for cpm sever.
     * like the dueSoon
     *
     * @param serializedMap
     * @param milestone
     */
    private static void applyCreateMilestoneInfo(final Map<String, Object> serializedMap,
                                                 final DynamicEntity milestone) {
        String dueSoonDays = SystemConfigManager.getInstance().getConfigValue(CpmUtil.DUESOON_DAYS_SYSTEM_CONFIG);
        if (StringUtils.isEmpty(dueSoonDays)) {
            dueSoonDays = DEFAULT_DUE_DAY_SOON;
        }
        final Map<String, String> properties = new HashMap<>();
        properties.put(MilestoneConstants.DUE_SOON_DAYS, dueSoonDays);
        serializedMap.put(MilestoneConstants.PROPERTIES, properties);
        serializedMap.put(CpmTask.UPDATE_USER_NAME, milestone.get(CpmTask.UPDATE_USER_NAME));
    }

    public static JsonNode handleMilestoneToJsonNode(final DynamicEntity milestone, final List<DynamicEntity>
            cpmAssigneeList, final Boolean isUpdate) throws ParseException {
        final ObjectNode milestoneNode = new ObjectNode(factory);

        final Map<String, Object> serialize = getMilestoneSerialized(milestone, cpmAssigneeList);

        if (serialize == null || serialize.isEmpty()) {
            return null;
        }
        for (final Entry<String, Object> entry : serialize.entrySet()) {
            // update should support null update, so if update case, pass it
            if (entry.getValue() == null && !isUpdate) {
                continue;
            }
            if (System.CPM_ID.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.CPM_ID, (String) entry.getValue());
                continue;
            }
            if (System.MILESTONE_CODE.equals(entry.getKey())) {
                milestoneNode.put(System.MILESTONE_CODE, (String) entry.getValue());
                continue;
            }
            if (System.MILESTONE_NAME.equals(entry.getKey())) {
                milestoneNode.put(System.MILESTONE_NAME, (String) entry.getValue());

                continue;
            }
            if (CpmTask.DOMAIN_ID.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.DOMAIN_ID, (String) entry.getValue());
                continue;
            }
            if (CpmTask.INTERNAL_SEQ_NO.equals(entry.getKey())) {
                milestoneNode.put(CpmTask.INTERNAL_SEQ_NO,
                        (entry.getValue() == null) ? null : entry.getValue().toString());
                continue;
            }
            if (System.PLAN_START_DATE.equals(entry.getKey())) {
                milestoneNode.put(System.PLAN_START_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));
                continue;
            }
            if (System.PLAN_END_DATE.equals(entry.getKey())) {
                milestoneNode.put(System.PLAN_END_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));
                continue;
            }
            if (System.START_DATE.equals(entry.getKey())) {
                milestoneNode.put(System.START_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));
                continue;
            }
            if (System.END_DATE.equals(entry.getKey())) {
                milestoneNode.put(System.END_DATE,
                        entry.getValue() == null ? null
                                : entry.getValue().toString().substring(dateStringStart, dateStringEnd));
                continue;
            }
            if (CpmTask.STATUS.equals(entry.getKey())) {
                milestoneNode.put(CpmTask.STATUS, entry.getValue() == null ? null : entry.getValue().toString());
                continue;
            }
            if (CpmTask.DESCRIPTION.equals(entry.getKey())) {
                milestoneNode.put(MilestoneConstants.DESCRIPTION, (String) entry.getValue());
                continue;
            }
            if (CpmTask.CPM_TASK_ASSIGNEES.equals(entry.getKey())) {
                final List<DynamicEntity> docs = (List<DynamicEntity>) entry.getValue();
                final JsonNode assignees = docs == null ? new ObjectMapper().createArrayNode()
                        : AssigneeConverter.handleToAssigneeListJson(docs);
                milestoneNode.set(CPM_ASSIGNEES_LIST, assignees);
                continue;
            }
            if (MilestoneConstants.REASON_DESCRIPTION.equals(entry.getKey())) {
                milestoneNode.put(CpmTask.REASON_DESCRIPTION, entry.getValue() == null ? null : entry.getValue().toString());
                continue;
            }
            if (MilestoneConstants.REASON.equals(entry.getKey())) {
                ObjectNode objectNode = new ObjectMapper().createObjectNode();
                Object value = entry.getValue();
                if (value != null) {
                    objectNode.put("code", value.toString());
                }
                milestoneNode.set(CpmTask.REASON_ID, objectNode);
            }
            if (MilestoneConstants.PERCENTAGE.equals(entry.getKey())) {
                milestoneNode.put(CpmTask.PERCENTAGE, entry.getValue() == null ? null : entry.getValue().toString());
                continue;
            }
            if (MilestoneConstants.QUANTITY.equals(entry.getKey())) {
                milestoneNode.put(CpmTask.QUANTITY, entry.getValue() == null ? null : entry.getValue().toString());
                continue;
            }
            if (CpmTask.DURATION.equals(entry.getKey())) {
                milestoneNode.put(CpmTask.DURATION, entry.getValue() == null ? null : entry.getValue().toString());
                continue;
            }
        }
        return milestoneNode;
    }

    private static Map<String, Object> getMilestoneSerialized(final DynamicEntity milestone, final List<DynamicEntity>
            cpmAssigneeList) {
        Map<String, Object> serializedMap;
        serializedMap = milestone;
        applyUpdateMilestoneInfoForAssignee(serializedMap, milestone, cpmAssigneeList);
        return serializedMap;
    }

    private static void applyUpdateMilestoneInfoForAssignee(final Map<String, Object> serializedMap, final DynamicEntity
            milestone, final List<DynamicEntity> cpmAssigneeList) {
        if (serializedMap == null || serializedMap.isEmpty()) {
            // no need to post this milestone to cpm server
            return;
        }
        final CntUser user = AuthenticationUtil.getUser();
        final String cpmId = (String) milestone.get(System.CPM_ID);
        final String milestoneCode = milestone.getString(System.MILESTONE_CODE);
        final String milestoneName = milestone.getString(System.MILESTONE_NAME);
        serializedMap.put(System.CPM_ID, cpmId);
        serializedMap.put(System.MILESTONE_CODE, milestoneCode);
        serializedMap.put(System.MILESTONE_NAME, milestoneName);
        serializedMap.put(System.START_DATE, milestone.getDateTime(System.START_DATE));
        serializedMap.put(System.END_DATE, milestone.getDateTime(System.END_DATE));
        serializedMap.put(System.STATUS, milestone.getString(System.STATUS));
        serializedMap.put(System.DESCRIPTION, milestone.getString(System.DESCRIPTION));
        serializedMap.put(System.PLAN_START_DATE, milestone.getDateTime(System.PLAN_START_DATE));
        serializedMap.put(System.PLAN_END_DATE, milestone.getDateTime(System.PLAN_END_DATE));
        serializedMap.put(CpmTask.CPM_TASK_ASSIGNEES, cpmAssigneeList);
        if (user != null) {
            serializedMap.put(EntityConstants.PTY_UPDATE_USER, user.getUsername());
            serializedMap.put(EntityConstants.UPDATE_USER_NAME, user.getDisplayUsername());
            serializedMap.put(EntityConstants.PTY_UPDATED_ON, DateTime.now());
            serializedMap.put(CpmTask.UPDATED_BY, user.getDisplayUsername());
        }
    }
}
