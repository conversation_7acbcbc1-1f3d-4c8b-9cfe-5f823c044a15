// Copyright (c) 1998-2019 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CBX 9.2.0 GA
// ============================================================================
// CHANGE LOG
// CBX 9.2.0 GA : 2019-01-16, lewis.liu, KME-4330
// CNT.8.12.0 : 2018-10-18, lewis.liu, KME-3910
// CNT.6.1   : 2016-12-22, kwinly.zhang, CNT-27270
// CNT.5.18.0 : 2016-09-26, kwinly.zhang, CNT-23973
// CNT.5.16 : 2016-07-01, mark.huang, CNT-24116
// CNT.5.14 : 2016-05-19, wilson.lun, CNT-22753
// CNT.5.13 : 2016-03-31. kwinly.zhang, CNT-21974
// CNT.5.13 : 2016-03-22. kwinly.zhang, CNT-21977
// CNT.5.12.1 : 2016-2-23, shane.li, creation
// ============================================================================
package com.cbx.ws.rest.jaxrs.json;

import java.io.IOException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response.Status;
import javax.ws.rs.ext.MessageBodyReader;
import javax.ws.rs.ext.Provider;
import net.sf.json.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.cbx.ws.rest.exception.APIRuntimeException;
import com.cbx.ws.rest.jaxrs.converter.EntityUnmarshaller;
import com.cbx.ws.rest.util.RESTUtil;
import com.cbx.ws.rest.util.apiReprentation.APIRepresentationManager;
import com.core.cbx.data.constants.ApiRepresentation;
import com.core.cbx.data.constants.ExceptionConstants;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.EntityConstants;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 */
@Consumes(MediaType.APPLICATION_JSON)
@Provider
public class DynamicEntityUnmarshaller implements MessageBodyReader<DynamicEntity> {

    private static final Logger logger = LogManager.getLogger(DynamicEntityUnmarshaller.class);

    @Context
    private HttpServletRequest request;

    @Override
    public boolean isReadable(final Class<?> type, final Type genericType, final Annotation[] annotations,
            final MediaType mediaType) {
        return MediaType.APPLICATION_JSON_TYPE.getType().equals(mediaType.getType())
                && MediaType.APPLICATION_JSON_TYPE.getSubtype().equals(mediaType.getSubtype())
                && DynamicEntity.class.isAssignableFrom(type);
    }

    @SuppressWarnings("unchecked")
    @Override
    public DynamicEntity readFrom(final Class<DynamicEntity> type, final Type genericType,
            final Annotation[] annotations, final MediaType mediaType,
            final MultivaluedMap<String, String> httpHeaders, final InputStream entityStream) throws IOException {
        Map<String, Object> map = null;
        try {
            map = new ObjectMapper().readValue(entityStream, Map.class);
        } catch (final Exception e) {
            throw new APIRuntimeException(Status.BAD_REQUEST);
        }
        return readDynamicEntityByApiEntity(map, RESTUtil.getApiVersion(this.request));
    }

    public DynamicEntity readDynamicEntityByApiEntity(Map<String, Object> map, String apiVersion) {
        DynamicEntity entity = null;
        if (MapUtils.isNotEmpty(map)) {
            try {
                //Start PEPL-1747
                final net.sf.json.JSONObject jsonBody = net.sf.json.JSONObject.fromObject(map);
                logger.info("The json body is : " + jsonBody.toString());
                //End PEPL-1747
                final Map<String, Object> context = new LinkedHashMap<String, Object>();
                context.put(ApiRepresentation.API_VERSION, apiVersion);
                context.put(EntityUnmarshaller.KEY_RAW_INPUT, map);
                final String docType = ObjectUtils.toString(map.get(ApiRepresentation.DOC_TYPE));
                if (StringUtils.isBlank(docType)) {
                    throw new APIRuntimeException(Status.BAD_REQUEST);
                }
                final DynamicEntity apiRepresentation = APIRepresentationManager
                        .loadAPIRepresentationByDocType(docType, apiVersion);
                if (apiRepresentation == null) {
                    throw new APIRuntimeException(Status.BAD_REQUEST);
                }
                final String entityName = apiRepresentation.getString(ApiRepresentation.ENTITY);

                context.put(EntityUnmarshaller.KEY_API_REPRESENTAITON, apiRepresentation);
                context.put(EntityConstants.PTY_ENTITY_NAME, entityName);

                final List<Map<String, Object>> internalReference = new ArrayList<Map<String, Object>>();
                context.put(EntityUnmarshaller.KEY_INTERNAL_REFERENCE, internalReference);

                final Map<String, Object> entityPath = new HashMap<String, Object>();
                context.put(EntityUnmarshaller.KEY_ENTITY_PATH, entityPath);

                final EntityUnmarshaller unmarshaller = new EntityUnmarshaller();
                entity = unmarshaller.unmarshal(map, context);
                processInternalReference(context);
                entity.put(ApiRepresentation.API_VERSION, apiVersion);

                internalReference.clear();
                entityPath.clear();
            } catch (final Exception e) {
                logger.error("Message: {}; Description: {}", ExceptionConstants.DATA_EXCEPTION_070074, "REST API Failed unmarshaller", e);
                throw new APIRuntimeException(e, Status.BAD_REQUEST);
            }
        }
        return entity;
    }

    /**
     * @param context
     */
    @SuppressWarnings("unchecked")
    private void processInternalReference(final Map<String, Object> context) {
        final List<Map<String, Object>> internalReference = (List<Map<String, Object>>) context
                .get(EntityUnmarshaller.KEY_INTERNAL_REFERENCE);
        final Map<String, Object> entityPath = (Map<String, Object>) context.get(EntityUnmarshaller.KEY_ENTITY_PATH);
        for (final Map<String, Object> map : internalReference) {
            final DynamicEntity entity = (DynamicEntity) map.get(EntityUnmarshaller.KEY_ENTITY);
            final String fieldId = (String) map.get(EntityUnmarshaller.KEY_FIELD_ID);
            final String path = (String) map.get(EntityUnmarshaller.KEY_PATH);
            entity.put(fieldId, entityPath.get(path));
        }
    }
}
