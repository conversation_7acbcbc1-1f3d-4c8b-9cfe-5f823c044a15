const refNo = "GS25-000004";
const domainId = "PEPL";
const module = "vpo";
const position = "postprocess";
const actions = ["save","saveAndConfirm"];
const script = `import com.cbxsoftware.rest.service.costing.calculation.CalculationHelper
import com.cbxsoftware.rest.repository.LookupBookRepository
import com.cbxsoftware.rest.helper.VpoHelper
import com.cbxsoftware.rest.common.auth.AuthenticationFacade
import com.cbxsoftware.rest.entity.vpo.Vpo
import com.cbxsoftware.rest.entity.vpo.VpoItem
import com.cbxsoftware.rest.entity.lookup.Lookup
import com.cbxsoftware.rest.entity.lookup.LookupBook
import com.cbxsoftware.rest.enums.DocStatus
import com.cbxsoftware.rest.service.costing.calculation.CalculationException
import com.cbxsoftware.rest.exception.*
import com.cbxsoftware.rest.util.SpringUtils
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.SerializationUtils
import org.apache.commons.lang3.StringUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.util.*
import java.util.stream.Collectors

class UsdPriceExchangeRateConversion {
    def calculationHelper = com.cbxsoftware.rest.util.SpringUtils.getBean(CalculationHelper)
    def lookupBookRepository = com.cbxsoftware.rest.util.SpringUtils.getBean(LookupBookRepository)
    def vpoHelper = com.cbxsoftware.rest.util.SpringUtils.getBean(VpoHelper)
    def authenticationFacade = com.cbxsoftware.rest.util.SpringUtils.getBean(AuthenticationFacade)
    def exchangeRateTable = new HashMap<String, BigDecimal>()

    def execute(vpo) {
        def cloneVpo = SerializationUtils.clone(vpo)
        vpoHelper.chargeAndDiscountOnItemUpdate(cloneVpo)
        vpoHelper.chargeAndDiscountOnDocUpdate(cloneVpo)
        vpoHelper.orderHeaderFieldUpdate(cloneVpo)
        def calculator = calculationHelper.getCalculator()
        def retailerPODate = vpo.vpoDate
        def vpoItemList = vpo.vpoItemList
        if (CollectionUtils.isNotEmpty(vpoItemList)) {
            def total = BigDecimal.ZERO
            vpoItemList.each { vpoItem ->
                calculateUsdPrice(vpoItem, retailerPODate)
                total = calculator.add(total, calculateCostPriceUsd(calculator, vpoItem, cloneVpo))
            }
            def totalQuantity = cloneVpo.totalQty
            if (totalQuantity != 0) {
                vpo.customFields.setDecimal(2, calculator.divide(total, totalQuantity))
            }
            vpo.currency = vpoItemList.findResult { vpoItem ->
    			vpoItem.customFields.getCodelist(5)
			}
            calculateVpoHeaderBasePrice(vpo, cloneVpo, calculator)
        }
    }

    def calculateCostPriceUsd(calculator, vpoItem, cloneVpo) {
        def temp = BigDecimal.ZERO
        def vpoItemList = cloneVpo.vpoItemList
        if (CollectionUtils.isNotEmpty(vpoItemList)) {
            for (def cloneVpoItem : vpoItemList) {
                if (StringUtils.equals(vpoItem.id, cloneVpoItem.id)) {
                    def usdPrice = vpoItem.customFields.getDecimal(2)
                    def quantity = cloneVpoItem.shipQty
                    temp = calculator.multiply(usdPrice, quantity)
                    break
                }
            }
        }
        return temp
    }

    def calculateVpoHeaderBasePrice(vpo, cloneVpo, calculator) {
        def totalAmount = cloneVpo.totalAmt
        def totalQuantity = cloneVpo.totalQty
        if (totalQuantity != 0) {
            vpo.customFields.setDecimal(1, calculator.divide(totalAmount, totalQuantity))
        } else {
            vpo.customFields.setDecimal(1, null)
        }
    }

    def calculateUsdPrice(vpoItem, retailerPODate) {
        def confirmedCurrencyCode = Optional.ofNullable(vpoItem.customFields.getCodelist(5)).map({ it.code }).orElse(null)
        def exchangeRate = lookUpUsdExchangeRate(confirmedCurrencyCode, retailerPODate)
        def confirmedPrice = vpoItem.sellPrice
        vpoItem.customFields.setDecimal(2, confirmedPrice.multiply(exchangeRate))
    }

    def lookUpUsdExchangeRate(confirmedCurrencyCode, retailerPODate) {
        if ('USD'.equalsIgnoreCase(confirmedCurrencyCode)) {
            return BigDecimal.ONE
        }
        if (exchangeRateTable.containsKey(confirmedCurrencyCode)) {
            return exchangeRateTable.get(confirmedCurrencyCode)
        }
        def exchangeRate = BigDecimal.ZERO
        def lookupItems = getLookupItemsExchangeRateOrderByStartDateDesc(confirmedCurrencyCode, 'USD', retailerPODate)
        if (CollectionUtils.isNotEmpty(lookupItems)) {
            def expression = lookupItems.get(0).customFields.getDecimal(1).toString()
            exchangeRate = new BigDecimal(expression)
        }
        exchangeRateTable.put(confirmedCurrencyCode, exchangeRate)
        return exchangeRate
    }

    def getLookupItemsExchangeRateOrderByStartDateDesc(currencyFrom, currencyTo, retailerPODate) {
        def domainId = authenticationFacade.domainId
        def lookupBook = lookupBookRepository.findByDomainIdAndNameAndDocStatus(domainId, 'CCY_EXCHANGE_RATE', DocStatus.ACTIVE.value)
        if (lookupBook != null) {
            def lookupsList = lookupBook.lookupsList
            return lookupsList.stream()
                .filter({ lookupItem ->
                    StringUtils.equals(lookupItem.customFields.getText(1), currencyFrom) &&
                    StringUtils.equals(lookupItem.customFields.getText(2), currencyTo) &&
                    lookupItem.customFields.getDate(1).isBefore(retailerPODate) &&
                    !lookupItem.disabled
                })
                .sorted({ o1, o2 ->
                    def startFrom1 = o1.customFields.getDate(1)
                    def startFrom2 = o2.customFields.getDate(1)
                    (startFrom1 == null || startFrom2 == null) ? 0 : startFrom2.compareTo(startFrom1)
                })
                .limit(1)
                .collect(Collectors.toList())
        }
        return null
    }
}

def usdPriceExchangeRateConversion = new UsdPriceExchangeRateConversion()
usdPriceExchangeRateConversion.execute(entity)`;

db.groovyScript.updateOne(
    { refNo: refNo },
    {
        $set: {
            version: 1,
            domainId: domainId,
            hubDomainId: domainId,
            isLatest: true,
            docStatus: "active",
            createUser: "system",
            createUserName: "system",
            updateUser: "system",
            updateUserName: "system",
            module: module,
            position: position,
            script: script,
            actions: actions,
            updatedOn: new Date()
        },
        $setOnInsert: {
            createdOn: new Date()
        }
    },
    { upsert: true }
);