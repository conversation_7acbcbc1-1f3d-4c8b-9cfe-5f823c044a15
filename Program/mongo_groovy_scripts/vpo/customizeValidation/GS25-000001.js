const refNo = "GS25-000001";
const domainId = "PEPL";
const module = "vpo";
const position = "customizeValidation";
const actions = ["save", "saveAndConfirm"];
const script = `import java.time.LocalDate
import java.util.stream.Collectors
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import com.cbxsoftware.rest.common.auth.AuthenticationFacade
import com.cbxsoftware.rest.dto.validation.ValidationFailedDto
import com.cbxsoftware.rest.entity.common.EmbedCodelist
import com.cbxsoftware.rest.entity.fact.Certification
import com.cbxsoftware.rest.entity.fact.Fact
import com.cbxsoftware.rest.entity.item.Item
import com.cbxsoftware.rest.entity.lookup.Lookup
import com.cbxsoftware.rest.entity.lookup.LookupBook
import com.cbxsoftware.rest.entity.vpo.Vpo
import com.cbxsoftware.rest.entity.vpo.VpoItem
import com.cbxsoftware.rest.exception.validation.FieldException
import com.cbxsoftware.rest.service.customField.CustomFieldService
import com.cbxsoftware.rest.service.ActionParameters
import com.cbxsoftware.rest.service.DomainAttributeService
import com.cbxsoftware.rest.service.FactoryDocumentService
import com.cbxsoftware.rest.service.ItemDocumentService
import com.cbxsoftware.rest.service.LookupBookDocumentService
import com.cbxsoftware.rest.util.CommonUtil
import com.cbxsoftware.rest.util.SpringUtils

class PeplVpoValidator {

    void validateItemAndFactoryLisense(Vpo vpo, ActionParameters parameters, List<ValidationFailedDto> validationExceptionList) {
        if (!isAxIntegration() && !isFactoryLisensePass(vpo, parameters)) {
            FieldException fieldException = new FieldException("The required license of associated factory is missing/invalid for current Customer.", "headerFactory")
            validationExceptionList.addAll(fieldException.toFailedDtoList())
        }
    }

    private boolean isFactoryLisensePass(Vpo vpo, ActionParameters parameters) {
        def itemDocumentService = SpringUtils.getBean(ItemDocumentService)
        def customFieldService = SpringUtils.getBean(CustomFieldService)
        def lookupBookDocumentService = SpringUtils.getBean(LookupBookDocumentService)
        def factoryDocumentService = SpringUtils.getBean(FactoryDocumentService)
        Fact newFactory = vpo.headerFactory
        List<VpoItem> vpoItems = vpo.vpoItemList
        if (newFactory == null || CollectionUtils.isEmpty(vpoItems)) {
            return true
        }
        
        Item item = itemDocumentService.findLatestByRefNo(vpoItems[0].itemId.refNo).orElse(null)
        if (item == null) {
            return true
        }

        EmbedCodelist itemLicensor = customFieldService.getCustomFieldValue(item, "custCodelist23").orElse(null)
        if (itemLicensor == null) {
            return true
        }
        
        LookupBook lookupBook = lookupBookDocumentService.findLatestByName("VALIDATE_LICENSOR")
        Map<String, Lookup> licensorMap = lookupBook.lookupsList.stream().collect(Collectors.toMap({ lookup -> 
            customFieldService.getCustomFieldValue(lookup, "custText1").get() 
        }, { lookup -> lookup }))
        
        String itemLicensorCode = itemLicensor.code
        Vpo originalVpo = parameters.getValue("originalRevisionDoc") as Vpo
        Fact originalFactory = originalVpo?.headerFactory
        
        if (!licensorMap.containsKey(itemLicensorCode) || 
            (vpo.refNo != null && (newFactory == null || (originalFactory != null && StringUtils.equals(originalFactory.factCode, newFactory.factCode))))) {
            return true
        }
        
        Fact factory = factoryDocumentService.findLatestByRefNo(newFactory.refNo).get()
        List<Certification> certifications = factory.certificationList
        String customerName = vpo.custId?.businessName
        LocalDate expectedDate = vpo.vpoDate
        Long bufferPeriod = customFieldService.getCustomFieldValue(licensorMap[itemLicensorCode], "custNumber1").orElse(null) as Long

        for (certificate in CommonUtil.safeCollection(certifications)) {
            EmbedCodelist factLicense = customFieldService.getCustomFieldValue(certificate, "custCodelist1").orElse(null)
            String factLicenseCode = factLicense?.code
            LocalDate expireDate = certificate.expireDate
            EmbedCodelist retailer = customFieldService.getCustomFieldValue(certificate, "custCodelist2").orElse(null)
            
            if (StringUtils.equals(itemLicensorCode, factLicenseCode) && 
                expireDate != null && 
                retailer != null &&
                expectedDate.plusDays(bufferPeriod).compareTo(expireDate) <= 0 && 
                StringUtils.equalsIgnoreCase(retailer.name, customerName)) {
                return true
            }
        }
        return false
    }

    private boolean isAxIntegration() {
        def authenticationFacade = SpringUtils.getBean(AuthenticationFacade)
        def domainAttributeService = SpringUtils.getBean(DomainAttributeService)
        Boolean isAxIntegration = false
        String userLoginId = authenticationFacade.userLoginId
        String domainAttributeLoginId = domainAttributeService.getValueByDomain("ax.integration.loginId", authenticationFacade.domainId)
        String axIntegrationLoginId = domainAttributeLoginId ?: "api@PEPL"
        
        if (StringUtils.equals(userLoginId, axIntegrationLoginId)) {
            isAxIntegration = true
        }
        return isAxIntegration
    }
}
def peplVpoValidator = new PeplVpoValidator()
peplVpoValidator.validateItemAndFactoryLisense(entity, parameters, validationExceptionList)`;

db.groovyScript.updateOne(
    { refNo: refNo },
    { 
        $set: { 
            version: 1,
            domainId: domainId,
            hubDomainId: domainId,
            isLatest: true,
            docStatus: "active",
            createUser: "system",
            createUserName: "system",
            updateUser: "system",
            updateUserName: "system",
            module: module,
            position: position,
            script: script,
            actions: actions,
            updatedOn: new Date()
        },
        $setOnInsert: { 
            createdOn: new Date()
        }
    },
    { upsert: true }
);