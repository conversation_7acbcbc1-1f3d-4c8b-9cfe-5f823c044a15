const refNo = "GS25-000002";
const domainId = "PEPL";
const module = "vpo";
const position = "preprocess";
const actions = ["saveAndConfirm"];
const script = `import com.cbxsoftware.rest.entity.vpo.Vpo
import com.cbxsoftware.rest.service.ActionParameters
import com.cbxsoftware.rest.common.proxy.CustomTableProxy
import java.util.HashMap

class VpoSaveProcessor {
      def handleVpoOfficeInheritance(Vpo newVpo, ActionParameters parameters) {
            if (parameters.parameters.isNewEntity && newVpo.vendorId && newVpo.vendorId.customFields) {
                if (!newVpo.customFields) {
                    newVpo.customFields = CustomTableProxy.constructByDynamicModelMap(new HashMap())
                }
                
                newVpo.customFields.setCodelist(11, newVpo.vendorId.customFields.getCodelist(11))
            }
      }
}

def vpoSaveProcessor = new VpoSaveProcessor()
vpoSaveProcessor.handleVpoOfficeInheritance(entity, parameters)`;

db.groovyScript.updateOne(
    { refNo: refNo },
    { 
        $set: { 
            version: 1,
            domainId: domainId,
            hubDomainId: domainId,
            isLatest: true,
            docStatus: "active",
            createUser: "system",
            createUserName: "system",
            updateUser: "system",
            updateUserName: "system",
            module: module,
            position: position,
            script: script,
            actions: actions,
            updatedOn: new Date()
        },
        $setOnInsert: { 
            createdOn: new Date()
        }
    },
    { upsert: true }
);