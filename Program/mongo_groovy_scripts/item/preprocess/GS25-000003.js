const refNo = "GS25-000003";
const domainId = "PEPL";
const module = "item";
const position = "preprocess";
const actions = ["save","saveAndConfirm"];
const script = `import com.cbxsoftware.rest.entity.common.EmbedCodelist
import org.apache.commons.lang.StringUtils
import com.cbxsoftware.rest.entity.item.Item
class ItemNoSetter {
    void setItemNo(Item item) {
        final String buyerItemNo = item.getBuyerItemNo()
        final EmbedCodelist embedCodelist = item.getSeason()
        String seasonName = StringUtils.EMPTY
        if (embedCodelist != null) {
            seasonName = embedCodelist.getName()
        }
        item.setItemNo(buyerItemNo + "-" + seasonName)
    }
}
def itemNoSetter = new ItemNoSetter()
itemNoSetter.setItemNo(entity)`;

db.groovyScript.updateOne(
    { refNo: refNo },
    {
        $set: {
            version: 1,
            domainId: domainId,
            hubDomainId: domainId,
            isLatest: true,
            docStatus: "active",
            createUser: "system",
            createUserName: "system",
            updateUser: "system",
            updateUserName: "system",
            module: module,
            position: position,
            script: script,
            actions: actions,
            updatedOn: new Date()
        },
        $setOnInsert: {
            createdOn: new Date()
        }
    },
    { upsert: true }
);