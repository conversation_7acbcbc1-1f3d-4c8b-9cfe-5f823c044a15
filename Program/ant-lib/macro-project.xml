<project>

    <!-- =================================
            Load Property Setting
     ================================= -->
    <property file="build.properties"/>

    <path id="builder.class.path">
        <fileset dir="${dir.lib}" includes="**/*.jar"/>
        <fileset dir="ant-lib" includes="**/*.jar"/>
    </path>

    <!-- =================================
        Macro: upper
        Description: Upper case
    ================================= -->
    <scriptdef language="javascript" name="upper">
        <attribute name="string" />
        <attribute name="to" />
        project.setProperty( attributes.get( "to" ), attributes.get("string").toUpperCase() );
    </scriptdef>

    <macrodef name="genSystemMessage">
        <attribute name="messageTxtPath" description="Path of SystemMessage TXT file"/>
        <attribute name="targetSQLPath" description="Path of the generated SQL"/>
        <attribute name="domainId" default="/" description="Domain ID, default to be ROOT (/)"/>
        <attribute name="entityVersion" default="1" description="Entity version of SystemMessage, default to be 1"/>
        <sequential>
            <if>
                <not>
                    <available file="@{messageTxtPath}" type="file"/>
                </not>
                <then>
                    <echo>Cannot find the message file:</echo>
                    <echo>@{messageTxtPath}</echo>
                    <echo>System Message SQL will not be generated.</echo>
                </then>
                <else>
                    <taskdef name="gentSystemMessageSqlTask"
                             classname="com.core.cbx.generator.util.SystemMessageSqlGenerator">
                        <classpath refid="builder.class.path"/>
                    </taskdef>
                    <gentSystemMessageSqlTask
                        domainId="@{domainId}"
                        resourceFile="@{messageTxtPath}"
                        startLine="1"
                        endLine="-1"
                        delimiter="\t"
                        overwrite="true"
                        targetFileName="@{targetSQLPath}"
                        entityVersion="@{entityVersion}"
                    />
                </else>
            </if>
        </sequential>
    </macrodef>
	
	
    <macrodef name="applyProjectScriptsOnly" description="Apply project DB script">
        <attribute name="project.name" description="The name of project"/>
        <attribute name="project.version" description="The version of project"/>
        <attribute name="dir.release.full" description="The release folder of @{project.name}-pack-full" />
        <attribute name="dir.apply.script.base" description="The base folder of the DB script to be applied" />
        <attribute name="dir.build.dump.tmp" description="The temporary folder for running db scripts" />
        <attribute name="dir.run.sql" description="The interims folder"/>
        <attribute name="dir.release" description="The Name of release folder"/>
        <attribute name="release.db.name" description="The release name of the db"/>
        <attribute name="release.db.host" description="Host of the DB"/>
        <attribute name="release.db.port" description="Host of the DB"/>
        <attribute name="release.db.pwd" description="Password of the DB"/>
        <attribute name="release.db.user" description="User of the DB"/>

        <sequential>
            <echo>Going to apply the @{project.name} db_scripts</echo>
            <var name="dir.run_sql.project_db_scripts" value="@{dir.run.sql}/03b.release_db_scripts" />
            <mkdir dir="${dir.run_sql.project_db_scripts}" />
            <copy preservelastmodified="true" todir="${dir.run_sql.project_db_scripts}">
                <fileset dir="@{dir.release.full}/db_scripts">
                    <include name="*.sql" />
                    <exclude name="@{project.name}.@{project.version}.sql" />
                </fileset>
            </copy>
            <execdirsql dbhost="@{release.db.host}" dbname="@{release.db.name}" dbport="@{release.db.port}" dbpwd="@{release.db.pwd}" dbuser="@{release.db.user}" dir="${dir.run_sql.project_db_scripts}" />

            <echo>Going to log to cnt_release_log</echo>
            <var name="dir.run_sql.release_log" value="@{dir.run.sql}/04.release_log" />
            <mkdir dir="${dir.run_sql.release_log}" />
            <var name="buildtime" unset="true" />
            <tstamp>
                <format property="buildtime" pattern="yyyyMMddHHmmssSSS" timezone="Hongkong" />
            </tstamp>
            <concat destfile="${dir.run_sql.release_log}/zzz-update-version.sql">
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO product_team_read;
    GRANT USAGE ON SCHEMA public TO product_team_read;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO product_team_read;


    insert into cnt_release_log (release_system, version, applied_on, description)
    VALUES ('_SYS', 't${buildtime}', CURRENT_TIMESTAMP, 'applied @{project.name} db_script: @{project.name}.@{project.version}');

            </concat>
            <execdirsql dbhost="@{release.db.host}" dbname="@{release.db.name}" dbport="@{release.db.port}" dbpwd="@{release.db.pwd}" dbuser="@{release.db.user}" dir="${dir.run_sql.release_log}" />
        </sequential>
    </macrodef>

    <macrodef name="execdirsql"
              description="Rull all SQL in the dir (Depends on marcodef: execsql)
                           Note: Need to load the dbProfile properties file (Run the task: -verify-load-dbprofile)">
        <attribute name="dbhost" description="Host Name/ IP of the DB"/>
        <attribute name="dbport" description="DB Port" default="5432"/>
        <attribute name="dbuser" description="DB User name"/>
        <attribute name="dbname" description="DB User name"/>
        <attribute name="dbpwd" description="DB Password"/>
        <attribute name="dir" description="Folder that store the SQL to be executed"/>
        <attribute name="logdir" default="" description="Folder that store the log files"/>
        <sequential>
            <echo>[execdirsql] SQL Folder: @{dir}</echo>
            <fail>
                <condition><not><available file="@{dir}" type="dir" /></not></condition>-
= **[ ERROR ]** ===============================================================
 Folder not found:
 @{dir}
===============================================================================
            </fail>
            <if><equals arg1="@{logdir}" arg2=""/><then>
                <tstamp><format property="cbx-runSqlTime" pattern="yyyyMMddHHmmssSSS"/></tstamp>
                <var name="actualLogDir" value="@{dir}.${cbx-runSqlTime}.logs"/>
            </then><else>
                <var name="actualLogDir" value="@{logdir}"/>
            </else></if>

            <mkdir dir="${actualLogDir}"/>
            <for param="file">
                <path>
                    <sort xmlns:rcmp="antlib:org.apache.tools.ant.types.resources.comparators">
                        <fileset dir="@{dir}" includes="*.sql" casesensitive="false"/>
                    </sort>
                </path>
                <sequential>
                    <propertyregex override="yes" property="sqlFileName" input="@{file}"
                                   regexp=".*[\\/](.*)\.sql"
                                   replace="\1.sql" casesensitive="false"/>
                    <echo>SQL: ${sqlFileName}</echo>
                    <execsql
                        dbhost="@{dbhost}"
                        dbport="@{dbport}"
                        dbname="@{dbname}"
                        dbuser="@{dbuser}"
                        dbpwd="@{dbpwd}"
                        sqlfile="@{file}"
                        logfile="${actualLogDir}/${sqlFileName}.log"/>
                </sequential>
            </for>
            <echo>Log files stored in ${actualLogDir}</echo>
        </sequential>
    </macrodef>

        <macrodef name="execsql" description="Run single SQL file.">
        <attribute name="dbhost" description="Host Name/ IP of the DB"/>
        <attribute name="dbport" description="DB Port" default="5432"/>
        <attribute name="dbdriver" description="DB Port" default="org.postgresql.Driver"/>
        <attribute name="dbuser" description="DB User name"/>
        <attribute name="dbname" description="DB User name"/>
        <attribute name="dbpwd" description="DB Password"/>
        <attribute name="dbautocommit" description="DB Password" default="true"/>
        <attribute name="sqlfile" description="SQL file to be run"/>
        <attribute name="logfile" default="sql.log" description="Log file"/>
        <sequential>

            <trycatch>
                <try>
                    <exec executable="psql" output="@{logfile}" failonerror="true">
                        <env key="PGPASSWORD" value="@{dbpwd}"/>
                        <env key="PGCLIENTENCODING" value="UTF8"/>
                        <arg line="-v ON_ERROR_STOP=1"/>
                        <arg line="-U @{dbuser}"/>
                        <arg line="-h @{dbhost}"/>
                        <arg line="-p @{dbport}"/>
                        <arg line="-d @{dbname}"/>
                        <arg line="-f @{sqlfile}"/>
                        <!-- <arg line="-L @{logfile}"/> -->
                    </exec>
                </try>
                <catch>
                    <echo>Last 50 lines of log file: @{logfile}</echo>
                    <echo>-------------------------------------</echo>
                    <echo>....</echo>
                    <var name="cbx.execsql.error" unset="true"/>
                    <loadfile srcfile="@{logfile}" property="cbx.execsql.error">
                        <filterchain>
                            <filterreader classname="org.apache.tools.ant.filters.TailFilter">
                                <param name="lines" value="50"/>
                            </filterreader>
                        </filterchain>
                    </loadfile>
                    <echo>${cbx.execsql.error}</echo>
                    <echo>-------------------------------------</echo>
                    <fail>Error when running: @{sqlfile}</fail>
                </catch>
            </trycatch>

        </sequential>
    </macrodef>

    <macrodef name="hasFilesInsideFolder" description="Check if there are files inside the specified folder">
        <attribute name="folder" description="Path of the folder to check"/>
        <attribute name="to" description="Property name of the result to be saved"/>
        <attribute name="filepattern" default="**/*.*" description="The pattern to be matched"/>
        <sequential>
            <if>
                <available file="@{folder}" type="dir"/>
                <then>
                    <condition property="@{to}">
                        <resourcecount count="0" when="greater">
                            <fileset dir="@{folder}">
                                <include name="@{filepattern}"/>
                            </fileset>
                        </resourcecount>
                    </condition>
                </then>
                <else>
                </else>
            </if>
        </sequential>
    </macrodef>
    <macrodef name="customize-restapi-jar">
        <attribute name="dir.cbx.restapi" description="Project customized product directories"/>
        <attribute name="dir.temp" description="The temp directory"/>
        <attribute name="dir.product.release" description="The project release directory"/>
        <attribute name="depend.product.version" description="The depend product version"/>
        <attribute name="project.name" description="Name of the current project eg:ldl, twl, dlgl..."/>
        
        <sequential>
            <hasFilesInsideFolder folder="@{dir.cbx.restapi}" to="has-customized-restapi"/>
            <if>
                <istrue value="${has-customized-restapi}"/>
                <then>
                    <mkdir dir="@{dir.temp}"/>
                    <echo>@{dir.product.release}/@{depend.product.version}</echo>
                    <copy todir="@{dir.temp}/zips">
                        <fileset dir="@{dir.product.release}/@{depend.product.version}/">
                            <include name="cbx-restapi-*-source.zip"/>
                        </fileset>
                        <mapper from="cbx-restapi-.*\.zip" to="cbx-restapi.zip" type="regexp"/>
                    </copy>
                    <unzip dest="@{dir.temp}/cbx-restapi" src="@{dir.temp}/zips/cbx-restapi.zip"/>
                    <copy overwrite="true" todir="@{dir.temp}/cbx-restapi">
                        <fileset dir="@{dir.cbx.restapi}"/>
                    </copy>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="customize-cbx_tby_int-jar">
        <attribute name="dir.cbx.tby.int" description="Project customized product directories"/>
        <attribute name="dir.temp" description="The temp directory"/>
        <attribute name="dir.product.release" description="The project release directory"/>
        <attribute name="depend.product.version" description="The depend product version"/>
        <attribute name="project.name" description="Name of the current project eg:ldl, twl, dlgl..."/>

        <sequential>
            <hasFilesInsideFolder folder="@{dir.cbx.tby.int}" to="has-customized-cbx_tby_int"/>
            <if>
                <istrue value="${has-customized-cbx_tby_int}"/>
                <then>
                    <mkdir dir="@{dir.temp}"/>
                    <copy todir="@{dir.temp}/zips">
                        <fileset dir="@{dir.product.release}/@{depend.product.version}/">
                            <include name="cbx_tby_int*source.zip"/>
                        </fileset>
                        <mapper from="cbx_tby_int-.*\.zip" to="cbx_tby_int.zip" type="regexp"/>
                    </copy>
                    <unzip dest="@{dir.temp}/cbx_tby_int" src="@{dir.temp}/zips/cbx_tby_int.zip"/>
                    <copy overwrite="true" todir="@{dir.temp}/cbx_tby_int">
                        <fileset dir="@{dir.cbx.tby.int}"/>
                    </copy>
                </then>
            </if>
        </sequential>
    </macrodef>

    <macrodef name="packCasWar">
        <attribute name="project.version" description="The vserion of the project"/>
        <attribute name="project.name" description="The name of the project"/>
        <attribute name="dir.target" description="The name of the target directory"/>
        <attribute name="dir.cas.war" description="The cbx cas war directory"/>
        <attribute name="dir.cas" description="The cbx cas directory"/>
        <attribute name="dir.release.cbx-project.name"
                   description="The directory to store the release cbx-{project.name}"/>
        <attribute name="depend.product.version" description="The depend product version"/>
        <attribute name="dir.temp" description="The temp directory"/>

        <sequential>
            <if>
                <available file="@{dir.cas.war}"/>
                <then>
                    <copy todir="@{dir.cas}" overwrite="true">
                        <fileset dir="@{dir.cas.war}"/>
                    </copy>
                </then>
            </if>
            <echo>Pack the cas-@{depend.product.version}-@{project.name}.@{project.version}.war file</echo>
            <echo>dir.cas: @{dir.cas}</echo>
            <war destfile="@{dir.target}/cas-@{depend.product.version}-@{project.name}.@{project.version}.war" needxmlfile='false'>
                <fileset dir="@{dir.cas}"/>
            </war>
            <if>
                <isset property="war.delta.includes.@{project.version}"/>
                <then>
                    <propertycopy from="war.delta.includes.@{project.version}" name="war.delta.includes"/>
                </then>
                <else>
                    <var name="war.delta.includes" value="none"/>
                </else>
            </if>
            <if>
                <available file="@{dir.cas.war}"/>
                <then>
                    <zip destfile="@{dir.target}/cas-war-update-@{project.name}.@{project.version}.zip">
                        <fileset dir="@{dir.cas.war}" includes="${war.delta.includes}"/>
                    </zip>
                </then>
            </if>
        </sequential>
    </macrodef>



    <macrodef name="genChecksumWithPathInfo"
            description="Create checksum file in folder (attribute: dir) with file name prefix with attribute prefix">
        <attribute name="dir"/>
        <attribute name="prefix" default=""/>
        <sequential>
            <delete file="@{dir}/@{prefix}checksum.md5"/>
            <var name="file.md5"/>
            <var name="file.path"/>
            <var name="dir" value="@{dir}"/>
            <for param="file">
                <path>
                    <fileset dir="@{dir}" excludes="*.md5tmp, *.MD5, *.md5" casesensitive="false"/>
                </path>
                <sequential>
                    <var name="file.path" value="@{file}"/>
                    <var name="file.md5" unset="true"/>
                    <checksum file="${file.path}" property="file.md5"/>
                    <!-- <propertyregex override="yes" property="filename" input="@{file}"
                                   regexp=".*[\\/]([^\\/]*)" replace="\1" casesensitive="false"/> -->

                    <script language="javascript"><![CDATA[
                        //-- Code to replace the file path to the checksum type 4 required
                        var filePath = '' + project.getProperty('file.path');
                        var basedir = '' + project.getProperty('basedir');
                        var dir = '' + project.getProperty('dir');

                        var basedir2 = basedir.replace(/\\/g, "/");
                        var dir2 = dir.replace(/\\/g, "/");
                        var filePath2 = filePath.replace(/\\/g, "/");

                        var len = dir2.length;
                        var basedir3 = basedir2 + "/" + dir2;
                        if (filePath2.indexOf(basedir3) > -1) {
                            len = basedir3.length;
                        }

                        /* -- debug
                        echo = project.createTask("echo");
                        echo.setMessage("---\nf:" + filePath2 + "\nb:" + basedir3 + "\nd:" + dir2 + "\n" + len);
                        echo.perform();
                        */

                        filePath2 = filePath2.substring(len + 1);
                        project.setProperty('newFileName', filePath2);
                    ]]></script>
                    <concat destfile="@{dir}/@{prefix}checksum.md5" append="yes" fixlastline="no"
                            outputencoding="UTF-8">${file.md5}  ./${newFileName}${line.separator}</concat>
                </sequential>
            </for>
            <fixcrlf file="@{dir}/@{prefix}checksum.md5" eol="lf" eof="remove" />
            <echo>Checksum (With Path Info) file generated:</echo>
            <echo>      @{prefix}checksum.md5</echo>
        </sequential>
    </macrodef>



</project>
