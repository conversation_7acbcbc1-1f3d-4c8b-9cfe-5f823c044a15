// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.15.9.0P.04.0
// ============================================================================
// CHANGE LOG
// PEPL.15.9.0P.04.0 : 2025-09-01, Gwyn.gao, PEPL-3429
// PEPL.15.9.0P.03.0 : 2025-08013, Gwyn.gao, PEPL-3305
// ============================================================================
package com.core.pepl.action;

import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.cbx.ws.rest.client.cpm.RestDispatcher;
import com.cbx.ws.rest.client.cpm.RestDispatcherFactory;
import com.cbx.ws.rest.jaxrs.exception.RestAPIException;
import com.core.cbx.action.ActionContext;
import com.core.cbx.action.ActionDispatcher;
import com.core.cbx.action.actionContext.SaveMilestones;
import com.core.cbx.action.exception.ActionException;
import com.core.cbx.action.workerAction.WorkerAction;
import com.core.cbx.common.type.DateTime;
import com.core.cbx.data.codelist.service.CodelistManager;
import com.core.cbx.data.constants.Cpm;
import com.core.cbx.data.constants.CpmTask;
import com.core.cbx.data.constants.Vpo;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.util.CpmUtil;
import com.core.pepl.data.constants.PgsVpo;

public class PgsExcelSaveWorkerAction implements WorkerAction {

    public static String CPM_UPLOAD = "CPM_UPLOAD";
    private final Logger logger = LogManager.getLogger(this.getClass());
    private DynamicEntity entity;
    private boolean isSaved = false;
    private boolean isUploadDataValid = true;
    private final static RestDispatcher restClient = RestDispatcherFactory.createDefaultClient();
    private ActionContext actionContext;

    public PgsExcelSaveWorkerAction(final DynamicEntity entity) {
        this.setEntity(entity);
    }

    @Override
    public void execute() throws ActionException {
        logger.info("welcome to PgsExcelSaveWorkerAction");

        if (Vpo.ENTITY_NAME_VPO.equals(getEntity().getEntityName()) && CPM_UPLOAD.equals(getEntity().getString(PgsVpo.IMPORT_MARK))) {
            updateCpmForImportedVpos();
        }
    }

    /**
     * @throws ActionException
     * @throws RestAPIException
     *
     */
    private void updateCpmForImportedVpos() throws ActionException {
        // skip saving vpo, reset the flag
        isSaved = true;

        final DynamicEntity vpo = getEntity();
        final String cpmUploadFields = vpo.getString(PgsVpo.CPM_UPLOAD_FIELDS);
        try {
            final List<Map<String, Object>> cpmUploadFieldContents = readAndInstallCpmUploadFieldContents(cpmUploadFields);

            logger.info("cpmUploadFieldContents: {}" + cpmUploadFieldContents);

            if(this.isUploadDataValid){
                final String cpmUploadComments = vpo.getString(PgsVpo.CPM_UPLOAD_COMMENTS);
                final List<Map<String, Object>> cpmUploadCommentContents = readAndInstallCpmUploadCpmUploadCommentContents(cpmUploadComments);

                updateVpoCpm(vpo, cpmUploadFieldContents, cpmUploadCommentContents);
            }
        } catch (final Exception e) {
            logger.error("ERROR, updateCpmForImportedVpos", e);
            throw new ActionException("error", e);
        }
    }

    private void updateVpoCpm(final DynamicEntity vpo, final List<Map<String, Object>> cpmUploadFieldContents, final List<Map<String, Object>> cpmUploadCommentContents) throws ActionException, RestAPIException {
        final List<String> cpmIds = CpmUtil.findLatestCpmIdList(vpo);

        if (CollectionUtils.isNotEmpty(cpmIds)) {
            final List<DynamicEntity> vpoCpmDocList = restClient.getCpms(cpmIds);
            boolean taskModified = false;

            for (final DynamicEntity vpoCpmDoc : vpoCpmDocList) {
                final Collection<DynamicEntity> vpoCpmTaskList = vpoCpmDoc.getEntityCollection(Cpm.CPM_TASKS);

                for (final DynamicEntity vpoCpmTask : vpoCpmTaskList) {
                    final String taskId = vpoCpmTask.getString(CpmTask.TASK_ID);

                    for (final Map<String, Object> updateInfo : cpmUploadFieldContents) {
                        if (taskId.equals(updateInfo.get(CpmTask.TASK_ID))) {
                            vpoCpmTask.putAll(updateInfo);
                        }
                    }

                    for (final Map<String, Object> updateInfo : cpmUploadCommentContents) {
                        if (taskId.equals(updateInfo.get(CpmTask.TASK_ID))) {
                            vpoCpmTask.putAll(updateInfo);
                        }
                    }

                    if (vpoCpmTask.isModifiedEntity()) {
                        taskModified = true;
                    }
                }
            }

            if (taskModified) {
                ActionDispatcher.execute(new SaveMilestones(vpo, vpoCpmDocList));
            }
        }
    }

    private final static String[] fieldsForCpm = new String[] { "status::codelist::CPM_TASK_STATUS", "planStart::date", "planEnd::date", "actualStart::date", "actualEnd::date" };
    private final static String MARK_EMPTY = "[[<<empty>>]]";
    private final static String MARK_END = "[[<<end>>]]";
    private final static String field_mark = ":";
    private final static String field_separator = ",";
    private final static String task_separator = ";";
    private final static String fieldDefine_separator = "::";
    private final static String JAVA_DATE_FORMAT = "yyyy-MM-dd";

    private final static String char_backslash_n = "\n";
    private final static String char_backslash_r = "\r";
    private final static String char_slash = "/";
    private final static String char_hyphen = "-";
    private final static String domain_pgs = "PEPL";
    private final static String type_date = "date";
    private final static String type_codelist = "codelist";

    private List<Map<String, Object>> readAndInstallCpmUploadFieldContents(final String cpmUploadFields) throws DataException {
        final List<Map<String, Object>> updateInfoList = new ArrayList<>();

        if (StringUtils.isEmpty(cpmUploadFields)) {
            return updateInfoList;
        }

        final String[] taskInfoList = StringUtils.splitByWholeSeparatorPreserveAllTokens(cpmUploadFields, task_separator);

        for (final String taskFieldsInfo : taskInfoList) {
            if (StringUtils.isEmpty(taskFieldsInfo)) {
                continue;
            }

            final Map<String, Object> updateInfo = new HashMap<>();
            final String taskCode = StringUtils.remove(
                    StringUtils.remove(
                            StringUtils.substringBefore(taskFieldsInfo, field_mark),
                            char_backslash_n),
                    char_backslash_r);
            updateInfo.put(CpmTask.TASK_ID, taskCode);

            final String taskInfo = StringUtils.substringAfter(taskFieldsInfo, field_mark);
            final String[] taskInfoFields = StringUtils.splitByWholeSeparatorPreserveAllTokens(taskInfo, field_separator);

            for (int i = 0; i < fieldsForCpm.length && i < taskInfoFields.length; i++) {
                String content = taskInfoFields[i];

                if (StringUtils.isNotEmpty(content)) {
                    final String[] fieldDefinition = StringUtils.splitByWholeSeparator(fieldsForCpm[i], fieldDefine_separator);
                    final String fieldId = fieldDefinition[0];

                    if (MARK_EMPTY.equals(content)) {
                        updateInfo.put(fieldId, null);
                    } else {
                        final String fieldType = fieldDefinition[1];

                        if (fieldType != null && type_date.equals(fieldType)) {
                            content = StringUtils.replace(content, char_slash, char_hyphen);
                            final DateTime dateTime = DateTime.parseToDateTime(content, JAVA_DATE_FORMAT);

                            updateInfo.put(fieldId, dateTime);
                        } else if (fieldType != null && fieldType.startsWith(type_codelist)) {
                            final String codelistName = fieldDefinition[2];
                            final DynamicEntity codelist = CodelistManager.loadCodelistItem(codelistName, domain_pgs, content);

                            if (codelist != null) {
                                updateInfo.put(fieldId, codelist);
                            }
                        } else {
                            updateInfo.put(fieldId, content);
                        }
                    }
                }
            }

            DateTime planStart = updateInfo.get(CpmTask.PLAN_START) == null ? null : (DateTime) updateInfo.get(CpmTask.PLAN_START);
            DateTime planEnd = updateInfo.get(CpmTask.PLAN_END) == null ? null : (DateTime) updateInfo.get(CpmTask.PLAN_END);
            DateTime actualStart = updateInfo.get(CpmTask.ACTUAL_START) == null ? null : (DateTime) updateInfo.get(CpmTask.ACTUAL_START);
            DateTime actualEnd = updateInfo.get(CpmTask.ACTUAL_END) == null ? null : (DateTime) updateInfo.get(CpmTask.ACTUAL_END);

            if(!checkDateValid(planStart, planEnd, actualStart, actualEnd)){
                this.setIsUploadDataValid(false);
                break;
            }

            if(planStart != null && planEnd != null){
                updateInfo.put(CpmTask.PLANNED_DURATION, planStart.numDaysFrom(planEnd));
            } else if (planStart == null && planEnd != null) {
                updateInfo.put(CpmTask.PLANNED_DURATION, null);
            }

            if(actualStart != null && actualEnd != null){
                updateInfo.put(CpmTask.DURATION, actualStart.numDaysFrom(actualEnd));
            }else if(actualStart == null && actualEnd != null){
                updateInfo.put(CpmTask.DURATION, null);
            }

            updateInfoList.add(updateInfo);
        }

        return updateInfoList;
    }

    private List<Map<String, Object>> readAndInstallCpmUploadCpmUploadCommentContents(final String cpmUploadComments) {
        final List<Map<String, Object>> updateInfoList = new ArrayList<>();

        if (StringUtils.isEmpty(cpmUploadComments)) {
            return updateInfoList;
        }

        final String[] taskInfoList = StringUtils.splitByWholeSeparatorPreserveAllTokens(cpmUploadComments, MARK_END);

        for (final String taskFieldsInfo : taskInfoList) {
            if (StringUtils.isEmpty(taskFieldsInfo)) {
                continue;
            }

            final Map<String, Object> updateInfo = new HashMap<String, Object>();
            final String taskCode = StringUtils.remove(
                    StringUtils.remove(
                            StringUtils.substringBefore(taskFieldsInfo, field_mark),
                            char_backslash_n),
                    char_backslash_r);
            updateInfo.put(CpmTask.TASK_ID, taskCode);

            final String content = StringUtils.substringAfter(taskFieldsInfo, field_mark);
            if (StringUtils.isNotEmpty(content)) {
                if (MARK_EMPTY.equals(content)) {
                    updateInfo.put(CpmTask.DESCRIPTION, null);
                } else {
                    updateInfo.put(CpmTask.DESCRIPTION, content);
                }
            }
            updateInfoList.add(updateInfo);
        }
        return updateInfoList;
    }

    private boolean checkDateValid(DateTime planStart, DateTime planEnd, DateTime actualStart, DateTime actualEnd) {
        boolean planEndCheck = (planStart != null && planEnd != null && planEnd.gt(planStart)) || Objects.equals(planEnd, planStart) || planStart == null;
        boolean planActualStartCheck = (actualStart != null && actualEnd != null && actualEnd.gt(actualStart)) || Objects.equals(actualEnd, actualStart) || actualStart == null;

        return planEndCheck && planActualStartCheck;
    }

    public void setIsUploadDataValid(final boolean isUploadDataValid) {
        this.isUploadDataValid = isUploadDataValid;
    }

    public boolean isSaved() {
        return isSaved;
    }

    public void setSaved(final boolean isSaved) {
        this.isSaved = isSaved;
    }

    public DynamicEntity getEntity() {
        return entity;
    }

    public void setEntity(final DynamicEntity entity) {
        this.entity = entity;
    }

    public ActionContext getActionContext() {
        return actionContext;
    }

    public void setActionContext(final ActionContext actionContext) {
        this.actionContext = actionContext;
    }

}
