// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.15.9.0P.03.0
// ============================================================================
// CHANGE LOG
// PEPL.15.9.0P.03.0 : 2025-08013, Gwyn.gao, PEPL-3305
// ============================================================================

package com.core.pepl.action;

import org.apache.commons.lang.StringUtils;

import com.core.cbx.imp.excel.ExcelEntitiesConverterAndSaver;

/**
 * <AUTHOR>
 */
public class AsyncImportRawDataAction extends com.core.cbx.action.AsyncImportRawDataAction {

    @Override
    protected ExcelEntitiesConverterAndSaver getConverterAndAllowCustomization(String fileType,
            boolean isConvertAndSave) {
        if (StringUtils.equalsIgnoreCase(fileType, "EXCEL") && isConvertAndSave) {
            return new com.core.pepl.imp.excel.ExcelEntitiesConverterAndSaver();
        } else {
            return super.getConverterAndAllowCustomization(fileType, isConvertAndSave);
        }
    }
}
