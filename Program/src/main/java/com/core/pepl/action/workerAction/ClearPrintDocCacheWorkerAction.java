// Copyright (c) 1998-2015 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.9.3
// ============================================================================
// CHANGE LOG
// PCPL.5.9GA.03.2 : 2015-09-09, harry.tang, PEGP-331
// ============================================================================
package com.core.pepl.action.workerAction;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.core.cbx.action.exception.ActionException;
import com.core.cbx.action.workerAction.AbstractWorkerAction;
public class ClearPrintDocCacheWorkerAction extends AbstractWorkerAction {

    protected final Logger logger = LogManager.getLogger(this.getClass());

    private static String printDocCache = "PRINT_DOC_CACHE";

    @Override
    protected void process() throws ActionException {
//        final String protocol = StringUtils.defaultIfBlank(
//                SystemConfigManager.getInstance().getConfigValue(PrintConstants.SYS_CONF_REPORT_CACHE_PROTOCOL,
//                        PrintConstants.ROOT_DOMAIN), FileManagerFactory.PROTOCOL_DB);
//        final FileManager fileManager = FileManagerFactory.getInstance().getFileManager(protocol);
//        try {
//            fileManager.deleteFilesByPrefixAndExpireDate(printDocCache, DateTime.now().plusDays(Integer.valueOf(1)));
//        } catch (final DataException e) {
//            logger.error("ERROR", "Can not clear PrintDocCache.", e);
//            throw new ActionException("ERROR", e.getMessage(), e);
//        }
    }
}
