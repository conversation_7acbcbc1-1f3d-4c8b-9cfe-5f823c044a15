// Copyright (c) 1998-2013 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.8 GA
// ============================================================================
// CHANGE LOG
// CNT.5.8 GA : 2015-05-06, shermen.wu, PEGP-86
// ============================================================================

package com.core.pepl.printForm;

/**
 * <AUTHOR>
 */

public class Sort {

    // use for getting the compare field id
    private String[] fieldPath;

    private boolean isAsc = true;

    private boolean nullLast = true;

    private Object defaultFornull = null;

    public Sort() {

    };

    public Sort(final String fieldPath) {
        this.fieldPath = new String[] {fieldPath};
    }

    public Sort(final String[] fieldPath) {
        this.fieldPath = fieldPath;
    }

    public Sort(final String[] fieldPath, final boolean isAsc) {
        this.fieldPath = fieldPath;
        this.isAsc = isAsc;
    }

    public Sort(final String[] fieldPath, final boolean isAsc, boolean nullLast) {
        this.fieldPath = fieldPath;
        this.isAsc = isAsc;
        this.nullLast = nullLast;
    }

    public Sort(final String[] fieldPath, final boolean isAsc, boolean nullLast, Object defaultFornull) {
        this.fieldPath = fieldPath;
        this.isAsc = isAsc;
        this.nullLast = nullLast;
        this.defaultFornull = defaultFornull;
    }

    public String[] getFieldPath() {
        return fieldPath;
    }

    public boolean isAsc() {
        return isAsc;
    }

    public boolean isNullLast() {
        return nullLast;
    }

    public Object getDefaultFornull() {
        return defaultFornull;
    }

}
