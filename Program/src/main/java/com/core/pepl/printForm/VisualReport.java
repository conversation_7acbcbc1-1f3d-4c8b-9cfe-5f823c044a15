// Copyright (c) 1998-2015 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.6.5.0
// ============================================================================
// CHANGE LOG
// PEPL.6.6.0P.10.0 : 2017-08-16, harry.tang, PEPL-469
// PEPL.6.5.0P.5.0 : 2017-07-06, jovan.liu, PEPL-153
// PCPL.5.17GA.03.0: 2016-11-01, harry.tang, PEGP-1076
// PCPL.5.17GA.02.1: 2016-11-14, harry.tang, PEGP-1085
// PCPL.5.17GA.01.2: 2016-10-12, harry.tang, PEGP-1045
// PCPL.5.12GA.03.1: 2016-06-14, harry.tang, PEGP-909
// PCPL.5.12GA.03.0: 2016-06-01, harry.tang, PEGP-779& PEGP-887
// PCPL.5.10GA.02.0: 2015-12-15, harry.tang, PEGP-424
// ============================================================================
package com.core.pepl.printForm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.core.cbx.action.exception.ActionException;
import com.core.cbx.common.type.DateTime;
import com.core.cbx.data.DynamicEntityModel;
import com.core.cbx.data.constants.Codelist;
import com.core.cbx.data.constants.Color;
import com.core.cbx.data.constants.SampleEvaluation;
import com.core.cbx.data.constants.SampleTracker;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.DynamicEntityImp;
import com.core.cbx.data.exception.DataException;

/**
 * <AUTHOR>
 */
public class VisualReport {

    public static final String INDEX = "index";
    public static final String NUMBER = "number";
    public static final String PAGES = "pages";
    public static final String QUERY_LIST_RELATED_SES = "listRelatedSampleEvaluations";

    private VisualReport() {

    }
    
    public static DynamicEntity loadFullEntity(final String id, final String entityName) throws DataException {
        if (id == null) {
            return new DynamicEntityImp();
        }
        
        DynamicEntity fullSampleTracker = DynamicEntityModel.findUniqueEntityBy(entityName, "id", id, true);
        
        final Map<String, Object> params = new HashMap<String, Object>();
        params.put("stRefNo", fullSampleTracker.getReference());
        params.put("stDomainId", fullSampleTracker.getDomainId());
        
        setRelatedEvaluationToDetailList(fullSampleTracker, params);
        
        return fullSampleTracker;
    }
    
    public static DynamicEntity loadFullEntityByRefNo(final String refNo, final String entityName) throws DataException {
        if (refNo == null) {
            return new DynamicEntityImp();
        }
        
        DynamicEntity fullSampleTracker =DynamicEntityModel.findUniqueEntityBy(entityName, "refNo", refNo, true);
        final Map<String, Object> params = new HashMap<String, Object>();
        params.put("stRefNo", fullSampleTracker.getReference());
        params.put("stDomainId", fullSampleTracker.getDomainId());
        
        setRelatedEvaluationToDetailList(fullSampleTracker, params);
        
        return fullSampleTracker;
    }

    private static void setRelatedEvaluationToDetailList(DynamicEntity fullSampleTracker, Map<String, Object> params) throws DataException {
        List<DynamicEntity> sampleEvaluations = DynamicEntityModel.findByQuery(QUERY_LIST_RELATED_SES, params);
        
        final List<DynamicEntity> sampleSe = new ArrayList<DynamicEntity>();
        final List<DynamicEntity> materialSe = new ArrayList<DynamicEntity>();
        final List<String> seIds = new ArrayList<String>();
        for (final DynamicEntity sampleEvaluation : sampleEvaluations) {
            seIds.add(sampleEvaluation.getId());
        }
        
        for (final String id : seIds) {
        	DynamicEntity fullDoc = DynamicEntityModel.findUniqueEntityBy(
        			SampleEvaluation.ENTITY_NAME_SAMPLE_EVALUATION, SampleEvaluation.ID,
        			id, false);
        	
        	final DynamicEntity evaluationType = fullDoc.getEntity(SampleEvaluation.EVALUATION_TYPE);
        	if (evaluationType != null) {
        		evaluationType.getString(Codelist.CODE);
        		if (StringUtils.equals("EF01", evaluationType.getString(Codelist.CODE))) {
        			
        			if (fullDoc.getEntity(SampleEvaluation.SAMPLE_TYPE) != null) {
        				DynamicEntity fullSampleType =  DynamicEntityModel.findUniqueEntityBy(
        						Codelist.ENTITY_NAME_CODELIST,Codelist.ID,
        						fullDoc.getEntity(SampleEvaluation.SAMPLE_TYPE).getId(),false);
        				fullDoc.put(SampleEvaluation.SAMPLE_TYPE, fullSampleType);
        			};
        			
        			sampleSe.add(fullDoc);
        		} else {
        			
        			if (fullDoc.getEntity(SampleEvaluation.MATERIAL_SAMPLE_TYPE) != null) {
        				DynamicEntity fullSampleType =  DynamicEntityModel.findUniqueEntityBy(
        						Codelist.ENTITY_NAME_CODELIST,Codelist.ID,
        						fullDoc.getEntity(SampleEvaluation.MATERIAL_SAMPLE_TYPE).getId(),false);
        				fullDoc.put(SampleEvaluation.SAMPLE_TYPE, fullSampleType);        				
        			};
        			
        			materialSe.add(fullDoc);
        		}
        	}
        }
        
        fullSampleTracker.put("sampleDetail", sampleSe);
        fullSampleTracker.put("materialDetail", materialSe);
	}

	@Deprecated
    public static DateTime findLastUpdateDate(final DynamicEntity sampleTracker, final String sampleTypeCode) {
        if (sampleTypeCode == null || sampleTracker == null) {
            return null;
        }
        final Collection<DynamicEntity> sampleDetails = sampleTracker.getEntityCollection(SampleTracker.SAMPLE_DETAIL);
        DynamicEntity maxVersionDetail = new DynamicEntityImp();
        maxVersionDetail = matchSampleTypeCode(sampleTypeCode, sampleDetails, maxVersionDetail);

        if (maxVersionDetail.getDateTime(SampleTracker.UPDATED_ON) == null) {
            final Collection<DynamicEntity> materialDetails = sampleTracker
                    .getEntityCollection(SampleTracker.MATERIAL_DETAIL);
            maxVersionDetail = matchSampleTypeCode(sampleTypeCode, materialDetails, maxVersionDetail);
        }
        if (maxVersionDetail.getDateTime(SampleTracker.UPDATED_ON) == null) {
            final Collection<DynamicEntity> documentDetails = sampleTracker
                    .getEntityCollection(SampleTracker.DOCUMENT_DETAIL);
            maxVersionDetail = matchSampleTypeCode(sampleTypeCode, documentDetails, maxVersionDetail);
        }

        final DynamicEntity sampleEvaluation = maxVersionDetail.getEntity(SampleTracker.SAMPLE_EVALUATION);
        if (sampleEvaluation != null){
            return sampleEvaluation.getDateTime(SampleEvaluation.UPDATED_ON);
        }
        return maxVersionDetail.getDateTime(SampleTracker.UPDATED_ON);
    }

    @Deprecated
    private static DynamicEntity matchSampleTypeCode(final String sampleTypeCode,
            final Collection<DynamicEntity> sampleDetails, DynamicEntity maxVersionDetail) {
        for (final DynamicEntity sampleDetail : sampleDetails) {
            final DynamicEntity sampleType = sampleDetail.getEntity(SampleTracker.SAMPLE_TYPE);
            if (sampleType != null && sampleTypeCode.equals(sampleType.getString(Codelist.CODE))) {
                if (maxVersionDetail.getVersion() == null || maxVersionDetail.getVersion() < sampleDetail.getVersion()) {
                    maxVersionDetail = sampleDetail;
                }
            }
        }
        return maxVersionDetail;
    }


    public static DateTime findLatestQAEvaluateDateInST(final DynamicEntity sampleTracker, final String sampleTypeCode,
            final String detailType, final boolean needCheckSampleEvaluation) {
        if (sampleTypeCode == null || sampleTracker == null) {
            return null;
        }
        final Collection<DynamicEntity> details = sampleTracker.getEntityCollection(detailType);

        return findLatestQAEvaluateDateInDetails(sampleTypeCode, details, needCheckSampleEvaluation);
    }

    public static DateTime findLatestQAEvaluateDateInDetails(final String sampleTypeCode, final Collection<DynamicEntity> details, final boolean needCheckSampleEvaluation) {
        DateTime latestDate = null;
        DynamicEntity latestDetail = new DynamicEntityImp();
        for (final DynamicEntity currentSE : details) {
        	
            final DynamicEntity evaluationType = currentSE.getEntity(SampleEvaluation.EVALUATION_TYPE);
            DynamicEntity sampleType = new DynamicEntityImp();
         	if (evaluationType != null) {
         		evaluationType.getString(Codelist.CODE);
         		if (StringUtils.equals("EF01", evaluationType.getString(Codelist.CODE))) {
         			sampleType = currentSE.getEntity(SampleEvaluation.SAMPLE_TYPE);
         		} else {
         			sampleType = currentSE.getEntity(SampleEvaluation.MATERIAL_SAMPLE_TYPE);
         		}
         	}
        	
            if (sampleType != null && sampleTypeCode.equals(sampleType.getString(Codelist.CODE))) {
                if (needCheckSampleEvaluation) {
                    final DateTime currentFitDate = currentSE.getDateTime(SampleEvaluation.CURRENT_FIT_DATE);
                    if (currentFitDate == null) {
                        continue;
                    }

                    final DynamicEntity latestSE = latestDetail;
                    if (latestSE == null) {
                        latestDetail = currentSE;
                        latestDate = currentFitDate;
                        continue;
                    }
                    final DateTime latestFitDate = latestSE.getDateTime(SampleEvaluation.CURRENT_FIT_DATE);

                    if (latestFitDate == null || latestFitDate.compareTo(currentFitDate) < 0) {
                        latestDetail = currentSE;
                        latestDate = currentFitDate;
                    }
                } else {
                    final DateTime latestUpdatedOn = latestDetail.getDateTime(SampleEvaluation.UPDATED_ON);
                    final DateTime nowUpdatedOn = currentSE.getDateTime(SampleTracker.UPDATED_ON);
                    if (latestUpdatedOn == null || latestUpdatedOn.compareTo(nowUpdatedOn) < 0) {
                        latestDetail = currentSE;
                        latestDate = nowUpdatedOn;
                    }
                }
            }
        }

        return latestDate;
    }

    private static void sortDetails(final List<DynamicEntity> sampleDetails) {
        Collections.sort(sampleDetails, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {
                final DynamicEntity sampleType1 = o1.getEntity(SampleEvaluation.SAMPLE_TYPE);
                final DynamicEntity sampleType2 = o2.getEntity(SampleEvaluation.SAMPLE_TYPE);
                if (sampleType1 == null ){
                    return -1;
                }else if (sampleType2 == null){
                    return 1;
                }
                final String sampleTypeCode1 = sampleType1.getString(Codelist.CODE);
                final String sampleTypeCode2 = sampleType2.getString(Codelist.CODE);
                if (sampleTypeCode1.compareTo(sampleTypeCode2) == 0) {
                    final Long version1 = o1.getLong(SampleEvaluation.SAMPLE_VERSION);
                    final Long version2 = o2.getLong(SampleEvaluation.SAMPLE_VERSION);
                    return -(version1.compareTo(version2));
                } else {
                    return sampleTypeCode1.compareTo(sampleTypeCode2);
                }
            }
        });
    }

    public static Collection<DynamicEntity> buildDetailsGrid(final Collection<DynamicEntity> details,
            final String sampleTypeCode) throws DataException {
        final Collection<DynamicEntity> grids = new ArrayList<DynamicEntity>();
//        final Collection<DynamicEntity> latestDetails = getLatestVersionSample(details);
        String lastSampleTypeCode = null;
        for (final DynamicEntity detail : details) {
            if (detail == null || (detail.getEntity(SampleEvaluation.SAMPLE_TYPE) == null && detail.getEntity("documentType") == null)
                    || detail.getBoolean(SampleEvaluation.CUST_CHECKBOX1, false)) {
                continue;
            }
            
            String code = StringUtils.EMPTY;
            final DynamicEntity evaluationType = detail.getEntity(SampleEvaluation.EVALUATION_TYPE);
        	if (evaluationType != null) {
        		evaluationType.getString(Codelist.CODE);
        		if (StringUtils.equals("EF01", evaluationType.getString(Codelist.CODE))) {
        			code = detail.getEntity(SampleEvaluation.SAMPLE_TYPE).getString(Codelist.CODE);
        		} else {
        			code = detail.getEntity(SampleEvaluation.MATERIAL_SAMPLE_TYPE).getString(Codelist.CODE);
        		}
        	}

            if (sampleTypeCode != null && !sampleTypeCode.equals(code)) {
                continue;
            }

            if (lastSampleTypeCode == null) {
                lastSampleTypeCode = code;
            } else if (lastSampleTypeCode != null && !lastSampleTypeCode.equals(code)) {
                grids.add(new DynamicEntityImp());
                lastSampleTypeCode = code;
            }
            grids.add(detail);
        }
        return grids;
    }

    public static Collection<DynamicEntity> buildDetailsGrid(final DynamicEntity sampleTracker,
            final String detailType, final String entityName, final String sampleTypeCode) throws DataException {
        return buildDetailsGrid(sampleTracker.getEntityCollection(detailType), sampleTypeCode);
    }

    public static Collection<DynamicEntity> listSampleType(final List<DynamicEntity> sampleDetails,
            final String sampleTypeCode) {
        final Collection<DynamicEntity> sampleTypeDetails = new ArrayList<DynamicEntity>();
        sortDetails(sampleDetails);
        String lastSampleTypeCode = null;
        for (final DynamicEntity detail : sampleDetails) {
            if (detail == null || detail.getEntity(SampleEvaluation.SAMPLE_TYPE) == null
                    || detail.getBoolean(SampleEvaluation.CUST_CHECKBOX1, false)) {
                continue;
            }

            final String code = detail.getEntity(SampleEvaluation.SAMPLE_TYPE).getString(Codelist.CODE);
            if (sampleTypeCode != null && !sampleTypeCode.equals(code)) {
                continue;
            }

            if (lastSampleTypeCode != null && lastSampleTypeCode.equals(code)) {
                continue;
            } else {
                lastSampleTypeCode = code;
                sampleTypeDetails.add(detail);
            }
        }
        return sampleTypeDetails;
    }

    public static List<DynamicEntity> getSampleResultsFromSampleRecords(final DynamicEntity doc,
            final String sampleTypeCode, final String sampleRecordPath)
            throws ActionException {
        final List<DynamicEntity> measurementSizes = new ArrayList<DynamicEntity>();
        final Collection<DynamicEntity> children = doc.getEntityCollection(sampleRecordPath);
        if (CollectionUtils.isEmpty(children)) {
            return measurementSizes;
        }

        final Map<String, DynamicEntity> latestSampleMap = new TreeMap<String, DynamicEntity>();
        for (final DynamicEntity child : children) {

            if (child == null || child.getEntity(SampleEvaluation.SAMPLE_TYPE) == null
                    || child.getBoolean(SampleEvaluation.CUST_CHECKBOX1, false)) {
                continue;
            }

            final DynamicEntity code = child.getEntity(SampleEvaluation.SAMPLE_TYPE);
            if (code == null || !sampleTypeCode.equals(code.getString(Codelist.CODE))) {
                continue;
            }
            final String sampleId = child.getString(SampleEvaluation.SAMPLE_ID);
            final DynamicEntity verEntity = latestSampleMap.get(sampleId);
            if (verEntity != null
                    && child.getBigDecimal(SampleEvaluation.SAMPLE_VERSION).compareTo(
                            verEntity.getBigDecimal(SampleEvaluation.SAMPLE_VERSION)) <= 0) {
                continue;
            }
            latestSampleMap.put(sampleId, child);
        }

        for (final DynamicEntity de : latestSampleMap.values()) {
            measurementSizes.add(de);
        }

        return measurementSizes;
    }

    public static List<Map<String, Integer>> listFitMeasurement(final List<DynamicEntity> sizes, final int recordPerPage) {
        final List<Map<String, Integer>> indexList = new ArrayList<Map<String, Integer>>();
        if (CollectionUtils.isNotEmpty(sizes)) {
            int pages = (sizes.size() / recordPerPage);
            int leftRecords = sizes.size() % recordPerPage;
            if (leftRecords != 0) {
                pages += 1;
            } else {
                leftRecords = recordPerPage;
            }
            for (int i = 0; i < pages; i++) {
                final Map<String, Integer> row = new HashMap<String, Integer>();
                row.put(INDEX, i * recordPerPage);
                row.put(NUMBER, leftRecords);
                row.put(PAGES, pages);
                indexList.add(row);
            }
        }
        return indexList;
    }

    public static String getAllColours(final List<DynamicEntity> details) {
        final Set<String> colours = new TreeSet<String>();
        if (CollectionUtils.isNotEmpty(details)) {
            for (final DynamicEntity detail : details) {
                String colorName = detail.getString(SampleEvaluation.COLOR_AND_PATTERN_VALUE);
                if (StringUtils.isEmpty(colorName)) {
                    colorName = detail.getString(SampleEvaluation.ALT_COLOR_AND_PATTERN);
                }
                if (StringUtils.isNotEmpty(colorName)){
                	colours.add(colorName);
                }
            }
        }
        String allColour = null;
        if (CollectionUtils.isNotEmpty(colours)) {
            for (final String colour : colours) {
                if (allColour != null) {
                    allColour += "," + colour;
                } else {
                    allColour = colour;
                }
            }
        }
        return allColour;
    }

    public static String getAllQAComments(final List<DynamicEntity> details, final String fieldId) throws DataException {
        final StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(details)) {
            for (final DynamicEntity sampleEvaluation : details) {
                DynamicEntity fullSampleEvaluation;
                if (sampleEvaluation.isFullEntity()) {
                    fullSampleEvaluation = sampleEvaluation;
                } else {
                    fullSampleEvaluation = DynamicEntityModel.findUniqueEntityBy(
                            SampleEvaluation.ENTITY_NAME_SAMPLE_EVALUATION, SampleEvaluation.ID,
                            sampleEvaluation.getId(), false);
                }
                if (fullSampleEvaluation == null) {
                    continue;
                }
                final String comment = fullSampleEvaluation.getString(fieldId);
                if (StringUtils.isEmpty(comment)) {
                    continue;
                }
                final String sizeCode = sampleEvaluation.getString(SampleEvaluation.SIZE_CODE, "[NA]");
                if (sb.length() > 0) {
                    sb.append("\n" + sizeCode + " - " + comment);
                } else {
                    sb.append(sizeCode + " - " + comment);
                }
            }
        }
        return sb.toString();
    }

    @Deprecated
    public static String getAllQAComments(final List<DynamicEntity> details) throws DataException {
        return getAllQAComments(details, SampleEvaluation.CURRENT_COMMENTS);
    }

    public static List<DynamicEntity> getAllQASupportingImages(final List<DynamicEntity> details) throws DataException {
        final List<DynamicEntity> images = new ArrayList<DynamicEntity>();
        if (CollectionUtils.isNotEmpty(details)) {
            for (final DynamicEntity sampleEvaluation : details) {
                DynamicEntity fullSampleEvaluation;
                if (sampleEvaluation.isFullEntity()) {
                    fullSampleEvaluation = sampleEvaluation;
                } else {
                    fullSampleEvaluation = DynamicEntityModel.findUniqueEntityBy(
                            SampleEvaluation.ENTITY_NAME_SAMPLE_EVALUATION, SampleEvaluation.ID,
                            sampleEvaluation.getId(), true);
                }
                if (fullSampleEvaluation == null) {
                    continue;
                }
                images.addAll(fullSampleEvaluation.getEntityCollection(SampleEvaluation.SAMPLE_EVALUATION_IMAGES));
            }
        }
        return images;
    }

    public static Collection makeListToBeShownNice(final Collection<DynamicEntity> original, final int number) {
        final List<DynamicEntity> rlt = new ArrayList<DynamicEntity>(number);
        if (CollectionUtils.isEmpty(original)) {
            for (int i = 0; i < number; i++) {
                rlt.add(new DynamicEntityImp());
            }
            return rlt;
        }
        final int originalSize = original.size();
        final int reminder = originalSize % number;
        if (reminder == 0) {
            return original;
        } else {
            final int page = originalSize / number + 1;
            final int target = page * number;
            for (int i = 0; i < target - originalSize; i++) {
                original.add(new DynamicEntityImp());
            }
            return original;
        }
    }

    @Deprecated
    private static Collection<DynamicEntity> getLatestVersionSample(final Collection<DynamicEntity> collections) {
        final Collection<DynamicEntity> results = new ArrayList<DynamicEntity>();
        for (final DynamicEntity collection : collections) {
            DynamicEntity result = collection;
            boolean isNotRepeat = true;
            if (collection.get(SampleEvaluation.SAMPLE_ID) == null) {
                results.add(result);
                isNotRepeat = false;
            } else {
                for (final DynamicEntity dynamicEntity : results) {
                    if ((dynamicEntity.get(SampleEvaluation.SAMPLE_ID) != null)
                            && (collection.get(SampleEvaluation.SAMPLE_ID).equals(dynamicEntity
                                    .get(SampleEvaluation.SAMPLE_ID)))) {
                        isNotRepeat = false;
                    }
                }
            }

            if (isNotRepeat) {
                int version = Integer.parseInt((String) collection.get(SampleEvaluation.SAMPLE_VERSION));
                for (final DynamicEntity compareCollection : collections) {
                    if ((compareCollection.get(SampleEvaluation.SAMPLE_ID) != null)
                            && (collection.get(SampleEvaluation.SAMPLE_ID).equals(compareCollection
                                    .get(SampleEvaluation.SAMPLE_ID)))) {
                        final int compareVersion = Integer.parseInt((String) compareCollection
                                .get(SampleEvaluation.SAMPLE_VERSION));
                        if (version < compareVersion) {
                            result = compareCollection;
                            version = compareVersion;
                        }
                    }
                }
                results.add(result);
            }
        }

        for (final DynamicEntity result : results) {
            result.put(SampleEvaluation.IS_LATEST, Boolean.valueOf(true));
        }

        return results;
    }

}
