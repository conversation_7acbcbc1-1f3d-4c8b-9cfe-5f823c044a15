// Copyright (c) 1998-2013 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.12.22.0P.09.20
// ============================================================================
// CHANGE LOG
// PEPL.12.22.0P.09.20 : 2024-08-01, sam.liang, PEPL-3173, PEPL-3176
// PEPL.6.6.0P.20.1 : 2017-12-06, sam.liang, PEPL-700
// PEPL.6.5.0P.5.0 : 2017-07-06, jovan.liu, PEPL-153
// PEPL.6.5.0P.4.0 : 2017-07-03, jovan.liu, <PERSON>EPL-27
// PCPL.5.10GA.00.1 : 2015-11-09, <PERSON>, PEGP-465
// CNT.5.9.2  : 2015-07-31, shermen.wu, PEGP-287
// CNT.5.9.1  : 2015-07-23, <PERSON> Tang, PEGP-281
// CNT.5.9.1  : 2015-06-17, <PERSON> Tang, PEGP-213
// CNT.5.9.1  : 2015-07-16, shermen.wu, PEGP-269
// CNT.5.8 GA : 2015-05-06, shermen.wu, PEGP-86
// ============================================================================

package com.core.pepl.printForm;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.core.cbx.data.constants.Codelist;
import com.core.cbx.data.constants.Item;
import com.core.cbx.data.constants.Spec;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.exception.DataException;

/**
 * <AUTHOR>
 */
public final class Pom {

    private Pom() {

    }

    private static String f1 = "field1";
    private static String f2 = "field2";
    private static String f3 = "field3";
    private static String f4 = "field4";
    private static String f5 = "field5";
    private static String f6 = "field6";
    private static String f7 = "field7";
    private static String f8 = "field8";
    private static String f9 = "field9";
    private static String f10 = "field10";
    private static String f11 = "field11";
    private static String f12 = "field12";
    private static String f13 = "field13";
    private static String f14 = "field14";
    private static String f15 = "field15";
    private static String f16 = "field16";
    private static String[] keys = new String[] {f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15, f16};

    private static String key_measurement = "measurement";
    private static String SAMPLE_SIZE_COLUMN = "sampleSizeColumn";
    public static final String FABRIC = "CL01";

    public static Collection exludeEntityByString(final Collection<DynamicEntity> children, final String filed,
            final String excludeValue) {
        final List<DynamicEntity> result = new ArrayList<DynamicEntity>();
        for (final DynamicEntity entity : children) {
            if (entity != null && entity.getString(filed) != null) {
                if (StringUtils.equalsIgnoreCase(excludeValue, entity.getString(filed).trim())) {
                    continue;
                }
                result.add(entity);
            }
        }
        sortMeasurements(result);
        return result;
    }

    private static void sortMeasurements(final List<DynamicEntity> result) {
        Collections.sort(result, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {
                final Long resultSeq1 = o1.getLong(Item.SEQ, 0L);
                final Long resultSeq2 = o2.getLong(Item.SEQ, 0L);
                return resultSeq1.compareTo(resultSeq2);
            }
        });
    }

    @Deprecated
    public static Collection getMeasurementHeader(final Collection<DynamicEntity> specSize) throws DataException {
        return getMeasurementHeader(specSize, null, true);
    }

    public static Collection getMeasurementHeader(final Collection<DynamicEntity> itemSize,
            final String sampleSizeId, final boolean includeTolerances) throws DataException {
        final List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        // sort specSize by sequence
        sortItemSize((List<DynamicEntity>) itemSize);
        // fill values into result
        addLabels(result, (List<DynamicEntity>)itemSize, sampleSizeId, includeTolerances);
        return result;
    }

    @Deprecated
    public static Collection getMeasurementDetail(final Collection<DynamicEntity> specMeasurement,
            final Collection<DynamicEntity> specMeasurementSize, final Collection<DynamicEntity> specSize)
            throws DataException {
        return getMeasurementDetail(specMeasurement, specMeasurementSize, specSize, null, true);
    }

    public static Collection getMeasurementDetail(final Collection<DynamicEntity> specMeasurement,
            final Collection<DynamicEntity> specMeasurementSize, final Collection<DynamicEntity> itemSize,
            final String sampleSizeId, final boolean includeTolerances) throws DataException {
        final List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        // sort specSize by sequence
        sortItemSize((List<DynamicEntity>) itemSize);
        // fill values into result
        mapMeasurementSize(result, specMeasurement, (List<DynamicEntity>) itemSize, specMeasurementSize,
                sampleSizeId, includeTolerances);
        return result;
    }

    /**
     * @param itemSize
     */
    private static void sortItemSize(final List<DynamicEntity> itemSize) {
        Collections.sort(itemSize, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {
                final Long sizeSeq1 = o1.getLong(Item.SIZE_SEQ, 0L);
                final Long sizeSeq2 = o2.getLong(Item.SIZE_SEQ, 0L);
                return sizeSeq1.compareTo(sizeSeq2);
            }
        });
    }

    /**
     * @param result
     * @param specMeasurement
     * @param itemSize
     */
    private static void mapMeasurementSize(final List<Map<String, Object>> result,
            final Collection<DynamicEntity> specMeasurement, final List<DynamicEntity> itemSizes,
            final Collection<DynamicEntity> specMeasurementSize, final String sampleSizeId,
            final boolean includeTolerances) {
        Map<String, Object> row = null;

        // third row :measurementSize data
        for (final DynamicEntity measurement : specMeasurement) {
            row = new HashMap<String, Object>();
            int i = 0;
            final String measurementId = measurement.getId();
            final BigDecimal sampleMeasurement = measurement.getBigDecimal(Item.SAMPLE_MEASUREMENT);

            for (final DynamicEntity itemSize : itemSizes) {
            	//skip inactive size
            	if (itemSize.getBoolean(Item.IS_INACTIVE, false)) {
            		continue;
            	}
            	
                if (CollectionUtils.isEmpty(specMeasurementSize)) {
                    i++;
                } else {
                    final String itemSizeId = itemSize.getId();
                    for (final DynamicEntity size : specMeasurementSize) {

                        // there may be some dirty data in the database, skip such kind of the data
                        if(size.getEntity(Item.SPEC_MEASUREMENT) == null ||
                                size.getEntity(Item.ITEM_SIZE) == null	){
                            continue;
                        }

                        if (size.getEntity(Item.SPEC_MEASUREMENT).getId().equals(measurementId)
                                && size.getEntity(Item.ITEM_SIZE).getId().equals(itemSizeId)) {
                            row.put(keys[i++],  size.getBigDecimal(Item.MEASUREMENT, sampleMeasurement));
                            break;
                        }
                    }
                }
            }
            if (includeTolerances) {
                row.put(keys[i++], measurement.getBigDecimal(Item.TOLERANCE_POSITIVE,null));
                row.put(keys[i++], measurement.getBigDecimal(Item.TOLERANCE_NEGATIVE,null));
            } else {
                row.put(keys[i++], measurement.getBigDecimal(Item.TOLERANCE_POSITIVE,null));
            }
            row.put(key_measurement, measurement);
            row.put(SAMPLE_SIZE_COLUMN, getSampleSizeColumn(itemSizes,sampleSizeId));
            result.add(row);
        }
    }

    private static int getSampleSizeColumn(final List<DynamicEntity> sortedItemSizes, final String sampleSizeId){
        if ( CollectionUtils.isEmpty(sortedItemSizes) || StringUtils.isEmpty(sampleSizeId)) {
            return 0;
        }
        int i = 1;
        for(final DynamicEntity sz: sortedItemSizes){
        	//skip inactive size
        	if (sz.getBoolean(Item.IS_INACTIVE, false)) {
        		continue;
        	}
        	
            if(sz != null && sampleSizeId.equals(sz.getId())){
                 return i;
            }
            i++ ;
        }
        return 0;
    }

    /**
     * @param result
     * @param itemSizes
     */
    private static void addLabels(final List<Map<String, Object>> result, final List<DynamicEntity> itemSizes,
            final String sampleSizeId, final boolean includeTolerances) {
        Map<String, Object> row;
        int k = 0;
        // first row :Labels: size Name and Tolerances
        row = new HashMap<String, Object>();
        for (final DynamicEntity size : itemSizes) {
            // skip inactive size
            if (size.getBoolean(Item.IS_INACTIVE, false)) {
                continue;
            }

            row.put(keys[k++], size.getString(Item.SIZE_NAME));
        }
        if (includeTolerances) {
            row.put(keys[k++], "Tolerances");
            row.put(keys[k++], "Tolerances");
        } else {
            row.put(keys[k++], "Tolerances");
        }
        final int sampleSizeColumn = getSampleSizeColumn(itemSizes,sampleSizeId);
        row.put(SAMPLE_SIZE_COLUMN, sampleSizeColumn);
        result.add(row);

        // second row :Labels: size's altLabel and (+),(-)
        k = 0;
        row = new HashMap<String, Object>();
        for (final DynamicEntity size : itemSizes) {
        	//skip inactive size
        	if (size.getBoolean(Item.IS_INACTIVE, false)) {
        		continue;
        	}
            row.put(keys[k++], size.getString(Item.SIZE_DISPLAY_NAME));
        }
        if (includeTolerances) {
            row.put(keys[k++], "(+)");
            row.put(keys[k++], "(-)");
        }else{
            row.put(keys[k++], "(+/-)");
        }
        row.put(SAMPLE_SIZE_COLUMN, sampleSizeColumn);
        result.add(row);
    }

    @Deprecated
    public static Collection getMeasurementImage(final DynamicEntity specSet) throws DataException {
        return getSpecMeasurementImage(specSet, Spec.CODE, "POM Sketch");
    }

    @Deprecated
    public static Collection getSpecMeasurementImage(final DynamicEntity specSet,
            final String fieldName, final Object value) throws DataException {
        final List<DynamicEntity> specMeasurement = (List<DynamicEntity>) specSet.getEntityCollection(Spec.SPEC_MEASUREMENTS);
        return getSpecMeasurementImage(specMeasurement, fieldName, value);
    }

    public static Collection getSpecMeasurementImage(final List<DynamicEntity> specMeasurement,
            final String fieldName, final Object value) throws DataException {
        Util.sortListByLongFields(specMeasurement, Item.SEQ);
        final List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (final DynamicEntity measurement : specMeasurement) {
            if (value == null){
               if (measurement.getEntity(Item.IMAGE_ID) != null){
                    final Map<String, Object> row = new HashMap<String, Object>();
                    row.put(f1, measurement.getEntity(Item.IMAGE_ID));
                    result.add(row);
                }
            } else if (value.equals(measurement.get(fieldName))) {
                final Map<String, Object> row = new HashMap<String, Object>();
                row.put(f1, measurement.getEntity(Item.IMAGE_ID));
                result.add(row);
            }
        }
        return result;
    }

    @Deprecated
    public static String getMaterialNamesByType(final DynamicEntity specSet, final String code) {
        if (specSet != null) {
            final List<DynamicEntity> specMaterials = (List<DynamicEntity>) specSet.getEntityCollection(Spec.SPEC_MATERIAL);
            return getMaterialNamesByType(specMaterials, code);
        }
        return StringUtils.EMPTY;
    }

    public static String getMaterialNamesByType(final List<DynamicEntity> specMaterials, final String code) {
        final StringBuilder fabric = new StringBuilder();
        if (CollectionUtils.isNotEmpty(specMaterials)) {
            for (final DynamicEntity material : specMaterials) {
                final DynamicEntity materialType = material.getEntity(Item.MATERIAL_TYPE);
                final String materialName = material.getString(Item.MATERIAL_NAME);
                if (materialType == null || !code.equals(materialType.getString(Codelist.CODE))
                        || StringUtils.isEmpty(materialName)) {
                    continue;
                }
                if (fabric.length() != 0) {
                    fabric.append("; " + materialName);
                } else {
                    fabric.append(materialName);
                }
            }
        }
        return fabric.toString();
    }
    
    public static int getNumberOfActiveSize(final Collection<DynamicEntity> itemSizes) {
    	int NumberOfSize = 0 ;
    	for (final DynamicEntity size : itemSizes) {
    		// skip inactive size
    		if (size.getBoolean(Item.IS_INACTIVE, false)) {
    			continue;
    		}
    		
    		NumberOfSize++;
    	}
    	return NumberOfSize;
    }
}
