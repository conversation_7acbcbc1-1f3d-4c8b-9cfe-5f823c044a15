// Copyright (c) 1998-2013 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.6.6.0
// ============================================================================
// CHANGE LOG
// PEPL.8.2.0P.R2.5.0 : 2018-04-16, caroline.qu, PEPL-665
// PEPL.6.6.0P.7.0 : 2017-07-24, jovan.liu, PEPL-27_v2
// PEPL.6.5.0P.4.0 : 2017-07-03, jovan.liu, PEPL-27
// CNT.5.9.2  : 2015-08-19, harry.tang, PEGP-305
// CNT.5.9.2  : 2015-07-31, shermen.wu, PEGP-287
// CNT.5.9.1  : 2015-07-16, shermen.wu, PEGP-269
// CNT.5.8 GA : 2015-05-06, shermen.wu, PEGP-86
// ============================================================================

package com.core.pepl.printForm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.core.cbx.data.constants.Codelist;
import com.core.cbx.data.constants.Item;
import com.core.cbx.data.constants.Spec;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.DynamicEntityImp;
import com.core.cbx.data.exception.DataException;

/**
 * <AUTHOR>
 */
public final class Bom {

    private Bom() {

    }

    private static String f1 = "field1";
    private static String f2 = "field2";
    private static String f3 = "field3";
    private static String f4 = "field4";
    private static String f5 = "field5";
    private static String f6 = "field6";
    private static String f7 = "field7";
    private static String f8 = "field8";
    private static String f9 = "field9";
    private static String f10 = "field10";
    private static String f11 = "field11";
    private static String f12 = "field12";
    private static String f13 = "field13";
    private static String f14 = "field14";
    private static String f15 = "field15";
    private static String f16 = "field16";
    private static String f17 = "field17";
    private static String f18 = "field18";
    private static String f19 = "field19";
    private static String f20 = "field20";
    private static String f21 = "field21";
    private static String f22 = "field22";
    private static String[] keys = new String[] {f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15, f16,
            f17, f18, f19, f20, f21, f22};
    private static String key_part = "part";
    final static String MATERIAL_TYPE_CODE = "CL01";

    @Deprecated
    public static Collection getData(final DynamicEntity specSet) throws DataException {
        final Collection<DynamicEntity> specColor = specSet.getEntityCollection(Spec.SPEC_COLOR);
        final Collection<DynamicEntity> specPartColor = specSet.getEntityCollection(Spec.SPEC_PART_COLOR);
        final Collection<DynamicEntity> specParts = specSet.getEntityCollection(Spec.SPEC_PART);
        final Collection<DynamicEntity> specMaterials = specSet.getEntityCollection(Spec.SPEC_MATERIAL);
        return getData(specColor, specPartColor, specParts, specMaterials);
    }

    public static Collection getData(final Collection<DynamicEntity> itemColor, final Collection<DynamicEntity> specColorBomItemColor,
            final Collection<DynamicEntity> specColorBom, final Collection<DynamicEntity> specMaterial) throws DataException {
        final List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();

        final Collection<DynamicEntity> printingParts = constructParts(specColorBom, specMaterial);

        if (CollectionUtils.isEmpty(printingParts)) {
            result.add(new HashMap(0));
            return result;
        }

        // sort specParts by Material Type, Material No
        sortParts((List<DynamicEntity>) printingParts);
        // fill values into result
        mapRow(result, specMaterial, itemColor, printingParts, specColorBomItemColor);
        return result;
    }

    private static Collection<DynamicEntity> constructParts(final Collection<DynamicEntity> specColorBom,
            final Collection<DynamicEntity> specMaterial) {
        final List<DynamicEntity> printingParts = new ArrayList<DynamicEntity>();
        if (CollectionUtils.isEmpty(specMaterial)) {
            return printingParts;
        }
        final Set<String> materialIds = new HashSet<String>();
        if (CollectionUtils.isNotEmpty(specColorBom)) {
            for (final DynamicEntity scb : specColorBom) {
                materialIds.add(scb.getEntity(Item.SPEC_MATERIAL).getId());
                printingParts.add(scb);
            }
        }

        for (final DynamicEntity sm : specMaterial) {
            if (materialIds.add(sm.getId())) {
                final DynamicEntity scb = new DynamicEntityImp();
                scb.setId("dummy_" + sm.getId());
                scb.put(Item.SPEC_MATERIAL, sm);
                printingParts.add(scb);
            }
        }
        return printingParts;
    }

    private static void sortParts(final List<DynamicEntity> printingParts) {
        Collections.sort(printingParts, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {
                final DynamicEntity specMaterial1 = o1.getEntity(Item.SPEC_MATERIAL);
                final DynamicEntity specMaterial2 = o2.getEntity(Item.SPEC_MATERIAL);

                final String materialType1 = specMaterial1.getString(Item.MATERIAL_TYPE_NAME, "");
                final String materialType2 = specMaterial2.getString(Item.MATERIAL_TYPE_NAME, "");
                int rlt = materialType1.compareTo(materialType2);
                if (rlt != 0) {
                    return rlt;
                }

                final String materialNo1 = specMaterial1.getString(Item.MATERIAL_NO, "");
                final String materialNo2 = specMaterial2.getString(Item.MATERIAL_NO, "");
                rlt = materialNo1.compareTo(materialNo2);
                if (rlt != 0) {
                    return rlt;
                }

                final String materialName1 = specMaterial1.getString(Item.MATERIAL_NAME, "");
                final String materialName2 = specMaterial2.getString(Item.MATERIAL_NAME, "");
                return materialName1.compareTo(materialName2);
            }
        });
    }

    public static void mapRow(final List<Map<String, Object>> result, final Collection<DynamicEntity> specMaterials,
            final Collection<DynamicEntity> itemColor, final Collection<DynamicEntity> printingParts,
            final Collection<DynamicEntity> specColorBomItemColor) throws DataException {
        final String isDuplicated = "isDuplicated";
        Map<String, Object> row;
        final Set<String> checkDuplicate = new HashSet<String>();
        for (final DynamicEntity part : printingParts) {
            row = new HashMap<String, Object>();
            int i = 0;
            final DynamicEntity specMaterial = part.getEntity(Item.SPEC_MATERIAL);
            final String combineFields = (specMaterial.getBigDecimal(Item.CONSUMPTION) == null ? "" : (specMaterial
                    .getBigDecimal(Item.CONSUMPTION).toString() + ","))
                    + (specMaterial.getString(Item.NOTES_OR_INSTRUCTIONS) == null ? "" : (specMaterial.getString(
                            Item.NOTES_OR_INSTRUCTIONS, "") + ","))
                    + specMaterial.getString(Item.CUST_CODELIST3_NAME, "");
            // check the duplicate by 3 fields
            final boolean isDuplicate = !checkDuplicate.add(specMaterial.getString(Item.MATERIAL_NAME)
                    + specMaterial.getString(Item.MATERIAL_NO) + combineFields);
            if (isDuplicate) {
                // skip 4 fields
                i += 4;
                row.put(isDuplicated, true);
            } else {
                row.put(keys[i++], specMaterial.getEntity(Item.MATERIAL_IMAGE_ID));
                row.put(keys[i++], specMaterial.getString(Item.MATERIAL_NAME));
                row.put(keys[i++], specMaterial.getString(Item.MATERIAL_NO));
                row.put(keys[i++], combineFields);
                row.put(isDuplicated, false);

              //PEPL-665 show COMPOSITION value when MATERIAL_TYPE = Fabric
                final DynamicEntity materialType = specMaterial.getEntity(Item.MATERIAL_TYPE);
                if(materialType != null){
                    String materialTypeCode = materialType.getString(Codelist.CODE);
                    if(MATERIAL_TYPE_CODE.equals(materialTypeCode)){
                        row.put(Item.COMPOSITION, specMaterial.getString(Item.COMPOSITION));
                    }
                }else{
                    row.put(Item.COMPOSITION, null);
                }
            }
            row.put(keys[i++], specMaterial.getString(Item.CUST_CODELIST1_NAME));
            row.put(keys[i++], specMaterial.getString(Item.CUST_CODELIST2_NAME));
            row.put(key_part, part);
            mapColors(specColorBomItemColor, row, part, i);
            result.add(row);
        }
    }

    private static void mapColors(final Collection<DynamicEntity> specColorBomItemColor, final Map<String, Object> row,
            final DynamicEntity part, int i) throws DataException {
        final List<DynamicEntity> spcColors = Util.findEntities(specColorBomItemColor, new Find("specColorBom.id", part.getId()));
        DynamicEntity mc;
        // limit of the number of specPartColor are 6
        for (int j = 0; j < spcColors.size() && j < 6; j++) {
            mc = spcColors.get(j).getEntity(Item.MATERIAL_COLOR);
            if (mc != null) {
                row.put(keys[i++], StringUtils.defaultIfEmpty(mc.getString(Item.SHORT_NAME), ""));
            }
        }
    }
}
