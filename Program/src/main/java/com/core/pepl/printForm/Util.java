// Copyright (c) 1998-2013 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.8 GA
// ============================================================================
// CHANGE LOG
// PEPL.8.2.0P.R2.5.0 : 2018-04-16, caroline.qu, PEPL-665
// PCPL.5.10GA.00.1 : 2015-11-09, <PERSON>, PEGP-465
// CNT.5.8 GA : 2015-05-06, shermen.wu, PEGP-86
// ============================================================================

package com.core.pepl.printForm;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.core.cbx.action.constants.ExcelOperationConstants;
import com.core.cbx.action.exception.ActionException;
import com.core.cbx.common.type.DateTime;
import com.core.cbx.data.DynamicEntityModel;
import com.core.cbx.data.constants.Hcl;
import com.core.cbx.data.constants.Item;
import com.core.cbx.data.constants.Spec;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.DynamicEntityImp;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.data.search.Criterion;
import com.core.cbx.data.search.Order;
import com.core.cbx.data.search.Restriction;
import com.core.cbx.hcl.HclDef;
import com.core.cbx.hcl.HclNode;
import com.core.cbx.util.HclUtil;

/**
 * <AUTHOR>
 */
public final class Util {

    private Util() {

    }

    public static DynamicEntity getSpecEntity(final String refNo, final Long ver) throws DataException {
        final Criterion c = new Criterion(Spec.ENTITY_NAME_SPEC);
        c.addRestriction(Restriction.eq(Spec.REF_NO, refNo));
        c.addRestriction(Restriction.eq(Spec.VERSION, ver));
        c.addOrder(Order.desc(Spec.UPDATED_ON));
        return DynamicEntityModel.findUniqueBy(c, true);
    }

    public static DynamicEntity getItemEntity(final String refNo, final Long ver) throws DataException {
        final Criterion c = new Criterion(Item.ENTITY_NAME_ITEM);
        c.addRestriction(Restriction.eq(Item.SPEC_REF, refNo));
        //c.addRestriction(Restriction.eq(Item.SPEC_VER, ver));
        c.addOrder(Order.desc(Item.UPDATED_ON));
        return DynamicEntityModel.findUniqueBy(c, true);
    }

    public static Collection getTickedSpecRequirementEntity(final Collection<DynamicEntity> specRequirement) throws DataException {

        final List<DynamicEntity> result = new ArrayList<DynamicEntity>();

        if (CollectionUtils.isNotEmpty(specRequirement)) {
            for (final DynamicEntity sr : specRequirement) {
                if(Boolean.TRUE.equals(sr.get(Item.CUST_CHECKBOX1))){
                    result.add(sr);
                }
            }
        }

        return result;
    }

    public static List<DynamicEntity> findEntities(final Collection<DynamicEntity> children,
            final Find... findingCriteriaes) throws DataException {
        final List<DynamicEntity> result = new ArrayList<DynamicEntity>();
        entityForLoop: for (final DynamicEntity de : children) {
            if (de == null) {
                continue;
            }
            for (final Find fc : findingCriteriaes) {
                if (!fc.evaluate(de)) {
                    continue entityForLoop;
                }
            }
            result.add(de);
        }
        return result;
    }

    public static DynamicEntity findEntity(final Collection<DynamicEntity> children, final Find... findingCriteriaes)
            throws DataException {
        final List<DynamicEntity> des = findEntities(children, findingCriteriaes);
        return CollectionUtils.isEmpty(des) ? new DynamicEntityImp() : des.get(0);
    }

    public static DynamicEntity loadFullEntity(final DynamicEntity snapshot) throws DataException {
        if (snapshot == null) {
            return new DynamicEntityImp();
        }
        if (!snapshot.isFullEntity()) {
            return DynamicEntityModel.findUniqueEntityBy(snapshot.getEntityName(), "id", snapshot.getId(), true);
        }
        return snapshot;
    }

    public static DynamicEntity loadFullEntity(final String id, final String entityName) throws DataException {
        if (id == null) {
            return new DynamicEntityImp();
        }
        return DynamicEntityModel.findUniqueEntityBy(entityName, "id", id, true);
    }

    public static String concatValues(final Collection<DynamicEntity> children, final String field,
            final String separator) {
        final StringBuilder sb = new StringBuilder();
        for (final DynamicEntity de : children) {
            if (de == null || StringUtils.isEmpty(de.getString(field))) {
                continue;
            }
            sb.append(de.getString(field));
            sb.append(separator);
        }
        return StringUtils.removeEnd(sb.toString(), separator);
    }

    public static String getHcl1stNode(final DynamicEntity doc) throws ActionException {
        String rlt = StringUtils.EMPTY;
        final DynamicEntity hclEntity = doc.getEntity(Item.MERCHANDISE_HIERARCHY);
        if (hclEntity != null) {
            try {
                final HclDef hclDef = HclUtil.findHclDefByName(ExcelOperationConstants.HCL_PRODUCT_HIERARCHY);
                final String lineage = hclEntity.getString(Hcl.LINEAGE);
                final HclNode[] hclNodes = hclDef.findNodesByLineage(lineage);
                if (hclNodes.length >= 1) {
                    final DynamicEntity Hcl1st = hclNodes[0].getHclNodeEntity();
                    rlt = Hcl1st.getString("code") + "-" + Hcl1st.getString("name");
                }
            } catch (final DataException e) {
                throw new ActionException("000101", "Data exception found. " + "(hclName="
                        + hclEntity.getString(Hcl.NAME) + ")");
            }
        }
        return rlt;
    }

    public static String toString(final Object o) {
        if (o == null) {
            return null;
        }
        return o.toString();
    }

    public static List<DynamicEntity> getOddImages(final List<DynamicEntity> images) {
        int i = 1;
        final List<DynamicEntity> result = new ArrayList<DynamicEntity>();
        for (final DynamicEntity de : images) {
            if (de != null && i % 2 == 0) {
                result.add(de);
            }
            i++;
        }
        return result;
    }

    public static List<DynamicEntity> getEvenImages(final List<DynamicEntity> images) {
        int i = 1;
        final List<DynamicEntity> result = new ArrayList<DynamicEntity>();
        for (final DynamicEntity de : images) {
            if (de != null && i % 2 == 1) {
                result.add(de);
            }
            i++;
        }
        return result;
    }

    public static List<DynamicEntity> sortListByStringFields(final List<DynamicEntity> collection, final String field) {
        Collections.sort(collection, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {
                final String fieldValue1 = o1.getString(field, "");
                final String fieldValue2 = o2.getString(field, "");
                return fieldValue1.compareTo(fieldValue2);
            }
        });
        return collection;
    }

    public static List<DynamicEntity> sortListByLongFields(final List<DynamicEntity> collection, final String field) {
        Collections.sort(collection, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {
                final Long fieldValue1 = o1.getLong(field, Long.MIN_VALUE);
                final Long fieldValue2 = o2.getLong(field, Long.MIN_VALUE);
                return fieldValue1.compareTo(fieldValue2);
            }
        });
        return collection;
    }

    public static Object getValueFromEntity(final DynamicEntity de, final String[] pathes) throws ActionException {
        Object finalValue = null;
        for (final String path : pathes) {
            Object value = null;
            if (finalValue == null) {
                value = de.get(path);
            } else if (finalValue instanceof DynamicEntity) {
                value = ((DynamicEntity) finalValue).get(path);
            }
            if (value == null) {
                return null;
            } else if (value instanceof DynamicEntity && !((DynamicEntity) value).isFullEntity()) {
                try {
                    finalValue = DynamicEntityModel.findUniqueEntityBy(((DynamicEntity) value).getEntityName(), "id",
                            ((DynamicEntity) value).getId(), false);
                } catch (final DataException e) {
                    throw new ActionException(e.getId(), e);
                }
            } else {
                finalValue = value;
            }
        }
        return finalValue;
    }

    public static List<DynamicEntity> sortList(final List<DynamicEntity> collection, final Sort... sorts) {
        Collections.sort(collection, new Comparator<DynamicEntity>() {
            @Override
            public int compare(final DynamicEntity o1, final DynamicEntity o2) {

                int rlt = 0;

                for (final Sort s : sorts) {

                    final String[] fieldPath = s.getFieldPath();
                    final Object defaultObj = s.getDefaultFornull();

                    Object fieldValue1 = null;
                    Object fieldValue2 = null;

                    // to get the field value from entity
                    try {
                        fieldValue1 = getValueFromEntity(o1, fieldPath);
                    } catch (final Throwable e) {
                        // treat the value is null when exception occureed
                    }
                    try {
                        fieldValue2 = getValueFromEntity(o2, fieldPath);
                    } catch (final Throwable e) {
                        // treat the value is null when exception occureed
                    }

                    // consider business need, default value for null case
                    if (fieldValue1 == null) {
                        fieldValue1 = defaultObj;
                    }
                    if (fieldValue2 == null) {
                        fieldValue2 = defaultObj;
                    }

                    // compare values
                    if (fieldValue1 == null || fieldValue2 == null) {
                        // compare value when one of them is null
                        rlt = compareNullObject(fieldValue1, fieldValue2, s);
                    } else {
                        // compare according to fieldValue1 class, assume value1 is same type as value2
                        rlt = compareNotNullObject(fieldValue1, fieldValue2, s);
                    }

                    // only continue looping to support multiple fields sorting when current compare result is equal
                    if (rlt != 0) {
                        break;
                    }
                }
                return rlt;
            }

            private int compareNullObject(final Object fieldValue1, final Object fieldValue2, final Sort s) {
                int rlt = 0;
                // default value when null
                if (s.isNullLast()) {
                    if (fieldValue1 == null && fieldValue2 != null) {
                        rlt = 1;
                    } else if (fieldValue2 == null && fieldValue1 != null) {
                        rlt = -1;
                    } else if (fieldValue2 == null && fieldValue1 == null) {
                        rlt = 0;
                    }
                } else {
                    if (fieldValue1 == null && fieldValue2 != null) {
                        rlt = -1;
                    } else if (fieldValue2 == null && fieldValue1 != null) {
                        rlt = 1;
                    } else if (fieldValue2 == null && fieldValue1 == null) {
                        rlt = 0;
                    }
                }
                return rlt;
            }

            private int compareNotNullObject(final Object o1, final Object o2, final Sort s) {
                int rlt = 0;
                if (o1 instanceof BigDecimal) {
                    rlt = ((BigDecimal) o1).compareTo((BigDecimal) o2);
                } else if (o1 instanceof Long) {
                    rlt = ((Long) o1).compareTo((Long) o2);
                } else if (o1 instanceof DateTime) {
                    rlt = ((DateTime) o1).compareTo((DateTime) o2);
                } else {
                    rlt = o1.toString().compareTo(o2.toString());
                }

                // consider if ascending
                if (!s.isAsc()) {
                    rlt = -1 * rlt;
                }
                return rlt;
            }
        });
        return collection;
    }

    public static void main(final String[] args) throws Exception {
        final DynamicEntity lev3 = new DynamicEntityImp();
        lev3.setEntityName("lev3");

        lev3.putValue("value1", new BigDecimal("123"));
        lev3.putValue("value2", DateTime.parseToDateTime("20100101", "yyyyMMdd"));
        lev3.putValue("value3", "abc");

        final DynamicEntity lev2 = new DynamicEntityImp();
        lev2.setEntityName("lev2");
        lev2.put("lev3", lev3);
        lev2.putValue("value1", new BigDecimal("123"));
        lev2.putValue("value2", DateTime.parseToDateTime("20100101", "yyyyMMdd"));
        lev2.putValue("value3", "1235");

        final DynamicEntity lev1 = new DynamicEntityImp();
        lev1.setEntityName("lev1");
        lev1.put("lev2", lev2);
        lev1.putValue("value1", new BigDecimal("123"));
        lev1.putValue("value2", DateTime.parseToDateTime("20100101", "yyyyMMdd"));
        lev1.putValue("value3", "1234");
        lev1.putValue("value4", "name1,name2");

        final List<DynamicEntity> lists = new ArrayList<DynamicEntity>();
        lists.add(lev1);
        lists.add(lev2);
        lists.add(lev3);

        // //////////test/////////////////////////
        System.out.println("Following result should be true");
        System.out.println("Test [Contains]: " + new Find("value4", "~", "name2").evaluate(lev1));

        System.out.println("Test [Less than]: " + new Find("value1", "<", 123.100).evaluate(lev3));
        System.out.println("Test NOT [Greater than or equal to]: " + !new Find("value1", ">=", 123.100).evaluate(lev3));
        System.out.println("Test [Greater than or equal to]: " + new Find("value1", ">=", 1).evaluate(lev3));
        System.out.println("Test [less than]: " + !new Find("value1", "<", 1).evaluate(lev3));
        System.out.println(new Find("lev3.value2", DateTime.parseToDateTime("2010JAN01", "yyyyMMMdd")).evaluate(lev2));
        System.out.println(!new Find("lev3.value2", "<", DateTime.parseToDateTime("2010JAN01", "yyyyMMMdd"))
                .evaluate(lev2));
        System.out.println(new Find("lev2.lev3.value3", "abc").evaluate(lev1));
        System.out.println(!new Find().isNull("lev2.lev3").evaluate(lev1));
        System.out.println();
        System.out.println("Following test will not print null");

        System.out.println(Util.findEntity(lists, new Find("value3", "=", "abc")).getEntityName());

        for (final DynamicEntity de : Util.findEntities(lists, new Find("value3", ">=", "123"), new Find("value1",
                ">=", new BigDecimal("1.00")))) {
            System.out.print(de.getEntityName() + ", ");
        }
        System.out.println();

        final DynamicEntity typeListOpA = new DynamicEntityImp();
        typeListOpA.put("name", "A");
        final DynamicEntity typeListOpB = new DynamicEntityImp();
        typeListOpB.put("name", "B");
        final DynamicEntity typeListOpC = new DynamicEntityImp();
        typeListOpC.put("name", "C");

        final DynamicEntity top1 = new DynamicEntityImp();
        final List<DynamicEntity> typeList1 = new ArrayList<DynamicEntity>();
        typeList1.add(typeListOpA);
        typeList1.add(typeListOpB);
        top1.put("type", typeList1);
        top1.setEntityName("top1");

        final DynamicEntity top2 = new DynamicEntityImp();
        final List<DynamicEntity> typeList2 = new ArrayList<DynamicEntity>();
        typeList2.add(typeListOpA);
        typeList2.add(typeListOpC);
        top2.put("type", typeList2);
        top2.setEntityName("top2");

        final DynamicEntity top3 = new DynamicEntityImp();
        final List<DynamicEntity> typeList3 = new ArrayList<DynamicEntity>();
        typeList3.add(typeListOpB);
        typeList3.add(typeListOpC);
        top3.put("type", typeList3);
        top3.setEntityName("top3");

        final List<DynamicEntity> topList = new ArrayList<DynamicEntity>();
        topList.add(top1);
        topList.add(top2);
        topList.add(top3);

        System.out.println(Util.findEntity(topList, new Find("type", new Find("name", "C"))));

        // //////////test/////////////////////////
        final List<DynamicEntity> demo = new ArrayList<DynamicEntity>();
        final DynamicEntity demo1 = new DynamicEntityImp();
        demo1.put("dafault", true);
        demo1.setEntityName("demo1");
        final DynamicEntity demo2 = new DynamicEntityImp();
        demo2.put("dafault", false);
        demo2.setEntityName("demo2");
        demo.add(demo1);
        demo.add(demo2);
        System.out.println(Util.findEntity(demo, new Find("dafault", true)));

    }

}
