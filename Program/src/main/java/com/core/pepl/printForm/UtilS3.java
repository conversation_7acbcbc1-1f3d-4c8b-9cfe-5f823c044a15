// Copyright (c) 1998-2013 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.8.2.0 GA
// ============================================================================
// CHANGE LOG
// PEPL.8.2.0P.2R.39.0 : 2019-08-27, milo.luo, PEPL-1464
// ============================================================================

package com.core.pepl.printForm;

import java.io.File;
import java.io.InputStream;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.profile.ProfileCredentialsProvider;
import com.amazonaws.auth.profile.ProfilesConfigFile;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.S3Object;

/**
 * <AUTHOR>
 */
public final class UtilS3 {
	
    private static final String S3_ATTACHMENT_PREFIX = "ATTACHMENT/";

    private UtilS3() {

    }

    public static byte[] getImageFromS3(final String filePath, final String bucketName, final String credentialsPath, final String configS3){
    	
    	if(StringUtils.isBlank(filePath) || StringUtils.isBlank(bucketName) || StringUtils.isBlank(credentialsPath) || StringUtils.isBlank(configS3)){
    		return null;
    	}
    	
    	InputStream is = null;
    	byte[] data = null;
        try {
            is = resolveFile(filePath, bucketName, credentialsPath, configS3);
            if (is != null) {
            	  data = IOUtils.toByteArray(is);
            }
        } catch (Exception e) {
            System.out.println("can not resolveFile in getImageFromS3" + e);
		}  finally {
            IOUtils.closeQuietly(is);
        }
        return data;
    }
    
    private static InputStream resolveFile(final String path, final String bucketName, final String credentialsPath, final String configS3) throws Exception {
        InputStream data = null;
            data = resolveRealFile(path, bucketName, credentialsPath, configS3);
            if (data == null) {
                return data;
            }
        return data;
    }
    
	public static String getTempFolderPath() {
		String tempFolderPath = FileUtils.getTempDirectoryPath();
		if (!StringUtils.endsWith(tempFolderPath, File.separator)) {
			tempFolderPath = tempFolderPath + File.separator;
		}
		return tempFolderPath;
	}
    
    private static InputStream resolveRealFile(final String filePath, final String bucketName, 
    		final String credentialsPath, final String configS3) throws Exception {
        if (bucketName == null) {
            throw new Exception("Cannot find bucketName.");
        }

        final AmazonS3Client s3Client = connectS3(credentialsPath, configS3);
        InputStream in = null;
        try {
            final String s3FilePath = resolveS3AttachmentKey(filePath);
            final S3Object s3Obj = s3Client.getObject(bucketName, s3FilePath);
            in = s3Obj.getObjectContent();
        } catch (final Exception e) {
        	System.out.println("Get s3 file error:" + "Failed to resolve " + filePath + " file." + e);
        }
        return in;
    }
    
    private static String resolveS3AttachmentKey(final String filePath) {
        return S3_ATTACHMENT_PREFIX + filePath;
    }

    public static AmazonS3Client connectS3(final String credentialsPath, final String configS3) {
        AWSCredentialsProvider  credentialsProvider = createCredentialsProvider(credentialsPath);
        final AmazonS3Client s3Client = new AmazonS3Client(credentialsProvider.getCredentials());
        final String region = getRegion(configS3);
        if (StringUtils.isNotBlank(region)) { // use region
            s3Client.withRegion(Regions.fromName(getRegion(configS3)));
        }
        s3Client.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
        return s3Client;
    }
    
    public static String getRegion(final String configS3) {
        if (StringUtils.isNotBlank(configS3)) {
            final String[] s3Confs = StringUtils.split(configS3, '.');
            if (s3Confs.length == 2) {
                return s3Confs[0];
            }
        }

        return null;
    }
    
    private static AWSCredentialsProvider createCredentialsProvider(final String credentialsPath) {
        ProfilesConfigFile configFile = null;
        if (StringUtils.isNotBlank(credentialsPath)) {
            configFile = new ProfilesConfigFile(credentialsPath);
        } else {
            configFile = new ProfilesConfigFile();
        }
        AWSCredentialsProvider provider = new ProfileCredentialsProvider(configFile, null);
        return provider;
    }
    
}
