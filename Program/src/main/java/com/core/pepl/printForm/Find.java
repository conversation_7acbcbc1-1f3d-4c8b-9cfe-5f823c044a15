// Copyright (c) 1998-2013 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.8 GA
// ============================================================================
// CHANGE LOG
// CNT.5.8 GA : 2015-05-06, shermen.wu, PEGP-86
// ============================================================================

package com.core.pepl.printForm;

import java.math.BigDecimal;
import java.util.Collection;

import org.apache.commons.lang.StringUtils;

import com.core.cbx.common.type.DateTime;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.exception.DataException;

/**
 * <AUTHOR>
 */

public class Find {

   private String field;

   private String operater;

   private Object value;

   private boolean checkNull = false;

   private CompareType compareType;

   public Find() {

   };

   public Find(final String field, final String operater, final Object value) {
       this.field = field;
       this.operater = operater;
       this.value = value;
       this.checkNull = false;
       setCompareType();
   }

   public Find(final String field, final Object value) {
       this.field = field;
       this.value = value;
       this.operater = "=";
       this.checkNull = false;
       setCompareType();
   }

   public Find(final Object value) {
       this.field = "id";
       this.value = value;
       this.operater = "=";
       this.checkNull = false;
       setCompareType();
   }

   public Find isNull(final String field) {
       this.field = field;
       this.checkNull = true;
       setCompareType();
       return this;
   }

   private enum CompareType {
       find, number, date, object,Boolean
   }

   private void setCompareType() {
       if (value instanceof Find) {
           compareType = CompareType.find;
       } else if (value instanceof Number) {
           compareType = CompareType.number;
       } else if (value instanceof DateTime) {
           compareType = CompareType.date;
       } else if (value instanceof Boolean) {
           compareType = CompareType.Boolean;
       }else {
           compareType = CompareType.object;
       }
   }

   private Object getObject(final DynamicEntity de, final String field) {
       switch (compareType) {
           case find:
               return de.getEntityCollection(field);
           case number:
               return de.getBigDecimal(field);
           case date:
               return de.getDateTime(field);
           case Boolean:
               return de.getBoolean(field);
           default:
               return de.get(field);
      }
   }

   public boolean contain(final String valueGetFromEntity,final String value) {
       final String[] str = valueGetFromEntity.split(",");
       if (str == null) {
        return false;
       }
       final int level = str.length;
       String tem = null;
       if (level >= 1) {
           for (int i = 0; i < level; i++) {
               tem = str[i];
               if (StringUtils.equals(tem, value)) {
                   return true;
               }
           }
       }
       return false;
   }

   boolean evaluate(final DynamicEntity entity) throws DataException {
       final String[] fields = field.split("\\.");
       final int level = fields.length;
       Object valueGetFromEntity = null;
       if (level > 1) {
           DynamicEntity tem = entity;
           for (int i = 0; i < level - 1; i++) {
               tem = tem.getEntity(fields[i]);
               if (tem == null) {
                   break;
               }
                tem = Util.loadFullEntity(tem);
           }
           if (tem != null) {
               valueGetFromEntity = getObject(tem, fields[level - 1]);
           } else {
               valueGetFromEntity = null;
           }
       } else {
           valueGetFromEntity =  getObject(entity, fields[0]);
       }

       if (checkNull) {
           return valueGetFromEntity == null;
       }
       if (valueGetFromEntity == null) {
           return false;
       }
       int comareResult = 0;
       switch (compareType) {
           case find:
               for(final DynamicEntity de: (Collection<DynamicEntity>)valueGetFromEntity){
                   if(((Find)value).evaluate(de)){
                       return true;
                   }
               }
               return false;
           case number:
               BigDecimal bd = null;
               if(value instanceof BigDecimal){
                   bd = (BigDecimal) value;
               }else{
                   bd =  new BigDecimal(((Number)value).doubleValue());
               }
               comareResult = bd.compareTo((BigDecimal) valueGetFromEntity);
               break;
           case date:
               comareResult = ((DateTime) value).compareTo((DateTime) valueGetFromEntity);
               break;
           case Boolean:
               comareResult = ((Boolean) value).compareTo((Boolean)valueGetFromEntity);
           default:
               if ("~".equals(operater)) {
                   comareResult = contain(valueGetFromEntity.toString(),value.toString() ) ? 0 : -1;
               } else {
                   comareResult = value.equals(valueGetFromEntity) ? 0 : -1;
               }
           }
       if ("=".equals(operater)) {
           return comareResult == 0;
       } else if (">".equals(operater)) {
           return comareResult < 0;
       } else if ("<".equals(operater)) {
           return comareResult > 0;
       } else if (">=".equals(operater)) {
           return comareResult <= 0;
       } else if ("<=".equals(operater)) {
           return comareResult >= 0;
       } else if ("~".equals(operater)) {
           return comareResult == 0;
       } else if ("<>".equals(operater)){
           return comareResult != 0;
       }
       throw new UnsupportedOperationException("Unsupport operater: " + operater);
   }
 }

