package com.core.pepl.codec;

import com.core.cbx.common.type.DateTime;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;

public class DateTimeCodec implements Codec<DateTime> {
    @Override
    public DateTime decode(BsonReader reader, DecoderContext decoderContext) {
        String dateTimeStr = reader.readString();
        return new DateTime(dateTimeStr);
    }

    @Override
    public void encode(BsonWriter writer, DateTime value, EncoderContext encoderContext) {
        writer.writeString(value.toString());
    }

    @Override
    public Class<DateTime> getEncoderClass() {
        return DateTime.class;
    }
}
