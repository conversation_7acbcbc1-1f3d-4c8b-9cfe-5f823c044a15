// Copyright (c) 1998-2017 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION PEPL.15.9.0P.03.0
// ============================================================================
// CHANGE LOG
// PEPL.15.9.0P.03.0 : 2025-08013, Gwyn.gao, PEPL-3305
// ============================================================================

package com.core.pepl.imp.excel;

import org.apache.commons.lang.StringUtils;

import com.core.cbx.action.ActionContext;
import com.core.cbx.action.actionContext.SaveAndConfirm;
import com.core.cbx.action.actionContext.SaveDoc;
import com.core.cbx.action.exception.ActionException;
import com.core.cbx.data.constants.Fact;
import com.core.cbx.data.constants.Vendor;
import com.core.cbx.data.def.EntityDefManager;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.imp.MessageLogger;
import com.core.pepl.action.PgsExcelSaveWorkerAction;

/**
 * <AUTHOR>
 */
public class ExcelEntitiesConverterAndSaver extends com.core.cbx.imp.excel.ExcelEntitiesConverterAndSaver {

    @Override
    protected void saveImportedDoc(final MessageLogger msgLogger, ActionContext saveDocContext) throws ActionException {
        final PgsExcelSaveWorkerAction pewa = new PgsExcelSaveWorkerAction(saveDocContext.getDoc());

        try {
            pewa.execute();
            saveDocContext.setDoc(pewa.getEntity());
        } catch (final ActionException e) {
            // for validation message collecting
            final ActionContext executingContext = pewa.getActionContext();
            if (executingContext != null) {
                saveDocContext.setAllResult(executingContext.getResultMap());
            }

            throw e;
        }

        // default logic
        if (!pewa.isSaved()) {
            super.saveImportedDoc(msgLogger, saveDocContext);
        }
    }

    @Override
    protected ActionContext createSaveDocContext(final DynamicEntity entity) {
        String module = null;

        try {
            module = EntityDefManager.getModuleCodeByEntityName(entity.getEntityName());
        } catch (final DataException e) {
            return new SaveDoc(entity);
        }

        if (entity != null
                && (StringUtils.equals(module, Vendor.MODULE_ID) || StringUtils.equals(module, Fact.MODULE_ID))) {
            return new SaveAndConfirm(entity);
        }

        return super.createSaveDocContext(entity);
    }
}
