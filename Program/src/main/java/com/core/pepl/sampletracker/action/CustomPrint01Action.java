// Copyright (c) 1998-2015 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.6.5.0
// ============================================================================
// CHANGE LOG
// PEPL.6.5.0P.5.0 : 2017-07-06, jovan.liu, PEPL-153
// PCPL.5.10GA.02.0: 2015-12-2, harry.tang, PEGP-424
// ============================================================================
package com.core.pepl.sampletracker.action;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.core.cbx.action.actionContext.CustomPrint01;
import com.core.cbx.action.actionContext.PrintDoc;
import com.core.cbx.action.actionContext.PrintForm;
import com.core.cbx.action.exception.ActionException;
import com.core.pepl.action.workerAction.ClearPrintDocCacheWorkerAction;

/**
 * <AUTHOR>
 */
public class CustomPrint01Action<T extends PrintDoc> extends com.core.cbx.action.CustomPrint01Action<CustomPrint01> {
    protected final Logger logger = LogManager.getLogger(getClass());

    @Override
    protected void process(final PrintForm actionContext) throws ActionException {
        logger.info("Start sampleTracker-PrintDocAction for PEPL");
        new ClearPrintDocCacheWorkerAction().execute();

        super.process(actionContext);
    }
}
