// Copyright (c) 1998-2015 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.5.12GA
// ============================================================================
// CHANGE LOG
// PCPL.5.12GA.03.0: 2016-05-25, harry.tang, PEGP-779
// ============================================================================
package com.core.pepl.sampletracker.action;

import java.io.File;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.core.cbx.action.PrintDocHelper;
import com.core.cbx.action.actionContext.CustomPrint02;
import com.core.cbx.action.actionContext.PrintDoc;
import com.core.cbx.action.actionContext.PrintForm;
import com.core.cbx.action.exception.ActionException;
import com.core.pepl.action.workerAction.ClearPrintDocCacheWorkerAction;

/**
 * <AUTHOR>
 */
public class CustomPrint02Action<T extends PrintDoc> extends com.core.cbx.action.CustomPrint02Action<CustomPrint02> {
    protected final Logger logger = LogManager.getLogger(getClass());
    private static final String JASPER_DIR = "JASPER_DIR";

    @Override
    protected void process(final PrintForm actionContext) throws ActionException {
        logger.info("Start sampleTracker-PrintDocAction for PEPL");
        new ClearPrintDocCacheWorkerAction().execute();

        final Map<String, Object> params = actionContext.getParams();
        final File fileFolder = PrintDocHelper.getJasperFolder();
        params.put(JASPER_DIR, fileFolder.getAbsolutePath() + File.separator);
        actionContext.setParams(params);
        super.process(actionContext);
    }
}
