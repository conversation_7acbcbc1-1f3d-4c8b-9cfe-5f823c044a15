package com.core.pepl.command.handler;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.springframework.security.oauth2.common.OAuth2AccessToken;

import com.cbx.rest.search.Oauth2TokenUtil;
import com.cbx.rest.search.RestApiClient;
import com.core.cbx.command.entity.Command;
import com.core.cbx.command.handler.CommandHandlerImp;
import com.core.cbx.common.exception.SystemException;
import com.core.cbx.conf.service.SystemConfigManager;
import com.core.cbx.data.DynamicEntityModel;
import com.core.cbx.data.constants.User;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.data.search.Criterion;
import com.core.cbx.data.search.Restriction;
import com.core.cbx.data.serializer.SerializationManager;
import com.core.cbx.security.acl.UserUtil;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;

/**
 * <AUTHOR>
 */
public class UserLoginTokenCommandHandler extends CommandHandlerImp {
    public static String LOGIN_URL;
    public static String MONGODB_URI;
    public static String MONGODB_DATABASE;
    public static String MONGODB_COLLECTION;

    static {
        MONGODB_URI = SystemConfigManager.getInstance().getConfigValue("resource.mongodb.uri");
        MONGODB_DATABASE = SystemConfigManager.getInstance().getConfigValue("resource.mongodb.database");
        MONGODB_DATABASE = StringUtils.isEmpty(MONGODB_DATABASE) ? "entity" : MONGODB_DATABASE;
        MONGODB_COLLECTION = "UserLoginToken";
    }

    @Override
    protected void processMessage(Command command, final Map<String, Object> context) throws SystemException, DataException {
        LOGGER.info("-------------- Begin UserLoginTokenCommandHandler --------------");
        Map<String, Object> tokenResult = new LinkedHashMap<>();
        try {
            tokenResult.put("transactionId", command.getReference());
            byte[] bytes = command.getPayloadAsBytes(0);
            Map<String, Object> map = (HashMap) SerializationManager.deserializeBytes(bytes);
            String password = (String) map.get("password");
            final String domainId = command.getDomainId();
            final String loginId = command.getLoginId();
            tokenResult.put("username", loginId);
            final Criterion criterion = new Criterion(User.ENTITY_NAME_USER);
            final Restriction dbloginId = Restriction.eq(User.LOGIN_ID, loginId);
            criterion.addRestriction(Restriction.and(dbloginId));
            criterion.setSelectFields(Arrays.asList(User.ID));
            final DynamicEntity dbuser = DynamicEntityModel.findUniqueBy(criterion, false);
            if(dbuser == null || !UserUtil.isPasswordMatch(dbuser, password)) {
                if(StringUtils.isEmpty(MONGODB_URI)) {
                    tokenResult.put("status", "500");
                } else {
                    tokenResult.put("status", "401");
                }
            } else {
                OAuth2AccessToken token = RestApiClient.getInstance().getOAuth2AccessToken(loginId, domainId);
                if (null == token) {
                    tokenResult.put("status", "401");
                } else {
                    String accessToken = token.getValue();
                    String tokenType = token.getTokenType();
                    int expiresIn = token.getExpiresIn();
                    String jti = token.getAdditionalInformation().get("jti") != null? 
                            token.getAdditionalInformation().get("jti").toString() : token.getAdditionalInformation().get("tokenId").toString();
                    Set<String> setScope = token.getScope();
                    String scope = null;
                    if (setScope != null) {
                        StringBuilder sb = new StringBuilder();
                        for (String element : setScope) {
                            sb.append(element);
                            sb.append(" ");
                        }
                        if (sb.length() > 1) {
                            sb.setLength(sb.length() - 1);
                        }
                        scope = sb.toString();
                    }
                    tokenResult.put("username", loginId);
                    tokenResult.put("domainId", domainId);
                    tokenResult.put("accessToken", accessToken);
                    tokenResult.put("tokenType", tokenType);
                    tokenResult.put("expireDateTime", LocalDateTime.ofInstant(token.getExpiration().toInstant(), ZoneId.systemDefault()));
                    tokenResult.put("expiresInSeconds", expiresIn);
                    tokenResult.put("scope", scope);
                    tokenResult.put("jti", jti);
                    tokenResult.put("status", "200");
                }
            }
        } catch (Exception e) {
            tokenResult.put("status", "401");
        } finally {
            try (MongoClient mongoClient = MongoClients.create(MONGODB_URI)){
                MongoDatabase database = mongoClient.getDatabase(MONGODB_DATABASE);
                MongoCollection<Document> userLoginToken = database.getCollection(MONGODB_COLLECTION);
                Document parse = new Document(tokenResult);
                userLoginToken.insertOne(parse);
            }
            LOGGER.info("-------------- End UserLoginTokenCommandHandler --------------");
        }
    }
}
