package com.core.pepl.command.handler;

import com.cbx.rest.search.RestApiClient;
import com.cbx.ws.rest.RESTContext;
import com.cbx.ws.rest.action.APIActionExecutor;
import com.cbx.ws.rest.exception.APIException;
import com.cbx.ws.rest.exception.APIRuntimeException;
import com.cbx.ws.rest.exception.FileSizeLimitException;
import com.cbx.ws.rest.exception.RESTExceptionConstants;
import com.cbx.ws.rest.jaxrs.converter.EntityMarshaller;
import com.cbx.ws.rest.jaxrs.exception.APIExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.APIRuntimeExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.AbstractExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.ActionDenyExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.ActionExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.DataDenyExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.DataUpdatedByOthersExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.DocLockedByOthersExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.DocNotLatestActionExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.DocSaveExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.DocValidationExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.ErrorDetail;
import com.cbx.ws.rest.jaxrs.exception.ErrorResponseEntity;
import com.cbx.ws.rest.jaxrs.exception.FileSizeLimitExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.IPChangeActionExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.RelatedDocLockedByOthersExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.SystemExceptionMapper;
import com.cbx.ws.rest.jaxrs.exception.ThrowableMapper;
import com.cbx.ws.rest.jaxrs.json.DynamicEntityUnmarshaller;
import com.cbx.ws.rest.jaxrs.resource.AbstractResource;
import com.cbx.ws.rest.response.ResponseEntity;
import com.cbx.ws.rest.response.SimpleResponseEntity;
import com.cbx.ws.rest.response.StreamEntityNode;
import com.cbx.ws.rest.util.RESTUtil;
import com.cbx.ws.rest.util.apiReprentation.APIRepresentationManager;
import com.core.cbx.action.exception.ActionDenyException;
import com.core.cbx.action.exception.ActionException;
import com.core.cbx.action.exception.DataUpdatedByOthersException;
import com.core.cbx.action.exception.DocLockedByOthersException;
import com.core.cbx.action.exception.DocNotLatestActionException;
import com.core.cbx.action.exception.DocSaveException;
import com.core.cbx.action.exception.DocValidationException;
import com.core.cbx.action.exception.IPChangeActionException;
import com.core.cbx.action.exception.RelatedDocLockedByOthersException;
import com.core.cbx.action.workerAction.BuildMetaDataHistoryWorkerAction;
import com.core.cbx.command.entity.Command;
import com.core.cbx.command.handler.CommandHandlerImp;
import com.core.cbx.common.exception.SystemException;
import com.core.cbx.conf.service.SystemConfigManager;
import com.core.cbx.data.DynamicEntityModel;
import com.core.cbx.data.constants.Activities;
import com.core.cbx.data.constants.ApiRepresentation;
import com.core.cbx.data.constants.CommandLog;
import com.core.cbx.data.constants.User;
import com.core.cbx.data.def.EntityDefManager;
import com.core.cbx.data.def.EntityDefModel;
import com.core.cbx.data.def.entity.FieldDefinition;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.cbx.data.entity.DynamicEntityImp;
import com.core.cbx.data.entity.EntityConstants;
import com.core.cbx.data.exception.DataDenyException;
import com.core.cbx.data.exception.DataException;
import com.core.cbx.data.search.Criterion;
import com.core.cbx.data.search.Restriction;
import com.core.cbx.data.serializer.SerializationManager;
import com.core.cbx.resource.service.SystemMessageManager;
import com.core.cbx.security.AuthenticationUtil;
import com.core.cbx.security.acl.UserUtil;
import com.core.cbx.validation.ValidationErrorMessageModel;
import com.core.cbx.validation.entity.ValidationError;
import com.core.pepl.codec.DateTimeCodec;
import com.core.snp.ref.system.util.JsonConverter;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.jboss.resteasy.mock.MockHttpRequest;
import org.jboss.resteasy.specimpl.LinkImpl;
import org.springframework.security.oauth2.common.OAuth2AccessToken;

import javax.servlet.RequestDispatcher;
import javax.servlet.ServletInputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpSession;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URI;
import java.security.Principal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ExecuteAPIActionCommandHandler extends CommandHandlerImp {
    public static String MONGODB_URI;
    public static String MONGODB_DATABASE;
    public static String MONGODB_COLLECTION;
    private static final String KEY_SPLIT_MARK_1 = ".";
    private static final String KEY_SPLIT_MARK_2 = "#";
    public static final String MESSAGE_ID = "REF095";

    static {
        MONGODB_URI = SystemConfigManager.getInstance().getConfigValue("resource.mongodb.uri");
        MONGODB_DATABASE = SystemConfigManager.getInstance().getConfigValue("resource.mongodb.database");
        MONGODB_DATABASE = StringUtils.isEmpty(MONGODB_DATABASE) ? "entity" : MONGODB_DATABASE;
        MONGODB_COLLECTION = "ApiResultData";
    }

    @Override
    protected void processMessage(Command command, final Map<String, Object> context) throws SystemException, DataException {
        boolean isSwitchUser = Boolean.FALSE;
        LOGGER.info("-------------- Begin ExecuteAPIActionCommandHandler --------------");
        Map<String, Object> result = new LinkedHashMap<>();
        byte[] bytes = command.getPayloadAsBytes(0);
        Map<String, Object> map = (HashMap) SerializationManager.deserializeBytes(bytes);
        Object body = map.get("body");
        String requestMethod = (String) map.get("requestMethod");
        String requestUrl = (String) map.get("requestUrl");
        StringBuffer requestUrlBuffer = (StringBuffer) map.get("requestUrlBuffer");
        String requestQueryString = (String) map.get("requestQueryString");
        Map<String, String> requestHeaderMap = (Map<String, String>) map.get("requestHeaderMap");
        Map<String, String[]> requestParameterMap = (Map<String, String[]>) map.get("requestParameterMap");
        String pathInfo = (String) map.get("pathInfo");

        HttpServletRequest request = new CustomHttpServletRequest(
                requestMethod, requestUrl, requestUrlBuffer, requestQueryString, requestHeaderMap, requestParameterMap, pathInfo);
        try {
            if (!StringUtils.equals(command.getLoginId(), AuthenticationUtil.SYSTEM_TEMP_LOGIN)) {
                AuthenticationUtil.switchUser(command,command.getLoginId());
                isSwitchUser = Boolean.TRUE;
            }
            result.put("transactionId", command.getReference());

            AbstractResource apiResourceImp = new APIResourceImp(request);
            Response response;
            if (body == null) {
                response = apiResourceImp.executeAPI(request);
            }else {
                DynamicEntityUnmarshaller dynamicEntityUnmarshaller = new DynamicEntityUnmarshaller();

                DynamicEntity entity = dynamicEntityUnmarshaller.readDynamicEntityByApiEntity((Map<String, Object>) body, RESTUtil.getApiVersion(request));
                response = apiResourceImp.executeAPI(request, entity);
            }
            if (response == null) {
                if(StringUtils.isEmpty(MONGODB_URI)) {
                    result.put("status", 500);
                } else {
                    result.put("status", 401);
                }
            } else {
                result.put("response", null);
                result.put("headers", null);
                SimpleResponseEntity entity = (SimpleResponseEntity) response.getEntity();
                MultivaluedMap<String, Object> headers = response.getHeaders();
                if (entity != null) {
                    final Map<String, Object> dataMap = new LinkedHashMap<>();
                    if (entity.getEntity() instanceof DynamicEntity) {
                        this.writeEntity(entity, request, dataMap);
                    } else if (entity.getEntity() instanceof StreamEntityNode) {
                        StreamEntityNode streamEntityNode = (StreamEntityNode) entity.getEntity();
                        dataMap.put("bytes", streamEntityNode.getBytes());
                        dataMap.put("fileName", streamEntityNode.getFileName());
                        dataMap.put("fileSize", streamEntityNode.getFileSize());
                    } else {
                        Map<String, Object> entity1 = (Map<String, Object>) entity.getEntity();
                        dataMap.putAll(entity1);
                    }
                    result.put("response", dataMap);
                }
                if (headers != null && headers.get("Link") != null) {
                    List<Object> linkList = headers.get("Link");
                    List<Map<String, Object>> linkMapList = new ArrayList<>();
                    for (Object link : linkList) {
                        if (link instanceof LinkImpl) {
                            LinkImpl linkImpl = (LinkImpl) link;
                            String rel = linkImpl.getRel();
                            URI uri = linkImpl.getUri();
                            String uriString = uri.toString();
                            Map<String, Object> linkImplMap = new HashMap<>();
                            linkImplMap.put("rel", rel);
                            linkImplMap.put("uri", uriString);
                            linkMapList.add(linkImplMap);
                        }
                    }
                    result.put("Link", linkMapList);
                }
                result.put("status", response.getStatus());
            }
        } catch (DataUpdatedByOthersException e) {
            DataUpdatedByOthersExceptionMapper mapper = new DataUpdatedByOthersExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (RelatedDocLockedByOthersException e) {
            RelatedDocLockedByOthersExceptionMapper mapper = new RelatedDocLockedByOthersExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (DocLockedByOthersException e) {
            DocLockedByOthersExceptionMapper mapper = new DocLockedByOthersExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (DocNotLatestActionException e) {
            DocNotLatestActionExceptionMapper mapper = new DocNotLatestActionExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (DocSaveException e) {
            DocSaveExceptionMapper mapper = new DocSaveExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (FileSizeLimitException e) {
            FileSizeLimitExceptionMapper mapper = new FileSizeLimitExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (IPChangeActionException e) {
            IPChangeActionExceptionMapper mapper = new IPChangeActionExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (DocValidationException e) {
            DocValidationExceptionMapper mapper = new DocValidationExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (APIException e){
            APIExceptionMapper mapper = new APIExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (APIRuntimeException e) {
            APIRuntimeExceptionMapper mapper = new APIRuntimeExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (ActionDenyException e) {
            ActionDenyExceptionMapper mapper = new ActionDenyExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (ActionException e) {
            ActionExceptionMapper mapper = new ActionExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (DataDenyException e) {
            DataDenyExceptionMapper mapper = new DataDenyExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (SystemException e) {
            SystemExceptionMapper mapper = new SystemExceptionMapper();
            toResponse(result, request, e, mapper);
        } catch (Throwable e) {
            ThrowableMapper mapper = new ThrowableMapper();
            toResponse(result, request, e, mapper);
        } finally {
            CodecRegistry customCodecRegistry = CodecRegistries.fromCodecs(new DateTimeCodec());
            CodecRegistry defaultCodecRegistry = MongoClientSettings.getDefaultCodecRegistry();
            CodecRegistry combinedCodecRegistry = CodecRegistries.fromRegistries(customCodecRegistry, defaultCodecRegistry);
            MongoClientSettings settings = MongoClientSettings.builder()
                    .applyConnectionString(new ConnectionString(MONGODB_URI))
                    .codecRegistry(combinedCodecRegistry)
                    .build();
            try (MongoClient mongoClient = MongoClients.create(settings)){
                MongoDatabase database = mongoClient.getDatabase(MONGODB_DATABASE);
                MongoCollection<Document> userLoginToken = database.getCollection(MONGODB_COLLECTION);
                Document parse = new Document(result);
                userLoginToken.insertOne(parse);
            }
            if (isSwitchUser) {
                AuthenticationUtil.exitSwitchUser();
            }
            LOGGER.info("-------------- End UserLoginTokenCommandHandler --------------");
        }
    }

    public class APIResourceImp extends AbstractResource{
        public APIResourceImp(final HttpServletRequest request) {
            this.request = request;
        }
    }

    public class CustomHttpServletRequest implements HttpServletRequest {
        private String method;
        private String requestURI;
        private StringBuffer requestURIBuffer;
        private String queryString;
        private Map<String, String> headers;
        private Map<String, String[]> parameters;
        private String pathInfo;

        public CustomHttpServletRequest(String method, String requestURI, StringBuffer requestURIBuffer, String queryString,
                                        Map<String, String> headers, Map<String, String[]> parameters, String pathInfo) {
            this.method = method;
            this.requestURI = requestURI;
            this.requestURIBuffer = requestURIBuffer;
            this.queryString = queryString;
            this.headers = headers;
            this.parameters = parameters;
            this.pathInfo = pathInfo;
        }

        @Override
        public String getMethod() {
            return method;
        }

        @Override
        public String getRequestURI() {
            return requestURI;
        }

        @Override
        public StringBuffer getRequestURL() {
            return requestURIBuffer;
        }

        @Override
        public String getQueryString() {
            return queryString;
        }

        @Override
        public String getHeader(String name) {
            return headers.get(name);
        }

        @Override
        public Enumeration<String> getHeaders(String name) {
            List<String> values = new ArrayList<>();
            if (headers.containsKey(name)) {
                values.add(headers.get(name));
            }
            return Collections.enumeration(values);
        }

        @Override
        public Enumeration<String> getHeaderNames() {
            return Collections.enumeration(headers.keySet());
        }

        @Override
        public String getParameter(String name) {
            String[] values = parameters.get(name);
            if (values != null && values.length > 0) {
                return values[0];
            }
            return null;
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            return parameters;
        }

        @Override
        public Enumeration<String> getParameterNames() {
            return Collections.enumeration(parameters.keySet());
        }

        @Override
        public String[] getParameterValues(String name) {
            return parameters.get(name);
        }

        @Override
        public String getAuthType() {
            return null;
        }

        @Override
        public Cookie[] getCookies() {
            return new Cookie[0];
        }

        @Override
        public long getDateHeader(String name) {
            return 0;
        }

        @Override
        public int getIntHeader(String name) {
            return 0;
        }

        @Override
        public String getContextPath() {
            return null;
        }

        @Override
        public String getPathInfo() {
            return this.pathInfo;
        }

        @Override
        public String getPathTranslated() {
            return null;
        }

        @Override
        public String getProtocol() {
            return null;
        }

        @Override
        public String getScheme() {
            return null;
        }

        @Override
        public String getServerName() {
            return null;
        }

        @Override
        public int getServerPort() {
            return 0;
        }

        @Override
        public String getRemoteUser() {
            return null;
        }

        @Override
        public boolean isUserInRole(String role) {
            return false;
        }

        @Override
        public java.security.Principal getUserPrincipal() {
            return null;
        }

        @Override
        public String getRequestedSessionId() {
            return null;
        }

        @Override
        public String getServletPath() {
            return null;
        }

        @Override
        public javax.servlet.http.HttpSession getSession(boolean create) {
            return null;
        }

        @Override
        public javax.servlet.http.HttpSession getSession() {
            return null;
        }

        @Override
        public boolean isRequestedSessionIdValid() {
            return false;
        }

        @Override
        public boolean isRequestedSessionIdFromCookie() {
            return false;
        }

        @Override
        public boolean isRequestedSessionIdFromURL() {
            return false;
        }

        @Override
        public boolean isRequestedSessionIdFromUrl() {
            return false;
        }

        @Override
        public Object getAttribute(String name) {
            return null;
        }

        @Override
        public Enumeration<String> getAttributeNames() {
            return Collections.emptyEnumeration();
        }

        @Override
        public String getCharacterEncoding() {
            return null;
        }

        @Override
        public void setCharacterEncoding(String env) throws UnsupportedEncodingException {
        }

        @Override
        public int getContentLength() {
            return 0;
        }

        @Override
        public String getContentType() {
            return null;
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            return null;
        }

        @Override
        public BufferedReader getReader() throws IOException {
            return null;
        }

        @Override
        public String getRemoteAddr() {
            return null;
        }

        @Override
        public String getRemoteHost() {
            return null;
        }

        @Override
        public void setAttribute(String name, Object o) {
        }

        @Override
        public void removeAttribute(String name) {
        }

        @Override
        public Locale getLocale() {
            return null;
        }

        @Override
        public Enumeration<Locale> getLocales() {
            return Collections.emptyEnumeration();
        }

        @Override
        public boolean isSecure() {
            return false;
        }

        @Override
        public RequestDispatcher getRequestDispatcher(String path) {
            return null;
        }

        @Override
        public String getRealPath(String path) {
            return null;
        }

        @Override
        public int getRemotePort() {
            return 0;
        }

        @Override
        public String getLocalName() {
            return null;
        }

        @Override
        public String getLocalAddr() {
            return null;
        }

        @Override
        public int getLocalPort() {
            return 0;
        }
    }

    private void writeEntity(final SimpleResponseEntity<DynamicEntity> entity, final HttpServletRequest request, final Map<String, Object> map) {
        final DynamicEntity doc = entity.getEntity();
        String apiVersion = RESTUtil.getApiVersion(request);
        if (doc != null) {
            final Map<String, Object> context = new LinkedHashMap<String, Object>();
            context.put(ApiRepresentation.API_VERSION, apiVersion);
            context.put(EntityMarshaller.KEY_MAIN_ENTITY, doc);
            final Map<Object, String> entityPath = new HashMap<Object, String>();
            context.put(EntityMarshaller.KEY_ENTITY_PATH, entityPath);
            processEntityPath(doc, context, StringUtils.EMPTY);
            new EntityMarshaller(doc).marshal(map, context);
            entityPath.clear();
        }
    }

    private void processEntityPath(final DynamicEntity doc, final Map<String, Object> context, final String path) {
        if (doc == null) {
            return;
        }
        try {
            final String mainEntityName;
            if (EntityDefManager.isMainEntity(doc.getEntityName())) {
                mainEntityName = doc.getEntityName();
            } else {
                mainEntityName = EntityDefManager.getMainEntityName(doc.getEntityName());
            }
            final String representationKey = mainEntityName + "_Representation_" + ObjectUtils.toString(context.get(ApiRepresentation.API_VERSION));
            DynamicEntity apiRepresentation = (DynamicEntity) context.get(representationKey);
            if (apiRepresentation == null) {
                apiRepresentation = APIRepresentationManager.loadAPIRepresentationByEntityName(
                        mainEntityName, ObjectUtils.toString(context.get(ApiRepresentation.API_VERSION)));
                context.put(representationKey, apiRepresentation);
            }
            final Map<Object, String> entityPath = (Map<Object, String>) context.get(EntityMarshaller.KEY_ENTITY_PATH);
            for (final Map.Entry<String, Object> entry : doc.entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                final FieldDefinition fieldDef = EntityDefManager.getFieldDefinition(doc.getEntityName(),
                        entry.getKey());
                if (fieldDef == null) {
                    continue;
                }
                if (EntityDefModel.isCollectionFieldType(fieldDef.getFieldType())) {
                    final String fieldName = APIRepresentationManager.getMappingNameByFieldId(apiRepresentation,
                            entry.getKey(), doc.getEntityName());
                    final List<DynamicEntity> childEntities = (List<DynamicEntity>) doc.getEntityCollection(entry
                            .getKey());
                    String tempPath = StringUtils.EMPTY;
                    for (final DynamicEntity entity : childEntities) {
                        if (StringUtils.isEmpty(path)) {
                            tempPath = fieldName + KEY_SPLIT_MARK_2
                                    + entity.getString(EntityConstants.PTY_INTERNAL_SEQ_NO);
                        } else {
                            tempPath = path + KEY_SPLIT_MARK_1 + fieldName + KEY_SPLIT_MARK_2
                                    + entity.getString(EntityConstants.PTY_INTERNAL_SEQ_NO);
                        }
                        entityPath.put(entity, tempPath);
                        processEntityPath(entity, context, tempPath);
                    }
                }
            }
        } catch (final DataException e) {
            LOGGER.error("REST API: Source can not be found.", e);
        }
    }

    private void toResponse(final Map<String, Object> result, final HttpServletRequest request, final Throwable e, final AbstractExceptionMapper mapper) {
        mapper.setRequest(request);
        Response response = mapper.toResponse(e);
        result.put("status", response.getStatus());
        ErrorResponseEntity entity = (ErrorResponseEntity) response.getEntity();
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("code", entity.getCode());
        List<Map<String, Object>> errors = new ArrayList<>();
        Map<String, Object> error = new HashMap<>();
        for (ErrorDetail errorDetails : entity.getErrors()) {
            error.put("message", errorDetails.getMessage());
            error.put("location", errorDetails.getLocation());
            error.put("name", errorDetails.getName());
            errors.add(error);
        }
        bodyMap.put("errors", errors);
        result.put("response", bodyMap);
    }
}
