// Copyright (c) 1998-2015 Core Solutions Limited. All rights reserved.
// ============================================================================
// CURRENT VERSION CNT.6.5.0
// ============================================================================
// CHANGE LOG
// PEPL.6.5.0P.4.0 : 2017-07-03, jovan.liu, PEPL-27
// PCPL.5.10GA.00.0: 2015-10-22, harry.tang, PEGP-441
// PCPL.5.9GA.03.4 : 2015-09-17, shermen.wu, PEGP-345
// PCPL.5.9GA.03.2 : 2015-09-08, harry.tang, PEGP-331
// ============================================================================
package com.core.pepl.item.action;

import com.core.cbx.action.actionContext.CustomPrint01;
import com.core.cbx.action.actionContext.PrintDoc;
import com.core.cbx.action.actionContext.PrintForm;
import com.core.cbx.action.constants.PrintConstants;
import com.core.cbx.action.exception.ActionException;
import com.core.cbx.data.constants.Item;
import com.core.cbx.data.entity.DynamicEntity;
import com.core.pepl.action.workerAction.ClearPrintDocCacheWorkerAction;

public class CustomPrint01Action<T extends PrintDoc> extends
com.core.cbx.action.CustomPrint01Action<CustomPrint01>{

    private static final String OUTPUT_FILE_NAME = "outputFileName";

    @Override
    protected void process(final PrintForm actionContext) throws ActionException {
        logger.info("Start Item-PrintDocAction for PEPL");
        new ClearPrintDocCacheWorkerAction().execute();
        final DynamicEntity doc = actionContext.getDoc();

        super.process(actionContext);
        actionContext.setResult(OUTPUT_FILE_NAME, doc.getString(Item.ITEM_NO) + "."
                + PrintConstants.REPORT_EXTENSION_PDF);
    }

}
